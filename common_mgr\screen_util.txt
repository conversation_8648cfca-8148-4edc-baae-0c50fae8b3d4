local os = os
local typeof = typeof
local require = require
local math = math
local package = package

local event = require "event"
local Screen = CS.UnityEngine.Screen
local Application = CS.UnityEngine.Application
local RuntimePlatform = CS.UnityEngine.RuntimePlatform
local ParticleSystemRenderer = CS.UnityEngine.ParticleSystemRenderer
local Q1SDK = CS.Q1.Q1SDK
local const = require "const"
local RectTransform = CS.UnityEngine.RectTransform
local GameObject = CS.UnityEngine.GameObject
local device_param_util = require "device_param_util"

--- 屏幕工具
module("screen_util")

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

--- 上一次记录的屏幕尺寸
local prevScreenSize = { x = Screen.width, y = Screen.height }

--- 当前的屏幕尺寸
local currScreenSize = { x = prevScreenSize.x, y = prevScreenSize.y }

--- 获取上一次记录的屏幕尺寸
---@return { x: number, y: number }
function GetPrevScreenSize()
    return prevScreenSize
end

--- 获取当前的屏幕尺寸
---@return { x: number, y: number }
function GetScreenSize()
    return currScreenSize
end

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

--- 获取屏幕宽高比
---@return number
function GetScreenAspect()
    return (currScreenSize.x / currScreenSize.y)
end

--- 获取正交摄像机的尺寸
---@param objSizeX number 物体的水平尺寸
---@return number
function GetCameraSizeWithObjSizeX(objSizeX)
    local screenAspect = GetScreenAspect()
    return (objSizeX / (2 * screenAspect))
end

--- 获取正交摄像机的垂直移动范围
---@param objSize { x: number, y: number } 物体的尺寸
---@param paddingTop number 顶部间距
---@param paddingBottom number 底部间距
---@return { min: number, max: number }
function GetCameraVerticalMovementRangeWithObjSize(objSize, paddingTop, paddingBottom)
    paddingTop = paddingTop or 0
    paddingBottom = paddingBottom or 0

    local halfObjSizeY = (objSize.y / 2)
    local cameraSize = GetCameraSizeWithObjSizeX(objSize.x)
    return {
        min = halfObjSizeY + cameraSize + paddingBottom,
        max = halfObjSizeY - cameraSize - paddingTop,
    }
end

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

--- 屏幕尺寸变化回调
---@param newSize { x: number, y: number } 新的尺寸
---@param oldSize { x: number, y: number } 旧的尺寸
local function OnScreenSizeChanged(newSize, oldSize)
    event.Trigger(event.SCREEN_SIZE_CHANGED_PREPASS, newSize, oldSize)
    event.Trigger(event.SCREEN_SIZE_CHANGED, newSize, oldSize)
end

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
--- 节流最小通知时间间隔
local THROTTLING_INTERVAL = 0.5

--- 上一次通知的时间
local lastNotifyTime = 0
--是否是折叠屏全屏
local isFoldableScreen = false
--是否显示小游戏内遮罩
local isShowMiniGameShiled = false

--宽高
width = Screen.width
height = Screen.height
--折叠屏全屏比例
foldableScreenRatio = 0.769
--超长屏界限
longScreenRatioLimit = 0.44

--- 每帧调用
function Update()
    if not const.OPEN_SCREEN_ADAPTIVE then
        return
    end

    local AndroidScreenUtil = require "AndroidScreenUtil"
    if not AndroidScreenUtil.IsFoldable() then
        if (os.server_time() - lastNotifyTime < THROTTLING_INTERVAL) then
            return
        end
    end

    local screenWidth = Screen.width
    local screenHeight = Screen.height
    if Application.platform == RuntimePlatform.Android then
        screenWidth = AndroidScreenUtil.GetScreenWidth()
        screenHeight = AndroidScreenUtil.GetScreenHeight()
    end

    if screenWidth == 0 or screenHeight == 0 then
        return
    end

    if screenWidth == currScreenSize.x and screenHeight == currScreenSize.y then
        return
    end

    -- 记录屏幕尺寸
    prevScreenSize.x = currScreenSize.x
    prevScreenSize.y = currScreenSize.y
    currScreenSize.x = screenWidth
    currScreenSize.y = screenHeight
    --dirty = false
    SetIsFoldableScreen(screenWidth, screenHeight)
    SetWidthAndHeight(screenWidth, screenHeight)
    CheckShowOrHideShield()

    Screen.SetResolution(screenWidth, screenHeight, true)

    if Application.platform == RuntimePlatform.Android then
        local displayHeight = AndroidScreenUtil.GetDisplayHeight()
        device_param_util.ResetScreenHeightAdaptScale(screenHeight/displayHeight)
    elseif Application.platform == RuntimePlatform.IPhonePlayer then
        device_param_util.SetScreenHeightAdaptScale(screenHeight)
    end
    

    -- 通知
    lastNotifyTime = os.server_time()
    local util = require "util"
    util.ReseteAdaptiveWidthScaleFactorWithEff()
    OnScreenSizeChanged(currScreenSize, prevScreenSize)
end

--- 是否已初始化
local isInited = false

--- 初始化
function Init()
    if not const.OPEN_SCREEN_ADAPTIVE then
        return
    end
    if isInited then
        return
    end
    isInited = true
    local AndroidScreenUtil = require "AndroidScreenUtil"
    AndroidScreenUtil.GetSafeArea()
    SetIsFoldableScreen(Screen.width, Screen.height)
    SetWidthAndHeight(Screen.width, Screen.height)
    CheckShowOrHideShield()
    --新老包都支持屏幕变化
    if Q1SDK.Instance and Q1SDK.Instance.RegisterOnConfigurationChanged then
        local q1sdk = require "q1sdk"
        q1sdk.RegisterOnConfigurationChanged(Update)
    else
        event.Register(event.CSUpdate, Update)
    end
    event.Register(event.CHECK_FULL_SCREEN_SHIELD, CheckShowOrHideShield)
end

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
--检查分辨率改变时候 遮罩是否显示 仅在折叠屏全屏显示
function CheckShowOrHideShield()
    local ui_window_mgr = require "ui_window_mgr"

    if package.loaded["puzzlegame_mgr"] == nil then
        if isFoldableScreen then
            -- if not ui_window_mgr:IsModuleShown("ui_shield") then
            --     ui_window_mgr:ShowModule("ui_shield")
            -- end
        else
            if ui_window_mgr:IsModuleShown("ui_shield") then
                ui_window_mgr:UnloadModule("ui_shield")
            end
        end
    else
        local puzzlegame_mgr = require "puzzlegame_mgr"
        if isFoldableScreen then
            if puzzlegame_mgr.GetIsRunPuzzleGame() and puzzlegame_mgr.GetIsTowerGame() then
                --内置塔楼游戏
                if not ui_window_mgr:IsModuleShown("ui_shield") then
                    ui_window_mgr:ShowModule("ui_shield")
                end
            elseif puzzlegame_mgr.GetIsRunPuzzleGame() and not puzzlegame_mgr.GetIsTowerGame() then
                --其他小游戏
                if ui_window_mgr:IsModuleShown("ui_puzzle_game_defeat") or ui_window_mgr:IsModuleShown("ui_puzzle_game_result") then
                    --小游戏失败、结算
                    if not ui_window_mgr:IsModuleShown("ui_shield") then
                        ui_window_mgr:ShowModule("ui_shield")
                    end
                else
                    --小游戏内
                    if isShowMiniGameShiled then
                        --展示小游戏遮罩
                        if not ui_window_mgr:IsModuleShown("ui_shield") then
                            ui_window_mgr:ShowModule("ui_shield")
                        end
                    else
                        if ui_window_mgr:IsModuleShown("ui_shield") then
                            ui_window_mgr:UnloadModule("ui_shield")
                        end
                    end
                end
            else
                --主游戏内
                -- if not ui_window_mgr:IsModuleShown("ui_shield") then
                --     ui_window_mgr:ShowModule("ui_shield")
                -- end
            end
        else
            if ui_window_mgr:IsModuleShown("ui_shield") then
                ui_window_mgr:UnloadModule("ui_shield")
            end
        end
    end
end

--- 当前设备屏幕是否为可折叠屏屏幕 全屏
function IsFoldableScreen()
    return isFoldableScreen
end

--设置展示小游戏内遮罩
function SetMiniGameShield(bool)
    isShowMiniGameShiled = bool
    CheckShowOrHideShield()
    event.Trigger(event.SET_MINI_GAME_SHIELD, bool)
end

function IsShowMiniGameShiled()
    return isShowMiniGameShiled
end

--设置宽高
function SetWidthAndHeight(screenWidth, screenHeight)
    if (isFoldableScreen) then
        width, height = 720, 1280
    else
        width, height = screenWidth, screenHeight
    end
end

--是否拉伸
function IsStretched(rt)
    local result = Vector2Equal(rt.anchorMin, { x = 0, y = 0 }) and
            Vector2Equal(rt.anchorMax, { x = 1, y = 1 }) and
            Vector2Equal(rt.offsetMin, { x = 0, y = 0 }) and
            Vector2Equal(rt.offsetMax, { x = 0, y = 0 })
    return result
end

--比较vector是否相等
function Vector2Equal(a, b)
    return a.x == b.x and a.y == b.y
end

--设置是否是折叠屏 全屏显示
function SetIsFoldableScreen(screenWidth, screenHeight)
    local aspectRatio = screenWidth / screenHeight
    isFoldableScreen = aspectRatio >= foldableScreenRatio
end

--- 设计分辨率比例
local DesignScreenRatio = 1280 / 720
--- 设计摄像机尺寸
local DesignCameraSize = 6.32
--- 摄像机尺寸范围
local CameraSizeRange = { min = 2, max = 7.9 }

--- 设置场景摄像机
function SetSceneCamera(camera)
    local curRatio = height / width
    local size = curRatio * (DesignCameraSize / DesignScreenRatio)
    size = math.clamp(size, CameraSizeRange.min, CameraSizeRange.max)
    camera.orthographicSize = size
end

--- 设置场景摄像机 限制特别最大尺寸
function SetSceneCameraMaxSize(camera, maxSize)
    local curRatio = height / width
    local size = curRatio * (DesignCameraSize / DesignScreenRatio)
    size = math.clamp(size, CameraSizeRange.min, maxSize)
    camera.orthographicSize = size
end

--所有粒子缩放
function SetParticleAllScale(node, scale)
    local util = require "util"
    if util.IsObjNull(node) then
        return
    end

    local childs = node:GetComponentsInChildren(typeof(ParticleSystemRenderer))
    if childs and childs.Length > 0 then
        for i = 0, childs.Length - 1 do
            util.SetTransformScale(childs[i].gameObject.transform, scale)
        end
    end
end

--所有粒子缩放 根据初始 适配
function SetParticleAllScaleByOrigin(node, origin, needAdpative)
    local util = require "util"
    if util.IsObjNull(node) then
        return
    end

    local childs = node:GetComponentsInChildren(typeof(ParticleSystemRenderer))
    local scaleFac = util.GetAdaptiveWidthScaleFactorWithEff()
    if childs and childs.Length > 0 then
        for i = 0, childs.Length - 1 do
            if needAdpative then
                childs[i].gameObject.transform.localScale = origin[i] * scaleFac
            else
                childs[i].gameObject.transform.localScale = origin[i]
            end
        end
    end
end

local canvasWithMeshRectTransform = nil
function GetCanvasWithMesh()
    if not canvasWithMeshRectTransform then
        canvasWithMeshRectTransform = GameObject.Find("UIRoot/CanvasWithMesh"):GetComponent(typeof(RectTransform))
    end
    return canvasWithMeshRectTransform
end

--所有粒子缩放
function GetParticleAllScale(node)
    local util = require "util"
    if util.IsObjNull(node) then
        return
    end

    local t = {}
    local childs = node:GetComponentsInChildren(typeof(ParticleSystemRenderer))
    if childs and childs.Length > 0 then
        for i = 0, childs.Length - 1 do
            t[i] = childs[i].gameObject.transform.localScale
        end
    end
    return t
end

local gameStandardSize = { x = 720, y = 1560 }
function GetScreenHeightAdaptScale()
    local screenH = height or Screen.height
    return screenH / gameStandardSize.y
end