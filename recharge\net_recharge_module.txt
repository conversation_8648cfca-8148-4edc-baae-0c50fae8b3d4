local require = require

local ipairs = ipairs
local pairs = pairs
local table = table
local dump = dump
local string = string
local tostring = tostring
local tonumber = tonumber
local os = os
local math = math
local type = type
local pcall = pcall

local log = require "log"
local q1sdk = require "q1sdk"
local login_pb = require "login_pb"
local lang = require "lang_util"
local flow_text = require "flow_text"
local util = require "util"
local game_scheme = require "game_scheme"
local skep_mgr    = require "skep_mgr"
local json = require "dkjson"
local game_config = require "game_config"
local package_pb = require "package_pb"
local adjust = require "adjust"
local firebase = require "firebase"
local facebook = require "facebook"
local ui_window_mgr = require "ui_window_mgr"
local challenge_pb = require "challenge_pb"
local prop_pb = require "prop_pb"
local const = require "const"
-- local cs_logcat_mgr = require "cs_logcat_mgr"
local files_version_mgr = require "files_version_mgr"
local money_type_mgr = require "money_type_mgr"
local popups_cache = require "popups_cache"
local festival_activity_cfg = require "festival_activity_cfg"
local super_month_gift_mgr = require "super_month_gift_mgr"

local WWW = CS.UnityEngine.WWW
local Debug = CS.UnityEngine.Debug
local Application = CS.UnityEngine.Application
local RuntimePlatform = CS.UnityEngine.RuntimePlatform
local Q1SDK         = CS.Q1.Q1SDK
local Application = CS.UnityEngine.Application
local DateTime = CS.System.DateTime
local DateTimeFormatInfo = CS.System.Globalization.DateTimeFormatInfo

module("net_recharge_module")
local logger = require("logger").new("sw_log_net_recharge_module", 0)
function localPrint( ... )
	logger.Warning(4,...)
end
local print = localPrint

local event = require "event"
local msg_pb = require "msg_pb"
local recharge_pb = require "recharge_pb"
local net = require "net"
local net_route = require "net_route"
local error_code_pb = require "error_code_pb"

local lastReportPostTime = 0  --上次反馈时间

local queue = {}
local pendingQueue = {}
local reportQueue = {}
local lastBuyTime = 0
local boxChooseCash = nil
local googleShopData = {}
local GooglePrice = {}
local parities = 1

ID_OFFSET = 10000
local PrintEnable = false
local stt = util.RegisterConsole("recharge_log",0,function ( st )
	PrintEnable = st == 1
end)

--region==================支付测试=============================
local IsTestRecharge = false
local TestRechargeSave = false
util.RegisterConsole("test_recharge_save",0,function ( st )
	TestRechargeSave = st == 1
end)

local KEY_1 = "test_recharge_1-1000"
local KEY_2 = "test_recharge_1001-2000"
util.RegisterConsole(KEY_1, 0, function(st)
	if st == 1 then
		local ok = pcall(function()
			---@type test_recharge
			local test_recharge = require "test_recharge"
			if test_recharge then
				IsTestRecharge = true
				test_recharge.TestRechargeAll(TestRechargeSave, 1, 1000, KEY_1)
			end

			PlayerPrefs.SetInt(KEY_1, 0) -- 需要还原，不然可能加载不到
			PlayerPrefs.Save()
		end)

		if not ok then
			log.Warning("test_recharge error")
		end
	end
end)

util.RegisterConsole(KEY_2, 0, function(st)
	if st == 1 then
		local ok = pcall(function()
			---@type test_recharge
			local test_recharge = require "test_recharge"
			if test_recharge then
				IsTestRecharge = true
				test_recharge.TestRechargeAll(TestRechargeSave, 1001, 2300, KEY_2)
			end

			PlayerPrefs.SetInt(KEY_2, 0) -- 需要还原，不然可能加载不到
			PlayerPrefs.Save()
		end)

		if not ok then
			log.Warning("test_recharge error")
		end
	end
end)

function TestEventTrigger(evt, status, errorCode)
	if IsTestRecharge then
		event.Trigger(evt, status, errorCode)
	end
end
--endregion==================支付测试=============================

-- 支付相关事件上报
function ReportRechargeAction(action, msg)
	local player_mgr = require "player_mgr"
	local strMsg = msg or ""

	local roleID = player_mgr.GetPlayerRoleID()
	local userID = player_mgr.GetPlayerUserID()
	local setting_server_data = require "setting_server_data"
	local worldID = setting_server_data.GetLoginWorldID()

	local roleName = player_mgr.GetRoleName()
	local roleLv = player_mgr.GetPlayerLV()

	q1sdk.UserEvent(worldID, roleID, roleName, roleLv, userID, action, strMsg)
end

-- 从显示用物品 id 按平台，渠道等方式映射到对应后台配置的物品 id
function GetRechargeGoodsID( goodsID )
	local realGoodsID = goodsID
	local recharge_data = require "recharge_data"
	-- 改成配置
	if not recharge_data.IsUseRechargeChannel() and game_config.ENABLE_GOOGLE then
		local files_version_mgr = require "files_version_mgr"
		local _recharge_offset = files_version_mgr.ApkUpdateConfigTryGetValue("recharge_offest")
		print("recharge_offest:", _recharge_offset)
		if _recharge_offset then
			realGoodsID = realGoodsID + ID_OFFSET * tonumber(tostring(_recharge_offset))
			print("realGoodsID:",realGoodsID)
			return realGoodsID
		end
	end


	realGoodsID = recharge_data.GetChannelGoodsID(realGoodsID)

	return realGoodsID
end

-- 是否是google账号
function isGooglePlay( )
	local googleLogin = false
	local account_data = require "account_data"
	local LastLoginType = account_data.GetLoginType()
	if LastLoginType == login_pb.enLoginPartnerID_Google then
		googleLogin = true
	end
	print("googleLogin>>>>>>>>>>>>>>>>>>",googleLogin)
	return googleLogin
end

function getTempId ( temp ,id )
	for k,v in pairs(temp) do
		if v.googleId == id then
			return v
		end
	end
	return false
end

function ClearGoogleShopData(  )
	googleShopData = {}
	parities = 1
	GooglePrice = {}
end

-- 初始化遍历RechargeGoogleShop请求所有的google商品信息
function SetGoogleShopData( )
	local q1sdk = require "q1sdk"
	local const = require "const"
	if not isGooglePlay() or not const.USE_GOOGLE_SDK then return end
	googleShopData = {}
	parities = 1
	for i=1,game_scheme:RechargeGoogleShop_nums() do
		local cfg = game_scheme:RechargeGoogleShop_1(i)
		local params = string.split(cfg.strName, "*")
		local id = params[2]
		if not getTempId(GooglePrice,id) then
			table.insert(GooglePrice,{price=cfg.iPrice,googleId = id})
		end
	end
	-- print("SetGoogleShopData")
	-- dump(GooglePrice)
	-- local str = '{"productId":"q1_hero_gp14","packageName":"","type":"inapp","price":"MYR62.99","price_amount_micros":62990000,"price_currency_code":"MYR","original_price":"MYR62.99","original_price_micros":62990000,"title":"Diamond*2500 (X-HERO: Marvelous Adventure)","description":"You will get Diamond*2500 after purchase","subscriptionPeriod":"","freeTrialPeriod":"","introductoryPrice":"","introductoryPriceAmountMicros":0,"introductoryPricePeriod":"","introductoryPriceCycles":"","iconUrl":"","rewardToken":false}'
	for i,v in ipairs(GooglePrice) do
		q1sdk.QueryGoogleProductInfoById(v.googleId,function(suc, code, str)
			if suc then
				-- print("suc:",suc,"code:",code ,"str:",str)
				local googleData = json.decode(str)
				-- local googleId = v.googleId--[[googleData.productId]]
				local googleId = googleData.productId
				googleShopData[googleId] = googleData
				-- dump(googleData)
				if parities == 1 and googleId == "q1_hero_gp9" then
					local price = getPriceByGooglePlayId(googleId)
					-- print("price:",price,price/100,googleId)
					-- print("googleData.price_amount_micro:",googleData.price_amount_micro,googleData.price_amount_micros/1000000)
					parities = --[[(price/100)]]keepTwoDecimalPlaces((price/100)/(googleData.price_amount_micros/1000000))
					print("parities:",parities)
				end
			end
		end)
	end
end

function keepTwoDecimalPlaces(decimal)
	decimal = decimal * 100
	if decimal % 1 < 0.1 then
		decimal=math.ceil(decimal)
	else
		decimal=math.floor(decimal)
	end
	return  decimal * 0.01
end

function getParities(  )
	return parities
end
-- 通过商品价钱获取GooglePlayId
function getGooglePlayIdByPrice ( price )
	-- print("price:",price)
	for k,v in ipairs(GooglePrice) do
		if v.price == price then
			return v.googleId
		end
	end
	return false
end

function getPriceByGooglePlayId ( GooglePlayId )
	-- print("price:",price)
	for k,v in ipairs(GooglePrice) do
		if v.googleId == GooglePlayId then
			return v.price
		end
	end
	return false
end

-- 通过商品goodId获取商品货币显示字符
function GetMoneyStrByGoodsID(goodId)
	local moneyStr = ""
	local oldMoneyStr = ""
	local rechargeCfg = game_scheme:Recharge_0(goodId)
	if rechargeCfg then
		if game_config.Q1SDK_DOMESTIC then
			moneyStr =  "￥"..rechargeCfg.iPrice/100
			oldMoneyStr = "￥"..rechargeCfg.iOldPrice/100
		else
			local GoogleShopData = GetGoogleShopDataById(goodId)
			if GoogleShopData then
				moneyStr= GoogleShopData.price
			else
				local recharge_data = require "recharge_data"
				if money_type_mgr.GetCurrencyTypeBySdk() then
					moneyStr = recharge_data.GetCurrentCurrencyStrBySdk(rechargeCfg.iPrice)
					oldMoneyStr = recharge_data.GetCurrentCurrencyStrBySdk(rechargeCfg.iOldPrice)
				else
					moneyStr = "$"..string.format("%.2f", recharge_data.GetExchangeCfgYuanByCurrencyType(rechargeCfg.iPrice))
					oldMoneyStr = "$" .. string.format("%.2f", recharge_data.GetExchangeCfgYuanByCurrencyType(rechargeCfg.iOldPrice))
				end
			end
		end
		--测试

		local CURRENCY_TYPE = files_version_mgr.GetCurrencyType()
		if CURRENCY_TYPE ~= "" then
			local GoogleShopData = GetGoogleShopDataById(goodId)
			if GoogleShopData then
				moneyStr= GoogleShopData.price
			else
				local currencyType = CURRENCY_TYPE
				local recharge_data = require "recharge_data"
				local currencyString = nil
				if money_type_mgr.GetCurrentSymbol() then
					--print("zzd____通过sdk获取货币符号 ",money_type_mgr.GetCurrentSymbol())
					currencyString = money_type_mgr.GetCurrentSymbol()
				else
					local currencyCfg = game_scheme:MoneyType_1(currencyType)
					if not currencyCfg then
						log.Error("MoneyType无%s的对应配置", currencyType)
						return
					end
					currencyString = currencyCfg.strCurrencyCharacter
				end

				-- print("rmb",rechargeCfg.iPrice,currencyType)
				print("当前货币类型",currencyType,"当前货币符号",currencyString,"当前货币数值",recharge_data.GetExchangeCfgYuanByCurrencyType(rechargeCfg.iPrice,currencyType)," rechargeCfg.iOldPrice:",rechargeCfg.iOldPrice)
				if money_type_mgr.GetCurrencyTypeBySdk() then
					moneyStr = recharge_data.GetCurrentCurrencyStrBySdk(rechargeCfg.iPrice)
					oldMoneyStr = recharge_data.GetCurrentCurrencyStrBySdk(rechargeCfg.iOldPrice)
					if oldMoneyStr == "" then
						local cfg = recharge_data.GetExchangeCfg(rechargeCfg.iOldPrice)
						if cfg[currencyType] then
							local value = string.format("%.2f", recharge_data.GetExchangeCfgYuanByCurrencyType(rechargeCfg.iOldPrice,currencyType))
							oldMoneyStr = string.format("%s%s", currencyString, SpecialCurrency(value))
						else
							oldMoneyStr = string.format("%s%.2f", "$", recharge_data.GetExchangeCfgYuanByCurrencyType(rechargeCfg.iOldPrice,"USD"))
							local properties =
							{
								moneyStr = moneyStr,
								oldMoneyStr = oldMoneyStr,
								iOldPrice = rechargeCfg.iOldPrice,
								currencyType = currencyType,
							}
							event.Trigger(event.GAME_EVENT_REPORT, "GetCurrencyBySdk_nodata",properties )
						end
					end
				else
					moneyStr = string.format("%s%.2f", currencyString,recharge_data.GetExchangeCfgYuanByCurrencyType(rechargeCfg.iPrice,currencyType))
					oldMoneyStr = string.format("%s%.2f", currencyString, recharge_data.GetExchangeCfgYuanByCurrencyType(rechargeCfg.iOldPrice,currencyType))
					if const.IsVietnamChannel() or const.IsVietnamIosChannel() then
						--越南包
						moneyStr = string.format("%.0f%s", recharge_data.GetExchangeCfgYuanByCurrencyType(rechargeCfg.iPrice,currencyType),currencyString)
						oldMoneyStr = string.format("%.0f%s", recharge_data.GetExchangeCfgYuanByCurrencyType(rechargeCfg.iOldPrice,currencyType),currencyString)
					end
				end
			end

		end
	end
	return moneyStr, oldMoneyStr, rechargeCfg and rechargeCfg.iPrice, rechargeCfg and rechargeCfg.iOldPrice
end

--特殊货币处理
--currencyStr:不带货币符号的两位小数
function SpecialCurrency(currencyStr)
	--土耳其货币特殊处理 ₺3.449,99
	if money_type_mgr.GetCurrencyTypeBySdk() == "TRY" then
		print("zzd____TRY  特殊处理",currencyStr)
		local valueStr = string.gsub(currencyStr,"%.",",")
		local length = string.len(valueStr)
		if length > 6 then --因为加了符号，需要大于7位才有千分位
			local index = length - 6
			local newValue = valueStr:sub(1,index).."."..valueStr:sub(index + 1)
			return newValue
		else
			return valueStr
		end
	else
		return currencyStr
	end
end

--Type 60:美金 149:(印)卢比 167:韩币
function GetVIPIntegralByExchangeRate(Type)
	if Type == -1 then
		local currencyType = files_version_mgr.GetCurrencyType()
		local indexIgnor = nil
		if currencyType == "INR" then
			Type = 149
		elseif currencyType == "KNW" then
			Type = 167
		elseif currencyType == "en" then
			Type = 60
		else
			Type = 59
		end
	end

	local vipRate
	if Type == 59 then
		vipRate = game_scheme:InitBattleProp_0(638).szParam.data[0]
	elseif Type == 60 then
		vipRate = game_scheme:InitBattleProp_0(638).szParam.data[1]
	elseif  Type == 149 then
		vipRate = game_scheme:InitBattleProp_0(638).szParam.data[2]
	elseif Type == 167 then
		vipRate = game_scheme:InitBattleProp_0(638).szParam.data[3]
		log.Error("Type == 167 vipRate = ",vipRate)
	else
		log.Error("InitBattleProp没有表数据--638")
	end
	return vipRate /100
end

--新加传入价格获取新的带有货币单位
function GetMoneyStrByNewPrice(price)
	local curStr = ""
	local CURRENCY_TYPE = files_version_mgr.GetCurrencyType() 	--获取当前所属货币类型
	local currencyString = ""
	if CURRENCY_TYPE ~= "" then
		if money_type_mgr.GetCurrentSymbol() then
			--print("zzd____通过sdk获取货币符号 ",money_type_mgr.GetCurrentSymbol())
			currencyString = money_type_mgr.GetCurrentSymbol()
		else
			local currencyCfg = game_scheme:MoneyType_1(CURRENCY_TYPE)	--根据货币符号表
			if not currencyCfg then
				log.Error("MoneyType无%s的对应配置", CURRENCY_TYPE)
				return
			end
			currencyString = currencyCfg.strCurrencyCharacter	--根据货币表获取符号
		end
	else
		if game_config.Q1SDK_DOMESTIC then	--如果没有货币类型 随便给个人民币或者美刀符号
			currencyString = "￥"
		else
			currencyString = "$"
		end
	end
	if price == nil then
		log.Error("当前传入price的值为空")
		price = 0
	end
	if type(price) == 'string'  then
		log.Error("当前传入price的值为字符串:",price)
		price = tonumber(string) or 0
	end
	curStr = string.format("%s%.2f", currencyString, tonumber(price))
	return curStr,currencyString, tonumber(price)
end

function GetMoneyType()
	local moneyTypeStr = ""
	if googleShopData and util.TableCount(googleShopData) > 0 then
		for k,v in pairs(googleShopData) do
			moneyTypeStr = getMoneyTypeByGoogleData(v)
			return v.price_currency_code,moneyTypeStr
		end
	end
	return "",moneyTypeStr
end

function getMoneyTypeByGoogleData( googleData )
	local moneyType = googleData.price_currency_code
	if moneyType == "GBP" then -- 英镑
		moneyStr = "£"
	elseif moneyType == "USD" then
		moneyStr = "US$"
	elseif moneyType == "MYR" then
		moneyStr = "MYR"
	elseif moneyType == "CNY" then
		moneyStr = "￥"
	elseif moneyType == "EUR" then
		moneyStr = "€"
	elseif moneyType == "JPY" then
		moneyStr = "￥"
	elseif moneyType == "CAD" then
		moneyStr = "CA$"
	elseif moneyType == "HKD" then
		moneyStr = "HK$"
	elseif moneyType == "CHF" then
		moneyStr = "CHF"
	elseif moneyType == "THB" then
		moneyStr = "฿"
	elseif moneyType == "SEK" then
		moneyStr = "SEK"
	elseif moneyType == "KRW" then
		moneyStr = "KRW"
	elseif moneyType == "SGD" then
		moneyStr = "SGD"
	elseif moneyType == "AUD" then
		moneyStr = "AU$"
	elseif moneyType == "INR" then
		moneyStr = "₹"
	elseif moneyType == "KRW" then
		moneyStr = "￦"
	elseif game_config.Q1SDK_DOMESTIC then
		moneyStr = "￥"
	else
		moneyStr = "$"
	end
	return moneyStr
end

function getParitiesPrice( price )
	local num1 = tonumber(price) / parities
	local num2 = string.format("%.2f", num1)
	return tonumber(num2)
end


function GetMoneyStrByPrice(price,isMoneyType)
	-- print("price:",price)
	local moneyStr = price
	if not price or price <= 0 then return moneyStr end

	if game_config.Q1SDK_DOMESTIC then
		moneyTypeStr = isMoneyType and "￥" or ""
		moneyStr = moneyTypeStr..price/100
	else
		local setparitiesPrice = function ( _price,_isMoneyType )
			local moneyType,moneyTypeStr = GetMoneyType(_price)
			if not _isMoneyType then
				moneyTypeStr = ""
			elseif moneyTypeStr == "" and _isMoneyType then
				moneyTypeStr = "$"
			end
			return moneyTypeStr..getParitiesPrice(_price)
		end
		if isGooglePlay() then
			local gpId = getGooglePlayIdByPrice(price)
			if gpId then
				local googleData = googleShopData[gpId]
				if googleData and googleData.price then
					if isMoneyType then
						moneyStr = googleData.price
					else
						moneyStr = googleData.price_amount_micros/1000000
					end
				else
					moneyStr =setparitiesPrice(price/100,isMoneyType)
				end
			else
				moneyStr = setparitiesPrice(price/100,isMoneyType)
			end
		else
			moneyStr = setparitiesPrice(price/100,isMoneyType)
		end
	end
	return moneyStr
end

-- 通过商品goodId获取商品货币类型显示字符(货币符号)
function GetMoneyTypeByGoodId(goodId)
	local moneyStr = ""
	if game_config.Q1SDK_DOMESTIC then
		moneyStr =  "￥"
	else
		local GoogleShopData = GetGoogleShopDataById(goodId)
		if GoogleShopData then
			moneyStr = getMoneyTypeByGoogleData(GoogleShopData)
		else
			moneyStr = "$"
		end
	end

	local currencyType = files_version_mgr.GetCurrencyType()
	if money_type_mgr.GetCurrentSymbol() then
		--print("zzd____通过sdk获取货币符号 ",money_type_mgr.GetCurrentSymbol())
		moneyStr = money_type_mgr.GetCurrentSymbol()
	else
		local currencyCfg = game_scheme:MoneyType_1(currencyType)
		if not currencyCfg then
			log.Error("MoneyType表中无%s的对应配置", currencyType)
			return
		end
		moneyStr = currencyCfg.strCurrencyCharacter
	end

	return moneyStr
end

-- 通过GooglePlayId获取商品googleShopData
function GetGoogleShopDataByGooglePlayId( gpId )
	return googleShopData[gpId] or nil
end

-- 通过商品（Recharge）StrName获取商品googleShopData
function GetGoogleShopDataByStrName(str)
	if not str or str == "" then return nil end
	print("GetGoogleShopDataByStrName:",str)
	local params = string.split(str, "*")
	local gsData = nil
	-- dump(params)
	if params and params[2] then
		gsData = googleShopData[params[2]] or nil
		if not gsData then return gsData end
		-- print(gsData)
		-- gsData = json.decode(gsData)
		-- dump(gsData)
		if gsData then
			print("price_currency_code:",gsData.price_currency_code)
		end
	else
		return gsData
	end
	return gsData
end

function GetGoogleShopDataById(Id)
	local goodId = GetRechargeGoodsID(Id)
	local goodcfg = game_scheme:Recharge_0(goodId)
	if not goodcfg then return nil end
	print("GetGoogleShopDataById:",goodcfg.strName)
	return GetGoogleShopDataByStrName(goodcfg.strName)
end
--@region 

--[[
message TMSG_RECHARGE_TOKENBUY_REQ
{
	required int32 goodsID = 1;                     // 代币购买goodsid
	required int32 rechargeNum = 2;                 // 代币购买数量
	required int32 payNum = 3;                      // 代币购买金额
}
--]]
--@endregion
function Send_RECHARGE_TOKENBUY_REQ(goodsID, rechargeNum, payNum)
	local lingshi_data = require "lingshi_data"
	if lingshi_data.GetLingshiRechargeable() then
		--林思恒修改，灵石购买统一取消海内外差别,问过龙国庆
		-- if not game_config.Q1SDK_DOMESTIC then
		-- 	goodsID = goodsID + 10000
		-- end
		lingshi_data.SendCommandLine(string.format("simulaterecharge %d %d", goodsID, rechargeNum))
		print(396, "<color=#FFFF00>正在使用Unity，通过灵石购买 >>>>></color> rechargeID = ", goodsID, rechargeNum)
		return
	end
	if os.time() - lastBuyTime < 4 then
		--event.Trigger(event.SHOW_FLOW_MESSAGE, 614)
		return
	end
	lastBuyTime = os.time()

	local msg = recharge_pb.TMSG_RECHARGE_TOKENBUY_REQ()

	local curGoodsID = GetRechargeGoodsID(goodsID)
	local cfg = game_scheme:Recharge_0(curGoodsID)
	msg.goodsID = curGoodsID --代币购买goodsid
	msg.rechargeNum = rechargeNum --代币购买数量
	local recharge_data = require "recharge_data"
	msg.payNum = recharge_data.GetCurrencyPrice_RechargeExchange(cfg.iPrice)

	if IsServerUseRechargeChannel() then
		print("支付服务器使用新方式")
		print("channelTag",util.GetChannelTag())
		msg.useDemergeTab = recharge_data.IsUseRechargeChannel()
		msg.packageTag = util.GetChannelTag()

	else
		print("支付服务器使用旧方式")
	end

	net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_RECHARGE_TOKENBUY_REQ, msg)
	print(" TMSG_RECHARGE_TOKENBUY_REQ ", curGoodsID,"payNum",msg.payNum)
end

--[[
message TMSG_RECHARGE_TOKENBUY_RSP
{
	required EnErrorCode errorCode = 1;             // 错误码
	required int32 goodsID = 2;                     // 代币购买goodsid
	required int32 rechargeNum = 3;                 // 代币购买数量
	required int32 payNum = 4;                      // 代币购买金额
	required int32 iTokenPrice = 5;                 // 消耗代币数量
}
]]--
function Recv_RECHARGE_TOKENBUY_RSP(msg)
	if PrintEnable then
		log.Error("=====",msg.errorCode,(msg.goodsID),(msg.rechargeNum),(msg.iTokenPrice))
	end
	if msg.errorCode ~= error_code_pb.enErr_NoError then
		local langCode = msg.errorCode + lang.KEY_ERROR_CODE_SERVER_BASE
		local langDes = lang.Get(langCode)
		event.Trigger(event.SHOW_FLOW_MESSAGE, langDes)
	else
		--TODO
		--增加代币支付上报
		local json_str = json.encode({
			pay_rechargeID=msg.goodsID,--充值ID
			pay_num=msg.rechargeNum,--支付数量
			pay_price=msg.iTokenPrice,--消耗代币数量
		})
		if PrintEnable then
			log.Error("json_str",json_str)
		end
		event.Trigger(event.GAME_EVENT_REPORT, "RechargeCoin", json_str)

		--红钻消耗时间
		local time = DateTime.Now:ToString("yyyy-MM-dd HH:mm:ss", DateTimeFormatInfo.InvariantInfo)
		--剩余红钻数量
		local tokennum = GetTokenValue()
		ReportRechargeAction("Redpay", "pay_num="..tostring(msg.payNum)..";consume_time="..time..";pay_goodsId="..tostring(msg.goodsID)..";token_num="..tostring(tokennum))
		-- local ui_grade_tip = require "ui_grade_tip"
		-- local isShow = ui_grade_tip.ShowGradeTip()
		-- if isShow then
		-- 	event.Trigger(event.GAME_EVENT_REPORT, "rating_reason", "{\"rating_reason\":2}")
		-- end
	end
end

function GetTokenValue()
	local value = false
	local goodNum = 0
	for i=0,game_scheme:InitBattleProp_0(907).szParam.count-1 do
		local goodID = game_scheme:InitBattleProp_0(907).szParam.data[i]
		local goodEntity = skep_mgr.GetGoodsEntity(goodID)
		goodNum = goodEntity and goodEntity:GetGoodsNum() or 0
		value = goodNum > 0
		if value == true then
			break
		end
	end
	return goodNum
end

--新增代币购买渠道
function Send_New_Recharge_REQ(goodsID, rechargeNum, payNum,callBack)
	local tokenNum = GetTokenValue()
	local isCanTokenBuy = tokenNum>0
	if PrintEnable then
		log.Error("===== goodsID",goodsID,"rechargeNum",rechargeNum,"tokenNum",tostring(tokenNum))
	end
	if isCanTokenBuy == true then
		--代币数量足够时弹出代币提示面板
		local win = ui_window_mgr:ShowModule("ui_token_tip")
		local isPackOfficial=const.IsOfficialChannel()
		if win then
			local curGoodsID = GetRechargeGoodsID(goodsID)
			local cfg = game_scheme:Recharge_0(curGoodsID)
			win:SetInputParam(function(isToken)
				if isToken then
					if tokenNum<cfg.iTokenPrice and Application.platform == RuntimePlatform.Android and not const.IsVietnamChannel() then
						GoTOWeb()
					else
						Send_RECHARGE_TOKENBUY_REQ(goodsID, rechargeNum, payNum)
					end
				else
					-- --TODO：欧美包和东亚包红钻：点击红钻线上充值按钮跳转到三方支付页面：https://xgame-ea.q1.com/pay/
					if isPackOfficial then
						GoTOWeb()
					else
						Send_RECHARGE_REQ(goodsID, rechargeNum, payNum)
					end

				end
				if callBack then
					callBack()
					callBack = nil
				end
			end, cfg.iTokenPrice,isPackOfficial and lang.Get(9271) or GetMoneyStrByGoodsID(goodsID),isPackOfficial and tokenNum<cfg.iTokenPrice)
		end
	else
		--TODO：欧美包和东亚包红钻不足时，点击弹窗提示红钻不足，弹窗上方显示文本红钻余额不足，请点击下方蓝色按钮前往购买，点击红钻线上充值按钮跳转到三方支付页面：https://xgame-ea.q1.com/pay/
		local isPackOfficial=const.IsOfficialChannel()
		if isPackOfficial then
			OpenUITokenTip()
		else
			Send_RECHARGE_REQ(goodsID, rechargeNum, payNum)
		end
		if callBack then
			callBack()
			callBack = nil
		end
	end
end

function OpenUITokenTip()
	local win = ui_window_mgr:ShowModule("ui_token_tip")
	if win then
		win:SetInputParam(function(isToken)
			--TODO：欧美包和东亚包红钻：点击红钻线上充值按钮跳转到三方支付页面：https://xgame-ea.q1.com/pay/
			GoTOWeb()
		end, "0",lang.Get(9271),true)
	end
end

--欧美官网包和亚太官网包走三方支付链接
function GoTOWeb()
	local player_mgr = require "player_mgr"
	local net_login_module = require "net_login_module"
	local areaString = ""
	local areaID = net_login_module.GetLoginAreaID()
	if areaID == 4 then
		areaString = "sa"--亚太
	elseif areaID == 5 then
		areaString = "ea"--欧美
	end
	local url_mgr = require "url_mgr"
	local url = url_mgr.SelectUrlByMode(url_mgr.OFFICIALPAY_URL)
	local coda_url = string.format(url,tostring(areaString),tostring(player_mgr.GetPlayerRoleID()))
	url_mgr.SelectUrlByMode(url_mgr.OFFICIALPAY_URL)
	log.Warning("三方支付链接 ",coda_url,"areaID:",areaID,"PlayerRoleID:",tostring(player_mgr.GetPlayerRoleID()))
	q1sdk.OpenUrl(coda_url)
end


--[[
message TMSG_RECHARGE_REQ// 充值请求, C->S
{
	required int32 goodsID = 1;                     // 充值类型
	required int32 rechargeNum = 2;                 // 充值数量
	required int32 payNum = 3;                      // 充值金额
}
--]]
--@endregion
function Send_RECHARGE_REQ(goodsID, rechargeNum, payNum)
	local lingshi_data = require "lingshi_data"
	local isAutoPop = popups_cache.HavePopWin() --是否自动弹窗的购买
	if lingshi_data.GetLingshiRechargeable() then
		--林思恒修改，灵石购买统一取消海内外差别
		-- if not game_config.Q1SDK_DOMESTIC then
		-- 	goodsID = goodsID + 10000
		-- end
		--本地unity测试港澳台货币灵石充值
		goodsID = GetRechargeGoodsID(goodsID)
		lingshi_data.SendCommandLine(string.format("simulaterecharge %d %d", goodsID, rechargeNum))
		print(396, "<color=#FFFF00>正在使用Unity，通过灵石购买 >>>>></color> rechargeID = ", goodsID, rechargeNum)
		print("是否弹窗购买" , isAutoPop)
		return
	end
	if os.time() - lastBuyTime < 4 then
		--event.Trigger(event.SHOW_FLOW_MESSAGE, 614)
		return
	end
	lastBuyTime = os.time()

	local curGoodsID = GetRechargeGoodsID(goodsID)
	local cfg = game_scheme:Recharge_0(curGoodsID)
	if cfg == nil then
		log.Error("goodsID:"..goodsID .. ", not find rechargeID:"..curGoodsID)
		return
	end
	local msg = recharge_pb.TMSG_RECHARGE_REQ()
	-- msg.payNum = payNum
	msg.rechargeNum = rechargeNum
	msg.goodsID = curGoodsID

	local recharge_data = require "recharge_data"

	msg.payNum = recharge_data.GetCurrencyPrice_RechargeExchange(cfg.iPrice)
	print("cfg.iPrice",cfg.iPrice,"msg.payNum:",msg.payNum)
	if IsServerUseRechargeChannel() then
		print("支付服务器使用新方式")
		print("channelTag",util.GetChannelTag())
		msg.useDemergeTab = recharge_data.IsUseRechargeChannel()
		msg.packageTag = util.GetChannelTag()
	else
		print("支付服务器使用旧方式")
	end
	local orderName = cfg.strName -- 在后面拼接东西
	PopDataFirstStage(isAutoPop)
	table.insert(queue, 1, { name = orderName, pay = msg.payNum, goodsID = msg.goodsID, diamond = cfg.iDiamondCount, price = cfg.iPrice })--新支付方式cfg.iPrice是人民币，旧支付方式cfg.iPrice是对应货币类型的金额
	--dump(queue)
	net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_RECHARGE_REQ, msg)
	log.Warning(" MSG_RECHARGE_REQ ", curGoodsID, " orderName ", orderName,"payNum",msg.payNum)
end

--服务器是否使用新支付配置,如果客户端使用新支付配置且域名开关开了，则使用新的，否则旧的
function IsServerUseRechargeChannel()
	local url_operation_mgr = require "url_operation_mgr"
	local recharge_data = require "recharge_data"

	return recharge_data.IsUseRechargeChannel()
	-- if recharge_data.IsUseRechargeChannel() and url_operation_mgr.GetUseRechargeChannel() then
	-- 	return true
	-- end
	-- return false
end

--@region 
--[[
message TMSG_RECHARGE_RSP// 充值回复, S->C
{
	required EnErrorCode errorCode = 1;             // 错误码
	required string gameOrderID = 2;                // 游戏订单号
	required string signature = 3;                  // 签名
	optional int32 bcUserID = 4;                    // 冰川用户ID
	optional int32 worldID = 5;                     // 游戏世界ID
}
--]]
--@endregion
function Recv_RECHARGE_RSP(msg)
	if msg.errorCode == error_code_pb.enErr_NoError and #queue > 0 then
		local order = queue[1]
		order.gameOrderID = msg.gameOrderID
		table.remove(queue, 1)
		table.insert(pendingQueue, order)
		PopDataSecStage(msg.gameOrderID)
		local net_login_module = require "net_login_module"
		local noLoginPay = login_pb.enLoginPartnerID_BingChuan ~= net_login_module.GetPartnerID()
		local orderItem = string.format('%d*%d*%d*%s', order.goodsID, order.pay, 1, order.name)

		local isShowWin = ui_window_mgr:IsModuleShown("ui_lineup_gift") --推荐礼包
		local _isRecommend = 0
		if isShowWin then
			_isRecommend = 1
		end
		local payNum = order.price
		if not game_config.Q1SDK_DOMESTIC then
			--新支付方式货币类型是人民币
			--新支付方式price是人民币，旧支付方式price是对应货币类型的金额，比如印度包就是印度币，港台包就是港台币（Recharge表和jenking工程的current_type配置是一样的）
			local fromMoneyType= IsServerUseRechargeChannel() and "zh" or nil
			payNum = money_type_mgr.GetMoneyExchangeRate("en",payNum,fromMoneyType,order.goodsID)
		end
		local json_str = json.encode({
			game_orderid=msg.gameOrderID,
			pay_type=order.goodsID,		-- num
			pay_amount=payNum/100,
			uuid=q1sdk.GetUUID(),
			imei_idfa=q1sdk.GetImeiMD5(),
			radid=q1sdk.GetRadid(),
			rsid=q1sdk.GetRsid(),
			
			isRecommend = _isRecommend,
			originalAmount = order.pay / 100,
			isNewRecharge = IsServerUseRechargeChannel()
		})
		event.Trigger(event.GAME_EVENT_REPORT, "recharge_init", json_str)

		local url_operation_mgr = require "url_operation_mgr"
		local disableAdjustOtherRecharge = url_operation_mgr.GetConfig("disableAdjustOtherRecharge")
		if not disableAdjustOtherRecharge then
			adjust.TrackEventRechargeInit(adjust.Event_RechargeInit)
		end
		firebase.TrackEvent(firebase.Event_RechargeInit)
		facebook.TrackUnlockAchievement("recharge_init")

		local player_mgr = require "player_mgr"
		local playerProp = player_mgr.GetPlayerProp() or {lv=1}

		local rechargeInfo = game_scheme:Recharge_0(order.goodsID)
		local currencyType = ""
		if rechargeInfo then
			currencyType = rechargeInfo.strCurrencyType
		end

		local goodsID = order.goodsID
		local gameOrderID = msg.gameOrderID
		local signature = msg.signature
		--Debug.LogError("delete code")
		log.Warning("orderItem:"..WWW.EscapeURL(orderItem)..",orderId:"..tostring(msg.gameOrderID)..",signature:"..tostring(msg.signature))
		log.Warning("serverID:",msg.worldID,"userId:",tostring(msg.bcUserID),"payNum:", order.pay/100,"orderItem:",WWW.EscapeURL(orderItem), "orderNO:", msg.gameOrderID,"currencyType:",currencyType,"signature:",tostring(msg.signature),"noLoginPay:", noLoginPay,"level:", playerProp.lv)

		-- 冰川渠道包需要自己上报购买下单事件上报
		q1sdk.TrackPayBegin(tonumber(msg.worldID), tostring(msg.bcUserID), tostring(order.pay/100), tostring(player_mgr.GetPlayerRoleID()), player_mgr.GetRoleName(), player_mgr.GetPlayerLV(), msg.gameOrderID, orderItem, msg.signature, currencyType)

		q1sdk.Pay(msg.worldID, tostring(msg.bcUserID), order.pay/100, WWW.EscapeURL(orderItem), msg.gameOrderID, currencyType, msg.signature, noLoginPay, playerProp.lv, function(isSuccess, intVar, strVar)
			local sucCode = Q1SDK.Instance.Q1_PAY_SUCCESS--game_config.Q1SDK_DOMESTIC and 3 or 1001
			--[[ 
				com.q1.sdk.callback.Q1CallBackCode中定义
				国内版3成功，4取消
				国外版,Android:
				    public static final int Q1_PAY_SUCCESS = 1001;
					public static final int Q1_PAY_ERROR = 1002;
					public static final int Q1_PAY_CANCEL = 1003;
			]]
			--nil	1002	Null data in IAB result (response: -1002:Bad response received)
			--nil	1002	User canceled. (response: -1005:User cancelled)
			log.Warning(isSuccess, intVar, strVar)
			local const = require "const"

			-- 这个是因为那个权限问题
			if not isSuccess then
				-- Error Code 6: 支付提示 CODE 6 “Google结算内部错误”,跳转到 google play service 设置界面去
				local shouldJumpAppSetting = false
				if Application.platform == RuntimePlatform.Android and intVar == 6 then
					shouldJumpAppSetting = true
				end

				if string.match(strVar, 'response: %-(%d+)') == '1002' or shouldJumpAppSetting then
					local message_box = require "message_box"
					local Lang = require "lang_res_key"
					function okCall(callbackData, nRet)
						if message_box.RESULT_YES == nRet then
							event.EventReport("recharge_permission",
									{
										error_code = tonumber(intVar),
										error_msg = tostring(strVar),
										goods_id = tostring(goodsID),
										gameOrderID = tostring(gameOrderID),
										signature = tostring(signature),
										orderItem = orderItem,
										
										isRecommend = _isRecommend,
									})

							q1sdk.GotoAppSetting()
						end
					end
					--9193
					--权限限制导致充值失败！
					--可以如下设置：
					--1、进入“权限管理”项
					--2、允许“后台弹出界面”
					--9192: 充值失败
					message_box.Open(lang.Get(9193), message_box.STYLE_YES, okCall, 0, Lang.KEY_OK, Lang.KEY_CANCEL, lang.Get(9192), message_box.en_msgbox_net_type)
				else
					log.Error("recharge failed:",strVar)
					local Q1_PAY_CANCEL = 1003 -- Q1SDK.Instance.Q1_PAY_CANCEL

					-- h5 支付时，纯客户端不能知道支付结果，只能等待服务器下载购买物品。 1004 ： 支付操作完成，但不确定是否成功
					local isPayFinishedUnconfirmed = (intVar == 1004)

					local channel_tag = util.GetChannelTag()
					local validStr = (strVar ~= nil and #strVar > 0)
					--为了兼容老包，保留const.SDK_EventType.SDK_PayCancel，(慎用慎用！！！)
					if Application.platform == RuntimePlatform.IPhonePlayer and validStr and (intVar ~= const.SDK_EventType.SDK_PayCancel or intVar ~= const.GLAEventType.GLAEventTypeofPayCancel) then
						--充值失败提示信息，当eventType == SDK_PayFail时，回调的字典，其中的message可以直接弹窗显示
						-- 国内版 只显示strVar里面的message
						if game_config.Q1SDK_DOMESTIC then
							local dkjson = require "dkjson"
							local jsonData = dkjson.decode(strVar)
							flow_text.Add(jsonData.message)
						else
							flow_text.Add(strVar)
						end
					elseif Application.platform == RuntimePlatform.Android and channel_tag == const.IsHuaWeiChannel() then
						if intVar == 60050 then
							-- 未绑定华为账号，请先绑定华为账号
							flow_text.Add(lang.Get(4078))
						end
					elseif Application.platform == RuntimePlatform.Android  and validStr and intVar ~= Q1_PAY_CANCEL
							and channel_tag ~= const.package_name_set.com_q1_hero then
						if (not game_config.Q1SDK_DOMESTIC) or (not isPayFinishedUnconfirmed) then
							--充值失败提示信息
							flow_text.Add(strVar)
						end
					end
					if (not game_config.Q1SDK_DOMESTIC) or (not isPayFinishedUnconfirmed) then
						local win = ui_window_mgr:ShowModule("ui_pay_error")
						if win then
							win:SetInputParam(tostring(msg.gameOrderID), goodsID)
						end
					else
						-- 不知道支付结果，成功还是失败
						-- 1682: 查看充值是否到账，如有问题，请联系客服
						flow_text.Add(lang.Get(1682))
					end
				end
				local rechargeTag = "recharge_failed"
				--为了兼容老包，保留const.SDK_EventType.SDK_PayCancel(慎用慎用！！！)
				if Application.platform == RuntimePlatform.IPhonePlayer and (intVar == const.SDK_EventType.SDK_PayCancel or intVar == const.GLAEventType.GLAEventTypeofPayCancel) then
					rechargeTag = "recharge_cancel"
				end

				event.EventReport(rechargeTag,
						{
							error_code = tonumber(intVar),
							error_msg = tostring(strVar),
							goods_id = tostring(goodsID),
							gameOrderID = tostring(gameOrderID),
							signature = tostring(signature),
							orderItem = orderItem,
							
							isRecommend = _isRecommend,
							isNewRecharge = IsServerUseRechargeChannel()
						})
			end

			if isSuccess then
				event.EventReport("recharge_success",
						{
							goods_id = tostring(goodsID),
							gameOrderID = tostring(gameOrderID),
							signature = tostring(signature),
							orderItem = orderItem,
							isNewRecharge = IsServerUseRechargeChannel()
						})

				-- 可能断线了。3s上报一次
				local reportFunc = function()
					Send_RECHARGE_PAYMENT_RESULT_NTF(intVar == sucCode and 0 or 1, msg.gameOrderID, order.goodsID, 1)
				end
				table.insert(reportQueue, {paymentResult = (intVar == sucCode and 0 or 1), gameOrderID = msg.gameOrderID, goodsID = order.goodsID, num = 1, ticker = util.IntervalCall(3, reportFunc, 0)})
			end

		end, goodsID)
	else
		local langCode = msg.errorCode + lang.KEY_ERROR_CODE_SERVER_BASE
		local langDes = lang.Get(langCode)
		local flow_text = require "flow_text"
		flow_text.Add(langDes)

		ReportRechargeAction("payError", "error="..tostring(msg.errorCode))
		log.Error("payError,errorCode=====:"..tostring(msg.errorCode))

		-- 从服务器请求订单失败
		event.EventReport("recharge_order_error",
				{
					order_error_code = tostring(msg.errorCode),
					order_lang_code = tostring(langCode),
					order_error_des = langDes
				})
	end
end

--@region 
--[[
message TMSG_FIRSTRECHARGEREWARD_REQ// 领取首充奖励请求, C->S
{
	required bool bReqRewardState = 1;             // 是否是请求是否领取过首充奖励状态
}
--]]
--@endregion
function Send_FIRSTRECHARGEREWARD_REQ(bReqRewardState)
	local msg = recharge_pb.TMSG_FIRSTRECHARGEREWARD_REQ()
	msg.bReqRewardState = bReqRewardState
	net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_FIRSTRECHARGEREWARD_REQ, msg)
end

--@region 
--[[
message TMSG_FIRSTRECHARGEREWARD_RSP// 领取首充奖励回复, S->C
{
	required bool bReqRewardState = 1;             // 是否是请求是否领取过首充奖励状态
	required int32 result = 2;             // 错误码 0正常；-4没充值；-3未领取；-2已领取；-1异常错误；1不能加英雄卡；2不能加钻石；3不能加金币
}
--]]
--@endregion
function Recv_FIRSTRECHARGEREWARD_RSP(msg)
end

--@region 
--[[
message TMSG_RECHARGE_RESULT_NTF// 充值结果通知, S->C
{
	required string gameOrderID = 1;                // 游戏订单号
	required RechargeResult result = 2;             // 充值结果
	optional uint32 goodsID = 3;                    // 购买的物品
	optional uint32 num = 4;                        // 购买物品数量
}
enum RechargeResult// 充值结果
{
	enRechargeResult_Success = 0;               // 成功
	enRechargeResult_Failure= 1;                // 失败
}
--]]
--@endregion
function Recv_RECHARGE_RESULT_NTF(msg)
	local key = msg.result == recharge_pb.enRechargeResult_Success and 9104 or 9105

	--是否是测试订单
	local isTest = false
	-- 是否红钻
	local _isCoin = 0
	if msg:HasField("bankCode") then
		local bankCode = string.lower(msg.bankCode)
		if string.find( bankCode,"_test",1) ~=nil then
			isTest = true
		end
		if msg.bankCode == "" then
			_isCoin = 1
		end
	else
		_isCoin = 1
	end
	log.Warning("bankCode:", msg.bankCode, ",isTest:", isTest, ",goodsID:", msg.goodsID)

	if msg.result ~= recharge_pb.enRechargeResult_Success then
		ReportRechargeAction("payError", "")
		--新支付方式货币类型是人民币
		local fromMoneyType= IsServerUseRechargeChannel() and "zh" or nil
		for i, v in ipairs(pendingQueue) do
			if v.gameOrderID == msg.gameOrderID then
				--fb测试订单不上报
				if not isTest then
					-- not Application.platform ~= RuntimePlatform.IPhonePlayer逻辑判断是否有问题?
					if not game_config.Q1SDK_DOMESTIC then
						local usdNum = money_type_mgr.GetMoneyExchangeRate("en",v.price,fromMoneyType,msg.goodsID)
						usdNum = usdNum/100
						if Application.platform == RuntimePlatform.Android then
							--2022.8.8 韩国包不上报FB
							-- if not const.IsTanwanKoreaChannel() then
							print("Q1SDK.Instance:logAddPaymentInfoEvent___usdNum_type:",type(usdNum))
							Q1SDK.Instance:logAddPaymentInfoEvent(false, usdNum)
							-- end
						elseif Application.platform == RuntimePlatform.IPhonePlayer then
							-- if not const.IsTanwanKoreaChannel() then
							facebook.TrackAddPaymentInfoEventWithValue(false, usdNum)
							-- end
						end
					end
				end

				-- local properties = string.format("{\"error_code\":%d,\"error_msg\":\"%s\"}", 0,"取消")
				local properties =
				{
					error_code = 0,
					error_msg = "取消",
					recharge_test = isTest,
				}
				event.Trigger(event.GAME_EVENT_REPORT, "recharge_no_success",properties )
			end
		end
		return
	end

	--位面2新增充值物品发货通知
	event.Trigger(event.GW_GET_RECHARGE_GOODS)

	-- 充值成功
	if msg:HasField("rechargeTimes") then
		if adjust.RechargeTimesCfg[msg.rechargeTimes] then
			adjust.TrackEvent(adjust.RechargeTimesCfg[msg.rechargeTimes])
		end

		if firebase.RechargeTimesCfg[msg.rechargeTimes] then
			firebase.TrackEvent(firebase.RechargeTimesCfg[msg.rechargeTimes])
		end
	end

	--adjust,firebase充值次数上报



	-- 充值成功上报
	local rechargeCfg = game_scheme:Recharge_0(msg.goodsID)
	local pay = 0
	if rechargeCfg and rechargeCfg.iPrice then
		pay = rechargeCfg.iPrice
	end

	--上报后台单位统一转换为美元
	local usNum = pay
	if not game_config.Q1SDK_DOMESTIC then
		--新支付方式货币类型是人民币
		local fromMoneyType= IsServerUseRechargeChannel() and "zh" or nil
		usNum = money_type_mgr.GetMoneyExchangeRate("en",usNum,fromMoneyType,msg.goodsID)
	end
	usNum = usNum/100

	if msg.rechargeTimes > 20 then
		--超过20次打点上报
		-- q1sdk.AdjustAndFirebaseReport("recharge_time", {value = "Over20"}, usNum, msg.gameOrderID)
		q1sdk.AdjustAndFirebaseReport("recharge_time", {value = "Over20"}, usNum)
	else
		-- q1sdk.AdjustAndFirebaseReport("recharge_time", {value = tostring(msg.rechargeTimes)}, usNum, msg.gameOrderID)
		q1sdk.AdjustAndFirebaseReport("recharge_time", {value = tostring(msg.rechargeTimes)}, usNum)
	end

	-- 回传回收价值，不能添加 msg.gameOrderID 参数，以屏蔽 Adjust 订单排重功能，否则仅第一个消息能上传到 Adjust
	-- 一半回收价值（付费次数不动）
	local rechargePay = math.dotTwo(usNum/2)
	q1sdk.AdjustAndFirebaseReport("Half_recharge", nil, rechargePay)
	-- 1/4回收价值(需要上报两次)
	rechargePay = math.dotTwo(usNum/4)
	q1sdk.AdjustAndFirebaseReport("Double_time025recharge", nil, rechargePay)
	q1sdk.AdjustAndFirebaseReport("Double_time025recharge", nil, rechargePay)
	-- -- 1/2回收价值(需要上报两次),2022/1/17,忽略点位
	-- rechargePay = math.dotTwo(usNum/2)
	-- q1sdk.AdjustAndFirebaseReport("Double_time05recharge", nil, rechargePay)
	-- q1sdk.AdjustAndFirebaseReport("Double_time05recharge", nil, rechargePay)

	-- 用户购买的付费总额回传1/4价值（付费次数不动）
	rechargePay = math.dotTwo(usNum/4)
	q1sdk.AdjustAndFirebaseReport("Onefourth_recharge", nil, rechargePay)
	-- 用户购买的付费总额回传1/3价值（付费次数不动）
	rechargePay = math.dotTwo(usNum/3)
	q1sdk.AdjustAndFirebaseReport("Onethird_recharge", nil, rechargePay)
	-- -- 用户购买的付费总额回传1/6价值（付费次数不动),2022/1/17,忽略点位
	-- rechargePay = math.dotTwo(usNum/6)
	-- q1sdk.AdjustAndFirebaseReport("Onesixth_recharge", nil, rechargePay)
	-- -- 用户购买的付费总额回传1/8价值（付费次数不动),2022/1/17,忽略点位
	-- rechargePay = math.dotTwo(usNum/8)
	-- q1sdk.AdjustAndFirebaseReport("Oneeighth_recharge", nil, rechargePay)
	-- -- 用户购买的付费总额回传1/10价值（付费次数不动),2022/1/17,忽略点位
	-- rechargePay = math.dotTwo(usNum/10)
	-- q1sdk.AdjustAndFirebaseReport("Onetenth_recharge", nil, rechargePay)
	-- 用户购买的付费总额回传1/6价值，付费次数上报2倍
	rechargePay = math.dotTwo(usNum/6)
	q1sdk.AdjustAndFirebaseReport("Double_time016recharge", nil, rechargePay)
	q1sdk.AdjustAndFirebaseReport("Double_time016recharge", nil, rechargePay)
	-- 用户购买的付费总额回传1/8价值，付费次数上报2倍
	rechargePay = math.dotTwo(usNum/8)
	q1sdk.AdjustAndFirebaseReport("Double_time0125recharge", nil, rechargePay)
	q1sdk.AdjustAndFirebaseReport("Double_time0125recharge", nil, rechargePay)
	-- -- 用户购买的付费总额回传1/用户购买的付费总额回传1/16价值，付费次数上报4倍,2022/1/17,忽略点位
	-- rechargePay = math.dotTwo(usNum/16)
	-- q1sdk.AdjustAndFirebaseReport("Four_time00625recharge", nil, rechargePay)
	-- q1sdk.AdjustAndFirebaseReport("Four_time00625recharge", nil, rechargePay)
	-- q1sdk.AdjustAndFirebaseReport("Four_time00625recharge", nil, rechargePay)
	-- q1sdk.AdjustAndFirebaseReport("Four_time00625recharge", nil, rechargePay)

	--local usNumStr = string.format("%.2f", usNum)
	--local propStr = string.format("{value = %s ,currency = USD}", usNum)

	-- 仅 Event_RechargeSuccess 可以传订单号 gameOrderID，其它所有事件都不能传 gameOrderID，否则 adjust 内置订单排重会导致其它事件上传失败
	-- event.Trigger(event.GAME_EVENT_REPORT, "Event_RechargeSuccess",xxx) 事件在触发数数上报时，函数内会检测是否同时上报 Adjust & firebase
	-- 所以这里也不能调用 AdjustAndFirebaseReport 来上报
	-- q1sdk.AdjustAndFirebaseReport("Event_RechargeSuccess", nil, usNum, msg.gameOrderID)

	-- local propStr = string.format("{currency = 'USD',value = %s, rechargePayUsd = %s, rechargeOrderid = %s}", usNum, usNum, msg.gameOrderID)
	local propStr = {
		currency = 'USD',
		value = tostring(usNum),
		rechargePayUsd = usNum,
		rechargeOrderid = msg.gameOrderID,
		originalAmount = pay / 100,
	}
	-- 使用 event.Trigger(event.GAME_EVENT_REPORT, "Event_RechargeSuccess",xxx) 上报
	-- firebase.TrackEvent(firebase.Event_RechargeSuccess, propStr)

	-- 2022-05-20这里要区分是否是福利充值,外网包且是test基本可以判定是福利
	if not game_config.ENABLE_Q1_DEBUG_MODE and isTest then
		event.Trigger(event.GAME_EVENT_REPORT, "Event_Recharge_Bonus", propStr)
	else
		event.Trigger(event.GAME_EVENT_REPORT, "Event_RechargeSuccess", propStr)
	end


	--log.Warning("Event_RechargeSuccess上报参数:" .. propStr)
	-- 判断是否需要弹出跳转评分界面
	local ReviewingUtil = require "ReviewingUtil"
	local grade_data = require "grade_data"
	local ui_grade_tip = require "ui_grade_tip"
	local isIOS = Application.platform == RuntimePlatform.IPhonePlayer
	local isShowRechargePopup = grade_data.GetRechargeTime()
	if isShowRechargePopup and grade_data.OPEN_FLAG == 1 and grade_data.GRADE_FLAG == 0 and not ReviewingUtil.IsReviewing() then
		--新增国内外限制
		if grade_data.CheckShowTime() and grade_data.CheckCurReasonTypeShowTime(4) then
			if game_config.Q1SDK_DOMESTIC then
				--国内IOS包前三次触发
				if isIOS then
					--位面2策划要求隐藏界面,国内IOS屏蔽老的IOS弹窗功能
					-- grade_data.SetReasonData(4)
					-- ui_window_mgr:ShowModule("ui_grade_tip")
				end
			else
				--海外版本(新增domain配置开关校验，港澳台官网包隐藏ui_grade_tip)
				--位面2策划要求隐藏界面 -- anren 策划需求主干打开 5/13
				---策划需求 unity关闭IOS评分弹窗,domain开关失效，安卓暂时关闭
				---策划需求先把安卓得评分弹窗打开（2025/7/22）
				if (Application.platform == RuntimePlatform.Android or Application.platform == RuntimePlatform.IPhonePlayer)then
					grade_data.SetReasonData(4)
					grade_data.IsShowGradeTip(true,4)
				end
			end
		end
	end

	log.Warning("判断是否满足弹出跳转评分的条件grade_data.OPEN_FLAG",grade_data.OPEN_FLAG,"grade_data.GRADE_FLAG",grade_data.GRADE_FLAG,"not ReviewingUtil.IsReviewing()",not ReviewingUtil.IsReviewing(),"grade_data.CheckShowTime()",grade_data.CheckShowTime(),"const.IsEnableGradeTipPop()",const.IsEnableGradeTipPop(),"const.IsEnableNewGradeTipPop()",const.IsEnableNewGradeTipPop(),"isShowRechargePopup",isShowRechargePopup,"grade_data.CheckCurReasonTypeShowTime(4)",grade_data.CheckCurReasonTypeShowTime(4))

	-- not Application.platform ~= RuntimePlatform.IPhonePlayer逻辑判断是否有问题?
	if not game_config.Q1SDK_DOMESTIC then
		if Application.platform == RuntimePlatform.Android then
			Q1SDK.Instance:logPurchaseWithUSD(usNum, tostring(msg.goodsID))
			Q1SDK.Instance:logAddPaymentInfoEvent(true, usNum)
		elseif Application.platform == RuntimePlatform.IPhonePlayer then
			facebook.logPurchaseWithUSD(usNum, tostring(msg.goodsID), "USD")
			facebook.TrackAddPaymentInfoEventWithValue(true, usNum)
		end
	end

	--累计充值上报
	-- q1sdk.RoportTotalRechargeEvent(pay, rechargeCfg.strCurrencyType == "USD", msg.gameOrderID)
	--这里新支付方式只能用人民币pay传进来 不可修改pay
	q1sdk.RoportTotalRechargeEvent(pay, rechargeCfg.strCurrencyType == "USD",msg.goodsID)
	--购买礼包打点上报
	q1sdk.RoportPurchaseGiftEvent(msg.goodsID, rechargeCfg.logType, usNum)
	-- q1sdk.RoportPurchaseGiftEvent(msg.goodsID, rechargeCfg.ITypeID, usNum, msg.gameOrderID)


	ReportRechargeAction("buyGoods", "id="..tostring(msg.gameOrderID))
	q1sdk.TrackUpdateTotalRevenue(pay)
	local player_mgr = require "player_mgr"
	
	local roleName = tostring(player_mgr.GetRoleName()) or ""

	local isPop = GetIsPopByOrderAndClear(msg.gameOrderID)
	local isShowWin = ui_window_mgr:IsModuleShown("ui_lineup_gift") --推荐礼包
	local _isRecommend = 0
	if isShowWin then
		_isRecommend = 1
	end
	-- local payNum = pay
	-- if not game_config.Q1SDK_DOMESTIC then
	-- 	--新支付方式货币类型是人民币
	-- 	local fromMoneyType= IsServerUseRechargeChannel() and "zh" or nil
	-- 	payNum = money_type_mgr.GetMoneyExchangeRate("en",payNum,fromMoneyType,msg.goodsID)
	-- end
	-- payNum = payNum/100
	local lingshi_data = require "lingshi_data"
	local json_str = {
		order_id_new=tostring(msg.gameOrderID),
		-- order_id=msg.gameOrderID,
		pay_type=msg.goodsID,		-- num
		pay_amount=usNum,
		pay_method="",
		uuid=q1sdk.GetUUID(),
		imei_idfa=q1sdk.GetImeiMD5(),
		radid=q1sdk.GetRadid(),
		rsid=q1sdk.GetRsid(),
		role_name = roleName,
		
		iGoodsIdfy = rechargeCfg and rechargeCfg.iGoodsIdfy,
		ChestType = rechargeCfg and rechargeCfg.ITypeID,
		
		isRecommend = _isRecommend,
		rechargePayUsd = usNum, --充值额度，单位美元，上报第三方sdk平台
		recharge_test = isTest,
		originalAmount = pay / 100,
		popup_auto = isPop and 1 or 0,
		analog_recharge = lingshi_data.GetLingshiRechargeable() and 1 or 0,
	}
	event.Trigger(event.GAME_EVENT_REPORT, "recharge", json_str)
	q1sdk.AdjustAndFirebaseReport("AddPaymentInfo", json_str)

	--local chose_recharge_method_mgr = require("chose_recharge_method_mgr")
	--if chose_recharge_method_mgr.GetIfTerminalRechargeType()  then
	--	event.Trigger(event.GAME_EVENT_REPORT, "terminal3_recharge", json_str)
	--end

	--国内广告流量充值打点
	if not util.ShouldUseCustomSDKUI() then
		q1sdk.reportPurchase(usNum)
	end


	if msg.rechargeTimes == 1 then
		-- 上报首次充值
		event.Trigger(event.GAME_EVENT_REPORT, "pay_first", json_str)
	end

	--adjust 打点
	local keyValueParam = {
		game_orderid=tostring(msg.gameOrderID) ,
		pay_type	=tostring(msg.goodsID),		-- num
	}
	local revenueParam = {num = usNum , type= game_config.Q1SDK_DOMESTIC and "CNY" or "USD", orderID = msg.gameOrderID}
	local log = require "log"
	if not isTest then
		--log.Warning("Event_Recharge")
		adjust.TrackEvent(adjust.Event_Recharge, keyValueParam, revenueParam)
	else
		--log.Warning("Event_Recharge_Test")
		adjust.TrackEvent(adjust.Event_Recharge_Test, keyValueParam)
	end
	facebook.TrackRechargeEvent(tostring(msg.goodsID),tostring(usNum))

	-------------------------------------------------------------------------------------------------

	local retFunc = function(goodsID, num,heroID)
		local cfg = game_scheme:Recharge_0(goodsID)

		if PrintEnable then
			log.Error("goodsID",goodsID,"goodsID%10000",goodsID%10000)
		end
		if (goodsID%10000) < 1000 then
			local count = cfg.iDiamondCount * (num or 1)
			util.DelayCall(1.6, function()
				if count ~= 0 then
					local str = string.format(lang.Get(key), tostring(count))
					flow_text.Add(str)
				else	-- 非钻石
					flow_text.Add(lang.Get((goodsID%10000) == 7 and 9150 or 9151))
				end
				local player_mgr = require "player_mgr"
				if player_mgr.GetIsShowVipPanel() and (player_mgr.GetPlayerOldVipLevel() < player_mgr.GetPlayerVipLevel()) then
					local ui_role_vip_up = require "ui_role_vip_up"
					ui_role_vip_up.ShowWithDate(player_mgr.GetPlayerOldVipLevel(),player_mgr.GetPlayerVipLevel())
				end
			end)
		else
			-- 别的处理
			local item_data = require "item_data"
			local reward_mgr = require "reward_mgr"
			local rewardArr={}
			-- if goodsID == 1056 and goodsID == 11056 then return end
			if PrintEnable then
				log.Error("#msg.rewardInfo",msg.rewardInfo and #msg.rewardInfo)
			end
			if msg.rewardInfo and #msg.rewardInfo > 0 then
				for k, v in ipairs(msg.rewardInfo) do
					if v.sid and #v.sid > 0 then
						for _k, _v in ipairs(v.sid) do
							if v.nType == 0 then
								local num = v.num
								local item_cfg = game_scheme:Item_0(v.ID)
								if item_cfg and item_cfg.type == item_data.Item_Type_Enum.Jewel then
									num = 1
								end
								table.insert( rewardArr, {id=v.ID, num=num, nType=item_data.Reward_Type_Enum.Item, sid=_v})
							elseif v.nType == 1 then
								local rewardData = reward_mgr.GetRewardGoods(v.ID)
								if rewardData then
									local item_cfg = game_scheme:Item_0(rewardData.id)
									if item_cfg and item_cfg.type == item_data.Item_Type_Enum.Jewel then
										rewardData.num = 1
										rewardData.sid = _v
									end
									table.insert( rewardArr, rewardData)
								end
							end
						end
					else
						if v.nType == 0 then
							table.insert( rewardArr, {id=v.ID, num=v.num, nType=item_data.Reward_Type_Enum.Item} )
						elseif v.nType == 1 then
							table.insert( rewardArr, reward_mgr.GetRewardGoods(v.ID))
						end
					end
				end
			else
				--local cfg = game_scheme:Recharge_0(goodsID)
				for i=0, cfg.iItems.count-1 do
					local id = cfg.iItems.data[i]
					local num = cfg.iItemsCount.data[i]

					--2020.1.15梁骐显，增加heroID用于英雄选择卡判断
					if id == 41017 and heroID then
						if not boxChooseCash then
							boxChooseCash = {}
							for i = 0, game_scheme:BoxChoose_nums() - 1 do
								local boxConfig = game_scheme:BoxChoose(i)
								boxChooseCash[boxConfig.heroid] = boxConfig.ID
							end
						end
						id = boxChooseCash[heroID]
					end

					table.insert( rewardArr, {id=id, num=num, nType=item_data.Reward_Type_Enum.Item} )
				end
				if cfg.iDiamondCount > 0 then
					table.insert( rewardArr, {id=2, num=cfg.iDiamondCount, nType=item_data.Reward_Type_Enum.Item} )
					table.insert( rewardArr, {id=8, num=cfg.iVipExp, nType=item_data.Reward_Type_Enum.Item} )
				end

				local rechargeExtraRewardNums = game_scheme:RechargeExtraReward_nums()
				for i=1, rechargeExtraRewardNums do
					local extraRewardCfg = game_scheme:RechargeExtraReward_0(i)
					if extraRewardCfg then
						local count = extraRewardCfg.nRechargeID.count
						for i=0, count do
							if extraRewardCfg.nRechargeID.data[i] == goodsID then
								local festival_activity_mgr = require "festival_activity_mgr"
								local cfg_activity = festival_activity_cfg.GetActivityCfgByAtyID(extraRewardCfg.nAtyID)
								if cfg_activity then
									local topicID = festival_activity_mgr.GetTopicIDByActivityID(cfg_activity.AtyID)
									if festival_activity_mgr.IsActivityOpen(topicID) then
										for i=0, extraRewardCfg.nExtraReward.count-1 do
											local reward = extraRewardCfg.nExtraReward.data[i]
											local reward_mgr = require "reward_mgr"
											table.insert( rewardArr, reward_mgr.GetRewardGoods(reward))
										end
									end
								end
								break
							end
						end
					end
				end
			end
			if PrintEnable then
				log.Error("#rewardArr",#rewardArr)
			end
			if #rewardArr > 0 then
				local iui_reward = require "iui_reward"
				iui_reward.Show(rewardArr,nil,function()
					local player_mgr = require "player_mgr"
					if player_mgr.GetIsShowVipPanel() and (player_mgr.GetPlayerOldVipLevel() < player_mgr.GetPlayerVipLevel()) then
						local ui_role_vip_up = require "ui_role_vip_up"
						ui_role_vip_up.ShowWithDate(player_mgr.GetPlayerOldVipLevel(),player_mgr.GetPlayerVipLevel())
					end
				end)
			else
				local player_mgr = require "player_mgr"
				if player_mgr.GetIsShowVipPanel() and (player_mgr.GetPlayerOldVipLevel() < player_mgr.GetPlayerVipLevel()) then
					local ui_role_vip_up = require "ui_role_vip_up"
					ui_role_vip_up.ShowWithDate(player_mgr.GetPlayerOldVipLevel(),player_mgr.GetPlayerVipLevel())
				end
			end

			if (goodsID%10000) == 1047 then --首冲合成引导
				local force_guide_system=require"force_guide_system"
				local force_guide_event=require"force_guide_event"
				force_guide_system.TriEnterEvent(force_guide_event.tPreFirstRechage)
				force_guide_system.TriComEvent(force_guide_event.cPreFirstRechage)
			elseif goodsID%10000 == 1202 then
				local properties = {
					role_name = player_mgr.GetRoleName(),
					ChestType = 1,
				}
				event.Trigger(event.GAME_EVENT_REPORT, "HookLevel_LevelChest_paidSuccess", properties)
			elseif goodsID%10000 == 1201 then
				local player_mgr = require "player_mgr"
				local properties = {
					role_name = player_mgr.GetRoleName(),
					ChestType = 2,
				}
				event.Trigger(event.GAME_EVENT_REPORT, "HookLevel_StarChest_paidSuccess", properties)
			end
		end
	end

	------------位面2礼包奖励处理---------------
	local retFunc_New = function(goodsID,num,heroID)
		local cfg = game_scheme:Recharge_0(goodsID)
		if PrintEnable then
			log.Error("goodsID",goodsID,"goodsID%10000",goodsID%10000)
		end
		local reward_mgr = require "reward_mgr"
		local item_data = require "item_data"
		local rewardArr = {}

		local GetRewardList = function(rewardInfo, rewardList )
			for k,v in ipairs(rewardInfo) do
				if v.sid and #v.sid > 0 then
					for _k, _v in ipairs(v.sid) do
						if v.nType == 0 then
							--物品
							local num = v.num
							table.insert( rewardList, {id=v.ID, num=num, nType=item_data.Reward_Type_Enum.Item, sid=_v})
						elseif v.nType == 1 then
							--英雄
							local num = v.num
							table.insert( rewardList, {id=v.ID, num=num, nType=item_data.Reward_Type_Enum.Hero, sid=_v})
						elseif v.nType == 2 then
							--幸存者
							local num = v.num
							table.insert( rewardList, {id=v.ID, num=num, nType=item_data.Reward_Type_Enum.Survivor, sid=_v})
						elseif v.nType == 3 then
							--士兵
							local num = v.num
							table.insert( rewardList, {id=v.ID, num=num, nType=item_data.Reward_Type_Enum.solider, sid=_v})
						end
					end
				else
					if v.nType == 0 then
						table.insert( rewardList, {id=v.ID, num=v.num, nType=item_data.Reward_Type_Enum.Item} )
					elseif v.nType == 1 then
						table.insert( rewardList, {id=v.ID, num=v.num, nType=item_data.Reward_Type_Enum.Hero})
					elseif v.nType == 2 then
						--幸存者
						local num = v.num
						table.insert( rewardList, {id=v.ID, num=num, nType=item_data.Reward_Type_Enum.Survivor, sid=_v})
					elseif v.nType == 3 then
						--士兵
						local num = v.num
						table.insert( rewardList, {id=v.ID, num=num, nType=item_data.Reward_Type_Enum.solider, sid=_v})
					end
				end
			end
			return rewardList
		end
		--固定奖励信息
		if msg.rewardInfo and #msg.rewardInfo > 0 then
			GetRewardList(msg.rewardInfo, rewardArr)
		end
		--随机奖励信息
		if msg.randomRewardInfo and #msg.randomRewardInfo > 0 then
			GetRewardList(msg.randomRewardInfo, rewardArr)
		end
		--如果随机奖励和固定奖励列表都为空
		if #rewardArr == 0 then
			--local cfg = game_scheme:Recharge_0(goodsID)
			for i=0, cfg.iItems.count-1 do
				local id = cfg.iItems.data[i]
				local num = cfg.iItemsCount.data[i]

				--2020.1.15梁骐显，增加heroID用于英雄选择卡判断
				if id == 41017 and heroID then
					if not boxChooseCash then
						boxChooseCash = {}
						for i = 0, game_scheme:BoxChoose_nums() - 1 do
							local boxConfig = game_scheme:BoxChoose(i)
							boxChooseCash[boxConfig.heroid] = boxConfig.ID
						end
					end
					id = boxChooseCash[heroID]
				end

				table.insert( rewardArr, {id=id, num=num, nType=item_data.Reward_Type_Enum.Item} )
			end
			if cfg.iDiamondCount > 0 then
				table.insert( rewardArr, {id=2, num=cfg.iDiamondCount, nType=item_data.Reward_Type_Enum.Item} )
				table.insert( rewardArr, {id=8, num=cfg.iVipExp, nType=item_data.Reward_Type_Enum.Item} )
			end

			-- local rechargeExtraRewardNums = game_scheme:RechargeExtraReward_nums()
			-- for i=1, rechargeExtraRewardNums do
			-- 	local extraRewardCfg = game_scheme:RechargeExtraReward_0(i)
			-- 	if extraRewardCfg then
			-- 		local count = extraRewardCfg.nRechargeID.count
			-- 		for i=0, count do
			-- 			if extraRewardCfg.nRechargeID.data[i] == goodsID then
			-- 				local festival_activity_mgr = require "festival_activity_mgr"
			-- 				local cfg_activity = festival_activity_cfg.GetActivityCfgByAtyID(extraRewardCfg.nAtyID)
			-- 				if cfg_activity then
			-- 					local topicID = festival_activity_mgr.GetTopicIDByActivityID(cfg_activity.AtyID)
			-- 					if festival_activity_mgr.IsActivityOpen(topicID) then
			-- 						for i=0, extraRewardCfg.nExtraReward.count-1 do
			-- 							local reward = extraRewardCfg.nExtraReward.data[i]
			-- 							local reward_mgr = require "reward_mgr"
			-- 							table.insert( rewardArr, reward_mgr.GetRewardGoods(reward))
			-- 						end
			-- 					end
			-- 				end
			-- 				break
			-- 			end
			-- 		end
			-- 	end
			-- end
		end
		if PrintEnable then
			log.Error("#rewardArr",#rewardArr)
		end
		if #rewardArr > 0 then
			--展示恭喜获得
			-- local listData = { title = "", dataList = rewardArr }
			-- local showData = {}
			-- table.insert(showData, listData)
			-- local ui_reward_result = require "ui_reward_result_new"
			-- ui_reward_result.SetInputParam(showData)
			-- ui_window_mgr:ShowModule("ui_reward_result_new")
			if super_month_gift_mgr.checkIsNotShowReward(goodsID) then 
				local super_month_gift_define = require "super_month_gift_define"
				event.Trigger(super_month_gift_define.OPEN_GET_REWARD_VIEW)
			else
				local reward_mgr = require "reward_mgr"
				reward_mgr.ShowReward(rewardArr)
			end
		end

		if (goodsID%10000) == 1047 then --首冲合成引导
			local force_guide_system=require"force_guide_system"
			local force_guide_event=require"force_guide_event"
			force_guide_system.TriEnterEvent(force_guide_event.tPreFirstRechage)
			force_guide_system.TriComEvent(force_guide_event.cPreFirstRechage)
		elseif goodsID%10000 == 1202 then
			local properties = {
				role_name = player_mgr.GetRoleName(),
				ChestType = 1,
			}
			event.Trigger(event.GAME_EVENT_REPORT, "HookLevel_LevelChest_paidSuccess", properties)
		elseif goodsID%10000 == 1201 then
			local properties = {
				role_name = player_mgr.GetRoleName(),
				ChestType = 2,
			}
			event.Trigger(event.GAME_EVENT_REPORT, "HookLevel_StarChest_paidSuccess", properties)
		end
	end

	print(396,tostring(msg))
	local processed = false
	for i, v in ipairs(pendingQueue) do
		if v.gameOrderID == msg.gameOrderID then
			processed = true
			retFunc_New(v.goodsID,nil,msg.heroID)
		end
	end

	if not processed and msg:HasField('goodsID') then
		retFunc_New(msg.goodsID, msg.num,msg.heroID)
	end
end

--@region 
--[[
message TMSG_RECHARGE_PAYMENT_RESULT_NTF// 付款成功通知，C->S
{
	required RechargePaymentResult paymentResult = 1;   // 付款结果
	required string gameOrderID = 2;                    // 游戏订单号
	required uint32 goodsID = 3;                        // 商品ID
	required uint32 num = 4;                            // 购买数量
}
enum RechargePaymentResult
{
	enPaymentResult_Made = 0;                   // 付款成功
	enPaymentResult_Cancle = 1;                 // 取消付款
}
--]]
--@endregion
function Send_RECHARGE_PAYMENT_RESULT_NTF(paymentResult, gameOrderID, goodsID, num)
	local msg = recharge_pb.TMSG_RECHARGE_PAYMENT_RESULT_NTF()
	msg.num = num
	msg.goodsID = goodsID
	msg.gameOrderID = gameOrderID
	msg.paymentResult = paymentResult
	net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_RECHARGE_PAYMENT_RESULT_NTF, msg)
end

--@region 
--[[
message TMSG_RECHARGE_PAYMENT_RESULT_CONFIRM_NTF// 付款成功通知确认，S->C
{
	required string gameOrderID = 1;                    // 游戏订单号
}
--]]
--@endregion
function Recv_RECHARGE_PAYMENT_RESULT_CONFIRM_NTF(msg)
	for i, v in ipairs(reportQueue) do
		if v.gameOrderID == msg.gameOrderID then
			util.RemoveDelayCall(v.ticker)
			table.remove(reportQueue, i)
			break
		end
	end
end

function Recv_GOODS_USEITEM_NTF(msg)
	local dataEx = {}
	for i=1,#msg.itemID do
		local listData = {}
		listData.sid = 0
		listData.id = msg.itemID[i]
		listData.num = msg.itemNum[i]
		listData.nType = item_data.Reward_Type_Enum.Item
		table.insert(dataEx, listData)
	end
	local iui_reward = require "iui_reward"
	iui_reward.Show(dataEx)
end

--终身卡领取奖励回复
function Recv_LIFETIME_CARD_REWARD(msg)
	if msg.errCode ~= 0 then
		flow_text.Add(lang.Get(100000 + msg.errCode))
	else
		local item_data = require "item_data"
		local list = {}
		local rewardData = {}
		for i, data in ipairs(msg.rewardArr) do
			if data.sid and #data.sid > 0 then
				for k, v in ipairs(data.sid) do
					local temp = {}
					temp.id = data.itemID
					temp.num = data.itemNum
					temp.itemSid = v
					temp.nType = item_data.Reward_Type_Enum.Item
					local cfg_item = game_scheme:Item_0(data.itemID)
					if cfg_item and cfg_item.type == prop_pb.GOODS_TYPE_EQUIP then
						temp.num = 1
					end
					table.insert(rewardData, temp)
				end
			else
				local temp = {}
				temp.id = data.itemID
				temp.num = data.itemNum
				temp.nType = item_data.Reward_Type_Enum.Item
				table.insert(rewardData, temp)
			end
		end
		table.sort(rewardData,function (a,b)
			return a.id == 2
		end)
		local listData = { title = "", dataList = rewardData }
		table.insert(list, listData)
		local ui_reward_result = require "ui_reward_result"
		ui_reward_result.SetInputParam(list, function()
		end, nil, nil, nil, true)
		ui_window_mgr:ShowModule("ui_reward_result")
	end

end

--是否弹窗相关内容
local popCacheByRecharge = false
local popCacheOrderId = {}
--第一阶段，记录RechargeId
function PopDataFirstStage(isPop)
	popCacheByRecharge = isPop
end
--第二阶段 rechargeId 映射到 OrderId
function PopDataSecStage(orderId)
	popCacheOrderId[orderId] = popCacheByRecharge
end
--第三阶段 上发数数时获取
function GetIsPopByOrderAndClear(orderId)
	local isPop = popCacheOrderId[orderId]
	isPop = isPop or false
	popCacheOrderId[orderId] = nil
	return isPop
end

-- /// 注册消息
-------------------------------------------------------------------
local MessageTable =
{
	{msg_pb.MSG_RECHARGE_PAYMENT_RESULT_CONFIRM_NTF,			Recv_RECHARGE_PAYMENT_RESULT_CONFIRM_NTF,			recharge_pb.TMSG_RECHARGE_PAYMENT_RESULT_CONFIRM_NTF},
	{msg_pb.MSG_RECHARGE_RESULT_NTF           ,			Recv_RECHARGE_RESULT_NTF          ,			recharge_pb.TMSG_RECHARGE_RESULT_NTF},
	{msg_pb.MSG_FIRSTRECHARGEREWARD_RSP       ,			Recv_FIRSTRECHARGEREWARD_RSP      ,			recharge_pb.TMSG_FIRSTRECHARGEREWARD_RSP},
	{msg_pb.MSG_RECHARGE_RSP                  ,			Recv_RECHARGE_RSP                 ,			recharge_pb.TMSG_RECHARGE_RSP},
	{msg_pb.MSG_GOODS_USEITEM_NTF			  ,			Recv_GOODS_USEITEM_NTF			  ,  		package_pb.TMSG_GOODS_USEITEM_NTF},
	{ msg_pb.MSG_LIFETIME_CARD_REWARD_RSP, Recv_LIFETIME_CARD_REWARD, challenge_pb.TMSG_LIFETIME_CARD_REWARD_RSP },

	{msg_pb.MSG_RECHARGE_TOKENBUY_RSP,					Recv_RECHARGE_TOKENBUY_RSP,				recharge_pb.TMSG_RECHARGE_TOKENBUY_RSP},

}

net_route.RegisterMsgHandlers(MessageTable)