local require = require
local typeof = typeof

local Button = CS.UnityEngine.UI.Button
local Image = CS.UnityEngine.UI.Image
local RectTransform = CS.UnityEngine.RectTransform
local ScrollRect = CS.UnityEngine.UI.ScrollRect
local Text = CS.UnityEngine.UI.Text


module("ui_halloween_activity_slot_machine_rule_info_pop_binding")

UIPath = "ui/prefabs/gw/activity/halloweenactivity/slotmachine/uihalloweenactivityslotmachineruleinfopop.prefab"

WidgetTable ={
	btn_closeBtn = { path = "btn_closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	img_bg = { path = "center/img_bg", type = Image, },
	rtf_reward_page = { path = "center/img_bg/rtf_reward_page", type = RectTransform, },
	sr_ScrollView = { path = "center/img_bg/rtf_reward_page/sr_ScrollView", type = ScrollRect, },
	rtf_content = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content", type = RectTransform, },
	txt_reward_page_line1 = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/txt_reward_page_line1", type = Text, },
	img_bg = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_3same/draw_result_item/img_bg", type = Image, },
	img_icon_1 = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_3same/draw_result_item/left/img_icon_1", type = Image, },
	img_icon_2 = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_3same/draw_result_item/left/img_icon_2", type = Image, },
	img_icon_3 = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_3same/draw_result_item/left/img_icon_3", type = Image, },
	img_icon_4 = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_3same/draw_result_item/left/img_icon_4", type = Image, },
	img_icon_5 = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_3same/draw_result_item/left/img_icon_5", type = Image, },
	img_icon_6 = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_3same/draw_result_item/left/img_icon_6", type = Image, },
	rtf_item = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_3same/draw_result_item/right/rtf_item", type = RectTransform, },
	txt_num = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_3same/draw_result_item/right/txt_num", type = Text, },
	txt_rate_num = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_3same/draw_result_item/right/txt_rate_num", type = Text, },
	txt_rate_label = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_3same/draw_result_item/right/txt_rate_label", type = Text, },
	txt_reward_page_line2 = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/txt_reward_page_line2", type = Text, },
	img_bg = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_2same/draw_result_item/img_bg", type = Image, },
	img_icon_1 = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_2same/draw_result_item/left/img_icon_1", type = Image, },
	img_icon_2 = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_2same/draw_result_item/left/img_icon_2", type = Image, },
	img_icon_3 = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_2same/draw_result_item/left/img_icon_3", type = Image, },
	img_icon_4 = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_2same/draw_result_item/left/img_icon_4", type = Image, },
	img_icon_5 = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_2same/draw_result_item/left/img_icon_5", type = Image, },
	img_icon_6 = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_2same/draw_result_item/left/img_icon_6", type = Image, },
	rtf_item = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_2same/draw_result_item/right/rtf_item", type = RectTransform, },
	txt_num = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_2same/draw_result_item/right/txt_num", type = Text, },
	txt_rate_num = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_2same/draw_result_item/right/txt_rate_num", type = Text, },
	txt_rate_label = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_2same/draw_result_item/right/txt_rate_label", type = Text, },
	txt_reward_page_line3 = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/txt_reward_page_line3", type = Text, },
	img_bg = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_0same/draw_result_item/img_bg", type = Image, },
	img_icon_1 = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_0same/draw_result_item/left/img_icon_1", type = Image, },
	img_icon_2 = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_0same/draw_result_item/left/img_icon_2", type = Image, },
	img_icon_3 = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_0same/draw_result_item/left/img_icon_3", type = Image, },
	img_icon_4 = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_0same/draw_result_item/left/img_icon_4", type = Image, },
	img_icon_5 = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_0same/draw_result_item/left/img_icon_5", type = Image, },
	img_icon_6 = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_0same/draw_result_item/left/img_icon_6", type = Image, },
	rtf_item = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_0same/draw_result_item/right/rtf_item", type = RectTransform, },
	txt_num = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_0same/draw_result_item/right/txt_num", type = Text, },
	txt_rate_num = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_0same/draw_result_item/right/txt_rate_num", type = Text, },
	txt_rate_label = { path = "center/img_bg/rtf_reward_page/sr_ScrollView/Viewport/rtf_content/rft_reward_page_0same/draw_result_item/right/txt_rate_label", type = Text, },
	rtf_rule_page = { path = "center/img_bg/rtf_rule_page", type = RectTransform, },
	sr_ScrollView = { path = "center/img_bg/rtf_rule_page/sr_ScrollView", type = ScrollRect, },
	rtf_content = { path = "center/img_bg/rtf_rule_page/sr_ScrollView/Viewport/rtf_content", type = RectTransform, },
	txt_reward_page_line1 = { path = "center/img_bg/rtf_rule_page/sr_ScrollView/Viewport/rtf_content/txt_reward_page_line1", type = Text, },
	btn_reward_dis = { path = "center/imt_title_bg/btn_reward_dis", type = Button, event_name = "OnBtnReward_disClickedProxy"},
	btn_rule_dis = { path = "center/imt_title_bg/btn_rule_dis", type = Button, event_name = "OnBtnRule_disClickedProxy"},
	btn_rule = { path = "center/imt_title_bg/btn_rule", type = Button, event_name = "OnBtnRuleClickedProxy"},
	btn_reward = { path = "center/imt_title_bg/btn_reward", type = Button, event_name = "OnBtnRewardClickedProxy"},

}
