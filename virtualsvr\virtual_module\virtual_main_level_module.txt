﻿---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by ha<PERSON><PERSON><PERSON>.
--- DateTime: 2025/9/5 10:17
--- desc : 主线关卡模块
local table = table
local require = require
local laymain_data = require "laymain_data"
local msg_pb = require "msg_pb"
local virtual_net = require "virtual_net"
local challenge_pb = require "challenge_pb"
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
module("virtual_main_level_module")

local PassLevelKey = "PassLevelKey"
local passLevel = 0
local stageType = 1

function MSG_PLAYER_ENTER_BATTLE_RSP(msg)
    if not msg then
        return
    end
    --设置主线关卡挑战回复
    local battleRsp = challenge_pb.TMSG_PLAYER_ENTER_BATTLE_RSP()
    battleRsp.idleStage = msg.hookLevel + 1
    battleRsp.passStage = msg.hookLevel
    battleRsp.stageType = stageType
    battleRsp.battleResult = true
    SetPassLevel(msg.hookLevel)
    virtual_net.SendMessageToClient(msg_pb.MSG_PLAYER_ENTER_BATTLE_RSP, battleRsp)
end

function GetLevelChallengeReqData()
    local levelChallengeReqData = {}
    -- 倒序遍历（推荐）
    for i = passLevel, 1, -1 do
        if i > 0 then
            local gw_ab_test_mgr = require "gw_ab_test_mgr"
            local buildType = gameType or gw_ab_test_mgr.GetHookLevelType()
            local hookLevelType = gw_ab_test_mgr.GetHookLevelMixedType()
            local msg = challenge_pb.TMSG_SET_HOOKLEVELCHALLENGE_RESULT_REQ()
            msg.hookLevel = hookLevel
            msg.starNum = 3
            msg.miniGameType = buildType
            if hookLevelType and hookLevelType ~= 1 then
                msg.abTestId = hookLevelType
            end
            local data = msg:SerializeToString()
            table.insert(levelChallengeReqData, data)
        end
    end
    return levelChallengeReqData
end

function PlayerInit()
    --DeletePassLevel()
    ReadPassLevel()
    laymain_data.InitLaymainData(nil, passLevel)
    laymain_data.setPassLevel(passLevel)
end

function SetPassLevel(level)
    --缓存本地
    passLevel = level
    -- 调用 Unity 的 PlayerPrefs（如果你能直接访问 CS namespace）
    PlayerPrefs.SetInt(PassLevelKey, passLevel)
    PlayerPrefs.Save()
end

function ReadPassLevel()
    passLevel = PlayerPrefs.GetInt(PassLevelKey, 0)
end
function GetPassLevel()
    return passLevel
end

function DeletePassLevel()
    PlayerPrefs.DeleteKey(PassLevelKey)
end