--- Created by fgy.
--- Changed by <PERSON><PERSON>.
--- DateTime: 2025/3/27
--- Des: 作为内部发出的综合处理中心,处理进入沙盘后的事件,涉及到多模块之间调度的,在这边处理

local require = require
local type = type

local gw_comp_name = require "gw_comp_name"
local event_allianceDuel_define = require "event_allianceDuel_define"
local war_zone_duel_define = require "war_zone_duel_define"
local gw_sand_event_define = require "gw_sand_event_define"
local sand_ui_event_define = require "sand_ui_event_define"
local event = require "event"
local gw_ed = require "gw_ed"
local Event = gw_ed.mgr

local gw_switch_utility = require "gw_switch_utility"
local OnSandLuaErr = gw_switch_utility.OnSandLuaErr

---@class GWSandInternalMgr
local GWSandInternalMgr = {}

--region 模块懒加载
-- 模块缓存
local _moduleCache = {}

local function _requireModule(name)
    if not _moduleCache[name] then
        _moduleCache[name] = require(name)
    end
    return _moduleCache[name]
end

local function _getSandData()
    return _requireModule("gw_sand_data")
end

local function _getSandCameraMgr()
    return _requireModule("gw_sand_camera_mgr")
end
--endregion

local EventNameToFunctionName = {
    [event.LANGUAGE_SETTING_CHANGED] = "OnLanguageChange",
    [gw_ed.GW_SAND_VIEW_LEVEL_CHANGE] = "OnViewLevelChange",
    [gw_ed.GW_SAND_CLICK_SCENE_EVENT] = "OnClickSceneEntity",
    [gw_sand_event_define.GW_SAND_UPDATE_SELF_DATA] = "UpdateSandBoxBaseInfo",

    [event_allianceDuel_define.ALLIANCE_RAID_START] = "UpdateSandBoxEntityState",
    [event_allianceDuel_define.ALLIANCE_RAID_END] = "UpdateSandBoxEntityState",
    [war_zone_duel_define.Evt_ALLIANCE_BATTLE_DUEL_CHANGE_NTF_Handler] = "CheckWarZoneDuelCongressAtk",

    [event.EVENT_SAND_ENTITY_UPDATE] = "UpdateSandRelocationEntityState",
    [event.EVENT_SAND_COMP_ENTITY_UPDATE_DELAY] = "UpdateSandRelocationEntityState",
}

--region Mgr Life
function GWSandInternalMgr.InitSand()
    Event:Register(gw_ed.GW_SAND_INTERNAL_DATA_CHANGE, GWSandInternalMgr.SandEventCenter)
    Event:Register(gw_ed.GW_SAND_CLICK_SCENE_EVENT, GWSandInternalMgr.CommonEventCenter)
    Event:Register(gw_ed.GW_SAND_VIEW_LEVEL_CHANGE, GWSandInternalMgr.CommonEventCenter)

    event.Register(gw_sand_event_define.GW_SAND_UPDATE_SELF_DATA, GWSandInternalMgr.CommonEventCenter)
    event.Register(gw_sand_event_define.GW_SAND_FUNCTION_TRIGGER, GWSandInternalMgr.SandEventCenter)
    event.Register(event.LANGUAGE_SETTING_CHANGED, GWSandInternalMgr.CommonEventCenter)

    event.Register(event_allianceDuel_define.ALLIANCE_RAID_START, GWSandInternalMgr.CommonEventCenter)
    event.Register(event_allianceDuel_define.ALLIANCE_RAID_END, GWSandInternalMgr.CommonEventCenter)
    event.Register(war_zone_duel_define.Evt_ALLIANCE_BATTLE_DUEL_CHANGE_NTF_Handler, GWSandInternalMgr.CommonEventCenter)

    -- 处理迁城的刷新
    event.Register(event.EVENT_SAND_ENTITY_UPDATE, GWSandInternalMgr.CommonEventCenter)
    event.Register(event.EVENT_SAND_COMP_ENTITY_UPDATE_DELAY, GWSandInternalMgr.CommonEventCenter)
end

function GWSandInternalMgr.Dispose()
    Event:Unregister(gw_ed.GW_SAND_INTERNAL_DATA_CHANGE, GWSandInternalMgr.SandEventCenter)
    Event:Unregister(gw_ed.GW_SAND_CLICK_SCENE_EVENT, GWSandInternalMgr.CommonEventCenter)
    Event:Unregister(gw_ed.GW_SAND_VIEW_LEVEL_CHANGE, GWSandInternalMgr.CommonEventCenter)

    event.Unregister(gw_sand_event_define.GW_SAND_UPDATE_SELF_DATA, GWSandInternalMgr.CommonEventCenter)
    event.Unregister(gw_sand_event_define.GW_SAND_FUNCTION_TRIGGER, GWSandInternalMgr.SandEventCenter)
    event.Unregister(event.LANGUAGE_SETTING_CHANGED, GWSandInternalMgr.CommonEventCenter)

    event.Unregister(event_allianceDuel_define.ALLIANCE_RAID_START, GWSandInternalMgr.CommonEventCenter)
    event.Unregister(event_allianceDuel_define.ALLIANCE_RAID_END, GWSandInternalMgr.CommonEventCenter)
    event.Unregister(war_zone_duel_define.Evt_ALLIANCE_BATTLE_DUEL_CHANGE_NTF_Handler, GWSandInternalMgr.CommonEventCenter)

    event.Unregister(event.EVENT_SAND_ENTITY_UPDATE, GWSandInternalMgr.CommonEventCenter)
    event.Unregister(event.EVENT_SAND_COMP_ENTITY_UPDATE_DELAY, GWSandInternalMgr.CommonEventCenter)
end

--- 通用事件处理中心
function GWSandInternalMgr.CommonEventCenter(eventName, ...)
    local funcName = EventNameToFunctionName[eventName]
    local luaFunction = GWSandInternalMgr[funcName]
    if luaFunction and type(luaFunction) == "function" then
        xpcall(luaFunction, OnSandLuaErr, ...)
    end
end

--- 沙盘事件处理中心
function GWSandInternalMgr.SandEventCenter(eventName, funcName, ...)
    local luaFunction = GWSandInternalMgr[funcName]
    if luaFunction and type(luaFunction) == "function" then
        xpcall(luaFunction, OnSandLuaErr, ...)
    end
end
--endregion

--region Sand Input Event
function GWSandInternalMgr.OnViewLevelChange(newLod, oldLod)
    local relocationUtil = _requireModule("gw_sand_relocation_entity_util")
    if relocationUtil.GetRelocationEntityState() then
        -- 刷新一下按钮的显示
        relocationUtil.SetMoveChoseHudState()
        return
    end
    _requireModule("sandbox_ui_mgr").GW_SAND_VIEW_LEVEL_CHANGE(newLod)
end

function GWSandInternalMgr.OnCameraZoomChange(distance)
    _getSandCameraMgr().SetCameraZoom(distance)
end
--endregion

--region Relocation Internal Event
function GWSandInternalMgr.OnSandRelocationEntityCompleted(state, gridPos)
    local relocationUtil = _requireModule("gw_sand_relocation_entity_util")
    relocationUtil.RelocationEntityCompleted(state, gridPos)
    relocationUtil.CancelRelocationEntitySid(not state)

    local level = _getSandCameraMgr().GetViewLevel()
    _requireModule("sandbox_ui_mgr").GW_SAND_VIEW_LEVEL_CHANGE(level)
end

function GWSandInternalMgr.OnSandAllianceBossRelocationSuccess(gridPos)
    if gridPos then
        _getSandCameraMgr().OnResetDxfAndJump(gridPos)
    end
end

--endregion

function GWSandInternalMgr.OnClickSceneEntity(_, funcName, tab, compID)
    _requireModule("gw_march_operate_util").OnRemoveOperateHudByClick(compID)
    _requireModule("gw_sand_relocation_entity_util").HideMoveBaseHud()
end

function GWSandInternalMgr.OnInputBegin()
    _requireModule("gw_march_operate_util").OnRemoveOperateHud()
    event.Trigger(sand_ui_event_define.GW_CLICK_EMPTY)
end

function GWSandInternalMgr.OnInputMove()
    _requireModule("gw_sand_relocation_entity_util").HideMoveBaseHud()

    event.Trigger(gw_sand_event_define.GW_SAND_INPUT_MOVE)
end

function GWSandInternalMgr.OnInputEnd()

end

function GWSandInternalMgr.OnClickUpEvent()
    --event.Trigger(sand_ui_event_define.GW_CLICK_EMPTY_STRICT)
end

function GWSandInternalMgr.OnClickTeamBySid(tab)
    if tab.sid and tab.sid > 0 then
        local comp = _requireModule("gw_map_util").GetMarchCompBySid(tab.sid)
        if comp then
            if comp:GetComponent(gw_comp_name.comp_march_item) then
                _requireModule("gw_march_operate_util").OnShowOperateHud(comp.serData, comp:GetComponent(gw_comp_name.comp_march_item))
            else
                OnSandLuaErr("GWSandInternalMgr.OnClickTeamBySid error sid:", gw_comp_name.comp_march_item)
            end
        else
            OnSandLuaErr("GWSandInternalMgr.OnClickTeamBySid error sid:", tab.sid)
        end
    else
        local gw_common_util = require "gw_common_util"
        gw_common_util.JumpToSid(tab.targetSid)
    end
end

function GWSandInternalMgr.OnLanguageChange()
    _requireModule("gw_sand_relocation_entity_util").HideMoveBaseHud()
end

function GWSandInternalMgr.UpdateSandBoxBaseInfo()
    if _getSandData().selfData.IsVisualAndBaseCoServiceState() then
        _requireModule("gw_map_util").OnChangedSandBaseEntity()
        local position = _getSandData().selfData.GetSelfBasePosition()
        if position and position.x > 0 and position.y > 0 then
            _getSandCameraMgr().OnResetToBase()
        end
    end
end

function GWSandInternalMgr.UpdateSandBoxEntityState()
    _requireModule("gw_map_util").OnUpdateSandBaseEntityResPop()
end

--检测是否处于战区对决的国会争夺阶段
function GWSandInternalMgr.CheckWarZoneDuelCongressAtk()
    if _requireModule("war_zone_duel_helper").IsCongressAtk() or _requireModule("war_zone_duel_helper").IsCongressAtkEnd() then
        GWSandInternalMgr.UpdateSandBoxEntityState()
    end
end

function GWSandInternalMgr.UpdateSandRelocationEntityState()
    -- 刷新迁城状态
    local relocationUtil = _requireModule("gw_sand_relocation_entity_util")
    if relocationUtil.GetRelocationEntityState() then
        -- 设置迁城界面坐标
        relocationUtil.SetMoveChoseHudState()
    end
end

return GWSandInternalMgr