local table = table
local require = require
local string = string
local ipairs = ipairs
local math = math
local tonumber = tonumber
local os = require "os"
local hero_first_charge_define = require "hero_first_charge_define"
local player_prefs = require "player_prefs"
local event_task_define = require "event_task_define"
local festival_activity_cfg = require "festival_activity_cfg"
local data_mgr = require "data_mgr"
local festival_activity_mgr = require "festival_activity_mgr"
local game_scheme = require "game_scheme"
local event = require "event"
local doomsday_define = require "doomsday_define"
local gw_common_util = require "gw_common_util"
local red_const = require "red_const"
local red_system = require "red_system"
local gw_event_activity_define = require "gw_event_activity_define"
local player_mgr = require "player_mgr"
local gw_task_mgr = require "gw_task_mgr"
local gw_task_const = require "gw_task_const"
local log = require "log"
local util = require "util"

module("hero_first_charge_data")

---所有数据存储
local _d = data_mgr:CreateData("hero_first_charge_data")
---非服务器数据存储
local mc = _d.mde.const
---@field FirstChargeHeroID number 首充英雄ID 
local FirstChargeHeroID = 104
--region 关卡数据
---@public function 获取普通和无尽关卡ID
function GetLevelCfg()
    if not mc.NormalLevelIDArr or not mc.EndlessLevelIDArr then
        local activityCfg = festival_activity_cfg.GetActivityCfgByActivityID(mc.miniGameActivityID)
        if activityCfg then
            mc.NormalLevelIDArr = {}
            for i = 0, activityCfg.ctnID1.count - 1 do
                table.insert(mc.NormalLevelIDArr, activityCfg.ctnID1.data[i])
            end

            mc.EndlessLevelIDArr = {}
            for i = 0, activityCfg.ctnID2.count - 1 do
                table.insert(mc.EndlessLevelIDArr, activityCfg.ctnID2.data[i])
            end
        end
    end
    return mc.NormalLevelIDArr, mc.EndlessLevelIDArr
end

---@public function 获取当前普通关卡
function GetCurrentNormalLevel()
    GetLevelCfg()
    local gw_independent_game_mgr = require "gw_independent_game_mgr"
    local currentLevelID = 0
    for i, v in ipairs(mc.NormalLevelIDArr) do
        local isChallenge = gw_independent_game_mgr.GetLevelCanChallenge(v) --是否解锁
        if isChallenge then --是否可以挑战
            currentLevelID = v      --当前关
        end
    end
    return currentLevelID
end

---@public function 获取无尽关卡是否解锁
function GetEndlessIsLock()
    local normalIDArr, endlessIDArr = GetLevelCfg()
    if endlessIDArr and endlessIDArr[1] then
        local gw_independent_game_mgr = require "gw_independent_game_mgr"
        local isChallenge = gw_independent_game_mgr.GetLevelCanChallenge(endlessIDArr[1]) --是否解锁
        local isFinish = gw_independent_game_mgr.GetIsFinishByLevelId(endlessIDArr[1])

        if isChallenge or isFinish then
            return true
        end
    end
    return false
end

---@public function 获取无尽关卡的前置关卡ID
function GetEndlessPreLevelID()
    if not mc.endlessPreLevelID or not mc.EndlessLevelNum then
        local normalIDArr, endlessIDArr = GetLevelCfg()
        if endlessIDArr and endlessIDArr[1] then
            local miniGameControlCfg = game_scheme:MiniGameLevelControl_0(endlessIDArr[1], 0)
            if miniGameControlCfg then
                mc.endlessPreLevelID = miniGameControlCfg.iPreLevel
            end
        end
        if normalIDArr and mc.endlessPreLevelID then
            for i, v in ipairs(normalIDArr) do
                if v == mc.endlessPreLevelID then
                    mc.EndlessLevelNum = i
                end
            end
        end
    end
    return mc.endlessPreLevelID,mc.EndlessLevelNum
end

---@public function 获取普通关卡奖励
function GetNormalLevelRewardList()
    if mc.NormalLevelIDArr then
        local rewardIDArr = {}
        for i, v in ipairs(mc.NormalLevelIDArr) do
            local miniGameControlCfg = game_scheme:MiniGameLevelControl_0(v, 0)
            if miniGameControlCfg then
                local tempData = {}
                tempData.name = miniGameControlCfg.szName
                tempData.rewardArr = {}
                tempData.LevelID = miniGameControlCfg.LevelID
                for i = 0, miniGameControlCfg.MonsterRewardId.count - 1 do
                    table.insert(tempData.rewardArr, miniGameControlCfg.MonsterRewardId.data[i])
                end
                for i = 0, miniGameControlCfg.BossRewardId.count - 1 do
                    table.insert(tempData.rewardArr, miniGameControlCfg.BossRewardId.data[i])
                end
                table.insert(rewardIDArr, tempData)
            end
        end
        return rewardIDArr
    end
end

---@public function 初始化id 数组
function InitMiniGameLevelIDArr()
    local normalIDArr, endlessIDArr = GetLevelCfg()
    if not normalIDArr or not endlessIDArr then
        return
    end
    
    if not mc.NormalMiniLevelIDArr or not mc.NormalMiniControlIDArr then
        mc.NormalMiniLevelIDArr = normalIDArr
        mc.NormalMiniControlIDArr = {}
        for i, v in ipairs(normalIDArr) do
            local miniGameControlCfg = game_scheme:MiniGameLevelControl_0(v,0)
            if miniGameControlCfg then
                table.insert(mc.NormalMiniControlIDArr, miniGameControlCfg.ID)
            end
        end
    end

    if not mc.EndlessMiniLevelIDArr or not mc.EndlessMiniControlIDArr then
        mc.EndlessMiniLevelIDArr = endlessIDArr
        mc.EndlessMiniControlIDArr = {}
        for i, v in ipairs(endlessIDArr) do
            local miniGameControlCfg = game_scheme:MiniGameLevelControl_0(v,0)
            if miniGameControlCfg then
                table.insert(mc.EndlessMiniControlIDArr, miniGameControlCfg.ID)
            end
        end
    end
end

function GetNormalLevelIDArr()
    return mc.NormalMiniLevelIDArr, mc.NormalMiniControlIDArr
end

function GetEndlessLevelIDArr()
    return mc.EndlessMiniLevelIDArr, mc.EndlessMiniControlIDArr
end
--endregion

--region 英雄养成数据
function GetHeroCultivateTaskIDArr()
    if not mc.cultivateTaskIDArr then
        local activityCfg = festival_activity_cfg.GetActivityCfgByActivityID(mc.rewardActivityID)
        if activityCfg then
            mc.cultivateTaskIDArr = {}
            for i = 0, activityCfg.ctnID1.count - 1 do
                table.insert(mc.cultivateTaskIDArr, activityCfg.ctnID1.data[i])
            end
        end
    end
    return mc.cultivateTaskIDArr
end

---@public function 获取首充英雄配置ID
function GetFirstChargeHeroID()
    return FirstChargeHeroID
end
--endregion

--region 活动相关方法
---@public function 设置主活动ID
function SetActivityId(activityID)
    mc.activityID = activityID
    event.Trigger(gw_event_activity_define.GW_SCHEDULE_DATA_UPDATE, festival_activity_cfg.ActivityCodeType.HeroFirstRecharge)
end

---@public function 获取主活动ID
function GetActivityID()
    return mc.activityID
end

---@public function 设置免费宝箱活动ID
function SetFreeBoxActivityData(activityID)
    mc.freeBoxActivityID = activityID
end

---@public function 获取免费宝箱活动ID
function GetFreeBoxActivityID()
    return mc.freeBoxActivityID
end

---@public function 设置小游戏活动ID
function SetMiniGameActivityId(activityID)
    mc.miniGameActivityID = activityID
end

---@public function 获取小游戏活动ID
function GetMiniGameActivityID()
    return mc.miniGameActivityID
end

function SetRankActivityId(activityID)
    mc.rankActivityID = activityID
end

---@public function 获取排行榜活动ID
function GetRankActivityID()
    return mc.rankActivityID or 225
end

---@public function 设置成就活动ID
function SetRewardActivityId(activityID)
    mc.rewardActivityID = activityID
end

---@public function 获取活动数据
function GetActivityData()
    if not mc.activityData then
        mc.activityData = festival_activity_mgr.GetActivityDataByActivityID(mc.activityID)
    end
    return mc.activityData
end
---@public function 获取每日领奖礼包ID
function GetEveryDayGiftRechargeID()

    if not mc.rechargeID or mc.rechargeID == 0 then
        local activityCfg = festival_activity_cfg.GetActivityCfgByActivityID(mc.freeBoxActivityID)
        if activityCfg then
            local contentID = activityCfg.ctnID1.data[0] --41201
            local activityContentCfg = game_scheme:ActivityContent_0(contentID)
            if activityContentCfg then
                mc.rechargeID = activityContentCfg.rechargeID.data[0] --901415
            end
        end
    end
    return mc.rechargeID
end
--endregion

---region 跳转界面
function SetJumpEndlessPanelModel()
    mc.isJumpEndlessPanel = true
end
---@public function 获取是否跳转界面
function IsJumpEndlessPanel()
    return mc.isJumpEndlessPanel
end
function ClearJumpEndlessCache()
    mc.isJumpEndlessPanel = false
end

---@public function 获取首充英雄碎片ID
function GetFirstChargeHeroDebrisID()
    if not mc.CompositeID or mc.UniversalShardID then
        local gw_hero_data = require "gw_hero_data"
        local heroEntity = gw_hero_data.GetHeroEntityByCfgId(FirstChargeHeroID)
        if heroEntity then
            local starLv = heroEntity:GetStar()

            local advanceInfo = game_scheme:HeroAdvance_0(FirstChargeHeroID,2,starLv+1)
            if advanceInfo then
                local heroCfg = game_scheme:Hero_0(FirstChargeHeroID)
                if heroCfg then
                    mc.CompositeID = heroCfg.CompositeID
                    mc.UniversalShardID = heroCfg.UniversalShardID
                    mc.needCount = advanceInfo.starcost.data[1]
                end
            else
                return mc.CompositeID, mc.UniversalShardID, mc.needCount,true
            end
        end
    end
    return mc.CompositeID, mc.UniversalShardID, mc.needCount
end

---@public function 判断是否能升星
function IsHeroCanUpgradeStar()
    local CompositeID, UniversalShardID,needCount,isMaxStarLevel = GetFirstChargeHeroDebrisID()
    if not CompositeID or not UniversalShardID or not needCount or isMaxStarLevel then
        return false
    end
    local costHave = player_mgr.GetPlayerOwnNum(CompositeID)
    local haveUniversalCount = player_mgr.GetPlayerOwnNum(UniversalShardID)
    if (costHave + haveUniversalCount) >= needCount then
        return true
    end
    return false
end

function GetRankingRewardByCfg()
    local num = game_scheme:RankingRewards_nums()
    local rankingRewards ={}
    for i = 0, num -1 do
        local cfg = game_scheme:RankingRewards(i)
        if cfg and cfg.rewardtype == 11 then
            if cfg.conditionalparameters[1] then
                local tempData = {
                    id = cfg.RewardID,
                    rankRange1 = cfg.conditionalparameters[1][0],
                    rankRange2 = cfg.conditionalparameters[1][1] and cfg.conditionalparameters[1][1] or cfg.conditionalparameters[1][0],
                    rewardId = cfg.Reward,
                    titleLangId = cfg.Title,
                }
                table.insert(rankingRewards, tempData)
            end
        end
    end
    return rankingRewards
end

---@deprecated 获取当前活动开启的天数 
function GetCurDayNew()
    local activityData = GetActivityData()
    local startTime = 0
    if activityData then
        startTime = activityData.startTimeStamp or 0
    end
    local curDay = math.ceil((os.server_time() - startTime)/86400)
    return curDay or 0
end

---@public function 判断关卡是否解锁
function JudgeLevelIsUnlock(levelId)
    local levelCfg = game_scheme:MiniGameLevelControl_0(levelId, 0)
    if levelCfg then
        local curDay = GetCurDayNew()
        if curDay >= levelCfg.ActivityTime then
            return true
        end
    end
    return false
end

function Clear()
    _d.mde:clear()
    mc = _d.mde.const
end
