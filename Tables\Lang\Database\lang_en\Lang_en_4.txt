return {
[726269]=[[Envoy <PERSON><PERSON>]],
[726270]=[[Envoy <PERSON>]],
[726271]=[[Envoy <PERSON>]],
[726272]=[[Envoy <PERSON>]],
[726273]=[[Envoy <PERSON>]],
[726274]=[[Envoy <PERSON>]],
[726275]=[[Envoy <PERSON>]],
[726276]=[[Envoy <PERSON>]],
[726277]=[[Envoy <PERSON>]],
[726278]=[[Envoy <PERSON>]],
[726279]=[[Envoy <PERSON>]],
[726280]=[[Repairman <PERSON>]],
[726281]=[[Repairman <PERSON>]],
[726282]=[[Repair<PERSON> <PERSON>]],
[726283]=[[Repairman <PERSON>]],
[726284]=[[<PERSON><PERSON><PERSON> <PERSON>]],
[726285]=[[Repairman <PERSON>]],
[726286]=[[Repairman <PERSON>]],
[726287]=[[Repair<PERSON> <PERSON>]],
[726288]=[[Repair<PERSON>]],
[726289]=[[Rep<PERSON><PERSON>]],
[726290]=[[Repairman <PERSON> <PERSON><PERSON>]],
[726291]=[[<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON>]],
[726292]=[[<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON>]],
[726293]=[[<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON>]],
[726294]=[[<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON>]],
[726295]=[[<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON>]],
[726296]=[[<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON>]],
[726297]=[[<PERSON><PERSON><PERSON> <PERSON> <PERSON>hard]],
[726298]=[[Repairman Albert Shard]],
[726299]=[[Repairman Maxwell Shard]],
[726300]=[[Priest Susan Shard]],
[726301]=[[Priest Karen Shard]],
[726302]=[[Priest Dorothy Shard]],
[726303]=[[Priest Sharon Shard]],
[726304]=[[Priest Katherine Shard]],
[726305]=[[Priest Anna Shard]],
[726306]=[[Priest Pam Shard]],
[726307]=[[Priest Kristen Shard]],
[726308]=[[Priest Emily Shard]],
[726309]=[[Priest Sophia Shard]],
[726310]=[[Priest Chloe Shard]],
[726311]=[[Priest Tara Shard]],
[726312]=[[Priest Mackenzie Shard]],
[726313]=[[Priest Susan Shard]],
[726314]=[[Priest Karen Shard]],
[726315]=[[Priest Dorothy Shard]],
[726316]=[[Priest Sharon Shard]],
[726317]=[[Priest Katherine Shard]],
[726318]=[[Priest Anna Shard]],
[726319]=[[Priest Pam Shard]],
[726320]=[[Builder Michael Shard]],
[726321]=[[Builder William Shard]],
[726322]=[[Builder Charles Shard]],
[726323]=[[Builder Mark Shard]],
[726324]=[[Builder Paul Shard]],
[726325]=[[Builder Kevin Shard]],
[726326]=[[Builder Edward Shard]],
[726327]=[[Builder Ronald Shard]],
[726328]=[[Builder Ryan Shard]],
[726329]=[[Builder Nicholas Shard]],
[726330]=[[Builder Brandon Shard]],
[726331]=[[Builder Franklin Shard]],
[726332]=[[Builder Adam Shard]],
[726333]=[[Builder Eric Shard]],
[726334]=[[Builder Jonathan Shard]],
[726335]=[[Builder Jordan Shard]],
[726336]=[[Builder Willy Shard]],
[726337]=[[Builder Albert Shard]],
[726338]=[[Builder Mason Shard]],
[726339]=[[Builder Jack Shard]],
[726340]=[[Hunter James Shard]],
[726341]=[[Hunter John Shard]],
[726342]=[[Hunter William Shard]],
[726343]=[[Hunter Richard Shard]],
[726344]=[[Hunter Joseph Shard]],
[726345]=[[Hunter Christopher Shard]],
[726346]=[[Hunter Drake Shard]],
[726347]=[[Hunter Mark Shard]],
[726348]=[[Hunter Donald Shard]],
[726349]=[[Hunter Paul Shard]],
[726350]=[[Hunter Joshua Shard]],
[726351]=[[Hunter Kevin Shard]],
[726352]=[[Hunter Brian Shard]],
[726353]=[[Hunter Ronald Shard]],
[726354]=[[Hunter Jeffrey Shard]],
[726355]=[[Hunter Nicholas Shard]],
[726356]=[[Hunter Eric Shard]],
[726357]=[[Hunter Steven Shard]],
[726358]=[[Hunter Brandon Shard]],
[726359]=[[Hunter Franklin Shard]],
[726360]=[[Nurse Mary Shard]],
[726361]=[[Nurse Susan Shard]],
[726362]=[[Nurse Karen Shard]],
[726363]=[[Nurse Margaret Shard]],
[726364]=[[Nurse Dorothy Shard]],
[726365]=[[Nurse Emily Shard]],
[726366]=[[Nurse Carol Shard]],
[726367]=[[Nurse Sharon Shard]],
[726368]=[[Nurse Katherine Shard]],
[726369]=[[Nurse Helen Shard]],
[726370]=[[Nurse Anna Shard]],
[726371]=[[Nurse Brenda Shard]],
[726372]=[[Nurse Pam Shard]],
[726373]=[[Nurse Kristen Shard]],
[726374]=[[Nurse Emily Shard]],
[726375]=[[Nurse Sophia Shard]],
[726376]=[[Nurse Chloe Shard]],
[726377]=[[Nurse Kelly Shard]],
[726378]=[[Nurse Caitlin Shard]],
[726379]=[[Nurse Faith Shard]],
[726380]=[[Nurse Lillian Shard]],
[726381]=[[Nurse Tara Shard]],
[726382]=[[Nurse Senna Shard]],
[726383]=[[Nurse Delilah Shard]],
[726384]=[[Nurse Rhea Shard]],
[726385]=[[Nurse Mackenzie Shard]],
[726386]=[[Nurse Camilla Shard]],
[726387]=[[Nurse Destiny Shard]],
[726388]=[[Nurse Mary Shard]],
[726389]=[[Nurse Susan Shard]],
[726390]=[[Nurse Karen Shard]],
[726391]=[[Nurse Margaret Shard]],
[727003]=[[Elite Supply Crate]],
[727004]=[[Gift Chest]],
[727005]=[[Random Treasure Map Shard]],
[727006]=[[Treasure Map Shard No.1]],
[727007]=[[Treasure Map Shard No.2]],
[727008]=[[Treasure Map Shard No.3]],
[727009]=[[Treasure Map Shard No.4]],
[727010]=[[Treasure Map Shard No.5]],
[727011]=[[Treasure Map Shard No.6]],
[727012]=[[Treasure Map Shard No.7]],
[727013]=[[Lucky Supply Crate]],
[727014]=[[Quest Refresh Scroll]],
[727015]=[[Cargo Refresh Voucher]],
[727016]=[[Altar Construction Materials]],
[727017]=[[Rare Expedition Chest]],
[727018]=[[Deluxe Expedition Chest]],
[727019]=[[Common Trial Chest]],
[727020]=[[Excellent Trial Chest]],
[727021]=[[Epic Trial Chest]],
[727022]=[[Legendary Trial Chest]],
[727023]=[[Race Points]],
[727024]=[[Arms Badge]],
[727026]=[[Intelligence Event]],
[727028]=[[Common Treasure]],
[727029]=[[rare treasure]],
[727030]=[[legendary treasure]],
[727039]=[[Town Chest]],
[727041]=[[Princess's Gift Chest]],
[727056]=[[Zombie stronghold clues]],
[727064]=[[Lord Key]],
[728001]=[[Enhancement Stone Lucky Chest]],
[728002]=[[Mythic Selection Chest]],
[728003]=[[Gear Boost Selection Chest]],
[728004]=[[Chest Key]],
[728005]=[[Pioneer Points]],
[728006]=[[Warrior Points]],
[728007]=[[Common S+ Hero Selection Card]],
[728008]=[[Upgrade Points]],
[728009]=[[Battle Supply Voucher]],
[728011]=[[S+ Hero Supplementary Selection Card]],
[728012]=[[Lucky Coin]],
[728013]=[[Deluxe S+ Hero Selection Card]],
[728014]=[[Pack Points]],
[728015]=[[Blue Badge Chest]],
[728016]=[[Purple Badge Chest]],
[728017]=[[Gold Badge Chest]],
[728018]=[[Miracle Coin]],
[728019]=[[Advanced S+ Hero Selection Card]],
[728020]=[[4th Team]],
[728021]=[[Team Gathering Speed Up]],
[728022]=[[Team DEF Boost]],
[728023]=[[Golden Airship Order]],
[728024]=[[Intelligence Points]],
[728025]=[[Shiny Badge]],
[728026]=[[Miracle Key]],
[728028]=[[Common Miracle Chest]],
[728029]=[[Fine Miracle Chest]],
[728030]=[[Advanced Miracle Chest]],
[728031]=[[Excellent Miracle Chest]],
[728032]=[[Legendary Miracle Chest]],
[728036]=[[Divine Beast Points]],
[728038]=[[Hammer of Renaissance]],
[728048]=[[Pet Legend Chest]],
[728049]=[[Pet Rare Chest]],
[728050]=[[Pet ordinary treasure chest]],
[728039]=[[Pumpkin Coins]],
[728040]=[[Mystery Candy]],
[728041]=[[Sweet Pumpkin]],
[728042]=[[Banquet Points]],
[728043]=[[Pumpkin Points]],
[728044]=[[Lucky Pumpkin Treasure Chest]],
[728045]=[[Little Pumpkin Treasure Chest]],
[728046]=[[Ghost Gift]],
[728047]=[[Banquet Treasure Chest]],
[728053]=[[Ghost's Keepsake]],
[728058]=[[Revival Points]],
[728101]=[[Accumulated Tokens-Newcomers]],
[728102]=[[Accumulated Tokens-New Server]],
[728103]=[[Accumulated Tokens-Cycle-Seven Days Accumulated Recharge]],
[728104]=[[Accumulated Tokens-Cycle-Daily Accumulated Recharge]],
[728105]=[[Accumulated Tokens-Festival]],
[728201]=[[Specials Key]],
[729000]=[[Default Castle]],
[729001]=[[Female Default Castle]],
[729002]=[[Carrot Cottage]],
[729003]=[[Fantasy Wings]],
[729004]=[[Default Castle]],
[729005]=[[Default Castle]],
[729006]=[[Default Castle]],
[729007]=[[Default Castle]],
[729008]=[[Default Castle]],
[729009]=[[Default Castle]],
[729010]=[[Default Castle]],
[729011]=[[Default Castle]],
[729012]=[[Default Castle]],
[729013]=[[Victorious Dragon (15 Days)]],
[729014]=[[Victorious Dragon (7 Days)]],
[729015]=[[Victorious Dragon (3 Days)]],
[729016]=[[Winter Wonder]],
[729017]=[[Mysterious Pumpkin]],
[729018]=[[Wings of Speed ​​(30 days)]],
[729019]=[[Wings of Speed ​​(15 days)]],
[729020]=[[Wings of Speed ​​(7 days)]],
[729021]=[[Wings of Speed ​​(3 days)]],
[729101]=[[Default Avatar Frame]],
[729102]=[[Monthly Card Avatar Frame]],
[729103]=[[Marshal Avatar Frame]],
[729104]=[[General Avatar Frame]],
[729105]=[[Captain Avatar Frame]],
[729106]=[[Commandant Avatar Frame]],
[729107]=[[Centurion Avatar Frame]],
[729108]=[[King's Lament Avatar Frame]],
[729109]=[[Conqueror Avatar Frame]],
[729110]=[[King's Legion Avatar Frame]],
[729111]=[[Honorable Lord]],
[729112]=[[Elite Lord]],
[729201]=[[Default Avatar 1]],
[729202]=[[Default Avatar 2]],
[729203]=[[Default Avatar 3]],
[729204]=[[Default Avatar 4]],
[729205]=[[Default Avatar 5]],
[729206]=[[Default Avatar 6]],
[729207]=[[Default Avatar 7]],
[729208]=[[Default Avatar 8]],
[729209]=[[Human Faction Avatar]],
[729210]=[[Forest Faction Avatar]],
[729211]=[[Nightfall Faction Avatar]],
[729301]=[[Borias (Permanent)]],
[729311]=[[Bahamut (Permanent)]],
[729400]=[[No visual effects]],
[729401]=[[Celebration Gift Box]],
[729402]=[[I Am Legend]],
[729403]=[[I Am Legend (30 Days)]],
[750001]=[[A common currency used across the world, essential for building your castle and making Union Donations.]],
[750002]=[[A rare currency that can be used to purchase various rare items and has many other uses.]],
[750003]=[[Required to upgrade heroes. Can be used to upgrade heroes and enhance their stats from the Hero Details page.]],
[750005]=[[Donate to your Union's Technology progress to gain Union Contribution, which can be exchanged for valuable items in the Union Store.]],
[750006]=[[Restores 10 of your Stamina.]],
[750007]=[[Restores 50 of your Stamina.]],
[750008]=[[Use to upgrade your VIP Level. Earn a large amount of heroes and scrolls, and unlock more perks.]],
[750009]=[[Use to research advanced Technology.]],
[750010]=[[Grants 1,000 Energy Crystals, which can be used to research advanced Technology.]],
[750011]=[[A symbol of the Guardians who stand against evil. Use it in the Redeem Shop of the Undead Treasure event to redeem great rewards.]],
[750012]=[[Earned by completing Faction Trial challenges. Use it in the Exchange Store to exchange for great rewards.]],
[750013]=[[Earn substantial War Merits during battlefield events. Use them in the Exchange Store to exchange for great rewards.]],
[750014]=[[Grants 10,000 War Merits, which can be used in the Exchange Store.]],
[750025]=[[Used to redeem packs.]],
[750026]=[[Complete Activity Quests for Activity Points. Reach Activity thresholds to exchange for great rewards.]],
[750034]=[[An unremarkable small key. Collecting a certain number may help rescue Verna.]],
[750035]=[[A common food resource essential for castle construction.]],
[750036]=[[A practical resource with multiple uses, essential for castle construction.]],
[750037]=[[Universal Stamina used for exploration and battles.]],
[750040]=[[Glory Medal]],
[750041]=[[Used to challenge opponents in the Novice Arena.]],
[750101]=[[Used for Advanced Recruitment, with a chance to obtain a Legendary Hero!]],
[750102]=[[Used to recruit Survivors, with a chance to obtain a Legendary Survivor!]],
[750103]=[[May contain the following rewards:]],
[750104]=[[Use to modify your player name.]],
[750105]=[[Accumulate EXP through daily Must-Buy events to claim great rewards once conditions are met.]],
[750106]=[[After obtaining it, you will permanently unlock the privilege of customizing your avatar, and you can upload your favorite pictures as your game avatar.]],
[750107]=[[The Mystic Beast "Borias" is surrounded by a zombie legion. <color=#319f38>Rescue</color> it to make it a loyal <color=#319f38>companion</color>.\nIt will significantly boost your <color=#319f38>hero's CP</color> and automatically cast its <color=#319f38>exclusive skill</color> in battle to secure victory.]],
[750108]=[[Unlock the Trade Wagon to access <color=#319f38>Trade</color>, where you can earn massive <color=#319f38>resources and rare items, including Diamonds, Recruit Vouchers, and S+ Hero Shards.</color>/n(Note: The Trade feature will only be available starting from Day 4 after the server launch.)]],
[750311]=[[Increases your Hero's ATK and HP!]],
[750312]=[[Increases your Hero's ATK and HP!]],
[750313]=[[Increases your Hero's ATK and HP!]],
[750314]=[[Increases your Hero's ATK and HP!]],
[750315]=[[Increases your Hero's ATK and HP!]],
[750316]=[[Increases your Hero's ATK and HP!]],
[750317]=[[Increases your Hero's ATK and HP!]],
[750318]=[[Increases your Hero's ATK and HP!]],
[750319]=[[Increases your Hero's ATK and HP!]],
[750320]=[[Increases your Hero's ATK and HP!]],
[750500]=[[Allows you to relocate anywhere on the world map.]],
[750501]=[[Automatically relocates to a rally point near your Union.]],
[750502]=[[Randomly relocates anywhere on the world map.]],
[750521]=[[Grants your castle 8 hours of peace, protecting it from attacks and scouting.\n<color=#E63838>Note: Troops outside the castle may still be vulnerable to attacks.</color>]],
[750522]=[[Grants your castle 12 hours of peace, protecting it from attacks and scouting.\n<color=#E63838>Note: Troops outside the castle may still be vulnerable to attacks.</color>]],
[750523]=[[Grants your castle 24 hours of peace, protecting it from attacks and scouting.\n<color=#E63838>Note: Troops outside the castle may still be vulnerable to attacks.</color>]],
[750524]=[[Grants your castle 3 days of peace, protecting it from attacks and scouting.\n<color=#E63838>Note: Troops outside the castle may still be vulnerable to attacks.</color>]],
[750541]=[[Reduces the current march time of your team by 50%.]],
[750542]=[[Reduces the current march time of your team by 25%.]],
[750601]=[[Grants 5 Diamonds.]],
[750602]=[[Grants 10 Diamonds.]],
[750603]=[[Grants 20 Diamonds.]],
[750604]=[[Grants 50 Diamonds.]],
[750605]=[[Grants 100 Diamonds.]],
[750606]=[[Grants 5,000 Diamonds.]],
[750607]=[[Grants 10,000 Diamonds.]],
[750608]=[[Grants 500 Diamonds.]],
[750650]=[[Grants 10 VIP EXP.]],
[750651]=[[Grants 50 VIP EXP.]],
[750652]=[[Grants 100 VIP EXP.]],
[750653]=[[Grants 500 VIP EXP.]],
[750654]=[[Grants 1,000 VIP EXP.]],
[750655]=[[Grants 2,500 VIP EXP.]],
[750671]=[[Extends your VIP status by 24 hours.]],
[750672]=[[Extends your VIP status by 7 days.]],
[750673]=[[Extends your VIP status by 30 days.]],
[750701]=[[Grants 1,000 Wheat.]],
[750702]=[[Grants 1,000 Iron.]],
[750703]=[[Grants 600 Gold.]],
[750704]=[[Grants 5,000 Wheat.]],
[750705]=[[Grants 5,000 Iron.]],
[750706]=[[Grants 3,000 Gold.]],
[750707]=[[Grants 10,000 Wheat.]],
[750708]=[[Grants 10,000 Iron.]],
[750709]=[[Grants 6,000 Gold.]],
[750710]=[[Grants 30,000 Wheat.]],
[750711]=[[Grants 30,000 Iron.]],
[750712]=[[Grants 18,000 Gold.]],
[750713]=[[Grants 50,000 Wheat.]],
[750714]=[[Grants 50,000 Iron.]],
[750715]=[[Grants 30,000 Gold.]],
[750716]=[[Grants 500,000 Wheat.]],
[750717]=[[Grants 500,000 Iron.]],
[750718]=[[Grants 300,000 Gold.]],
[750719]=[[Grants 1,000,000 Wheat.]],
[750720]=[[Grants 1,000,000 Iron.]],
[750721]=[[Grants 1,000,000 Gold.]],
[750751]=[[Use to select 1 of the following Excellent items: Wheat Level Chest, Iron Level Chest, or Gold Level Chest.]],
[750752]=[[Use to select 1 of the following Epic items: Wheat Level Chest, Iron Level Chest, or Gold Level Chest.]],
[750753]=[[Use to select 1 of the following Legendary items: Wheat Level Chest, Iron Level Chest, or Gold Level Chest.]],
[750754]=[[Use to select 1 of the following: 10,000 Wheat, 10,000 Iron, or 6,000 Gold.]],
[750761]=[[Grants {%s1} Wheat at the current Home Level.]],
[750762]=[[Grants {%s1} Wheat at the current Home Level.]],
[750763]=[[Grants {%s1} Wheat at the current Home Level.]],
[750764]=[[Grants {%s1} Wheat at the current Home Level.]],
[750765]=[[Grants {%s1} Iron at the current Home Level.]],
[750766]=[[Grants {%s1} Iron at the current Home Level.]],
[750767]=[[Grants {%s1} Iron at the current Home Level.]],
[750768]=[[Grants {%s1} Iron at the current Home Level.]],
[750769]=[[Grants {%s1} Gold at the current Home Level.]],
[750770]=[[Grants {%s1} Gold at the current Home Level.]],
[750771]=[[Grants {%s1} Gold at the current Home Level.]],
[750772]=[[Grants {%s1} Gold at the current Home Level.]],
[750801]=[[Reduces the corresponding time in the timer.]],
[750802]=[[Reduces the corresponding time in the timer.]],
[750803]=[[Reduces the corresponding time in the timer.]],
[750804]=[[Reduces the corresponding time in the timer.]],
[750805]=[[Reduces the corresponding time in the timer.]],
[750806]=[[Reduces the corresponding time in the timer.]],
[750811]=[[Reduces the corresponding time in the building timer.]],
[750812]=[[Reduces the corresponding time in the building timer.]],
[750813]=[[Reduces the corresponding time in the building timer.]],
[750814]=[[Reduces the corresponding time in the building timer.]],
[750815]=[[Reduces the corresponding time in the building timer.]],
[750816]=[[Reduces the corresponding time in the building timer.]],
[750821]=[[Reduces the corresponding time in the research timer.]],
[750822]=[[Reduces the corresponding time in the research timer.]],
[750823]=[[Reduces the corresponding time in the research timer.]],
[750824]=[[Reduces the corresponding time in the research timer.]],
[750825]=[[Reduces the corresponding time in the research timer.]],
[750826]=[[Reduces the corresponding time in the research timer.]],
[750831]=[[Reduces the corresponding time in the training timer.]],
[750832]=[[Reduces the corresponding time in the training timer.]],
[750833]=[[Reduces the corresponding time in the training timer.]],
[750834]=[[Reduces the corresponding time in the training timer.]],
[750835]=[[Reduces the corresponding time in the training timer.]],
[750841]=[[Reduces the corresponding time in the healing timer.]],
[750842]=[[Reduces the corresponding time in the healing timer.]],
[750843]=[[Reduces the corresponding time in the healing timer.]],
[750844]=[[Reduces the corresponding time in the healing timer.]],
[750845]=[[Reduces the corresponding time in the healing timer.]],
[750850]=[[Randomly grants 1 of the following items: 5-Minute Speed Up, 5-Minute Building Speed Up, 5-Minute Technology Speed Up, 5-Minute Training Speed Up, 5-Minute Healing Speed Up.]],
[750900]=[[Common Union Gift]],
[750901]=[[Lv. 1 Union Gift]],
[750902]=[[Lv. 2 Union Gift]],
[750903]=[[Lv. 3 Union Gift]],
[750904]=[[Lv. 4 Union Gift]],
[750905]=[[Lv. 5 Union Gift]],
[750906]=[[Lv. 6 Union Gift]],
[750907]=[[Lv. 7 Union Gift]],
[750908]=[[Lv. 8 Union Gift]],
[750909]=[[Lv. 9 Union Gift]],
[750910]=[[Lv. 10 Union Gift]],
[750911]=[[Lv. 11 Union Gift]],
[750912]=[[Lv. 12 Union Gift]],
[750913]=[[Lv. 13 Union Gift]],
[750914]=[[Lv. 14 Union Gift]],
[750915]=[[Lv. 15 Union Gift]],
[750916]=[[Lv. 16 Union Gift]],
[750917]=[[Lv. 17 Union Gift]],
[750918]=[[Lv. 18 Union Gift]],
[750919]=[[Lv. 19 Union Gift]],
[750920]=[[Lv. 20 Union Gift]],
[750921]=[[Lv. 21 Union Gift]],
[750922]=[[Lv. 22 Union Gift]],
[750923]=[[Lv. 23 Union Gift]],
[750924]=[[Lv. 24 Union Gift]],
[750925]=[[Lv. 25 Union Gift]],
[750926]=[[Lv. 26 Union Gift]],
[750927]=[[Lv. 27 Union Gift]],
[750928]=[[Lv. 28 Union Gift]],
[750929]=[[Lv. 29 Union Gift]],
[750930]=[[Lv. 30 Union Gift]],
[750950]=[[Ogre Chest]],
[750951]=[[Lv. 1 Ogre Chest]],
[750952]=[[Lv. 2 Ogre Chest]],
[750953]=[[Lv. 3 Ogre Chest]],
[750954]=[[Lv. 4 Ogre Chest]],
[750955]=[[Lv. 5 Ogre Chest]],
[750956]=[[Lv. 6 Ogre Chest]],
[750957]=[[Lv. 7 Ogre Chest]],
[750958]=[[Lv. 8 Ogre Chest]],
[750959]=[[Lv. 9 Ogre Chest]],
[750960]=[[Lv. 10 Ogre Chest]],
[750961]=[[Lv. 11 Ogre Chest]],
[750962]=[[Lv. 12 Ogre Chest]],
[750963]=[[Lv. 13 Ogre Chest]],
[750964]=[[Lv. 14 Ogre Chest]],
[750965]=[[Lv. 15 Ogre Chest]],
[750966]=[[Lv. 16 Ogre Chest]],
[750967]=[[Lv. 17 Ogre Chest]],
[750968]=[[Lv. 18 Ogre Chest]],
[750969]=[[Lv. 19 Ogre Chest]],
[750970]=[[Lv. 20 Ogre Chest]],
[750971]=[[Lv. 21 Ogre Chest]],
[750972]=[[Lv. 22 Ogre Chest]],
[750973]=[[Lv. 23 Ogre Chest]],
[750974]=[[Lv. 24 Ogre Chest]],
[750975]=[[Lv. 25 Ogre Chest]],
[750976]=[[Lv. 26 Ogre Chest]],
[750977]=[[Lv. 27 Ogre Chest]],
[750978]=[[Lv. 28 Ogre Chest]],
[750979]=[[Lv. 29 Ogre Chest]],
[750980]=[[Lv. 30 Ogre Chest]],
[751001]=[[Upon purchasing this pack, all Union members will receive an Advanced Union Gift (Note: If you are not in a Union, this chest cannot be obtained).]],
[751002]=[[Upon purchasing this pack, all Union members will receive an Advanced Union Gift (Note: If you are not in a Union, this chest cannot be obtained).]],
[751003]=[[Upon purchasing this pack, all Union members will receive an Advanced Union Gift (Note: If you are not in a Union, this chest cannot be obtained).]],
[751004]=[[Upon purchasing this pack, all Union members will receive an Advanced Union Gift (Note: If you are not in a Union, this chest cannot be obtained).]],
[751005]=[[Upon purchasing this pack, all Union members will receive an Advanced Union Gift (Note: If you are not in a Union, this chest cannot be obtained).]],
[751006]=[[Upon purchasing this pack, all Union members will receive an Advanced Union Gift (Note: If you are not in a Union, this chest cannot be obtained).]],
[751051]=[[Grants 50 Union Contribution.]],
[751052]=[[Grants 500 Union Contribution.]],
[751053]=[[Grants 2,500 Union Contribution.]],
[751100]=[[Princess's Gift of Gratitude]],
[751101]=[[Lv. 1 Princess's Gift of Gratitude]],
[751102]=[[Lv.2 Princess's Gift of Gratitude]],
[751103]=[[Lv.3 Princess's Gift of Gratitude]],
[751104]=[[Lv.4 Princess's Gift of Gratitude]],
[751105]=[[Lv.5 Princess's Gift of Gratitude]],
[751106]=[[Lv.6 Princess's Gift of Gratitude]],
[751107]=[[Lv.7 Princess's Gift of Gratitude]],
[751108]=[[Lv.8 Princess's Gift of Gratitude]],
[751109]=[[Lv.9 Princess's Gift of Gratitude]],
[751110]=[[Lv.10 Princess's Gift of Gratitude]],
[751111]=[[Lv.11 Princess's Gift of Gratitude]],
[751112]=[[Lv.12 Princess's Gift of Gratitude]],
[751113]=[[Lv.13 Princess's Gift of Gratitude]],
[751114]=[[Lv.14 Princess's Gift of Gratitude]],
[751115]=[[Lv.15 Princess's Gift of Gratitude]],
[751116]=[[Lv.16 Princess's Gift of Gratitude]],
[751117]=[[Lv.17 Princess's Gift of Gratitude]],
[751118]=[[Lv.18 Princess's Gift of Gratitude]],
[751119]=[[Lv.19 Princess's Gift of Gratitude]],
[751120]=[[Lv.20 Princess's Gift of Gratitude]],
[751121]=[[Lv.21 Princess's Gift of Gratitude]],
[751122]=[[Lv.22 Princess's Gift of Gratitude]],
[751123]=[[Lv.23 Princess's Gift of Gratitude]],
[751124]=[[Lv.24 Princess's Gift of Gratitude]],
[751125]=[[Lv.25 Princess's Gift of Gratitude]],
[751126]=[[Lv.26 Princess's Gift of Gratitude]],
[751127]=[[Lv.27 Princess's Gift of Gratitude]],
[751128]=[[Lv.28 Princess's Gift of Gratitude]],
[751129]=[[Lv.29 Princess's Gift of Gratitude]],
[751130]=[[Lv.30 Princess's Gift of Gratitude]],
[752003]=[[Grants {%s1} Hero EXP at the current Home Level.]],
[752004]=[[Grants {%s1} Hero EXP at the current Home Level.]],
[752005]=[[Grants {%s1} Hero EXP at the current Home Level.]],
[752100]=[[Used to upgrade hero skills.]],
[752101]=[[Universal material for A hero ascension.]],
[752102]=[[Universal material for S hero ascension.]],
[752103]=[[Universal material for S+ hero ascension.]],
[752201]=[[Collect 10 shards to obtain this hero. They can also be used to upgrade the hero's star.]],
[752202]=[[Collect 10 shards to obtain this hero. They can also be used to upgrade the hero's star.]],
[752203]=[[Collect 10 shards to obtain this hero. They can also be used to upgrade the hero's star.]],
[752204]=[[Collect 10 shards to obtain this hero. They can also be used to upgrade the hero's star.]],
[752205]=[[Collect 10 shards to obtain this hero. They can also be used to upgrade the hero's star.]],
[752206]=[[Collect 10 shards to obtain this hero. They can also be used to upgrade the hero's star.]],
[752207]=[[Collect 10 shards to obtain this hero. They can also be used to upgrade the hero's star.]],
[752208]=[[Collect 10 shards to obtain this hero. They can also be used to upgrade the hero's star.]],
[752209]=[[Collect 10 shards to obtain this hero. They can also be used to upgrade the hero's star.]],
[752210]=[[Collect 10 shards to obtain this hero. They can also be used to upgrade the hero's star.]],
[752211]=[[Collect 10 shards and rescue the hero to obtain them. Can also be used to star up the hero.]],
[752212]=[[Collect 10 shards to obtain this hero. They can also be used to upgrade the hero's star.]],
[752213]=[[Collect 10 shards to obtain this hero. They can also be used to upgrade the hero's star.]],
[752214]=[[Collect 10 shards to obtain this hero. They can also be used to upgrade the hero's star.]],
[752215]=[[Collect 10 shards to obtain this hero. They can also be used to upgrade the hero's star.]],
[752216]=[[Collect 10 shards to obtain this hero. They can also be used to upgrade the hero's star.]],
[752217]=[[Collect 10 shards to obtain this hero. They can also be used to upgrade the hero's star.]],
[752218]=[[Collect 10 shards to obtain this hero. They can also be used to upgrade the hero's star.]],
[752219]=[[Collect 10 shards to obtain this hero. They can also be used to upgrade the hero's star.]],
[752220]=[[Collect 10 shards to obtain this hero. They can also be used to upgrade the hero's star.]],
[752221]=[[Collect 10 shards to obtain this hero. They can also be used to upgrade the hero's star.]],
[752222]=[[Collect 10 shards to obtain this hero. They can also be used to upgrade the hero's star.]],
[752223]=[[Collect 10 shards to obtain this hero. They can also be used to upgrade the hero's star.]],
[752224]=[[Collect 10 shards to obtain this hero. They can also be used to upgrade the hero's star.]],
[752225]=[[Collect 10 shards to obtain this hero. They can also be used to upgrade the hero's star.]],
[752226]=[[Collect 10 shards to obtain this hero. They can also be used to upgrade the hero's star.]],
[752227]=[[Collect 10 shards to obtain this hero. They can also be used to upgrade the hero's star.]],
[752228]=[[Collect 10 shards to obtain this hero. They can also be used to upgrade the hero's star.]],
[752229]=[[Collect 10 shards to obtain this hero. They can also be used to upgrade the hero's star.]],
[752230]=[[Collect 10 shards to obtain this hero. They can also be used to upgrade the hero's star.]],
[752231]=[[Collect 10 shards to obtain this hero. They can also be used to upgrade the hero's star.]],
[753001]=[[Forged by the Eleven Heroes of Kelesik after the Cataclysm, this ultimate weapon was sent far into the past—where it ultimately triggered the Annihilation.]],
[753002]=[[As a transcendent entity beyond causality, the Veil does not possess a fixed physical form. It could manifest as a sword... or a suit of armor.]],
[753003]=[[Unveiling fate is not just a poetic metaphor—it has always been a literal truth.]],
[753004]=[[Centuries ago, astrologers called predestined fate causality. But now, causality must be defied—for we must forge a future where life prevails.]],
[753005]=[[Forged during the Second Mage War, this blade severed the Mad Sage and his Cursed Fire in the final battle at Evernight Vale.]],
[753006]=[[A coronation gift crafted by the Nightbornes for Uriel—only to be stolen by an unknown court jester after Uriel drank the blood of the Old Ones.]],
[753007]=[[Once a blade bearing the name of Discipline, it was reduced to scrap metal by the Cursed Fire during the First Mage War. Later, it was reforged in Evernight Vale under the name of Judgment.]],
[753008]=[[A relic forged by Uriel the Arbiter, granting its wearer the strength to traverse vast distances. The reinforcements it brought turned the tide, driving Uriel's minions out of Evernight Vale.]],
[753009]=[[The shimmering azure blade is an eternal nightmare that haunts its foes.]],
[753010]=[[A blue reaper on the battlefield, harvesting lives without mercy.]],
[753011]=[[Forged from cold blue steel, it grants its wearer an equally cold and calculated mind.]],
[753012]=[[Tirelessly moving from one place to another, never knowing rest.]],
[753013]=[[A razor-sharp dagger that slices through flesh with ease.]],
[753014]=[[A simple battle armor—offering just as simple protection.]],
[753015]=[[A sturdy helmet, doing its best to keep its wearer alive.]],
[753016]=[[Lightweight greaves—perfect for delivering a well-placed kick to someone's rear.]],
[753201]=[[4 Common Gear Crystals can be refined into a Rare Gear Crystal.]],
[753202]=[[4 Rare Gear Crystals can be refined into an Excellent Gear Crystal.]],
[753203]=[[Used to forge Excellent Gear. 4 Excellent Gear Crystals can be refined into an Epic Gear Crystal.]],
[753204]=[[Used to forge Legendary Gear. 4 Epic Gear Crystals can be refined into a Legendary Gear Crystal.]],
[753205]=[[Used to forge Legendary Gear.]],
[753250]=[[Equip Gear to increase Hero Stats.]],
[753252]=[[Grants a piece of Rare Gear of a random part.]],
[753253]=[[Grants a piece of Excellent Gear of a random part.]],
[753254]=[[Grants a piece of Epic Gear of a random part.]],
[753256]=[[A mysterious chest that could contain Legendary Gear!]],
[753301]=[[Use Upgrade Crystals to upgrade Gear.]],
[753302]=[[A key material for Legendary Gear promotion ranging from 1-Star to 4-Star.]],
[753303]=[[A key material for upgrading 4-Star Legendary Gear to 5-Star Mythic Gear.]],
[754000]=[[Increases Mystic Beast's EXP.]],
[754001]=[[Grants 10,000 Mystic Beast EXP.]],
[754002]=[[A special potion used to break through key level caps of a Mystic Beast.]],
[754003]=[[Grants 1,000 Mystic Beast EXP.]],
[754051]=[[Grants a Lv. 1 Mystic Beast Mark of a random part.]],
[754052]=[[Grants a Lv. 2 Mystic Beast Mark of a random part.]],
[754053]=[[Grants a Lv. 3 Mystic Beast Mark of a random part.]],
[754054]=[[Grants a Lv. 4 Mystic Beast Mark of a random part.]],
[754055]=[[Grants a Lv. 5 Mystic Beast Mark of a random part.]],
[754056]=[[Grants a Lv. 6 Mystic Beast Mark of a random part.]],
[754057]=[[Grants a Lv. 7 Mystic Beast Mark of a random part.]],
[754058]=[[Grants a Lv. 8 Mystic Beast Mark of a random part.]],
[754059]=[[Grants a Lv. 9 Mystic Beast Mark of a random part.]],
[754060]=[[Grants a Lv. 10 Mystic Beast Mark of a random part.]],
[754061]=[[Grants a Lv. 1 Mystic Beast Mark of a specific part.]],
[754062]=[[Grants a Lv. 2 Mystic Beast Mark of a specific part.]],
[754063]=[[Grants a Lv. 3 Mystic Beast Mark of a specific part.]],
[754064]=[[Grants a Lv. 4 Mystic Beast Mark of a specific part.]],
[754065]=[[Grants a Lv. 5 Mystic Beast Mark of a specific part.]],
[754066]=[[Grants a Lv. 6 Mystic Beast Mark of a specific part.]],
[754067]=[[Grants a Lv. 7 Mystic Beast Mark of a specific part.]],
[754068]=[[Grants a Lv. 8 Mystic Beast Mark of a specific part.]],
[754069]=[[Grants a Lv. 9 Mystic Beast Mark of a specific part.]],
[754070]=[[Grants a Lv. 10 Mystic Beast Mark of a specific part.]],
[754101]=[[A tribute to the brilliance of life.\n<color=#06CF00>Mystic Beast's HP +1350</color>]],
[754102]=[[A tribute to the brilliance of life.\n<color=#06CF00>Mystic Beast's HP +2025</color>]],
[754103]=[[A tribute to the brilliance of life.\n<color=#06CF00>Mystic Beast's HP +4556</color>]],
[754104]=[[A tribute to the brilliance of life.\n<color=#06CF00>Mystic Beast's HP +9700</color>]],
[754105]=[[A tribute to the brilliance of life.\n<color=#06CF00>Mystic Beast's HP +28219</color>]],
[754106]=[[A tribute to the brilliance of life.\n<color=#06CF00>Mystic Beast's HP +80025</color>]],
[754107]=[[A tribute to the brilliance of life.\n<color=#06CF00>Mystic Beast's HP +202939</color>]],
[754108]=[[A tribute to the brilliance of life.\n<color=#06CF00>Mystic Beast's HP +320350</color>\n<color=#06CF00>Mystic Beast's CRIT RES +1%</color>]],
[754109]=[[A tribute to the brilliance of life.\n<color=#06CF00>Mystic Beast's HP +504943</color>\n<color=#06CF00>Mystic Beast's CRIT RES +2%</color>]],
[754110]=[[A tribute to the brilliance of life.\n<color=#06CF00>Mystic Beast's HP +702792</color>\n<color=#06CF00>Mystic Beast's CRIT RES +3%</color>]],
[754121]=[[Endless radiance envelops this body, untouchable and unwavering, banishing all evil.\n<color=#06CF00>Mystic Beast's DEF +13</color>]],
[754122]=[[Endless radiance envelops this body, untouchable and unwavering, banishing all evil.\n<color=#06CF00>Mystic Beast's DEF +19</color>]],
[754123]=[[Endless radiance envelops this body, untouchable and unwavering, banishing all evil.\n<color=#06CF00>Mystic Beast's DEF +43</color>]],
[754124]=[[Endless radiance envelops this body, untouchable and unwavering, banishing all evil.\n<color=#06CF00>Mystic Beast's DEF +92</color>]],
[754125]=[[Endless radiance envelops this body, untouchable and unwavering, banishing all evil.\n<color=#06CF00>Mystic Beast's DEF +269</color>]],
[754126]=[[Endless radiance envelops this body, untouchable and unwavering, banishing all evil.\n<color=#06CF00>Mystic Beast's DEF +762</color>]],
[754127]=[[Endless radiance envelops this body, untouchable and unwavering, banishing all evil.\n<color=#06CF00>Mystic Beast's DEF +1933</color>]],
[754128]=[[Endless radiance envelops this body, untouchable and unwavering, banishing all evil.\n<color=#06CF00>Mystic Beast's DEF +3051</color>\n<color=#06CF00>Mystic Beast's CRIT +1%</color>]],
[754129]=[[Endless radiance envelops this body, untouchable and unwavering, banishing all evil.\n<color=#06CF00>Mystic Beast's DEF +4809</color>\n<color=#06CF00>Mystic Beast's CRIT +2.5%</color>]],
[754130]=[[Endless radiance envelops this body, untouchable and unwavering, banishing all evil.\n<color=#06CF00>Mystic Beast's DEF +6693</color>\n<color=#06CF00>Mystic Beast's CRIT +5%</color>]],
[754141]=[[She cast away eternity to bring forth the world's first glimmer of light.\n<color=#06CF00>Hero HP +675</color>]],
[754142]=[[She cast away eternity to bring forth the world's first glimmer of light.\n<color=#06CF00>Hero HP +1,012</color>]],
[754143]=[[She cast away eternity to bring forth the world's first glimmer of light.\n<color=#06CF00>Hero HP +2278</color>]],
[754144]=[[She cast away eternity to bring forth the world's first glimmer of light.\n<color=#06CF00>Hero HP +5819</color>]],
[754145]=[[She cast away eternity to bring forth the world's first glimmer of light.\n<color=#06CF00>Hero HP +15677</color>]],
[754146]=[[She cast away eternity to bring forth the world's first glimmer of light.\n<color=#06CF00>Hero HP +42195</color>]],
[754147]=[[She cast away eternity to bring forth the world's first glimmer of light.\n<color=#06CF00>Hero HP +92244</color>]],
[754148]=[[She cast away eternity to bring forth the world's first glimmer of light.\n<color=#06CF00>Hero HP +198446</color>\n<color=#06CF00>Hero HP +5%</color>]],
[754149]=[[She cast away eternity to bring forth the world's first glimmer of light.\n<color=#06CF00>Hero HP +324606</color>\n<color=#06CF00>Hero HP +10%</color>]],
[754150]=[[She cast away eternity to bring forth the world's first glimmer of light.\n<color=#06CF00>Hero HP +439245</color>\n<color=#06CF00>Hero HP +15%</color>]],
[754161]=[[Ashes draped over him, regret and rage smoldering within. \n<color=#06CF00>Mystic Beast ATK +64</color>]],
[754162]=[[Ashes draped over him, regret and rage smoldering within. \n<color=#06CF00>Mystic Beast ATK +96</color>]],
[754163]=[[Ashes draped over him, regret and rage smoldering within. \n<color=#06CF00>Mystic Beast ATK +217</color>]],
[754164]=[[Ashes draped over him, regret and rage smoldering within. \n<color=#06CF00>Mystic Beast ATK +462</color>]],
[754165]=[[Ashes draped over him, regret and rage smoldering within. \n<color=#06CF00>Mystic Beast ATK +1,344</color>]],
[754166]=[[Ashes draped over him, regret and rage smoldering within. \n<color=#06CF00>Mystic Beast ATK +3811</color>]],
[754167]=[[Ashes draped over him, regret and rage smoldering within. \n<color=#06CF00>Mystic Beast ATK +9664</color>]],
[754168]=[[Ashes draped over him, regret and rage smoldering within. \n<color=#06CF00>Mystic Beast ATK +15255</color>\n<color=#06CF00>Mystic Beast CRIT DMG +3%</color>]],
[754169]=[[Ashes draped over him, regret and rage smoldering within. \n<color=#06CF00>Mystic Beast ATK +24045</color>\n<color=#06CF00>Mystic Beast CRIT DMG +6%</color>]],
[754170]=[[Ashes draped over him, regret and rage smoldering within. \n<color=#06CF00>Mystic Beast ATK +33466</color>\n<color=#06CF00>Mystic Beast CRIT DMG +10%</color>]],
[754181]=[[An unyielding will compels his broken form to roam the earth. \n<color=#06CF00>Hero DEF +6</color>]],
[754182]=[[An unyielding will compels his broken form to roam the earth. \n<color=#06CF00>Hero DEF +9</color>]],
[754183]=[[An unyielding will compels his broken form to roam the earth. \n<color=#06CF00>Hero DEF +21</color>]],
[754184]=[[An unyielding will compels his broken form to roam the earth. \n<color=#06CF00>Hero DEF +55</color>]],
[754185]=[[An unyielding will compels his broken form to roam the earth. \n<color=#06CF00>Hero DEF +149</color>]],
[754186]=[[An unyielding will compels his broken form to roam the earth. \n<color=#06CF00>Hero DEF +401</color>]],
[754187]=[[An unyielding will compels his broken form to roam the earth. \n<color=#06CF00>Hero DEF +878</color>]],
[754188]=[[An unyielding will compels his broken form to roam the earth. \n<color=#06CF00>Hero DEF +1889</color>\n<color=#06CF00>Hero DEF +5%</color>]],
[754189]=[[An unyielding will compels his broken form to roam the earth. \n<color=#06CF00>Hero DEF +3091</color>\n<color=#06CF00>Hero DEF +10%</color>]],
[754190]=[[An unyielding will compels his broken form to roam the earth. \n<color=#06CF00>Hero DEF +4183</color>\n<color=#06CF00>Hero DEF +15%</color>]],
[754201]=[[Destiny's end was inevitable, yet together, they chose to defy fate. \n<color=#06CF00>Hero ATK +32</color>\n<color=#06CF00>Skill DMG +0.25%</color>]],
[754202]=[[Destiny's end was inevitable, yet together, they chose to defy fate. \n<color=#06CF00>Hero ATK +45</color>\n<color=#06CF00>Skill DMG +0.5%</color>]],
[754203]=[[Destiny's end was inevitable, yet together, they chose to defy fate. \n<color=#06CF00>Hero ATK +108</color>\n<color=#06CF00>Skill DMG +1%</color>]],
[754204]=[[Destiny's end was inevitable, yet together, they chose to defy fate. \n<color=#06CF00>Hero ATK +277</color>\n<color=#06CF00>Skill DMG +1.5%</color>]],
[754205]=[[Destiny's end was inevitable, yet together, they chose to defy fate. \n<color=#06CF00>Hero ATK +746</color>\n<color=#06CF00>Skill DMG +2%</color>]],
[754206]=[[Destiny's end was inevitable, yet together, they chose to defy fate. \n<color=#06CF00>Hero ATK +2009</color>\n<color=#06CF00>Skill DMG +2.5%</color>]],
[754207]=[[Destiny's end was inevitable, yet together, they chose to defy fate. \n<color=#06CF00>Hero ATK +4392</color>\n<color=#06CF00>Skill DMG +3.5%</color>]],
[754208]=[[Destiny's end was inevitable, yet together, they chose to defy fate. \n<color=#06CF00>Hero ATK +9449</color>\n<color=#06CF00>Skill DMG +5%</color>\n<color=#06CF00>Hero ATK +5%</color>]],
[754209]=[[Destiny's end was inevitable, yet together, they chose to defy fate. \n<color=#06CF00>Hero ATK +15457</color>\n<color=#06CF00>Skill DMG +7.5%</color>\n<color=#06CF00>Hero ATK +10%</color>]],
[754210]=[[Destiny's end was inevitable, yet together, they chose to defy fate. \n<color=#06CF00>Hero ATK +20916</color>\n<color=#06CF00>Skill DMG +10%</color>\n<color=#06CF00>Hero ATK +15%</color>]],
[754401]=[[Obtain crystal core and 1 advanced crystal core material]],
[754402]=[[Obtain crystal cores and 5 advanced crystal core materials]],
[754403]=[[Obtain crystal cores and 30 advanced crystal core materials]],
[754404]=[[Obtain crystal cores and 100 advanced crystal core materials]],
[754405]=[[After opening, you can choose a skill crystal core with quality of UR, and you will also get an additional high-level crystal raw material*100]],
[754406]=[[After opening, you can choose 1 skill crystal core with SSR quality, and you will also get 30 high-level crystal raw materials.]],
[754451]=[[Ability to create any SSR quality skill core in the Core Research Institute]],
[754452]=[[Ability to create any UR quality skill core at the Core Research Institute]],
[754453]=[[Used to increase the combat level of the mythical beast, providing 100 upgrade experience points]],
[754454]=[[Used to increase the combat level of the mythical beast, providing 10,000 upgrade experience points]],
[755000]=[[Can be used as upgrade material for most Decorations.]],
[755001]=[[Grants a random Orange Decoration from the list.]],
[755002]=[[Grants a random Purple Decoration from the list.]],
[755003]=[[Grants a random Blue Decoration from the list.]],
[755004]=[[Contains a certain number of universal Decoration parts.]],
[755101]=[[A massive bronze greatsword, heavy with history and raw power, forged in the glory of an ancient civilization.]],
[755102]=[[Thick and unyielding, this timeworn bronze shield stands as a testament to ancient defenses.]],
[755103]=[[A banner of heroism and strength.]],
[755104]=[[A bronze-forged helmet, ancient yet steadfast in its protection.]],
[755105]=[[A silver sword, its icy gleam and massive blade radiating pure strength.]],
[755106]=[[A silver shield, solid and unyielding—a true guardian in battle.]],
[755107]=[[A finely crafted silver helmet, both elegant and protective.]],
[755108]=[[A silver hero statue stands in solemn stillness, like a warrior in deep slumber.]],
[755109]=[[The silver holy grail glows with a mysterious aura, waiting to be awakened.]],
[755110]=[[An old golden oak barrel which seems to hold a hidden treasure within.]],
[755111]=[[It shimmers with a golden glow, as if guarding untold secrets.]],
[755112]=[[It radiates an aura of prestige and authority.]],
[755113]=[[A gold-forged helmet, gleaming with ancient prestige.]],
[755114]=[[A colossal golden blade, radiating sheer dominance.]],
[755115]=[[A golden shield, both ornate and unbreakable.]],
[755116]=[[A shell-shaped building, a fusion of art and creativity.]],
[755117]=[[An ancient amphitheater, echoing the glory of past battles.]],
[755118]=[[A white marble monument, a timeless symbol of love and art.]],
[755119]=[[The Castle Keep, often known as Tenshukaku, is a traditional Japanese stronghold with deep military and cultural roots.]],
[755120]=[[A towering torch, a beacon of freedom.]],
[755121]=[[Rising into the sky, this towering structure commands a chilling authority.]],
[755122]=[[A golden hammer forged with the pride of a master craftsman.]],
[755123]=[[A mysterious, towering ancient beast standing guard over the land.]],
[755124]=[[A legendary site made famous by stories and steeped in myth.]],
[755125]=[[Purple Decoration 6]],
[755126]=[[Purple Decoration 7]],
[755127]=[[Purple Decoration 8]],
[755128]=[[Purple Decoration 9]],
[755129]=[[Blue Decoration 1]],
[755130]=[[Blue Decoration 2]],
[755131]=[[Blue Decoration 3]],
[755132]=[[Blue Decoration 4]],
[755206]=[[A decoy that can be deployed to draw enemy fire with its funny appearance.]],
[756000]=[[Used to ascend and upgrade Survivors!]],
[756001]=[[Collect 10 shards to recruit this survivor. <color=#e63838> Explorer Laura knows the secret treasure location, and sending her to the castle will allow the Lord to bring back additional treasure when completing the intelligence mission! </color>]],
[756002]=[[Collect 10 shards to recruit this survivor. <color=#e63838>Lord Jeanne steals the core secrets and sends them to the headquarters to increase the troop limit of all heroes and increase their attack power</color>]],
[756003]=[[Blue Random Survivor]],
[756004]=[[Purple Random Survivor]],
[756005]=[[Gold Random Survivor]],
[756006]=[[A Blue Random Survivor chest. Open it to get a Blue Survivor at random.]],
[756007]=[[A Purple Random Survivor chest. Open it to get a Purple Survivor at random.]],
[756008]=[[An Orange Random Survivor chest. Open it to get an Orange Survivor at random.]],
[756011]=[[Used to ascend and upgrade Survivors!]],
[756012]=[[Used to ascend and upgrade Survivors!]],
[756013]=[[Used to ascend and upgrade Survivors!]],
[756014]=[[Used to ascend and upgrade Survivors!]],
[756015]=[[Used to ascend and upgrade Survivors!]],
[756016]=[[Used to ascend and upgrade Survivors!]],
[756017]=[[Used to ascend and upgrade Survivors!]],
[756018]=[[Used to ascend and upgrade Survivors!]],
[756019]=[[Used to ascend and upgrade Survivors!]],
[756020]=[[Used to ascend and upgrade Survivors!]],
[756021]=[[Used to ascend and upgrade Survivors!]],
[756022]=[[Used to ascend and upgrade Survivors!]],
[756023]=[[Used to ascend and upgrade Survivors!]],
[756024]=[[Used to ascend and upgrade Survivors!]],
[756025]=[[Used to ascend and upgrade Survivors!]],
[756026]=[[Used to ascend and upgrade Survivors!]],
[756027]=[[Used to ascend and upgrade Survivors!]],
[756028]=[[Used to ascend and upgrade Survivors!]],
[756029]=[[Used to ascend and upgrade Survivors!]],
[756030]=[[Used to ascend and upgrade Survivors!]],
[756031]=[[Used to ascend and upgrade Survivors!]],
[756032]=[[Used to ascend and upgrade Survivors!]],
[756033]=[[Used to ascend and upgrade Survivors!]],
[756034]=[[Used to ascend and upgrade Survivors!]],
[756035]=[[Used to ascend and upgrade Survivors!]],
[756036]=[[Used to ascend and upgrade Survivors!]],
[756037]=[[Used to ascend and upgrade Survivors!]],
[756038]=[[Used to ascend and upgrade Survivors!]],
[756039]=[[Used to ascend and upgrade Survivors!]],
[756040]=[[Used to ascend and upgrade Survivors!]],
[756041]=[[Used to ascend and upgrade Survivors!]],
[756042]=[[Used to ascend and upgrade Survivors!]],
[756043]=[[Used to ascend and upgrade Survivors!]],
[756044]=[[Used to ascend and upgrade Survivors!]],
[756045]=[[Used to ascend and upgrade Survivors!]],
[756046]=[[Used to ascend and upgrade Survivors!]],
[756047]=[[Used to ascend and upgrade Survivors!]],
[756048]=[[Used to ascend and upgrade Survivors!]],
[756049]=[[Used to ascend and upgrade Survivors!]],
[756050]=[[Used to ascend and upgrade Survivors!]],
[756051]=[[Used to ascend and upgrade Survivors!]],
[756052]=[[Used to ascend and upgrade Survivors!]],
[756053]=[[Used to ascend and upgrade Survivors!]],
[756054]=[[Used to ascend and upgrade Survivors!]],
[756055]=[[Used to ascend and upgrade Survivors!]],
[756056]=[[Used to ascend and upgrade Survivors!]],
[756057]=[[Used to ascend and upgrade Survivors!]],
[756058]=[[Used to ascend and upgrade Survivors!]],
[756059]=[[Used to ascend and upgrade Survivors!]],
[756060]=[[Used to ascend and upgrade Survivors!]],
[756061]=[[Used to ascend and upgrade Survivors!]],
[756062]=[[Used to ascend and upgrade Survivors!]],
[756063]=[[Used to ascend and upgrade Survivors!]],
[756064]=[[Used to ascend and upgrade Survivors!]],
[756065]=[[Used to ascend and upgrade Survivors!]],
[756066]=[[Used to ascend and upgrade Survivors!]],
[756067]=[[Used to ascend and upgrade Survivors!]],
[756068]=[[Used to ascend and upgrade Survivors!]],
[756069]=[[Used to ascend and upgrade Survivors!]],
[756070]=[[Used to ascend and upgrade Survivors!]],
[756071]=[[Used to ascend and upgrade Survivors!]],
[756072]=[[Used to ascend and upgrade Survivors!]],
[756073]=[[Used to ascend and upgrade Survivors!]],
[756074]=[[Used to ascend and upgrade Survivors!]],
[756075]=[[Used to ascend and upgrade Survivors!]],
[756076]=[[Used to ascend and upgrade Survivors!]],
[756077]=[[Used to ascend and upgrade Survivors!]],
[756078]=[[Used to ascend and upgrade Survivors!]],
[756079]=[[Used to ascend and upgrade Survivors!]],
[756080]=[[Used to ascend and upgrade Survivors!]],
[756081]=[[Used to ascend and upgrade Survivors!]],
[756082]=[[Used to ascend and upgrade Survivors!]],
[756083]=[[Used to ascend and upgrade Survivors!]],
[756084]=[[Used to ascend and upgrade Survivors!]],
[756085]=[[Used to ascend and upgrade Survivors!]],
[756086]=[[Used to ascend and upgrade Survivors!]],
[756087]=[[Used to ascend and upgrade Survivors!]],
[756088]=[[Used to ascend and upgrade Survivors!]],
[756089]=[[Used to ascend and upgrade Survivors!]],
[756090]=[[Used to ascend and upgrade Survivors!]],
[756091]=[[Used to ascend and upgrade Survivors!]],
[756092]=[[Used to ascend and upgrade Survivors!]],
[756093]=[[Used to ascend and upgrade Survivors!]],
[756094]=[[Used to ascend and upgrade Survivors!]],
[756095]=[[Used to ascend and upgrade Survivors!]],
[756096]=[[Used to ascend and upgrade Survivors!]],
[756097]=[[Used to ascend and upgrade Survivors!]],
[756098]=[[Used to ascend and upgrade Survivors!]],
[756099]=[[Used to ascend and upgrade Survivors!]],
[756100]=[[Used to ascend and upgrade Survivors!]],
[756101]=[[Used to ascend and upgrade Survivors!]],
[756102]=[[Used to ascend and upgrade Survivors!]],
[756103]=[[Used to ascend and upgrade Survivors!]],
[756104]=[[Used to ascend and upgrade Survivors!]],
[756105]=[[Used to ascend and upgrade Survivors!]],
[756106]=[[Used to ascend and upgrade Survivors!]],
[756107]=[[Used to ascend and upgrade Survivors!]],
[756108]=[[Used to ascend and upgrade Survivors!]],
[756109]=[[Used to ascend and upgrade Survivors!]],
[756110]=[[Used to ascend and upgrade Survivors!]],
[756111]=[[Used to ascend and upgrade Survivors!]],
[756112]=[[Used to ascend and upgrade Survivors!]],
[756113]=[[Used to ascend and upgrade Survivors!]],
[756114]=[[Used to ascend and upgrade Survivors!]],
[756115]=[[Used to ascend and upgrade Survivors!]],
[756116]=[[Used to ascend and upgrade Survivors!]],
[756117]=[[Used to ascend and upgrade Survivors!]],
[756118]=[[Used to ascend and upgrade Survivors!]],
[756119]=[[Used to ascend and upgrade Survivors!]],
[756120]=[[Used to ascend and upgrade Survivors!]],
[756121]=[[Used to ascend and upgrade Survivors!]],
[756122]=[[Used to ascend and upgrade Survivors!]],
[756123]=[[Used to ascend and upgrade Survivors!]],
[756124]=[[Used to ascend and upgrade Survivors!]],
[756125]=[[Used to ascend and upgrade Survivors!]],
[756126]=[[Used to ascend and upgrade Survivors!]],
[756127]=[[Used to ascend and upgrade Survivors!]],
[756128]=[[Used to ascend and upgrade Survivors!]],
[756129]=[[Used to ascend and upgrade Survivors!]],
[756130]=[[Used to ascend and upgrade Survivors!]],
[756131]=[[Used to ascend and upgrade Survivors!]],
[756132]=[[Used to ascend and upgrade Survivors!]],
[756133]=[[Used to ascend and upgrade Survivors!]],
[756134]=[[Used to ascend and upgrade Survivors!]],
[756135]=[[Used to ascend and upgrade Survivors!]],
[756136]=[[Used to ascend and upgrade Survivors!]],
[756137]=[[Used to ascend and upgrade Survivors!]],
[756138]=[[Used to ascend and upgrade Survivors!]],
[756139]=[[Used to ascend and upgrade Survivors!]],
[756140]=[[Used to ascend and upgrade Survivors!]],
[756141]=[[Used to ascend and upgrade Survivors!]],
[756142]=[[Used to ascend and upgrade Survivors!]],
[756143]=[[Used to ascend and upgrade Survivors!]],
[756144]=[[Used to ascend and upgrade Survivors!]],
[756145]=[[Used to ascend and upgrade Survivors!]],
[756146]=[[Used to ascend and upgrade Survivors!]],
[756147]=[[Used to ascend and upgrade Survivors!]],
[756148]=[[Used to ascend and upgrade Survivors!]],
[756149]=[[Used to ascend and upgrade Survivors!]],
[756150]=[[Used to ascend and upgrade Survivors!]],
[756151]=[[Used to ascend and upgrade Survivors!]],
[756152]=[[Used to ascend and upgrade Survivors!]],
[756153]=[[Used to ascend and upgrade Survivors!]],
[756154]=[[Used to ascend and upgrade Survivors!]],
[756155]=[[Used to ascend and upgrade Survivors!]],
[756156]=[[Used to ascend and upgrade Survivors!]],
[756157]=[[Used to ascend and upgrade Survivors!]],
[756158]=[[Used to ascend and upgrade Survivors!]],
[756159]=[[Used to ascend and upgrade Survivors!]],
[756160]=[[Used to ascend and upgrade Survivors!]],
[756161]=[[Used to ascend and upgrade Survivors!]],
[756162]=[[Used to ascend and upgrade Survivors!]],
[756163]=[[Used to ascend and upgrade Survivors!]],
[756164]=[[Used to ascend and upgrade Survivors!]],
[756165]=[[Used to ascend and upgrade Survivors!]],
[756166]=[[Used to ascend and upgrade Survivors!]],
[756167]=[[Used to ascend and upgrade Survivors!]],
[756168]=[[Used to ascend and upgrade Survivors!]],
[756169]=[[Used to ascend and upgrade Survivors!]],
[756170]=[[Used to ascend and upgrade Survivors!]],
[756171]=[[Used to ascend and upgrade Survivors!]],
[756172]=[[Used to ascend and upgrade Survivors!]],
[756173]=[[Used to ascend and upgrade Survivors!]],
[756174]=[[Used to ascend and upgrade Survivors!]],
[756175]=[[Used to ascend and upgrade Survivors!]],
[756176]=[[Used to ascend and upgrade Survivors!]],
[756177]=[[Used to ascend and upgrade Survivors!]],
[756178]=[[Used to ascend and upgrade Survivors!]],
[756179]=[[Used to ascend and upgrade Survivors!]],
[756180]=[[Used to ascend and upgrade Survivors!]],
[756181]=[[Used to ascend and upgrade Survivors!]],
[756182]=[[Used to ascend and upgrade Survivors!]],
[756183]=[[Used to ascend and upgrade Survivors!]],
[756184]=[[Used to ascend and upgrade Survivors!]],
[756185]=[[Used to ascend and upgrade Survivors!]],
[756186]=[[Used to ascend and upgrade Survivors!]],
[756187]=[[Used to ascend and upgrade Survivors!]],
[756188]=[[Used to ascend and upgrade Survivors!]],
[756189]=[[Used to ascend and upgrade Survivors!]],
[756190]=[[Used to ascend and upgrade Survivors!]],
[756191]=[[Used to ascend and upgrade Survivors!]],
[756192]=[[Used to ascend and upgrade Survivors!]],
[756193]=[[Used to ascend and upgrade Survivors!]],
[756194]=[[Used to ascend and upgrade Survivors!]],
[756195]=[[Used to ascend and upgrade Survivors!]],
[756196]=[[Used to ascend and upgrade Survivors!]],
[756197]=[[Used to ascend and upgrade Survivors!]],
[756198]=[[Used to ascend and upgrade Survivors!]],
[756199]=[[Used to ascend and upgrade Survivors!]],
[756200]=[[Used to ascend and upgrade Survivors!]],
[756201]=[[Used to ascend and upgrade Survivors!]],
[756202]=[[Used to ascend and upgrade Survivors!]],
[756203]=[[Used to ascend and upgrade Survivors!]],
[756204]=[[Used to ascend and upgrade Survivors!]],
[756205]=[[Used to ascend and upgrade Survivors!]],
[756206]=[[Used to ascend and upgrade Survivors!]],
[756207]=[[Used to ascend and upgrade Survivors!]],
[756208]=[[Used to ascend and upgrade Survivors!]],
[756209]=[[Used to ascend and upgrade Survivors!]],
[756210]=[[Used to ascend and upgrade Survivors!]],
[756211]=[[Used to ascend and upgrade Survivors!]],
[756212]=[[Used to ascend and upgrade Survivors!]],
[756213]=[[Used to ascend and upgrade Survivors!]],
[756214]=[[Used to ascend and upgrade Survivors!]],
[756215]=[[Used to ascend and upgrade Survivors!]],
[756216]=[[Used to ascend and upgrade Survivors!]],
[756217]=[[Used to ascend and upgrade Survivors!]],
[756218]=[[Used to ascend and upgrade Survivors!]],
[756219]=[[Used to ascend and upgrade Survivors!]],
[756220]=[[Used to ascend and upgrade Survivors!]],
[756221]=[[Used to ascend and upgrade Survivors!]],
[756222]=[[Used to ascend and upgrade Survivors!]],
[756223]=[[Used to ascend and upgrade Survivors!]],
[756224]=[[Used to ascend and upgrade Survivors!]],
[756225]=[[Used to ascend and upgrade Survivors!]],
[756226]=[[Used to ascend and upgrade Survivors!]],
[756227]=[[Used to ascend and upgrade Survivors!]],
[756228]=[[Used to ascend and upgrade Survivors!]],
[756229]=[[Used to ascend and upgrade Survivors!]],
[756230]=[[Used to ascend and upgrade Survivors!]],
[756231]=[[Used to ascend and upgrade Survivors!]],
[756232]=[[Used to ascend and upgrade Survivors!]],
[756233]=[[Used to ascend and upgrade Survivors!]],
[756234]=[[Used to ascend and upgrade Survivors!]],
[756235]=[[Used to ascend and upgrade Survivors!]],
[756236]=[[Used to ascend and upgrade Survivors!]],
[756237]=[[Used to ascend and upgrade Survivors!]],
[756238]=[[Used to ascend and upgrade Survivors!]],
[756239]=[[Used to ascend and upgrade Survivors!]],
[756240]=[[Used to ascend and upgrade Survivors!]],
[756241]=[[Used to ascend and upgrade Survivors!]],
[756242]=[[Used to ascend and upgrade Survivors!]],
[756243]=[[Used to ascend and upgrade Survivors!]],
[756244]=[[Used to ascend and upgrade Survivors!]],
[756245]=[[Used to ascend and upgrade Survivors!]],
[756246]=[[Used to ascend and upgrade Survivors!]],
[756247]=[[Used to ascend and upgrade Survivors!]],
[756248]=[[Used to ascend and upgrade Survivors!]],
[756249]=[[Used to ascend and upgrade Survivors!]],
[756250]=[[Used to ascend and upgrade Survivors!]],
[756251]=[[Used to ascend and upgrade Survivors!]],
[756252]=[[Used to ascend and upgrade Survivors!]],
[756253]=[[Used to ascend and upgrade Survivors!]],
[756254]=[[Used to ascend and upgrade Survivors!]],
[756255]=[[Used to ascend and upgrade Survivors!]],
[756256]=[[Used to ascend and upgrade Survivors!]],
[756257]=[[Used to ascend and upgrade Survivors!]],
[756258]=[[Used to ascend and upgrade Survivors!]],
[756259]=[[Used to ascend and upgrade Survivors!]],
[756260]=[[Used to ascend and upgrade Survivors!]],
[756261]=[[Used to ascend and upgrade Survivors!]],
[756262]=[[Used to ascend and upgrade Survivors!]],
[756263]=[[Used to ascend and upgrade Survivors!]],
[756264]=[[Used to ascend and upgrade Survivors!]],
[756265]=[[Used to ascend and upgrade Survivors!]],
[756266]=[[Used to ascend and upgrade Survivors!]],
[756267]=[[Used to ascend and upgrade Survivors!]],
[756268]=[[Used to ascend and upgrade Survivors!]],
[756269]=[[Used to ascend and upgrade Survivors!]],
[756270]=[[Used to ascend and upgrade Survivors!]],
[756271]=[[Used to ascend and upgrade Survivors!]],
[756272]=[[Used to ascend and upgrade Survivors!]],
[756273]=[[Used to ascend and upgrade Survivors!]],
[756274]=[[Used to ascend and upgrade Survivors!]],
[756275]=[[Used to ascend and upgrade Survivors!]],
[756276]=[[Used to ascend and upgrade Survivors!]],
[756277]=[[Used to ascend and upgrade Survivors!]],
[756278]=[[Used to ascend and upgrade Survivors!]],
[756279]=[[Used to ascend and upgrade Survivors!]],
[756280]=[[Used to ascend and upgrade Survivors!]],
[756281]=[[Used to ascend and upgrade Survivors!]],
[756282]=[[Used to ascend and upgrade Survivors!]],
[756283]=[[Used to ascend and upgrade Survivors!]],
[756284]=[[Used to ascend and upgrade Survivors!]],
[756285]=[[Used to ascend and upgrade Survivors!]],
[756286]=[[Used to ascend and upgrade Survivors!]],
[756287]=[[Used to ascend and upgrade Survivors!]],
[756288]=[[Used to ascend and upgrade Survivors!]],
[756289]=[[Used to ascend and upgrade Survivors!]],
[756290]=[[Used to ascend and upgrade Survivors!]],
[756291]=[[Used to ascend and upgrade Survivors!]],
[756292]=[[Used to ascend and upgrade Survivors!]],
[756293]=[[Used to ascend and upgrade Survivors!]],
[756294]=[[Used to ascend and upgrade Survivors!]],
[756295]=[[Used to ascend and upgrade Survivors!]],
[756296]=[[Used to ascend and upgrade Survivors!]],
[756297]=[[Used to ascend and upgrade Survivors!]],
[756298]=[[Used to ascend and upgrade Survivors!]],
[756299]=[[Used to ascend and upgrade Survivors!]],
[756300]=[[Used to ascend and upgrade Survivors!]],
[756301]=[[Used to ascend and upgrade Survivors!]],
[756302]=[[Used to ascend and upgrade Survivors!]],
[756303]=[[Used to ascend and upgrade Survivors!]],
[756304]=[[Used to ascend and upgrade Survivors!]],
[756305]=[[Used to ascend and upgrade Survivors!]],
[756306]=[[Used to ascend and upgrade Survivors!]],
[756307]=[[Used to ascend and upgrade Survivors!]],
[756308]=[[Used to ascend and upgrade Survivors!]],
[756309]=[[Used to ascend and upgrade Survivors!]],
[756310]=[[Used to ascend and upgrade Survivors!]],
[756311]=[[Used to ascend and upgrade Survivors!]],
[756312]=[[Used to ascend and upgrade Survivors!]],
[756313]=[[Used to ascend and upgrade Survivors!]],
[756314]=[[Used to ascend and upgrade Survivors!]],
[756315]=[[Used to ascend and upgrade Survivors!]],
[756316]=[[Used to ascend and upgrade Survivors!]],
[756317]=[[Used to ascend and upgrade Survivors!]],
[756318]=[[Used to ascend and upgrade Survivors!]],
[756319]=[[Used to ascend and upgrade Survivors!]],
[756320]=[[Used to ascend and upgrade Survivors!]],
[756321]=[[Used to ascend and upgrade Survivors!]],
[756322]=[[Used to ascend and upgrade Survivors!]],
[756323]=[[Used to ascend and upgrade Survivors!]],
[756324]=[[Used to ascend and upgrade Survivors!]],
[756325]=[[Used to ascend and upgrade Survivors!]],
[756326]=[[Used to ascend and upgrade Survivors!]],
[756327]=[[Used to ascend and upgrade Survivors!]],
[756328]=[[Used to ascend and upgrade Survivors!]],
[756329]=[[Used to ascend and upgrade Survivors!]],
[756330]=[[Used to ascend and upgrade Survivors!]],
[756331]=[[Used to ascend and upgrade Survivors!]],
[756332]=[[Used to ascend and upgrade Survivors!]],
[756333]=[[Used to ascend and upgrade Survivors!]],
[756334]=[[Used to ascend and upgrade Survivors!]],
[756335]=[[Used to ascend and upgrade Survivors!]],
[756336]=[[Used to ascend and upgrade Survivors!]],
[756337]=[[Used to ascend and upgrade Survivors!]],
[756338]=[[Used to ascend and upgrade Survivors!]],
[756339]=[[Used to ascend and upgrade Survivors!]],
[756340]=[[Used to ascend and upgrade Survivors!]],
[756341]=[[Used to ascend and upgrade Survivors!]],
[756342]=[[Used to ascend and upgrade Survivors!]],
[756343]=[[Used to ascend and upgrade Survivors!]],
[756344]=[[Used to ascend and upgrade Survivors!]],
[756345]=[[Used to ascend and upgrade Survivors!]],
[756346]=[[Used to ascend and upgrade Survivors!]],
[756347]=[[Used to ascend and upgrade Survivors!]],
[756348]=[[Used to ascend and upgrade Survivors!]],
[756349]=[[Used to ascend and upgrade Survivors!]],
[756350]=[[Used to ascend and upgrade Survivors!]],
[756351]=[[Used to ascend and upgrade Survivors!]],
[756352]=[[Used to ascend and upgrade Survivors!]],
[756353]=[[Used to ascend and upgrade Survivors!]],
[756354]=[[Used to ascend and upgrade Survivors!]],
[756355]=[[Used to ascend and upgrade Survivors!]],
[756356]=[[Used to ascend and upgrade Survivors!]],
[756357]=[[Used to ascend and upgrade Survivors!]],
[756358]=[[Used to ascend and upgrade Survivors!]],
[756359]=[[Used to ascend and upgrade Survivors!]],
[756360]=[[Used to ascend and upgrade Survivors!]],
[756361]=[[Used to ascend and upgrade Survivors!]],
[756362]=[[Used to ascend and upgrade Survivors!]],
[756363]=[[Used to ascend and upgrade Survivors!]],
[756364]=[[Used to ascend and upgrade Survivors!]],
[756365]=[[Used to ascend and upgrade Survivors!]],
[756366]=[[Used to ascend and upgrade Survivors!]],
[756367]=[[Used to ascend and upgrade Survivors!]],
[756368]=[[Used to ascend and upgrade Survivors!]],
[756369]=[[Used to ascend and upgrade Survivors!]],
[756370]=[[Used to ascend and upgrade Survivors!]],
[756371]=[[Used to ascend and upgrade Survivors!]],
[756372]=[[Used to ascend and upgrade Survivors!]],
[756373]=[[Used to ascend and upgrade Survivors!]],
[756374]=[[Used to ascend and upgrade Survivors!]],
[756375]=[[Used to ascend and upgrade Survivors!]],
[756376]=[[Used to ascend and upgrade Survivors!]],
[756377]=[[Used to ascend and upgrade Survivors!]],
[756378]=[[Used to ascend and upgrade Survivors!]],
[756379]=[[Used to ascend and upgrade Survivors!]],
[756380]=[[Used to ascend and upgrade Survivors!]],
[756381]=[[Used to ascend and upgrade Survivors!]],
[756382]=[[Used to ascend and upgrade Survivors!]],
[756383]=[[Used to ascend and upgrade Survivors!]],
[756384]=[[Used to ascend and upgrade Survivors!]],
[756385]=[[Used to ascend and upgrade Survivors!]],
[756386]=[[Used to ascend and upgrade Survivors!]],
[756387]=[[Used to ascend and upgrade Survivors!]],
[756388]=[[Used to ascend and upgrade Survivors!]],
[756389]=[[Used to ascend and upgrade Survivors!]],
[756390]=[[Used to ascend and upgrade Survivors!]],
[756391]=[[Used to ascend and upgrade Survivors!]],
[757003]=[[The most mysterious supply chest in the world. Open it to get abundant rewards at random.]],
[757004]=[[A special gift, honoring those who keep the fire alive on starless, frozen nights.]],
[757005]=[[Randomly grants a Treasure Map Piece numbered from 1 to 7, which can be used for excavating mysterious treasures.]],
[757006]=[[Treasure Map Shard No.1]],
[757007]=[[Treasure Map Shard No.2]],
[757008]=[[Treasure Map Shard No.3]],
[757009]=[[Treasure Map Shard No.4]],
[757010]=[[Treasure Map Shard No.5]],
[757011]=[[Treasure Map Shard No.6]],
[757012]=[[Treasure Map Shard No.7]],
[757013]=[[A lucky supply chest rewarded for completing Tavern Quests. Rumor has it that those favored by fate may uncover incredible riches within.]],
[757014]=[[Used to refresh Tavern Quests.]],
[757015]=[[Used to refresh goods on Trade Wagons.]],
[757016]=[[Used for Union Boss events. Making donations will boost the construction progress of the Altar and also grant Union Contribution.]],
[757017]=[[The incredible reward obtained by the great adventurer in an awe-inspiring adventure. Open it for a chance to obtain the Rare Legendary Decoration: Warrior's Stele, S+ Hero Universal Shards or other rewards.]],
[757018]=[[The legendary reward obtained by the greatest adventurer in an epic adventure. Open it for a chance to obtain the Rare Legendary Decoration: Warrior's Stele, S+ Hero Universal Shards, Legendary Resource Selection Chest or other rewards.]],
[757019]=[[Common reward obtained from Trials. Well done, keep up the good work!]],
[757020]=[[Excellent reward obtained from Trials. Your outstanding performance deserves this reward.]],
[757021]=[[Epic reward obtained from Trials. It's for the bravest hero.]],
[757022]=[[Legendary reward obtained from Trials. The world just witnessed the birth of another legend.]],
[757024]=[[Obtain the required number of medals to claim daily objective rewards.]],
[757026]=[[When acquired, it grants 1 Intelligence event.]],
[757039]=[[Union reward chest obtained from attacking cities in City Clash.]],
[757041]=[[A special gift, honoring those who keep the fire alive on starless, frozen nights.]],
[757056]=[[It can be obtained after completing the intelligence mission and used to start the zombie apocalypse event challenge.]],
[757064]=[[A golden key inlaid with precious gems, used to open the treasure chest in the Lord's Treasury event. It can be obtained by participating in the Lord's Parade event.]],
[758001]=[[Use to get 1,000-10,000 Enhancement Stones at random.]],
[758002]=[[Use to choose one reward from the list.]],
[758003]=[[Open to choose one of the following three rewards.]],
[758004]=[[Used to open rare chests.]],
[758005]=[[Accumulate Pioneer Points for Pioneer rewards!]],
[758006]=[[Obtained by completing Warrior Pass quests and used to level up the Warrior Pass.]],
[758007]=[[Use to select any of the following heroes' shards: Severa, Dragonic, Fenixia, Andrew, Daphne, Belial, or Yord.]],
[758008]=[[Points for the Enhancement Pass. Accumulate them to earn rewards.]],
[758009]=[[Used in the Full Preparation event to exchange for various rewards. This item will not be recycled when the event is over.]],
[758011]=[[Use to select any of the following heroes' shards: Rexar, Sophia, or Sparta.]],
[758012]=[[Used for draws in the Lucky Wheel event. This item will not be recycled when the event is over.]],
[758013]=[[Use to select any of the following heroes' shards: Severa, Dragonic, Fenixia, Andrew, Daphne, Belial, Yord, Rexar, Sophia, Sparta, Monica, Valkyr, Alvarez, Kataras, or Mirana.]],
[758014]=[[Accumulate the required number of points to earn amazing rewards.]],
[758015]=[[A treasure obtained by completing the Union Badge quests!]],
[758016]=[[A treasure obtained by completing the Union Badge quests!]],
[758017]=[[A treasure obtained by completing the Union Badge quests!]],
[758018]=[[Used for draws in the Lucky Wheel event. This item will not be recycled when the event is over.]],
[758019]=[[Used to select any of the following heroes' shards: Severa, Dragonic, Fenixia, Andrew, Daphne, Belial, Yord, Rexar, Sophia, Sparta, Monica, or Valkyr.]],
[758020]=[[When the Monthly Card is active, you can build and use the 4th Team to deploy one more team.]],
[758021]=[[When the Monthly Card is active, the gathering speed of all hero teams increases by 10%.]],
[758022]=[[When the Monthly Card is active, all hero teams receive 5% less damage from wild monsters.]],
[758023]=[[Use to summon a union aircraft carrying abundant rewards.]],
[758024]=[[Points for the Intelligence Pass. Accumulate them to earn rewards.]],
[758025]=[[Used in the Badge Market to exchange for items.]],
[758026]=[[Used to open Miracle Chests]],
[758028]=[[Open for a chance to claim double rewards]],
[758036]=[[Points used for Mystic Beast Pass activities, accumulated to receive battle pass rewards]],
[758038]=[[Hammers obtained in the territory revival event can be accumulated to receive revival rewards]],
[758039]=[[[Ghost's favorite pumpkin coin, golden and smells delicious] A lucky draw item in the Halloween party and lucky pumpkin event.]],
[758040]=[[[Strange candy, what will happen if you take a bite...] You can exchange rare items in the Halloween Party Ghost Shop]],
[758041]=[[[Pumpkins are sweeter than candy, an essential ingredient for Halloween parties!] Submit them at Halloween parties and carnivals to get generous rewards.]],
[758042]=[[[What a sumptuous Halloween feast!] Used to increase the feast level in Halloween parties and carnival feasts]],
[758043]=[[[Trick or Treat!] Halloween Party Pumpkin Battle Pass points can be used to upgrade the Battle Pass level]],
[758044]=[[[Wow! Witness the lucky moment!] Draw 3 identical pumpkins from the Halloween Party Lucky Pumpkins to receive a lucky treasure chest. Open it and you'll get Pumpkin Coins.]],
[758045]=[[[Someone must be messing around....] Draw 3 small gifts with different patterns from the lucky pumpkin at the Halloween party. Open them to get random rewards.]],
[758046]=[[Points used for Halloween recharge event]],
[758047]=[[[A carnival! Let's rock together!] Open the treasure chests you get from participating in the ghost party to get random rewards.]],
[758053]=[[[On this special day... I invite you... (other words are no longer legible)] Use this to host a ghost party. You can invite other lords to participate. You can participate up to 10 times a day. You can get rewards by participating in the party.]],
[758048]=[[A common treasure chest found by a puppy for its owner! There's a chance you'll receive <color=#F071FF>10 Enhancement Crystals, 100 Diamonds</color>, and other rewards.]],
[758049]=[[A rare treasure chest found by a puppy for its owner! It has a chance to receive <color=#D74700>1 Hero Recruitment Ticket, 1 Survivor Recruitment Ticket, 1 Cargo Refresh Ticket, 1 Mission Refresh Scroll, </color><color=#F071FF>10 Enhancement Crystals, 100 Diamonds</color>, and other rewards.]],
[758050]=[[A legendary treasure chest found by a puppy for its owner! It has a chance to receive <color=#D74700>1 S+ Hero Universal Fragment, 10 Hero Recruitment Tickets, 10 Survivor Recruitment Tickets, 1 Cargo Refresh Ticket, 1 Mission Refresh Scroll</color>, and other rewards.]],
[758058]=[[Points in the Renaissance Battle Pass can be accumulated to obtain rewards]],
[758101]=[[Accumulated Tokens-Newcomers]],
[758102]=[[Accumulated Tokens-New Server]],
[758103]=[[Accumulated Tokens-Cycle-Seven Days Accumulated Recharge]],
[758104]=[[Accumulated Tokens-Cycle-Daily Accumulated Recharge]],
[758105]=[[Accumulated Tokens-Festival]],
[758201]=[[Used to draw rewards in the Gear Specials event]],
[759000]=[[Default Castle 1]],
[759001]=[[Female Exclusive Castle Skins]],
[759002]=[[Turns the Castle's appearance into a carrot and grants additional stat bonuses\n\nWhen used:\nDMG dealt to monsters +5%\nWhen owned:\nDMG dealt to monsters +1%]],
[759003]=[[Turns the Castle's appearance into a flying squirrel and grants additional stat bonuses\n\nWhen used:\nHero ATK +5%\nWhen owned:\nHero HP +5%\nHero DEF +5%]],
[759004]=[[Default Castle 2]],
[759005]=[[Default Castle 3]],
[759006]=[[Default Castle 4]],
[759007]=[[Default Castle 5]],
[759008]=[[Default Castle 6]],
[759009]=[[Default Castle 7]],
[759010]=[[Default Castle 8]],
[759011]=[[Default Castle 9]],
[759012]=[[Default Castle 10]],
[759013]=[[Turns the Castle's appearance into a dragon and grants additional stat bonuses\n\nWhen used:\nHero ATK +5%\nWhen owned:\nHero ATK +5%]],
[759014]=[[Turns the Castle's appearance into a dragon and grants additional stat bonuses\n\nWhen used:\nHero ATK +5%\nWhen owned:\nHero ATK +5%]],
[759015]=[[Turns the Castle's appearance into a dragon and grants additional stat bonuses\n\nWhen used:\nHero ATK +5%\nWhen owned:\nHero ATK +5%]],
[759016]=[[The castle's appearance has been changed to an ice and snow castle. Wow~ so cool and magical!]],
[759017]=[[The trick-or-treaters have cast a spell and turned your castle into a giant pumpkin!]],
[759018]=[[The castle's appearance has been changed to flying wings, a building form that flies on the wind, making your castle full of speed and freedom.]],
[759102]=[[Obtained by purchasing the Monthly Card.]],
[759103]=[[Awarded to those who rank high on the Peak Arena Rankings.]],
[759108]=[[The president-exclusive avatar frame for the champion Kingdom in the Clash of Kingdoms!]],
[759109]=[[Obtained from the Clash of Kingdoms event.]],
[759110]=[[The exclusive avatar frame for the champion Kingdom in the Clash of Kingdoms!]],
[759111]=[[Obtain by joining the X Clash Club]],
[759113]=[[If you don’t want to be visited by trick-or-treaters, hand over the candy!]],
[759201]=[[Default Avatar 1]],
[759202]=[[Default Avatar 2]],
[759203]=[[Default Avatar 3]],
[759204]=[[Default Avatar 4]],
[759205]=[[Default Avatar 5]],
[759206]=[[Default Avatar 6]],
[759207]=[[Default Avatar 7]],
[759208]=[[Default Avatar 8]],
[759209]=[[Human Faction Avatar]],
[759210]=[[Forest Faction Avatar]],
[759211]=[[Nightfall Faction Avatar]],
[759301]=[[Default Mystic Beast Skin]],
[759311]=[[Permanently activates the Mystic Beast skin and grants additional stat bonuses\n\nWhen used:\nHero ATK +5%\nWhen owned:\nHero HP +5%]],
[759312]=[[A token of gratitude from Verna! Use to get random resources and materials. (There's a chance to get Verna Shards or 2,500 Union Contribution Points.)]],
[759400]=[[Default Castle visual effects.]],
[759401]=[[Permanent base effect—Celebration Gift Box.\nOwning this base effect grants all hero skills a permanent <color=#008E0B>+1.5%</color> damage increase.]],
[759402]=[[Permanent Castle visual effect: I Am Legend.\nUpon acquisition, it increases all of your heroes' Skill DMG by <color=#008E0B>1.5%</color> permanently.]],
[759403]=[[Limited-time Castle visual effect: I Am Legend.\nUpon acquisition, it increases all of your heroes' Skill DMG by <color=#008E0B>1.5%</color> before it expires.]],
[759404]=[[Wow! The whole castle smells like pumpkin!]],
[780001]='5',
[780002]='10',
[780003]='20',
[780004]='50',
[780005]='100',
[780006]='500',
[780007]='600',
[780008]='1.0K',
[780009]='2.5K',
[780010]='3.0K',
[780011]='5.0K',
[780012]='6.0K',
[780013]='10.0K',
[780014]='18.0K',
[780015]='30.0K',
[780016]='50.0K',
[780017]='300.0K',
[780018]='500.0K',
[780019]='1M',
[780101]='1m',
[780102]='5m',
[780103]='1H',
[780104]='3H',
[780105]='8H',
[780106]='15m',
[780201]=[[Lv. 1]],
[780202]=[[Lv. 2]],
[780203]=[[Lv. 3]],
[780204]=[[Lv. 4]],
[780205]=[[Lv. 5]],
[780206]=[[Lv. 6]],
[780207]=[[Lv. 7]],
[780208]=[[Lv. 8]],
[780209]=[[Lv. 9]],
[780210]=[[Lv. 10]],
[780211]='24H',
[780212]='7D',
[780213]='30D',
[781001]='Currency',
[781002]='Resource',
[781003]=[[Speed Up]],
[781004]='Hero',
[781005]='Survivor',
[781006]='Gear',
[781007]=[[Mystic Beast]],
[781008]='VIP',
[781009]='Union',
[781010]='Customization',
[781011]='Military',
[781012]='Stamina',
[781013]='Decoration',
[781014]='Treasure',
[781015]='Special',
[781016]=[[Castle Skin]],
[800001]=[[Log in for {%s1} day(s) in total]],
[800002]=[[Make any purchase {%s1} time(s)]],
[800003]=[[Upgrade buildings {%s1} time(s)]],
[800004]=[[Defeat any {%s1} Elite zombie(s)]],
[800005]=[[Gather any resource *{%s1}]],
[800006]=[[Speed up {%s1} minute(s)]],
[800007]=[[Train {%s1} soldier(s) of any level]],
[800008]=[[Upgrade {%s2} to Lv. {%s1}]],
[800009]=[[Clear dangerous tile No. {%s2}]],
[800010]=[[Complete Intelligence Quest {%s1} time(s)]],
[800011]=[[Challenge in the Novice Arena {%s1} time(s)]],
[800012]=[[Upgrade {%s1} hero(es) to Lv. {%s2}]],
[800013]=[[Upgrade Barracks to Lv. {%s1}]],
[800014]=[[Help allies {%s1} time(s)]],
[800015]=[[Research Technology {%s1} time(s)]],
[800016]=[[Upgrade the Drone to Lv. {%s1}]],
[800017]=[[Perform {%s1} Hero Recruit(s)]],
[800018]=[[Reach {%s1} Total CP]],
[800019]=[[Purchase a pack containing Diamonds [1 Diamond] ]],
[800020]=[[Perform 1 Hero Recruit]],
[800021]=[[Use 2,000 Hero EXP in a single instance]],
[800022]=[[Increase Building CP by 1]],
[800023]=[[Use 1 minute of Building Speed Up]],
[800024]=[[Train 1 Lv. 1 Troop]],
[800025]=[[Train 1 Lv. 2 Troop]],
[800026]=[[Train 1 Lv. 3 Troop]],
[800027]=[[Train 1 Lv. 4 Troop]],
[800028]=[[Train 1 Lv. 5 Troop]],
[800029]=[[Train 1 Lv. 6 Troop]],
[800030]=[[Train 1 Lv. 7 Troop]],
[800031]=[[Train 1 Lv. 8 Troop]],
[800032]=[[Train 1 Lv. 9 Troop]],
[800033]=[[Train 1 Lv. 10 Troop]],
[800034]=[[Use 1 minute of Training Speed Up]],
[800035]=[[Increase Technology CP by 1]],
[800036]=[[Use 1 minute of Technology Speed Up]],
[800037]=[[Consume 10 Mystic Beast EXP]],
[800038]=[[Spend 1 Stamina]],
[800039]=[[Daily login]],
[800040]=[[​Dispatch Trade Wagons {%s1} time(s)]],
[800041]=[[Plunder other players' Tavern Quests {%s1} time(s)]],
[800054]=[[Repair: {%s2}]],
[800055]=[[Build: {%s2}]],
[800056]=[[Deploy {%s1} Hero(es)]],
[800057]=[[Cultivate {%s1} area(s)]],
[800058]=[[Perform Survivor Recruitment {%s1} time(s)]],
[800059]=[[Assign {%s1} Survivor(s) to {%s2}]],
[800060]=[[Complete all chapter quests]],
[800061]=[[Perform Union Donation {%s1} time(s)]],
[800062]=[[Active VIP]],
[800063]=[[Make any purchase {%s1} time(s)]],
[800064]=[[Eliminate {%s1} Zombies in the Wild]],
[800065]=[[Attack other Lords {%s1} time(s)]],
[800066]=[[Dispatch troops to gather {%s1} {%s2}]],
[800067]=[[Obtain {%s1} piece(s) of gear of <color=#a036d5>Epic</color> or higher quality]],
[800068]=[[Perform {%s1} Mystic Beast upgrade(s)]],
[800069]=[[Recruit {%s1} Survivor(s) at the City Gate]],
[800070]=[[​Locate the Expedition Truck]],
[800071]=[[Join a Union with at least {%s1} members]],
[800072]=[[Union Daily Logins]],
[800073]=[[Union daily Activity contributors reach]],
[800074]=[[Log in every day.]],
[800075]=[[Castle Upgrade]],
[800076]=[[Fog Clearance]],
[800077]=[[Intelligence Quests]],
[800078]=[[Arena Challenges]],
[800079]=[[Hero Upgrade]],
[800080]=[[Troop Training]],
[800081]=[[Defeat Monsters]],
[800082]=[[Union Mutual-Help]],
[800083]=[[Arms Race]],
[800084]=[[Mystic Beast Upgrade]],
[800085]=[[Scientific Research]],
[800086]=[[Rally Battles]],
[800087]=[[Trade Wagon]],
[800088]=[[World Boss]],
[800089]=[[Advanced Troops]],
[800090]=[[Building Enhancement]],
[800091]=[[Hero Recruitment]],
[800092]=[[Power Enhancement]],
[800093]=[[Accelerate Development]],
[800094]=[[Tavern Quests]],
[800095]=[[Dispatch {%s1} Trade Wagon(s) of {%s2} quality or higher]],
[800096]=[[Perform {%s1} Tavern Quest(s) of <color=#f1ba4e>Orange</color> or higher quality]],
[800097]=[[Enhance {%s2} to {%s1}-Star]],
[800098]=[[Upgrade {%s2} to Lv. {%s1}]],
[800099]=[[Upgrade Skill {%s2} to Lv. {%s1}]],
[800100]=[[Own {%s1} Hero(es)]],
[800101]=[[Gather any resource {%s1} time(s)]],
[800102]=[[Complete {%s1} Tavern Quest(s)]],
[800103]=[[Dispatch {%s1} Trade Wagon(s)]],
[800104]=[[Reach {%s1} Activity Points today]],
[800105]=[[Eliminate a Lv. {%s2} Zombie in the Wild]],
[800106]=[[Upgrade any {%s1} hero(es) to {%s2}-Star or above]],
[800107]=[[Give Union Assistance {%s1} time(s)]],
[809901]=[[Log in for 1 day in total]],
[809902]=[[Log in for 2 days in total]],
[809903]=[[Log in for 3 days in total]],
[809904]=[[Log in for 4 days in total]],
[809905]=[[Log in for 5 days in total]],
[809906]=[[Upgrade Castle to Lv. 3]],
[809907]=[[Upgrade Castle to Lv. 6]],
[809908]=[[Upgrade Castle to Lv. 10]],
[809909]=[[Upgrade Castle to Lv. 14]],
[809910]=[[Upgrade Castle to Lv. 17]],
[809911]=[[Clear dangerous tile No. 10]],
[809912]=[[Clear dangerous tile No. 20]],
[809913]=[[Clear dangerous tile No. 30]],
[809914]=[[Clear dangerous tile No. 60]],
[809915]=[[Clear dangerous tile No. 100]],
[809916]=[[Complete Intelligence Quest 10 times]],
[809917]=[[Complete Intelligence Quest 20 times]],
[809918]=[[Complete Intelligence Quest 40 times]],
[809919]=[[Complete Intelligence Quest 70 times]],
[809920]=[[Complete Intelligence Quest 110 times]],
[809921]=[[Challenge in the Novice Arena 3 times]],
[809922]=[[Challenge in the Novice Arena 6 times]],
[809923]=[[Challenge in the Novice Arena 10 times]],
[809924]=[[Challenge in the Novice Arena 15 times]],
[809925]=[[Challenge in the Novice Arena 20 times]],
[809926]=[[Own 1 hero of Lv. 30 or higher]],
[809927]=[[Own 5 heroes of Lv. 30 or higher]],
[809928]=[[Own 1 hero of Lv. 50 or higher]],
[809929]=[[Own 5 heroes of Lv. 50 or higher]],
[809930]=[[Own 1 hero of Lv. 80 or higher]],
[809931]=[[Train 50 troops of any type]],
[809932]=[[Train 100 troops of any type]],
[809933]=[[Train 300 troops of any type]],
[809934]=[[Train 600 troops of any type]],
[809935]=[[Train 1200 troops of any type]],
[809936]=[[Defeat 1 Lv.5 Mine Zombie/Food Zombie/Gold Zombie]],
[809937]=[[Defeat 1 Lv.10 Mine Zombie/Food Zombie/Gold Zombie]],
[809938]=[[Defeat 1 Lv.15 Mine Zombie/Food Zombie/Gold Zombie]],
[809939]=[[Defeat 1 Lv.20 Mine Zombie/Food Zombie/Gold Zombie]],
[809940]=[[Defeat 1 Lv.25 Mine Zombie/Food Zombie/Gold Zombie]],
[809941]=[[Help allies 10 times]],
[809942]=[[Help allies 50 times]],
[809943]=[[Help allies 100 times]],
[809944]=[[Help allies 200 times]],
[809945]=[[Help allies 300 times]],
[809946]=[[Obtain 30,000 points in Arms Race]],
[809947]=[[Obtain 60,000 points in Arms Race]],
[809948]=[[Obtain 90,000 points in Arms Race]],
[809949]=[[Obtain 120,000 points in Arms Race]],
[809950]=[[Obtain 150,000 points in Arms Race]],
[809951]=[[Upgrade Mystic Beast to Lv. 5]],
[809952]=[[Upgrade Mystic Beast to Lv. 7]],
[809953]=[[Upgrade Mystic Beast to Lv. 10]],
[809954]=[[Upgrade Mystic Beast to Lv. 20]],
[809955]=[[Upgrade Mystic Beast to Lv. 30]],
[809956]=[[Upgrade Technology 5 times]],
[809957]=[[Upgrade Technology 10 times]],
[809958]=[[Upgrade Technology 15 times]],
[809959]=[[Upgrade Technology 30 times]],
[809960]=[[Upgrade Technology 60 times]],
[809961]=[[Defeat any Elite zombie 5 times]],
[809962]=[[Defeat any Elite zombie 10 times]],
[809963]=[[Defeat any Elite zombie 20 times]],
[809964]=[[Defeat any Elite zombie 30 times]],
[809965]=[[Defeat any Elite zombie 50 times]],
[809966]=[[Dispatch 2 Trade Wagons of Purple quality or above.]],
[809967]=[[Dispatch 4 Trade Wagons of Purple quality or above.]],
[809968]=[[Dispatch 8 Trade Wagons of Purple quality or above.]],
[809969]=[[Dispatch 4 Trade Wagons of Orange quality or above.]],
[809970]=[[Dispatch 8 Trade Wagons of Orange quality or above.]],
[809971]=[[Challenge the World Boss 2 times]],
[809972]=[[Challenge the World Boss 4 times]],
[809973]=[[Challenge the World Boss 6 times]],
[809974]=[[Challenge the World Boss 8 times]],
[809975]=[[Challenge the World Boss 10 times]],
[809976]=[[Upgrade Barracks to Lv. 3]],
[809977]=[[Upgrade Barracks to Lv. 6]],
[809978]=[[Upgrade Barracks to Lv. 10]],
[809979]=[[Upgrade Barracks to Lv. 14]],
[809980]=[[Upgrade Barracks to Lv. 17]],
[809981]=[[Reach 100,000 Building CP]],
[809982]=[[Reach 200,000 Building CP]],
[809983]=[[Reach 300,000 Building CP]],
[809984]=[[Reach 400,000 Building CP]],
[809985]=[[Reach 500,000 Building CP]],
[809986]=[[Perform 10 Hero Recruits]],
[809987]=[[Perform 20 Hero Recruits]],
[809988]=[[Perform 50 Hero Recruits]],
[809989]=[[Perform 100 Hero Recruits]],
[809990]=[[Perform 150 Hero Recruits]],
[809991]=[[Reach 100,000 Total CP]],
[809992]=[[Reach 200,000 Total CP]],
[809993]=[[Reach 500,000 Total CP]],
[809994]=[[Reach 1,000,000 Total CP]],
[809995]=[[Reach 2,000,000 Total CP]],
[809996]=[[Speed up 100 minutes]],
[809997]=[[Speed up 200 minutes]],
[809998]=[[Speed up 500 minutes]],
[809999]=[[Speed up 1,000 minutes]],
[810000]=[[Speed up 3,000 minutes]],
[810001]=[[Complete 3 Tavern Quests of Purple quality or above]],
[810002]=[[Complete 6 Tavern Quests of Purple quality or above]],
[810003]=[[Complete 9 Tavern Quests of Purple quality or above]],
[810004]=[[Complete 3 Tavern Quests of Orange quality or above]],
[810005]=[[Complete 6 Tavern Quests of Orange quality or above]],
[1000001]=[[Hero Pass]],
[1000002]=[[Pass Rewards]],
[1000003]=[[Daily Quests]],
[1000004]=[[Activity Quests]],
[1000005]='Free',
[1000006]=[[Premium Rewards]],
[1000007]=[[Advanced Pass]],
[1000009]='Placeholder',
[1000010]=[[Hero Pass]],
[1000011]=[[1. During the event, accumulated hero points can be used to receive pass rewards]],
[1000012]=[[2. Complete quests to earn points. Purchasing the Pass unlocks bonus points from quest rewards.]],
[1000013]=[[3. Any unclaimed rewards will be sent via mail at the end of the event.]],
[1000019]='Placeholder',
[1000020]=[[Hero Points]],
[1000021]=[[Hero Pass points can be accumulated to get rewards]],
[1000029]='Placeholder',
[1000030]=[[Hero Pass Daily Quest Rewards]],
[1000031]=[[Dear Lord, you had {%s1} unclaimed points from yesterday's Hero Pass daily quests. They've been automatically added to your total points.]],
[1000032]=[[Hero Pass Reward Reissue]],
[1000033]=[[Dear Lord, the Hero Pass event has ended. You have unclaimed rewards, please check them.]],
[1000039]='Placeholder',
[1000040]=[[Super Value]],
[1000101]=[[Upgrade Pass]],
[1000102]=[[Upgrade Rewards]],
[1000103]=[[Daily Quests]],
[1000104]=[[Activity Quests]],
[1000105]='Free',
[1000106]=[[Premium Rewards]],
[1000107]=[[Deluxe Rewards]],
[1000110]=[[Upgrade Pass Info]],
[1000111]=[[1. Earn Upgrade Rewards by accumulating Upgrade Points during the event.]],
[1000112]=[[2. Upgrade Points are earned by completing quests. Purchasing the Pass grants bonus points from quest rewards.]],
[1000113]=[[3. Any unclaimed rewards will be sent via mail at the end of the event.]],
[1000114]='Placeholder',
[1000120]=[[Upgrade Points]],
[1000121]=[[Points for the Upgrade Pass. Accumulate them to earn rewards.]],
[1000130]=[[Upgrade Pass Daily Quest Reward]],
[1000131]=[[Dear Lord, you had {%s1} unclaimed points from yesterday's Upgrade Pass daily quests. They've been automatically added to your total points.]],
[1000132]=[[Upgrade Pass Rewards Reissue]],
[1000133]=[[Dear Lord, the Upgrade Pass event has ended. Unclaimed rewards have been sent to your inbox.]],
[1000140]=[[Super Value]],
[1000201]=[[Available Upon Purchase]],
[1000202]=[[Total from Purchase]],
[1000203]=[[<size=28>Purchase the Pass<color=FFFFF> to keep earning</color> double quest rewards!</size>]],
[1000204]=[[Unlock Premium Rewards]],
[1000205]=[[Claim All]],
[1000206]='Claim',
[1000207]='Go',
[1000208]='{%s1}/{%s2}',
[1000209]=[[{%s1} day(s) {%s2}:{%s3}:{%s4}]],
[1000210]=[[Rewards Preview]],
[1000211]=[[Open to get one of the following rewards.]],
[1000212]=[[Infinity Chest]],
[1000213]=[[Deluxe Pass]],
[1000214]=[[Unlocks at <color=#319F38>%s</color> Points.]],
[1000215]=[[Unlock to claim rich rewards.]],
[1000301]=[[Path to Dominance]],
[1000302]=[[Power Up, Rewards Up!]],
[1000303]='{%s1}/{%s2}',
[1000304]=[[Claim All]],
[1000305]='Go',
[1000310]=[[Hero Training]],
[1000311]=[[Technology Research]],
[1000312]=[[Base upgrade]],
[1000313]=[[Soldier Training]],
[1000314]=[[Mystic Beast Training]],
[1000330]='Details',
[1000331]=[[1. Increase your CP and receive rewards when you reach your combat power target.]],
[1000332]=[[2. Any unclaimed rewards will be sent via mail at the end of the event.]],
[1000340]=[[Path to Dominance rewards]],
[1000341]=[[Dear Lord, the Path to Dominance event has ended. Your unclaimed rewards have been delivered via in-game mail—check them out!]],
[1000400]='Placeholder',
[1000401]=[[Hero Trial]],
[1000402]=[[Complete the current ascension quest to claim generous rewards.]],
[1000403]='Claim',
[1000404]='Go',
[1000405]=[[The heroes' trials are still underway—keep it up!]],
[1000406]=[[All Hero Trial quests completed.]],
[1000407]=[[Complete all current trial quests to proceed.]],
[1000410]='Details',
[1000411]=[[1. Complete all heroes' star level quests to earn rewards.]],
[1000412]=[[2. Unclaimed rewards will be sent via in-game mail after the event ends.]],
[1000420]=[[Hero Trial]],
[1000421]=[[Dear Lord, the Hero Trial event has ended. Your unclaimed rewards have been delivered via in-game mail—check them out!]],
[1000450]='Placeholder',
[1000451]=[[Prosperity Fund]],
[1000452]=[[Upgrade your base and receive generous rewards]],
[1000453]=[[Super Value]],
[1000454]='Free',
[1000455]='Deluxe',
[1000456]=[[Total rewards after purchase:]],
[1000457]=[[Purchase to get]],
[1000458]=[[Tap a blank space to close]],
[1000459]=[[The event has ended.]],
[1000460]='Details',
[1000461]=[[1. You can receive rewards if you meet the base level.]],
[1000462]=[[2. Purchase prosperity funds to receive additional rewards]],
[1000465]=[[Reward Value]],
[1000466]=[[Luxury Rebate]],
[1000467]='70000',
[1000468]=[[Upgrade castle level]],
[1000469]=[[Rebate Value]],
[1000500]='Placeholder',
[1000501]=[[Strongest Lord]],
[1000502]=[[Event Duration:]],
[1000503]='{%s1}d{%s2}:{%s3}:{%s4}',
[1000504]=[[My ranking:]],
[1000505]=[[Ranking locked]],
[1000506]=[[<size=24><color=#FFFFFF>Ranking Grand Prize</color></size>]],
[1000507]='I',
[1000508]='II',
[1000509]='III',
[1000510]='IV',
[1000511]='V',
[1000512]='VI',
[1000513]='VII',
[1000514]=[[Stage points:]],
[1000515]='Rewards',
[1000516]=[[Get points]],
[1000517]='Claim',
[1000518]='Go',
[1000519]='Expired',
[1000520]='Locked',
[1000521]='（%s）',
[1000522]=[[（Open after%s）]],
[1000523]=[[(Phase Ended)]],
[1000524]=[[Stage Ranking]],
[1000525]=[[Overall Ranking]],
[1000526]=[[My Rank]],
[1000527]=[[Ranking Rewards]],
[1000528]=[[The new phase has begun.]],
[1000529]='%s/%s',
[1000531]=[[Phase 1: Intelligence Event]],
[1000532]=[[Phase 2:Building Speed Up]],
[1000533]=[[Phase 3:Research Speed up]],
[1000534]=[[Phase 4:Consume EXP]],
[1000535]=[[Phase 5: Soldier Training]],
[1000536]=[[Phase 6:Kill Enemies]],
[1000537]=[[Points earned in first 6 phases.]],
[1000541]=[[Strongest Lord - Phase Ranking Rewards]],
[1000542]=[[Dear Lord, you performed well in the phase {%s1} of the Strongest Lord event and won the ranking {%s2}. This is your ranking reward, please check it.]],
[1000543]=[[Strongest Lord-Total Ranking Rewards]],
[1000544]=[[Dear Lord, you performed well in the Strongest Lord event and won the ranking {%s1}. This is your ranking reward. Please check it.]],
[1000545]=[[The Strongest Lord - Points Target Reward Reissue]],
[1000546]=[[Dear Lord, you have unclaimed points target rewards in the Strongest Lord event, which have been sent via email, please check~]],
[1000547]=[[Rally Assault – Quest Rewards Reissue]],
[1000548]=[[Dear Lord, you have unclaimed quest rewards in the Rallying Assault event, which have been sent via email, please check~]],
[1000560]=[[Event Rules:]],
[1000561]=[[1. During the event, you can get generous rewards by reaching the designated points target task at each phase.]],
[1000562]=[[2. Points can be obtained by completing points events. The points events in each phase are different:]],
[1000563]=[[Phase 1: Intelligence Event, Consume Stamina]],
[1000564]=[[Phase 2:Building Speed Up, Survivor Recruitment]],
[1000565]=[[Phase 3: Research Speed Up, Intelligence Event]],
[1000566]=[[Phase 4:Consume EXP, Recruit Heroes]],
[1000567]=[[Phase 5: Soldier Training, Stage Speed up]],
[1000568]=[[Phase 6:Kill Enemies, Wagon Dispatch]],
[1000569]=[[Phase 7: Points earned in first 6 phases.]],
[1000570]=[[3. At the end of each phase, the phase rankings will be settled, and ranking rewards will be delivered by email; at the end of the event, the total rankings will be settled, and ranking rewards will be delivered by email.]],
[1000571]=[[4. Point target rewards not collected in each phase will be delivered by email after the event ends.]],
[1000572]=[[5. During the event, points can only be obtained by purchasing packs with diamonds.]],
[1000601]=[[Purchase packs with diamonds (per 1 diamond)]],
[1000602]=[[Complete 1 Intelligence Quest.]],
[1000603]=[[Consume 1 Stamina]],
[1000604]=[[Collect 100 Food]],
[1000605]=[[Collect 100 Steel]],
[1000606]=[[Collect 60 Gold]],
[1000607]=[[For every 1 point increase in building CP]],
[1000608]=[[Any building speeds up for 1 minute]],
[1000609]=[[Perform 1 Survivor Recruitment]],
[1000610]=[[Complete 1 Intelligence Quest.]],
[1000611]=[[Increase technology CP by 1 point]],
[1000612]=[[Use 1-Minute Technology Research Speed Up]],
[1000613]=[[Conduct an advanced recruitment]],
[1000614]=[[Consumes 660 EXP]],
[1000615]=[[Spend 1 skill EXP book]],
[1000616]=[[Use 1-Minute Building Speed Up]],
[1000617]=[[Use 1-Minute Technology Research Speed Up]],
[1000618]=[[Use 1-Minute Training Speed Up]],
[1000619]=[[Train a level 1 soldier]],
[1000620]=[[Train a level 2 soldier]],
[1000621]=[[Train a level 3 soldier]],
[1000622]=[[Train a level 4 soldier]],
[1000623]=[[Train a level 5 soldier]],
[1000624]=[[Train a level 6 soldier]],
[1000625]=[[Train a level 7 soldier]],
[1000626]=[[Train a level 8 soldier]],
[1000627]=[[Train a level 9 soldier]],
[1000628]=[[Train a level 10 soldier]],
[1000629]=[[Send out 1 orange wagon]],
[1000630]=[[Perform 1 orange Tavern Quest]],
[1000631]=[[Use 1-Minute Healing Speed Up]],
[1000632]=[[Defeat 1 Lv. 1 Soldier]],
[1000633]=[[Defeat 1 Lv. 2 Soldier]],
[1000634]=[[Defeat 1 Lv. 3 Soldier]],
[1000635]=[[Defeat 1 Lv. 4 Soldier]],
[1000636]=[[Defeat 1 Lv. 5 Soldier]],
[1000637]=[[Defeat 1 Lv. 6 Soldier]],
[1000638]=[[Defeat 1 Lv. 7 Soldier]],
[1000639]=[[Defeat 1 Lv. 8 Soldier]],
[1000640]=[[Defeat 1 Lv. 9 Soldier]],
[1000641]=[[Defeat 1 Lv. 10 Soldier]],
[1000642]=[[Points reached 500]],
[1000643]=[[Points reached 1000]],
[1000644]=[[Points reached 3000]],
[1000645]=[[Points reached 6000]],
[1000646]=[[Points reached 10,000]],
[1000690]=[[Lord Sprint Pack]],
[1000701]=[[There are tons of items to purchase here]],
[1000702]=[[<size=22>New goods will arrive in {%s1}\n<color=#87F425>Restocked every {%s2} week(s)</color></size>]],
[1000703]=[[Sold Out]],
[1000704]=[[Unlock at VIP {%s1}]],
[1000705]=[[<size=22>New goods will arrive in {%s1}\n<color=#87F425>Restocked on the {%s2} of each month</color></size>]],
[1000706]=[[Diamond Store]],
[1000707]=[[VIP Shop]],
[1000708]=[[Union Store]],
[1000709]=[[Honor Shop]],
[1000710]=[[Trial Shop]],
[1000711]=[[New arrivals in {%s1}!]],
[1000713]=[[Exceeds the purchase limit]],
[1000714]=[[Shop not constructed]],
[1000715]=[[The shop is not unlocked]],
[1000718]=[[Any speed-up]],
[1000719]='Intelligence',
[1000720]=[[Union Duel Start]],
[1000721]=[[Duel Warm-up]],
[1000722]=[[Today's Quests]],
[1000723]='Rewards',
[1000724]=[[Tap anywhere to close]],
[1000725]=[[Intelligence Training]],
[1000726]=[[Castle Construction]],
[1000727]=[[Technology Research]],
[1000728]=[[Hero Upgrade]],
[1000729]=[[Full Preparation]],
[1000730]=[[Defeat the Enemy]],
[1000731]=[[Point Ranking]],
[1000732]=[[Point Rewards]],
[1000733]='Tutorial',
[1000734]='Ranking',
[1000735]=[[Player Name]],
[1000736]='CP',
[1000737]=[[Earn points to win the grand prize and lead your Union to victory!]],
[1000738]=[[Upgrade the Research Center to Lv. {%s1} to unlock Union Duel technology for more points.]],
[1000739]=[[Research technology to unlock Grade {%s1}–{%s2} rewards.]],
[1000740]=[[Daily Ranking]],
[1000741]=[[Weekly Ranking]],
[1000742]='Points',
[1000743]='Locked',
[1000744]='Monday',
[1000745]='Tuesday',
[1000746]='Wednesday',
[1000747]='Thursday',
[1000748]='Friday',
[1000749]='Saturday',
[1000750]=[[Not ranked]],
[1000751]=[[My Union]],
[1000752]=[[Today's Theme]],
[1000753]=[[Battle Results]],
[1000754]=[[Union Assault]],
[1000755]=[[Duel League]],
[1000756]=[[{%s1}, earns Union Duel Points]],
[1000757]=[[Union Points]],
[1000758]='MVP',
[1000759]=[[Total Weekly Points]],
[1000760]='Date',
[1000761]='Match',
[1000762]=[[Victory Points]],
[1000763]='Winner',
[1000764]=[[The event is about to begin]],
[1000765]=[[Event ends in]],
[1000766]=[[Advanced Relocation is required for this cross-server relocation.]],
[1000767]=[[Silver Group]],
[1000768]=[[Gold Group]],
[1000769]=[[Diamond Group]],
[1000770]=[[Union Duel Personal Rewards]],
[1000771]=[[Here are your unclaimed personal rewards from Union Duel:]],
[1000772]=[[Union Duel Victory]],
[1000773]=[[Required points not reached]],
[1000774]=[[Congratulations to your Union for winning Union Duel {%s1}, but since your Union Duel points did not reach {%s2}, you will not receive victory rewards.]],
[1000775]=[[You need to earn at least {%s1}/{%s2} points during the league to claim league rewards.]],
[1000776]=[[Rank {%s1} Union Member Rewards]],
[1000777]=[[Rank {%s1}–{%s2} Union Member Rewards]],
[1000778]=[[Your Union has not participated in Union Duel this week.]],
[1000779]=[[Join a Union and participate in the Pinnacle]],
[1000780]=[[Join Union]],
[1000781]=[[Duel Theme]],
[1000782]=[[When Union Assault begins, head to the Union's location to battle.]],
[1000783]=[[Union Duel League No. {%s1}]],
[1000784]=[[Advanced relocation is not required for this cross-server relocation.]],
[1000785]=[[Teleportation Coordinates: Warzone {%s1}, Union {%s2}]],
[1000786]='ATK',
[1000787]='Back',
[1000788]=[[At the end of the league, the top {%s1} Unions will be promoted to {%s2}.]],
[1000789]=[[At the end of the league, the bottom {%s1} Unions will be demoted to {%s2}.]],
[1000790]=[[Match Records]],
[1000791]=[[The match has not started yet.]],
[1000792]=[[Week {%s1}]],
[1000793]=[[Rewards Preview]],
[1000794]=[[Daily Victory Rewards]],
[1000795]=[[Weekly Rank Rewards (Victory)]],
[1000796]=[[Weekly Rank Rewards (Defeat)]],
[1000797]=[[Available when the day's Union Duel points reach a minimum of %s]],
[1000798]=[[Day {%s1}]],
[1000799]=[[Point Bonus]],
[1000800]=[[Duel Results]],
[1000801]='Go',
[1000802]=[[Research technology to earn points faster.]],
[1000803]=[[Value of {%s1}]],
[1000804]=[[Spend 1 Stamina]],
[1000805]=[[Complete 1 Intelligence Quest]],
[1000806]=[[Spend 660 Hero EXP]],
[1000807]=[[Spend 1 Mystic Beast EXP]],
[1000808]=[[Use 1 Mystic Beast Breakthrough Potion]],
[1000809]=[[Gather 100 Food (Intelligence Quests excluded)]],
[1000810]=[[Gather 100 Iron (Intelligence Quests excluded)]],
[1000811]=[[Gather 60 Gold (Intelligence Quests excluded)]],
[1000812]=[[Open a Chip Chest to claim 1 Advanced Chip Material]],
[1000813]=[[Use 1-Minute Building Speed Up]],
[1000814]=[[Increase building CP by 1]],
[1000815]=[[Dispatch 1 Legendary Wagon]],
[1000816]=[[Perform 1 orange Tavern Quest]],
[1000817]=[[​Perform 1 Survivor Recruitment]],
[1000818]=[[Use 1-Minute Technology Research Speed Up]],
[1000819]=[[Increase technology CP by 1]],
[1000820]=[[Use 1 Energy Crystal]],
[1000821]=[[Complete 1 Intelligence Quest]],
[1000822]=[[Open 1 Lv. 1 Mystic Beast Mark Chest]],
[1000823]=[[Open 1 Lv.2 Mystic Beast Mark Chest]],
[1000824]=[[Open 1 Lv.3 Mystic Beast Mark Chest]],
[1000825]=[[Open 1 Lv.4 Mystic Beast Mark Chest]],
[1000826]=[[Open 1 Lv.5 Mystic Beast Mark Chest]],
[1000827]=[[Open 1 Lv.6 Mystic Beast Mark Chest]],
[1000828]=[[Open 1 Lv.7 Mystic Beast Mark Chest]],
[1000829]=[[Perform 1 Hero Recruit]],
[1000830]=[[Spend 660 Hero EXP]],
[1000831]=[[Use 1 S+ Hero Shard]],
[1000832]=[[Use 1 S Hero Shard]],
[1000833]=[[Use 1 A Hero Shard]],
[1000834]=[[Spend 1 skill EXP book]],
[1000835]=[[Complete 1 Intelligence Quest]],
[1000836]=[[Use 1-Minute Building Speed Up]],
[1000837]=[[Increase building CP by 1]],
[1000838]=[[Use 1-Minute Technology Research Speed Up]],
[1000839]=[[Increase technology CP by 1]],
[1000840]=[[Use 1-Minute Training Speed Up​]],
[1000841]=[[Train 1 Lv. 1 Soldier]],
[1000842]=[[Train 1 Lv. 2 Soldier]],
[1000843]=[[Train 1 Lv. 3 Soldier]],
[1000844]=[[Train 1 Lv. 4 Soldier]],
[1000845]=[[Train 1 Lv. 5 Soldier]],
[1000846]=[[Train 1 Lv. 6 Soldier]],
[1000847]=[[Train 1 Lv. 7 Soldier]],
[1000848]=[[Train 1 Lv. 8 Soldier]],
[1000849]=[[Train 1 Lv. 9 Soldier]],
[1000850]=[[Train 1 Lv. 10 Soldier]],
[1000851]=[[Dispatch 1 Legendary Wagon]],
[1000852]=[[Perform 1 orange Tavern Quest]],
[1000853]=[[Use 1-Minute Building Speed Up]],
[1000854]=[[Use 1-Minute Technology Research Speed Up]],
[1000855]=[[Use 1-Minute Training Speed Up​]],
[1000856]=[[Use 1-Minute Healing Speed Up​]],
[1000857]=[[Defeat 1 Lv. 1 Soldier (specific matched union)]],
[1000858]=[[Defeat 1 Lv. 2 Soldier (specific matched union)]],
[1000859]=[[Defeat 1 Lv. 3 Soldier (specific matched union)]],
[1000860]=[[Defeat 1 Lv. 4 Soldier (specific matched union)]],
[1000861]=[[Defeat 1 Lv. 5 Soldier (specific matched union)]],
[1000862]=[[Defeat 1 Lv. 6 Soldier (specific matched union)]],
[1000863]=[[Defeat 1 Lv. 7 Soldier (specific matched union)]],
[1000864]=[[Defeat 1 Lv. 8 Soldier (specific matched union)]],
[1000865]=[[Defeat 1 Lv. 9 Soldier (specific matched union)]],
[1000866]=[[Defeat 1 Lv. 10 Soldier (specific matched union)]],
[1000867]=[[Defeat 1 Lv. 1 Soldier]],
[1000868]=[[Defeat 1 Lv. 2 Soldier]],
[1000869]=[[Defeat 1 Lv. 3 Soldier]],
[1000870]=[[Defeat 1 Lv. 4 Soldier]],
[1000871]=[[Defeat 1 Lv. 5 Soldier]],
[1000872]=[[Defeat 1 Lv. 6 Soldier]],
[1000873]=[[Defeat 1 Lv. 7 Soldier]],
[1000874]=[[Defeat 1 Lv. 8 Soldier]],
[1000875]=[[Defeat 1 Lv. 9 Soldier]],
[1000876]=[[Defeat 1 Lv. 10 Soldier]],
[1000877]=[[Lose 1 Lv. 1 Soldier]],
[1000878]=[[Lose 1 Lv. 2 Soldier]],
[1000879]=[[Lose 1 Lv. 3 Soldier]],
[1000880]=[[Lose 1 Lv. 4 Soldier]],
[1000881]=[[Lose 1 Lv. 5 Soldier]],
[1000882]=[[Lose 1 Lv. 6 Soldier]],
[1000883]=[[Lose 1 Lv. 7 Soldier]],
[1000884]=[[Lose 1 Lv. 8 Soldier]],
[1000885]=[[Lose 1 Lv. 9 Soldier]],
[1000886]=[[Lose 1 Lv. 10 Soldier]],
[1000887]=[[Consume Stamina]],
[1000888]=[[Building CP]],
[1000889]=[[Intercity Trade]],
[1000890]=[[Recruit Survivor]],
[1000891]=[[Acorn Tavern]],
[1000892]=[[Research CP]],
[1000893]=[[Energy Crystal]],
[1000894]=[[Research Speed Up]],
[1000895]=[[Hero Shards]],
[1000896]=[[Hero EXP]],
[1000897]=[[Recruit Hero]],
[1000898]=[[Upgrade Skill]],
[1000899]=[[Soldier Training]],
[1000900]=[[Soldier Training Speed Up]],
[1000901]=[[Full Preparation]],
[1000902]=[[Purchase Vouchers to redeem heroes and claim extra awesome rewards.]],
[1000903]=[[Voucher Purchase]],
[1000904]=[[Voucher Redemption]],
[1000905]='Guaranteed',
[1000906]=[[Randomly get one of the rewards.]],
[1000907]=[[Daily Purchase Limit: {%s1}/{%s2}]],
[1000908]=[[Special Offer]],
[1000909]='Premium',
[1000910]='Rare',
[1000911]='Deluxe',
[1000912]=[[Super Value]],
[1000913]=[[Redemption Notice]],
[1000914]=[[Remaining Today: {%s1}]],
[1000915]=[[Remaining in Event: {%s1}]],
[1000916]=[[Sold Out Today]],
[1000917]=[[Rewards claimed.]],
[1000920]=[[1. During the event, purchasing packs grants Vouchers and random rewards. Packs are refreshed daily.]],
[1000921]=[[2. Vouchers can be redeemed in the Redeem Shop for a variety of valuable items.]],
[1000922]=[[3. When purchasing packs that include Vouchers, you'll accumulate Voucher Progress. Reach certain milestones to claim bonus rewards. Unclaimed progress rewards will be sent via mail after the event ends.]],
[1000923]=[[4. Unused Vouchers from this event can still be redeemed in the next event once it begins.]],
[1000930]=[[Full Preparation Reward Reissue]],
[1000931]=[[Dear Lord, you have unclaimed Voucher Progress rewards from the Full Preparation event. They've been sent to you via in-game mail. Please check your inbox.]],
[1000940]=[[<color=#ffffff>Purchase limit reached.</color>]],
[1000951]=[[Daily Value Pack]],
[1000952]=[[Daily Premium Pack]],
[1000953]=[[Daily Rare Pack]],
[1000954]=[[Daily Deluxe Pack]],
[1000955]=[[Daily Super Pack]],
[1001001]=[[Lord Pass]],
[1001002]=[[Accumulate points]],
[1001003]=[[Get rich rewards]],
[1001004]=[[1. During the Lord Journey event, accumulated Lord Points can be used to claim Lord Pass rewards]],
[1001005]=[[2. Lord Points can be obtained by completing Lord rewards, and purchasing Lord Pass can unlock luxurious rewards]],
[1001006]=[[3. Any unclaimed rewards will be sent via mail at the end of the event.]],
[1001021]=[[Lucky Spin]],
[1001022]='Attempts',
[1001023]=[[Hero preview]],
[1001024]=[[Spin x{%s1}]],
[1001025]='Attempts',
[1001026]=[[Can be used at Lucky Spin]],
[1001027]=[[{%s1} pack]],
[1001028]=[[{%s1} Purchase(s) Remaining]],
[1001029]='Claim',
[1001030]=[[Super Value]],
[1001031]=[[Sold Out]],
[1001032]=[[Daily Free Pack]],
[1001033]=[[Daily Premium Lucky Pack]],
[1001034]=[[Daily Rare Lucky Pack]],
[1001035]=[[Daily Advanced Lucky Pack]],
[1001036]=[[Daily Luxurious Lucky Pack]],
[1001037]=[[Daily Super Lucky Pack]],
[1001038]=[[Diamond purchase]],
[1001039]=[[Insufficient items. Confirm spending {%s1} diamonds to exchange for {%s2} items to complete?]],
[1001040]=[[Don't remind me again today]],
[1001041]=[[No spin times today]],
[1001042]=[[Lucky Spin Event Reissue]],
[1001043]=[[Dear lord, you still have unclaimed rewards in the Lucky Spin event. They have been sent by email. Please check them.]],
[1001044]=[[Insufficient items]],
[1001045]=[[Current click operation is too frequent]],
[1001061]=[[Defeat the Enemy Event Incoming]],
[1001062]=[[Defeat the Enemy event approaching. Defeat the enemy army to earn points and amazing rewards.]],
[1001063]=[[The event is about to begin]],
[1001064]=[[Peace-lovers can do the following:]],
[1001065]=[[Activate Shield]],
[1001066]=[[Relocate to a Union rally point]],
[1001067]=[[Don't remind me this month]],
[1001068]=[[No ranking info]],
[1001069]=[[Rank Rewards]],
[1001070]=[[League Rewards]],
[1001071]=[[Ranking Rewards]],
[1001072]=[[Available when the day's Union Duel points reach a minimum of {%s1}]],
[1001073]=[[Available when the week's Union Duel points reach a minimum of {%s1}]],
[1001089]=[[Purchase a pack containing Diamonds (1 Diamond)]],
[1001090]=[[Union Duel Personal Ranking]],
[1001091]=[[Daily Participation Rewards]],
[1001092]=[[Union Duel Defeat]],
[1001093]=[[Union Duel Weekly Match Rewards]],
[1001094]=[[Union Duel Weekly Match Defeat]],
[1001095]=[[Battle Results]],
[1001096]=[[Union Duel League Rewards]],
[1001097]=[[Required Points Not Reached]],
[1001098]=[[Union Duel Personal Rewards]],
[1001099]=[[Your current point ranking in the Union Duel {%s1} today is {%s2}. Here are your rewards.]],
[1001100]=[[You participated in the Union Duel League {%s1} and won {%s2} point(s). Here are your rewards.]],
[1001101]=[[Congratulations to your Union for winning Union Duel {%s1}, but since your Union Duel points did not reach {%s2}, you will not receive the victory rewards.]],
[1001102]=[[Unfortunately, your Union lost the Union Duel {%s1}, but don't be discouraged. Let's focus on winning the next event!]],
[1001103]=[[Congratulations to your Union for securing the final victory in the Union Duel. Here are your weekly match victory rewards.]],
[1001104]=[[Congratulations to your Union for securing the final victory in the Union Duel, but since your Union Duel points did not reach {%s1}, you will not receive the weekly match victory rewards.]],
[1001105]=[[Unfortunately, your Union did not win this week's Union Duel, but your Union Duel points reached {%s1}, so here are your weekly match defeat rewards. Keep up the good work!]],
[1001106]=[[Unfortunately, your Union did not win this week's Union Duel, and your Union Duel points did not reach {%s1}, so you will not receive the weekly match defeat rewards.]],
[1001107]=[[Here are your unclaimed personal rewards from Union Duel:]],
[1001108]=[[Your Union ranked No. {%s2} in Group {%s1}. You have won a total score of {%s3}, ranking No. {%s4} within your Union. Here are your rewards. Keep up the good work and aim even higher!]],
[1001109]=[[Participation Conditions:\n1. Only players with a Lv. 10 castle or higher are eligible.\n2. Unions with 20 or more members are eligible.\n3. Only the top 32 Unions in each Warzone by CP will be eligible for matchmaking in the Union Duel for the upcoming week.\n\n\nHow to Play:\n1. Your Union will face off against an enemy Union in a week-long event, featuring a theme that rotates daily. Complete quests related to that theme to earn points. The Union with more points will win the points reward for that day. At the end of the week, the Union with the most points will win the event!\n2. Union Duel offers abundant rewards. Give it your all and fight for your Union!\n3. Switching Unions during the event will mean no Union points earned for one day, but your personal points will remain unaffected, and you can still open personal chests.]],
[1001110]=[[Once the Defeat the Enemy event begins on Saturday, the Union Assault will also start. Lords can head to the opposing Warzone to fight, while also protecting their own Union members.]],
[1001111]=[[Complete Intelligence Points]],
[1001112]=[[Use Speed-up Points]],
[1001113]=[[Advanced Recruitment Points]],
[1001114]=[[All Points (non-purchasable)]],
[1001115]=[[Building CP Points]],
[1001116]=[[Technology CP Points]],
[1001117]=[[Soldier Training Points]],
[1001118]=[[Kill Points]],
[1001119]=[[<color=#a036d5><size=30>How to Earn Points Fast</size></color>\n● Complete Intelligence Quests for massive points!\n● Level up your heroes!\n● Level up your Mystic Beasts!\n● Use as much stamina as possible!\n● Save some low-level soldiers for the Friday event.\n\n<color=#a036d5><size=30>Important Tips!</size></color>\n● Each Duel chest contains Energy Crystals, a key material for upgrading advanced technology. Open as many chests as possible each day for better rewards!\n● Upgrade Union Duel technology to unlock more chests. Completing quests also grants Duel points.]],
[1001120]=[[<color=#a036d5><size=30>How to Earn Points Fast</size></color>\n● Ensure all wagons are dispatched at S+ level!\n● Refresh Tavern quests until gold quests are available!\n● Use all Survivor Recruitment Vouchers.\n● Use speed-ups to accelerate building upgrades!\n● Save some low-level soldiers for the Friday event.\n\n<color=#a036d5><size=30>Important Tips!</size></color>\n● Each Duel chest contains Energy Crystals, a key material for upgrading advanced technology. Open as many chests as possible each day for better rewards!\n● Upgrade Union Duel technology to unlock more chests. Completing quests also grants Duel points.]],
[1001121]=[[<color=#a036d5><size=30>How to Earn Points Fast</size></color>\n● Complete Intelligence Quests for massive points!\n● Use speed-ups to accelerate technology research!\n● Open more Mystic Beast Chests on Wednesdays!\n● Save some low-level soldiers for the Friday event.\n\n<color=#a036d5><size=30>Important Tips!</size></color>\n● Each Duel chest contains Energy Crystals, a key material for upgrading advanced technology. Open as many chests as possible each day for better rewards!\n● Upgrade Union Duel technology to unlock more chests. Completing quests also grants Duel points.]],
[1001122]=[[<color=#a036d5><size=30>How to Earn Points Fast</size></color>\n● Use all Hero Recruitment Vouchers.\n● Level up your heroes!\n● Upgrade your heroes' skills for points rewards.\n● Star up your heroes.\n● Save Intelligence Quests for the Friday event.\n● Save some low-level soldiers for the Friday event.\n\n<color=#a036d5><size=30>Important Tips!</size></color>\n● Each Duel chest contains Energy Crystals, a key material for upgrading advanced technology. Open as many chests as possible each day for better rewards!\n● Upgrade Union Duel technology to unlock more chests. Completing quests also grants Duel points.]],
[1001123]=[[<color=#a036d5><size=30>How to Earn Points Fast</size></color>\n● Complete Intelligence Quests for massive points!\n● Train soldiers and use speed-ups to accelerate low-level soldier training.\n● Open more Mystic Beast Chests on Wednesdays.\n● Leave the chests for upgraded buildings unopened for the next Tuesday.\n\n<color=#a036d5><size=30>Important Tips!</size></color>\n● Each Duel chest contains Energy Crystals, a key material for upgrading advanced technology. Open as many chests as possible each day for better rewards!\n● Upgrade Union Duel technology to unlock more chests. Completing quests also grants Duel points.]],
[1001124]=[[<color=#a036d5><size=30>How to Earn Points Fast</size></color>\n● Lords not participating in attacks should activate shields!\n● Lords eager for battle can attack, but if the CP difference is significant, target smaller Unions first!\n● Assist in defending allies to repel enemies and earn points!\n● Ensure all wagons are dispatched at S+ level!\n● Use speed-ups to accelerate soldier training!\n● Use speed-ups to accelerate building upgrades!\n● Use speed-ups to accelerate injured soldier treatment!\n\n<color=#a036d5><size=30>Important Tips!</size></color>\n● Each Duel chest contains Energy Crystals, a key material for upgrading advanced technology. Open as many chests as possible each day for better rewards!\n● Upgrade Union Duel technology to unlock more chests. Completing quests also grants Duel points.]],
[1001126]=[[Mystic Beast Value Pack]],
[1001127]=[[Fast Development Pack]],
[1001128]=[[Scientific Research Pack]],
[1001129]=[[Super Value EXP Pack]],
[1001130]=[[Resource Grand Pack]],
[1001131]=[[Training Pack]],
[1001132]='Bye',
[1001133]=[[Research technology to unlock Grade 4–6 rewards]],
[1001134]=[[Research technology to unlock Grade 7–9 rewards]],
[1001135]=[[Hero Shard Pack]],
[1001136]=[[Victory in Yesterday's Duel]],
[1001137]=[[Defeat in Yesterday's Duel]],
[1001138]=[[Points Earned:]],
[1001139]='N/A',
[1001140]=[[Starting Soon]],
[1001141]=[[Event Rules:
1. Your Union will engage in a week-long Duel event against a rival Union. Each day will feature a new Duel Theme. Members can earn Duel Points by completing specific actions tied to that day's theme. The Union with the higher score each day wins the daily Duel and earns major points. At the end of the week, the Union with the highest total score wins the weekly Duel.
2. The Union Duel offers generous rewards, including Daily Personal Point Ranking Rewards, Daily Victory Union Rewards, and Weekly Victory Union Rewards. Rally your strength and fight for your Union!
3. Changing Unions during the event will not affect your personal points, but you will be unable to earn Union points for 24 hours.
4. Only packs that include Diamonds will grant Union Duel Points.]],
[1001301]=[[Recharge Gifts]],
[1001302]=[[Purchase packs to get additional total recharge crystals, and accumulate crystals to receive high prizes]],
[1001303]='{%s1}d{%s2}:{%s3}:{%s4}',
[1001304]=[[Daily reset:]],
[1001305]=[[1. Recharge during the event to total recharge event points.]],
[1001306]=[[2. You can claim rewards by total recharge points. Unclaimed rewards will be sent by email after the event.]],
[1001307]=[[Reissue of Total Recharge gifts]],
[1001308]=[[Dear lord, you still have unclaimed rewards in the total recharge event, which have been sent via email. Please check~]],
[1001309]=[[Recommended value packs]],
[1001310]=[[Builder's Pack]],
[1001311]=[[Eternal Pyramid Pack]],
[1001312]=[[Lv. 7 Value Pack]],
[1001313]=[[Lv. 10 Value Pack]],
[1001314]=[[Lv. 13 Value Pack]],
[1001320]=[[Hero shards, rare castle skins]],
[1001321]=[[Claimable reward value]],
[1001322]=[[Claim Now]],
[1001351]=[[Weekly Special Offer]],
[1001352]='{%s1}d{%s2}:{%s3}:{%s4}',
[1001353]=[[Weekly Limit {%s1}/{%s2}]],
[1001354]=[[1. The pack limits reset at 12:00 AM every Monday.]],
[1001355]=[[2. The purchase limit for unpurchased packs will reset without stacking.]],
[1001360]=[[Value Medal Pack]],
[1001361]=[[Castle Resource Pack]],
[1001362]=[[Value Stamina Pack]],
[1001363]=[[Power Surge Pack]],
[1001364]=[[Mystic Beast Mark Pack]],
[1001365]=[[Mystic Beast Potion Pack]],
[1001366]=[[Decorative Origem Pack]],
[1001367]=[[Value Gear Pack]],
[1001368]=[[Weekly Hero Training Pack]],
[1001369]=[[Energy Crystal Pack]],
[1001451]=[[Daily Must-Buy]],
[1001452]='Reset{%s1}d{%s2}:{%s3}:{%s4}',
[1001453]=[[Daily must-buy progress reward reissue]],
[1001454]=[[Dear Lord, your Daily Must-Buy point progress rewards have been reset. Unclaimed rewards have been detected and reissued via in-game mail. Check your inbox now!]],
[1001455]=[[1.Purchase the Daily Must-Buy to earn points. Accumulate points and claim Progress Rewards at specific milestones!]],
[1001456]=[[2. The points progress rewards will be refreshed after a certain period of time. If there are unclaimed rewards during the refresh, they will be reissued via email.]],
[1001457]=[[Daily Limit {%s1}/{%s2}]],
[1001460]=[[Daily Gear Enhancement Pack I]],
[1001461]=[[Daily Gear Enhancement Pack II]],
[1001462]=[[Daily Gear Enhancement Pack III]],
[1001465]=[[Daily Medal Pack I]],
[1001466]=[[Daily Medal Pack II]],
[1001467]=[[Daily Medal Pack III]],
[1001470]=[[Daily Mark Pack I]],
[1001471]=[[Daily Mark Pack II]],
[1001472]=[[Daily Mark Pack III]],
[1001475]=[[Daily Mystic Beast Potion Pack I]],
[1001476]=[[Daily Mystic Beast Potion Pack II]],
[1001477]=[[Daily Mystic Beast Potion Pack III]],
[1001480]=[[Daily Decorative Origem Pack I]],
[1001481]=[[Daily Decorative Origem Pack II]],
[1001482]=[[Daily Decorative Origem Pack III]],
[1001485]=[[Daily Speed Up Pack I]],
[1001486]=[[Daily Speed Up Pack II]],
[1001487]=[[Daily Speed Up Pack III]],
[1001490]=[[Daily Hero Training Pack I]],
[1001491]=[[Daily Hero Training Pack II]],
[1001492]=[[Daily Hero Training Pack III]],
[1001495]=[[Daily Wheat Pack I]],
[1001496]=[[Daily Wheat Pack II]],
[1001497]=[[Daily Wheat Pack III]],
[1001498]=[[Daily Wheat Pack IV]],
[1001500]=[[Daily Iron Pack I]],
[1001501]=[[Daily Iron Pack II]],
[1001502]=[[Daily Iron Pack III]],
[1001503]=[[Daily Iron Pack IV]],
[1001505]=[[Daily Value Gold Pack I]],
[1001506]=[[Daily Value Gold Pack II]],
[1001507]=[[Daily Value Gold Pack III]],
[1001508]=[[Daily Value Gold Pack IV]],
[1001509]=[[Daily Wheat Pack V]],
[1001510]=[[Daily Iron Pack V]],
[1001511]=[[Daily Value Gold Pack V]],
[1001512]=[[Daily Energy Crystal Pack I]],
[1001513]=[[Daily Energy Crystal Pack II]],
[1001514]=[[Daily Energy Crystal Pack III]],
[1001551]=[[Daily Special Offer]],
[1001552]=[[Daily Chest]],
[1001553]='Reset{%s1}d{%s2}:{%s3}:{%s4}',
[1001554]=[[One-click is a steal!]],
[1001555]=[[Get all pack rewards]],
[1001556]=[[Sold Out]],
[1001557]='Contains',
[1001558]='Change',
[1001559]='Selected',
[1001570]=[[The selected hero pack has been purchased today and cannot be switched.]],
[1001571]=[[The pack has been purchased individually and cannot be purchased in bulk]],
[1001572]=[[Sold Out]],
[1001573]='Switch',
[1001580]=[[1. Value Pack can only be purchased once per day. Purchase limits refresh daily at 00:00.]],
[1001581]=[[2. Purchasing the bundle grants extra discounts. Individual purchases of any pack will disable bundle purchase.]],
[1001582]=[[3. Heroes will unlock periodically. After unlocking, you can switch heroes and their packs will update with Hero Shards.]],
[1001601]=[[Deluxe Monthly Card]],
[1001602]='Duration',
[1001603]=[[Days remaining]],
[1001604]=[[Purchase to get]],
[1001605]=[[Daily Claim]],
[1001606]='Claim',
[1001607]=[[Claimed Today]],
[1001608]=[[Activate Monthly Card]],
[1001609]='Renewal',
[1001610]=[[[Deluxe Monthly Card] Daily Reward]],
[1001611]=[[Dear Lord, 
We’ve reissued yesterday’s unclaimed Deluxe Monthly Card Daily Rewards to your inbox. 
(Remaining: {%s1}d)]],
[1001612]=[[[Deluxe Monthly Card] Expiration Notice]],
[1001613]=[[Dear Lord,
Your Deluxe Monthly Card has expired. Access to benefits has been suspended.
Renew now to restore your benefits - daily premium rewards await!]],
[1001614]=[[VIP+30 Days]],
[1001615]=[[Monthly Card Privilege]],
[1001616]=[[Maximum monthly subscription time reached; cannot purchase.]],
[1001617]=[[Can be built upon activating the Monthly Card (Castle Lv. 2 and Scout required)]],
[1001618]=[[Activate the Monthly Card to unlock Auto-Translate.]],
[1001701]=[[Value Weekly Card]],
[1001702]=[[One-click is a steal!]],
[1001703]=[[One-click renewal]],
[1001704]=[[Rewards update daily at 00:00（%s）]],
[1001705]='Duration',
[1001706]=[[Days remaining]],
[1001707]=[[Purchase to get]],
[1001708]=[[Daily Claim]],
[1001709]=[[Total obtained]],
[1001710]=[[Weekly Card Daily Reward Reissue]],
[1001711]=[[Dear lord, the [{%s1}] you purchased has unclaimed daily rewards, which are now being reissued via email. There are {%s2} days remaining on the weekly card.]],
[1001712]=[[Daily Chest]],
[1001713]=[[%s day(s)]],
[1001730]=[[Wheat Weekly Card]],
[1001731]=[[EXP Weekly Card]],
[1001732]=[[Gold Weekly Card]],
[1001733]=[[Diamond Weekly Card]],
[1001734]=[[Iron Weekly Card]],
[1001740]=[[1. Weekly Card grants daily rewards during its duration. Reward collection refreshes at 00:00.]],
[1001741]=[[2. Weekly Card can be purchased multiple times to extend its duration.]],
[1001801]=[[First Recharge Rewards]],
[1001802]=[[Skill Preview]],
[1001803]=[[My bow and arrow strike fear into hearts.]],
[1001804]=[[<color=#fff553>S+ Hero Sera</color> & <color=#fff553>Avatar Perk</color>]],
[1001805]=[[Day 1]],
[1001806]=[[Day 2]],
[1001807]=[[Day 3]],
[1001808]=[[Rewards claimed.]],
[1001809]=[[Log in tomorrow to claim]],
[1001810]='%s',
[1001811]=[[Claim in %s]],
[1001851]=[[Complete <color=#ed810f>10-Day</color> Goal]],
[1001852]=[[Get <color=#ed810f>110</color> Free Draws!]],
[1001853]=[[<color=#87f425>Your union is inactive, please change it as soon as possible</color>]],
[1001854]=[[<color=#87f425>Completing daily tasks can increase the number of active people.</color>]],
[1001855]=[[Day {%s1} goal]],
[1001856]='Union',
[1001857]='Claim',
[1001858]=[[Union List]],
[1001859]=[[Complete the prerequisite quest to unlock]],
[1001860]='Countdown:',
[1001861]='Claimable',
[1001862]='(Locked)',
[1001901]=[[Castle Development Pack]],
[1001902]=[[Potion Pack]],
[1001903]=[[Hero Training Pack]],
[1001904]=[[Technology Pack]],
[1001905]=[[Origem Pack]],
[1001906]=[[Gear Enhancement Pack]],
[1001907]=[[Training Pack]],
[1001908]=[[Survivor Recruitment Pack]],
[1001909]=[[Mark Pack]],
[1001920]=[[Large amount of castle construction resources]],
[1001921]=[[Contains Mystic Beast Training materials]],
[1001922]=[[Large amount of hero CP up resources]],
[1001923]=[[Large amount of research speed up and required resources]],
[1001924]=[[Large amount of decoration upgrading resources]],
[1001925]=[[Large amount of gear enhancement resources]],
[1001926]=[[Contains training items and speed up resources]],
[1001927]=[[Large amount of survivor recruitment items]],
[1001928]=[[Contains mystic beast training mark resources]],
[1001990]=[[Pack Mall]],
[1002000]=[[A Handful of Diamonds]],
[1002001]=[[A trace amount of diamonds]],
[1002002]=[[Some diamonds]],
[1002003]=[[A Pile of Diamonds]],
[1002004]=[[A Cartload Of Diamonds]],
[1002005]=[[A massive amount of diamonds]],
[1002006]=[[Diamond Store]],
[1002007]=[[Spend {%s1} to purchase {%s2}?]],
[1002008]=[[Dear Lord, you have purchased diamonds in the Diamond Store. Here is your extra diamond reward, please check it!~]],
[1002009]=[[Diamond Shop Rewards]],
[1002051]=[[VIPs can purchase in limited quantities.]],
[1002052]=[[Limited purchases with Union Contribution.]],
[1002053]=[[Unlimited purchases with Diamonds.]],
[1002054]=[[Union Duel]],
[1002055]=[[Union Duel rewards.]],
[1002056]=[[Weekly Special Offer]],
[1002057]=[[Purchase from Weekly Special Offer to get Energy Crystals.]],
[1002058]=[[Hero Pass]],
[1002059]=[[Complete Hero Pass quests to earn Hero Shards.]],
[1002060]=[[Weekly Special Offer]],
[1002061]=[[Purchase in Weekly Specials to earn S+ Hero Universal Shards.]],
[1002062]=[[Bingo: Divine Beast Core]],
[1002063]=[[Seven-day Sign-in: Divine Beast Crystal Core]],
[1002064]=[[Participating in the event can gain combat experience items for the mythical beasts.]],
[1002065]=[[Divine Beast Crystal Core Battle Order]],
[1002066]=[[Complete tasks, earn points, and get corresponding rewards after reaching a certain level!]],
[1002067]=[[Divine Beast Crystal Core Gift Pack]],
[1002068]=[[Weekly Crystal Core Gift Pack]],
[1002069]=[[After opening the crystal core treasure chest, you can use the unwanted crystal cores as combat advancement materials for the divine beast.]],
[1002070]=[[Crystal Core Research Institute]],
[1002071]=[[Go to the Crystal Core Research Institute to make crystal cores]],
[1002501]=[[<size=28>Unlock 4th Team</size>
<size=32>Deploy an extra team!</size>]],
[1002502]=[[Gathering speed for all teams +10%]],
[1002503]=[[All teams take 5% less damage from wild monster attacks]],
[1002504]=[[Get extra teams]],
[1002505]=[[World chat full page translation]],
[1002506]=[[Unlock custom avatar]],
[1002601]=[[Epic Survivor Pack]],
[1002602]=[[Get rare building speed-up survivors now and build one step ahead!]],
[1002603]=[[Master's Hammer Pack]],
[1002604]=[[Obtain orange decorations to enhance construction speed!]],
[1002605]=[[Lv. 7 Castle Value Pack]],
[1002606]=[[Lv. 10 Castle Value Pack]],
[1002607]=[[Lv. 13 Castle Value Pack]],
[1002608]=[[Lv. 16 Castle Value Pack]],
[1002609]=[[Lv. 23 Castle Value Pack]],
[1002610]=[[Lv. 25 Castle Value Pack]],
[1002611]=[[Lv. 26 Castle Value Pack]],
[1002612]=[[Get massive resources immediately to help improve your strength!]],
[1002613]=[[Return to the Top Pack]],
[1002614]=[[Get resources immediately and quickly improve your strength!]],
[1002615]=[[Sword of Destiny Pack]],
[1002616]=[[Get Legendary Destiny Gear and massive training resources immediately]],
[1002617]=[[Destiny Equipment Pack]],
[1002618]=[[Get Legendary Destiny Gear and massive training resources immediately]],
[1002619]=[[Golden Marshal Pack]],
[1002620]=[[Get orange decorations immediately and massive training resources!]],
[1002621]=[[Sera Pack]],
[1002622]=[[Fenixia Pack]],
[1002623]=[[Andrew Pack]],
[1002624]=[[Daphne Pack]],
[1002625]=[[Belial Pack]],
[1002626]=[[Yord Pack]],
[1002627]=[[Rexar Pack]],
[1002628]=[[Sophia Pack]],
[1002629]=[[Sparta Pack]],
[1002630]=[[Monica Pack]],
[1002631]=[[Valkyr Pack]],
[1002632]=[[Alvarez Pack]],
[1002633]=[[Kataras Pack]],
[1002634]=[[Mirana Pack]],
[1002635]=[[Obtain hero shards and accelerate the improvement of hero strength!]],
[1002636]=[[Enhancement Value Pack]],
[1002637]=[[Massive Enhancement Stones to Boost Gear!]],
[1002638]=[[Lv. 19 Castle Value Pack]],
[1002639]=[[Lv. 21 Castle Value Pack]],
[1002640]=[[Lv. 28 Castle Value Pack]],
[1002690]=[[Building Time Reduction]],
[1002691]='20min',
[1002692]=[[Purchase Limit:]],
[1002701]=[[Dragonic's Gift]],
[1002702]=[[Gather shards to sharpen your edge in combat.]],
[1002703]=[[Limited-time Offer:]],
[1002704]=[[<size=58>Super Tank</size> <size=36>Rapid Ascension</size>]],
[1002705]=[[Dragonic's Gift I]],
[1002706]=[[Dragonic's Gift II]],
[1002707]=[[Dragonic's Gift III]],
[1002708]=[[Dragonic's Gift IV]],
[1002709]=[[Dragonic's Gift V]],
[1002721]=[[Combat Favorite Balanced Offense & Defense]],
[1002722]=[[Must-Have Tank Team Protection]],
[1002723]=[[Super Core Damage Dealer]],
[1002724]=[[Peak DPS Powerhouse]],
[1002725]=[[Massive Boost to Team ATK]],
[1002726]=[[Mitigate Enemy Damage]],
[1002727]=[[Team Guardian]],
[1002728]=[[Super AoE · Team Anchor]],
[1002729]=[[Insane Single-Target · Damage & Control]],
[1002730]=[[Super DMG Output · Core of the Team]],
[1002731]=[[Strong Taunt · Reduce Incoming Damage]],
[1002732]=[[All-Rounder in Front Row · Core Defender]],
[1002733]=[[Special Mechanic · Back Row Slayer]],
[1002734]=[[Steady DPS · Grows Stronger in Battle]],
[1002735]=[[Super AoE]],
[1002736]='CP',
[1002740]='Used',
[1002741]='Used',
[1002839]='Used',
[1002840]=[[Battle Plan]],
[1002841]=[[Start Battle]],
[1002842]=[[Union Trade Airship]],
[1002843]=[[Times Plundered]],
[1002844]=[[Next Stop Countdown]],
[1002845]=[[Defending Troops]],
[1002846]=[[No Passengers Onboard]],
[1002847]=[[Total Earnings This Trip]],
[1002848]=[[Plunders Today: {%s1}/{%s2}]],
[1002849]='Plunder',
[1002850]=[[Insufficient time since joining.]],
[1002851]='Passenger',
[1002852]=[[Union List]],
[1002853]=[[Arrival Time:]],
[1002854]=[[Airship In Progress]],
[1002855]=[[Welcome aboard this Union Airship Run!]],
[1002856]=[[Airship Port]],
[1002857]=[[Battle Log]],
[1002858]=[[Successfully repelled an attack from <color=#ed810f>{%s1}</color>!]],
[1003001]=[[Intel Pass]],
[1003002]=[[Intel Pass Info]],
[1003003]=[[1. Earn Intelligence Points during the event to claim rewards.]],
[1003004]=[[2. Complete quests to earn points. Purchasing the Pass unlocks bonus points from quest rewards.]],
[1003005]=[[3. Any unclaimed rewards will be sent via mail at the end of the event.]],
[1003006]=[[Intel Pass Reward Reissue]],
[1003007]=[[Dear Lord, the Intel Pass event has ended. Unclaimed rewards have been sent to your inbox.]],
[1003008]=[[Intel Chest]],
[1003011]=[[2nd Construction Queue]],
[1003012]=[[3rd Construction Queue]],
[1003013]=[[4th Construction Queue]],
[1003014]=[[Intel Pack]],
[1003015]=[[2nd Research Center Pack]],
[1003016]=[[Quick Purchase Daily Special Offers]],
[1003017]=[[Daily Special Basic Pack]],
[1003018]=[[Daily Special Intermediate Pack]],
[1003019]=[[Daily Special Advanced Pack]],
[1003020]=[[Quick Purchase All Weekly Cards]],
[1003021]=[[Enhancement Stone Pack I]],
[1003022]=[[Enhancement Stone Pack II]],
[1003023]=[[Enhancement Stone Pack III]],
[1003024]=[[Enhancement Stone Pack IV]],
[1003025]=[[Enhancement Stone Pack V]],
[1003026]=[[EXP Pack I]],
[1003027]=[[EXP Pack II]],
[1003028]=[[EXP Pack III]],
[1003029]=[[EXP Pack IV]],
[1003030]=[[EXP Pack V]],
[1003031]=[[Skill EXP Book Pack I]],
[1003032]=[[Skill EXP Book Pack II]],
[1003033]=[[Skill EXP Book Pack III]],
[1003034]=[[Skill EXP Book Pack IV]],
[1003035]=[[Skill EXP Book Pack V]],
[1003036]=[[Super Airship Pack]],
[1003110]='Used',
[1003111]=[[Badge Market]],
[1003112]=[[1. Use Badges during the event to redeem a variety of rare items.]],
[1003113]=[[2. Badges can be obtained from packs. Accumulating them unlocks milestone rewards.]],
[1003114]=[[3. Unused Badges will carry over to the next Market event but won't count toward milestone progress.]],
[1003115]=[[4. Any unclaimed milestone rewards will be sent via mail after the event ends.]],
[1003116]=[[Redeem badges for a variety of items.]],
[1003117]=[[Event Time Remaining: %s]],
[1003118]=[[Daily Purchase Limit: %s]],
[1003119]=[[Purchase Gift]],
[1003120]=[[%s Purchases Remaining]],
[1003140]=[[Daily Free Pack]],
[1003141]=[[Value Badge Pack]],
[1003142]=[[Premium Badge Pack]],
[1003143]=[[Rare Badge Pack]],
[1003144]=[[Deluxe Badge Pack]],
[1003145]=[[Super Value Badge Pack]],
[1003150]=[[Badge Market Rewards]],
[1003151]=[[Dear Lord, the Badge Market event has ended. Unclaimed milestone rewards have been sent to your inbox.]],
[1003152]=[[Purchase Pack]],
[1003153]='Return',
[1003200]='Used',
[1003201]='Survey',
[1003202]=[[Survey Rewards]],
[1003203]=[[Fill Out]],
[1003301]='Open',
[1003302]=[[Skip ]],
[1003303]=[[Rewards Preview]],
[1003304]=[[Obtain Keys]],
[1003305]=[[Not obtained]],
[1003306]=[[Remaining summons: {%s1}]],
[1003307]=[[Summon {%s1}]],
[1003308]=[[Free Summon {%s1}]],
[1003309]=[[Common Key Pack]],
[1003310]=[[Superior Key Pack]],
[1003311]=[[Advanced Key Pack]],
[1003312]=[[Epic Key Pack]],
[1003313]=[[Mythic Key Pack]],
[1003314]=[[Discard the Chest?]],
[1003315]=[[Don't remind me again today]],
[1003316]=[[Max chests. Further summons won't drop more Miracle Chests. Please discard undesired chests to make room for new ones. Summon anyway?]],
[1003317]=[[Rewards Preview]],
[1003318]=[[Open Chest]],
[1003319]=[[Current chest contains the following:]],
[1003320]=[[Chest Key Pack]],
[1003321]=[[A chest summon grants 10 points. Every Key used grants 5 points.]],
[1003322]=[[Item Rewards]],
[1003323]=[[(Each draw guarantees one of the following)]],
[1003324]=[[Chest Rewards]],
[1003325]=[[(Each draw has a chance to obtain)]],
[1003326]=[[Drop rate {%s1}]],
[1003327]=[[Each chest contains one of the following rewards:]],
[1003328]='Rules',
[1003329]=[[1. During the event, spend Diamonds to draw rewards. One free summon is available daily. Each summon guarantees item rewards, with a chance to obtain Miracle Chests as a bonus.
2. Opening Miracle Chests requires Keys. Higher-quality Miracle Chests offer better rewards but also cost more Keys.
3. Lords can possess up to 4 unopened Miracle Chests. If the limit is reached, no new Miracle Chests can be obtained from summons.
4. Opening Miracle Chests has a 5% chance to double rewards.
5. During the event, summoning and opening Miracle Chests grants points. Accumulate points to unlock milestone rewards.
6. Each summon grants 10 points. Every Key used grants 5 points.]],
[1003330]=[[Insufficient summons for the day.]],
[1003331]=[[Miracle Chest]],
[1003332]=[[Points required.]],
[1003333]=[[Daily Free Pack]],
[1003334]=[[Miracle Chest Reward Reissue]],
[1003335]=[[Dear Lord, the Miracle Chest event has ended. Your unclaimed rewards have been sent via mail.]],
[1003400]=[[Ruins Battlefield]],
[1003401]=[[Ruins Core]],
[1003402]=[[Energy Furnace]],
[1003403]=[[Energy Furnace I]],
[1003404]=[[Energy Furnace II]],
[1003405]=[[Research Center]],
[1003406]='Watchtower',
[1003407]=[[Field Hospital]],
[1003408]=[[Field Hospital I]],
[1003409]=[[Field Hospital II]],
[1003410]=[[Field Hospital III]],
[1003411]=[[Field Hospital IV]],
[1003412]='Barracks',
[1003413]=[[Mercenary Camp]],
[1003414]=[[Energy Mine]],
[1003415]=[[Ruins Chest]],
[1003416]='Sanctuary',
[1003417]=[[Battlefield Hospital]],
[1003418]='Rules',
[1003419]='Building',
[1003420]='Other',
[1003421]='Backstory',
[1003422]=[[A once-in-a-millennium sandstorm tore open the seal of the Sahara, revealing an obsidian obelisk carved with unknown symbols. The Global Archaeological Union's "Eye of Horus" detected continuous pulse signals being transmitted into deep space—yet every warzone government remains eerily silent...\nNo nation has acknowledged any connection to the ancient site, but relic fragments, still coated in desert sand, have already surfaced on Cairo's black market. Deep in the Amazon, a powerful conglomerate has activated the "Prometheus Protocol." Satellite imagery reveals that at least seven armed expedition teams are advancing toward the coordinates, braving the sandstorm.]],
[1003423]=[[Event Overview]],
[1003424]=[[1. Two Unions are matched onto the same battlefield—fighting for control over the Ruins Core.\n2. Unions earn [Battlefield Points] by holding buildings over time. The side with the most points secures battlefield victory.\n3. Due to mysterious forces at play, your soldiers will <color=#FF0000>not die</color> in battle. All gravely wounded soldiers will be automatically healed for free after the battle ends.\n4. On the battlefield, Lords will receive <color=#FF0000>1 free relocation chance</color> at set intervals.\n5. Each Union may dispatch up to <color=#FF0000>20 Lords</color> to the battlefield. At least <color=#FF0000>1 Lord</color> must be dispatched to initiate matchmaking.]],
[1003425]='Store',
[1003426]=[[Main Rewards]],
[1003427]=[[Registration ends at]],
[1003428]=[[Battle ends in]],
[1003429]=[[Matching other Unions]],
[1003430]=[[Waiting for the battle to start]],
[1003431]=[[Event ends in]],
[1003432]=[[This event has ended!]],
[1003433]=[[Only top %s Unions by CP may register]],
[1003434]='Register',
[1003435]=[[Enter Battlefield]],
[1003436]='Spectate',
[1003437]=[[Local Time]],
[1003438]=[[Participants: %s/%s]],
[1003439]=[[Operation only available to R4 members or above.]],
[1003440]=[[There may be delays in data refresh]],
[1003441]=[[Cannot re-enter after leaving the battlefield!]],
[1003442]='Squad',
[1003443]='You',
[1003444]=[[Battle Day:]],
[1003445]='Guide',
[1003446]=[[The Ruins Core of a lost civilization has been unearthed deep in the ancient desert! Archaeologists have deciphered inscriptions revealing that this millennia-old, sand-buried obelisk contains a "Genesis Engine" powerful enough to rewrite human history. The Global Archaeological Union has deployed elite troops to seize this relic of unimaginable technological power!\nLords, beware! The sandstorm is intensifying, and the obelisk's protective barrier is nearing collapse. Special forces from multiple factions are clashing on the outskirts—we must break through the ruins' perimeter first!\nMay the Goddess of Wisdom guide your tactics, my Lord. This is not just a test of might—it is a decision that will shape humanity's future!]],
[1003447]=[[[Starting Area] – Due to the sandstorm, we can only enter the battlefield from the outskirts of the ruins. During the battle, collect as many [Battlefield Points] as possible to secure final victory!]],
[1003448]=[[[Ruins Core] – This massive structure stands at the center of the ruins, radiating ancient and powerful energy. The Union that maintains control over it will continuously earn large amounts of [Battlefield Points].]],
[1003449]=[[[Energy Furnace] – Two ancient furnaces are located near each side's starting base. They are the most important resource points on the battlefield that generate [Battlefield Points] continuously! Make sure to capture and defend them as a priority!]],
[1003450]=[[[Strategic Facility] – Many ancient structures hidden within the ruins can grant powerful buffs to your troops.\n[Field Hospital] speeds up soldier recovery to maintain your troops' combat strength.\n[Research Center] unlocks more free relocation chances, allowing flexible tactical adjustments.\n[Watchtower] greatly boosts the speed of capturing bases. Seize the advantage quickly!]],
[1003451]=[[[Battlefield Points] – Controlled bases continuously generate [Battlefield Points], which accumulate in the occupier's total score. However, if a base is lost, excess points will scatter across the battlefield, awaiting new contenders!]],
[1003452]=[[[Collect Points] – After an enemy base falls, any [Battlefield Points] beyond the limit will scatter around it. Both sides can send troops to collect them.]],
[1003453]=[[[Energy Mine] – Ancient energy crystals will randomly appear across the battlefield. Send soldiers to gather them to earn [Battlefield Points]. They may appear anywhere, so keep scouting and seize the initiative!]],
[1003454]=[[Event Phases]],
[1003455]=[[[Participants]\n1. Only the top <color=#FF0000>32</color> Unions by total CP on the server may participate in each event.\n2. R4 or R5 Union members must decide on the battle time, official participants, and substitutes during the registration phase.\n3. Each Union may assign up to 20 official participants and 10 substitutes. Official participants have priority access to the battlefield.\n\n[Entering the Battlefield]\nPlayers must meet all of the following conditions to enter the battlefield:\n· Be an official participant or substitute for the battle\n· Belong to the registered Union\n· Have their Castle located outside the Contaminated Area near the Kingdom\n\n[Battle Phases]\nPreparation Phase: Participants from both sides may enter the battlefield's safe zone. Allies are marked in blue and enemies in red. Players cannot be attacked inside the safe zone.\n<color=#FF0000>Phase 1:</color> The Energy Furnace, Research Center, Watchtowers, and Field Hospitals become available for occupation. Occupying them grants [Battlefield Points].\n<color=#FF0000>Phase 2:</color>The Ruins Core, Barracks, and Mercenary Camps unlock for occupation. Secure them to earn [Battlefield Points] for your Union.\n<color=#FF0000>Phase 3:</color> Energy Mines will spawn randomly across the map. Gather them to earn [Battlefield Points].\n\nThe side with the most [Battlefield Points] at the end will claim victory.\n\nNote: After a building has been occupied for a certain period, it will produce excess [Battlefield Points] beyond its storage capacity. Capturing these buildings allows you to plunder these excess [Battlefield Points].]],
[1003456]='Union',
[1003457]='Me',
[1003458]=[[First Occupation Points]],
[1003459]=[[Occupation Output]],
[1003460]=[[Bonus Effect]],
[1003461]=[[The Ruins Core will become accessible some time after the battle begins. Occupying it grants a large amount of [Battlefield Points], making it the most critical building for securing victory.]],
[1003462]=[[The Energy Furnace will open at the start of the battle. Occupy it to gain a large amount of [Battlefield Points]; it is the main source of [Battlefield Points] production.]],
[1003463]=[[The Research Center will open at the start of the battle. Occupy it to gain a small amount of [Battlefield Points] and increase the frequency of your free relocations on the battlefield.]],
[1003464]=[[Free relocation frequency increases by 100% in the battlefield!]],
[1003465]=[[The Watchtower will open at the start of the battle. Occupy it to gain a small amount of [Battlefield Points] and increase the point output of all your occupied buildings by 10%!]],
[1003466]=[[Your occupied buildings will produce Battlefield Points 10% faster!]],
[1003467]=[[The Field Hospital will open at the start of the battle. Occupy it to gain a small amount of [Battlefield Points] and speed up the recovery of wounded soldiers.]],
[1003468]=[[After occupation, 15 soldiers of any level will recover every 10s.]],
[1003469]=[[The Barrack will become accessible some time after the battle begins. Occupy it to gain [Battlefield Points] and significantly enhance your heroes' stats!]],
[1003470]=[[Increases heroes' ATK, DEF, and HP by 15%]],
[1003471]=[[The Mercenary Camp will become accessible some time after the battle begins. Occupy it to gain [Battlefield Points] and weaken enemy heroes!]],
[1003472]=[[Decreases all enemy heroes' ATK, DEF, and HP by 15%]],
[1003473]=[[When control of a building changes, any excess [Battlefield Points] will scatter nearby. Dispatch scouting troops to collect them!]],
[1003474]=[[Becomes accessible some time after the battle begins. Dispatch troops to gather and earn [Battlefield Points]!]],
[1003475]=[[Eligibility Requirements:]],
[1003476]=[[Special Rules]],
[1003477]=[[Ruins Battlefield Rewards Preview]],
[1003478]=[[The event has not started yet.]],
[1003479]=[[Personal Rewards]],
[1003480]=[[Union Rewards]],
[1003481]=[[Participation Rewards]],
[1003482]=[[Victory Rewards]],
[1003483]=[[Defeat Rewards]],
[1003484]=[[Points Requirement]],
[1003485]='Rewards',
[1003486]=[[Current Points]],
[1003487]=[[All members of the winning Union will receive the following rewards:]],
[1003488]=[[All members of the losing Union will receive the following rewards:]],
[1003489]=[[Battle Time]],
[1003490]=[[You can select any of the following time slots to participate in the "Ruins Battlefield" battle!\nDuring the registration phase, time slots can be modified anytime. Once the registration phase ends, no changes are allowed!]],
[1003491]=[[Server Time]],
[1003492]=[[Your Time Zone]],
[1003493]=[[Confirm Time]],
[1003494]=[[Select Participants]],
[1003495]='Participants',
[1003496]='Substitutes',
[1003497]=[[Select Battle Time]],
[1003498]=[[Request to Join Battle]],
[1003499]=[[Please select all the battle sessions you wish to participate in (multiple selections are allowed):]],
[1003500]=[[Participants are full! Please unselect other members before proceeding.]],
[1003501]=[[Players with a Castle Level below 15 cannot participate.]],
[1003502]=[[Players who joined the Union less than 24 hours ago cannot participate.]],
[1003503]=[[Preparation Phase]],
[1003504]=[[Battle Map]],
[1003505]=[[Reward Phase]],
[1003506]=[[Status Overview]],
[1003507]=[[Relocation Speedup]],
[1003508]=[[Free relocation frequency increases by 100% in the battlefield]],
[1003509]=[[Occupation Speedup]],
[1003510]=[[Reduces the time required to capture buildings by 50%]],
[1003511]=[[Recovers 15 soldiers of any level every 10s]],
[1003512]=[[Increases heroes' ATK, DEF, and HP by 15%]],
[1003513]=[[Decreases all enemy heroes' ATK, DEF, and HP by 15%]],
[1003514]=[[Battlefield Details]],
[1003515]=[[Our Side]],
[1003516]='Enemy',
[1003517]='Rank',
[1003518]='Points',
[1003519]='Lord',
[1003520]=[[Leave Battlefield]],
[1003521]='Hospital',
[1003522]=[[The healing queue has unlimited capacity and does not consume Food, Iron, or Gold.\nSoldiers healed in the Field Hospital will not appear in this healing queue.]],
[1003523]=[[Gravely Wounded Soldiers]],
[1003524]=[[No Injured Soldiers]],
[1003525]=[[Healing in Progress]],
[1003526]=[[Speed Up]],
[1003527]=[[Total Healed]],
[1003528]=[[Healing Speed: %s soldiers/min]],
[1003529]=[[1. Occupying hospitals on the battlefield accelerates the recovery speed of soldiers.\n2. The displayed number of wounded soldiers may have a slight delay.]],
[1003530]='Claim',
[1003531]='Healed',
[1003532]=[[To be Healed]],
[1003533]=[[Total Wounded]],
[1003534]='Relocate',
[1003535]=[[Occupying [Research Center] reduces relocation cooldown by 50%.]],
[1003536]=[[1. On the battlefield, players receive 1 free relocation every 2 minutes. After use, a cooldown will apply.\n2. Players cannot relocate again until the cooldown ends.\n3. Occupying the Research Center reduces the relocation cooldown by 50%.]],
[1003537]=[[You can use <color=#FF0000>1</color> free relocation without consuming items.]],
[1003538]=[[Relocation available in %s. Unable to relocate now.]],
[1003539]=[[Production Efficiency:]],
[1003540]='Produced:',
[1003541]='Occupied:',
[1003542]='Unoccupied',
[1003543]=[[After 60s of occupation, some of the generated points will remain on the building and can be plundered by enemies.\nCurrent non-plunderable point generation rate: %s/s\nCurrent plunderable point generation rate: %s/s\nBuilding point production rate can be boosted by Watchtowers.]],
[1003544]=[[After 60s of occupation, some of the generated points will remain on the building and can be plundered by enemies.\nCurrent non-plunderable points: %s\nCurrent plunderable points: %s]],
[1003545]=[[Cannot be viewed from outside the battlefield]],
[1003546]=[[Battle Status]],
[1003547]=[[My Troops]],
[1003548]=[[Enemy Troops]],
[1003549]='Marching...',
[1003550]=[[In Garrison]],
[1003551]='ATK',
[1003552]=[[Rally ATK]],
[1003553]='Scout',
[1003554]='Go',
[1003555]='Neutral',
[1003556]=[[Enemy Occupied]],
[1003557]=[[Ally Occupied]],
[1003558]=[[Lv. 1 Energy Mine]],
[1003559]='Gatherer:',
[1003560]='N.A.',
[1003561]=[[Total Battlefield Points]],
[1003562]=[[Kill Points]],
[1003563]=[[Siege Points]],
[1003564]=[[Gathering Points]],
[1003565]=[[Average Score:]],
[1003566]=[[Highest Score:]],
[1003567]=[[Personal Data]],
[1003568]=[[Castles in the Sanctuary cannot be attacked or scouted]],
[1003569]=[[Protection Status]],
[1003570]=[[[Ruins Battlefield] Battle Results!]],
[1003571]=[[Dear Lord,\nThe fierce battle on the [Ruins Battlefield] has come to an end. Many Lords stood out with outstanding performances—let us thank them for their efforts and learn from their example!\nThe following players showed exceptional performance on the battlefield:]],
[1003572]=[[[Ruins Battlefield] Victory - Union-wide Rewards!]],
[1003573]=[[Dear Lord,\nThanks to the heroic efforts of your allies, your Union has claimed victory in the [Ruins Battlefield]! All members will receive the following rewards. Raise a cheer for our brave warriors!]],
[1003574]=[[[Ruins Battlefield] Participation - Union-wide Rewards!]],
[1003575]=[[Dear Lord,\nAlthough your Union was defeated in the [Ruins Battlefield], your allies fought bravely until the very end. Their courage has earned them honor and well-deserved rewards. Let us salute their determination!]],
[1003576]=[[[Ruins Battlefield] Victory Rewards!]],
[1003577]=[[Dear Lord,\nYour fearless efforts helped secure a glorious victory for your Union in the [Ruins Battlefield]! Thanks for your active participation and these rewards are well deserved by champions like you!]],
[1003578]=[[[Ruins Battlefield] Defeat Rewards!]],
[1003579]=[[Dear Lord,\nThough defeated in the [Ruins Battlefield], you fought valiantly and never gave up. Thanks for your active participation and these rewards are yours to claim!]],
[1003580]=[[[Ruins Battlefield] Personal Victory Rewards!]],
[1003581]=[[Dear Lord,\nYour fearless efforts helped secure a glorious victory for your Union in the [Ruins Battlefield]! Thanks for your active participation and these rewards are well deserved by champions like you!]],
[1003582]=[[[Ruins Battlefield] Personal Defeat Rewards!]],
[1003583]=[[Dear Lord,\nThough defeated in the [Ruins Battlefield], you fought valiantly and never gave up. Thanks for your active participation and these rewards are yours to claim!]],
[1003584]=[[Occupation Points]],
[1003585]=[[1. Only Unions ranked in the top 32 by total CP may participate.\n2. Lords must have a Castle Level above 15 to join the battle.]],
[1003586]=[[1. Defeating enemy soldiers grants a large number of [Personal Points], but no [Battlefield Points]. The higher the level of the defeated enemy soldiers, the more points you gain.\n2. Players receive 1 free relocation every 2 minutes. After use, a cooldown applies during which relocation is unavailable.\n3. Soldiers <color=#FF0000>will not die</color> on the battlefield—they will be gravely wounded and sent to the hospital, which has unlimited capacity. All gravely wounded soldiers will recover after the battle ends.\n4. Lords who leave the battlefield forcibly <color=#FF0000>cannot re-enter</color>. Temporary exits are allowed without a wait timer.\n5. When entering the battlefield, the independent Drill Ground will be automatically filled with the highest-level soldiers the Lord can train. These soldiers will not appear in the Lord's Home Drill Grounds or be taken out of the battlefield after the battle ends.\n6. All bonuses from Technology, VIP, Castle Appearances, Decorations, and Mystic Beasts will take effect during the battle.]],
[1003587]=[[Gameplay Tutorial]],
[1003588]=[[Truth and power lie hidden within the ruins!
1. On the Ruins Battlefield, your soldiers may be gravely wounded and sent to the hospital, but they will not truly perish.
2. All gravely wounded soldiers will automatically recover after the battle ends.
3. Ruins energy opens a free relocation channel every 2 minutes. Use it wisely to reposition your strategy and catch your opponents off guard!
4. The Castle Durability on the battlefield is 4,000.]],
[1003589]=[[Tap the [Points Panel] to view battlefield details. The side with the most points when the countdown ends wins!]],
[1003590]=[[Tap the [Battlefield Map] to check battlefield status and plan your battle strategy.]],
[1003591]=[[Wounded soldiers can be healed here.]],
[1003592]='History',
[1003593]='Log',
[1003594]='Personnel',
[1003595]=[[No Union at the moment.]],
[1003596]=[[Relocating to this location is not allowed. Please choose different coordinates.]],
[1003597]=[[Castles in safe areas cannot be attacked or scouted.]],
[1003598]='Squad',
[1003599]='Garrison',
[1003600]='Occupying',
[1003601]=[[Ruins War started]],
[1003602]='Register',
[1003603]='+{%s1}/s',
[1003604]=[[1-Hour Shield]],
[1003605]=[[Your castle cannot be attacked or scouted by players for 1 hour.\n<color=#E63838>Please note that troops outside the castle remain vulnerable.</color>]],
[1003606]=[[Point Speedup]],
[1003607]=[[Ruins War has started!]],
[1003608]=[[Ruins War in battle!]],
[1003609]=[[Only participants and substitutes are allowed to join the Ruins War:\nParticipants can enter the battlefield at any time.\nSubstitutes can only enter after the battle preparation phase.]],
[1003610]=[[Your Union is currently ranked {%s1} and does not meet the registration requirement.]],
[1003611]=[[In the Ruins War battlefield, healing does not consume resources. However, speedup items are still needed to accelerate healing.]],
[1003612]=[[The Ruins War has ended. Please wait for the next event.]],
[1003613]=[[You did not participate in the Ruins War.]],
[1003614]=[[There may be delays in data refresh.]],
[1003615]=[[Ruins War is about to begin.]],
[1003616]=[[Ruins Battlefield (Combat in Progress)]],
[1003617]=[[[Including You] ]],
[1003618]=[[Get ready!]],
[1003619]=[[My Lord, all military supplies are ready. Please give the order!]],
[1003620]=[[To war!]],
[1003621]=[[Unit quantity]],
[1003622]='Ready',
[1003623]=[[Battlefield Healing Buff]],
[1003624]=[[Enemy defending troops]],
[1003625]=[[Allied defending troops]],
[1003626]=[[Enemy marching teams]],
[1003627]=[[Allied marching teams]],
[1003628]=[[{%s1} dispatched {%s2} to Squad {%s3} and appointed them as {%s4}.]],
[1003629]=[[{%s1} removed {%s2} from Squad {%s3}.]],
[1003630]=[[{%s1} changed the participation time of Squad {%s2} to: {%s3} (server time).]],
[1003631]=[[All members on the winning side who enter the battlefield will receive:]],
[1003632]=[[All members on the losing side who enter the battlefield will receive:]],
[1003633]=[[Battle History]],
[1003634]=[[Victories: {%s1}]],
[1003635]=[[Battles: {%s1}]],
[1003636]=[[Defeats: {%s1}]],
[1003637]=[[Last Time]],
[1003638]=[[Highest Total Points]],
[1003639]=[[Highest Occupation Points Holder]],
[1003640]=[[Highest Gathering Points Holder]],
[1003641]=[[Highest Siege Points Holder]],
[1003642]=[[Highest Kill Points Holder]],
[1003643]=[[Reward conditions not met]],
[1003701]=[[Verna's Gift]],
[1003702]=[[Log in daily for abundant rewards to boost Verna's growth!]],
[1003703]=[[Total rewards after purchase:]],
[1003704]=[[Purchase now to obtain]],
[1003705]=[[<size=48>Log in to Obtain Heroes</size>]],
[1003706]=[[<size=28>Once-in-a-Lifetime: Boost Verna's Awakening</size>]],
[1003707]=[[Verna's Gift Reward Reissue]],
[1003708]=[[Dear Lord, your unclaimed rewards in Verna's Gift have been sent via mail.]],
[1003709]=[[1. Reach daily login milestones to claim rewards.]],
[1003710]=[[2. Purchase Verna's Gift for extra rewards.]],
[1003711]=[[3. Any unclaimed rewards will be sent via mail at the end of the event.]],
[1003712]='Day',
[1003713]=[[Event Duration:]],
[1003750]='Used',
[1003751]=[[Summon Gift]],
[1003752]=[[Perform hero summons for generous rewards.]],
[1003753]=[[Summon Gift Reward Reissue]],
[1003754]=[[Dear Lord, the Summon Gift event has ended. Unclaimed rewards have been sent via mail.]],
[1003755]=[[1. Accumulate summons in the Summon Hall to claim rewards.]],
[1003756]=[[2. Any unclaimed rewards will be sent via mail at the end of the event.]],
[1003757]=[[Obtain Items]],
[1003800]='Placeholder',
[1003801]=[[Gear Specials]],
[1003802]='Floor',
[1003803]='<size=32>%s1</size>/<size=26>%s2</size>',
[1003804]=[[Specials Quest]],
[1003805]=[[Key Pack]],
[1003806]=[[Start Treasure Hunt]],
[1003807]=[[Quick Treasure Hunt]],
[1003808]=[[Specials Key]],
[1003809]=[[Specials Rewards]],
[1003810]=[[%s have been consumed in the event]],
[1003811]=[[Stored Rewards]],
[1003812]=[[Rewards Claimed from Coffers]],
[1003813]='Claim',
[1003814]='Rewards',
[1003815]=[[Rewards obtained will be stored up here]],
[1003816]=[[Open Coffers to obtain massive rewards]],
[1003817]=[[Open Coffers in the Specials Vault to win rewards]],
[1003818]=[[Drop Rate Preview]],
[1003819]=[[Current Vault Floor: %s]],
[1003820]=[[Treasure Hunts]],
[1003821]=[[Grand Prize Drop Rate]],
[1003822]=[[Grand Prize Available on This Floor]],
[1003823]=[[Select Grand Prize]],
[1003824]=[[Save as Preset Reward]],
[1003825]=[[Reward Option]],
[1003826]=[[Treasure Hunting]],
[1003827]=[[Obtained from quests]],
[1003850]=[[Daily Free Specials]],
[1003851]=[[Daily Premium Specials Pack]],
[1003852]=[[Daily Rare Specials Pack]],
[1003853]=[[Daily Advanced Specials Pack]],
[1003854]=[[Daily Deluxe Specials Pack]],
[1003855]=[[Daily Superb Specials Pack]],
[1003856]=[[Share Invitation]],
[1003857]=[[Grand Prize]],
[1003870]=[[1. Opening one Coffer in the Vault requires 1 Specials Key, which can be obtained via Specials Quests or by purchasing Specials Packs.]],
[1003871]=[[2. Select the <color=#DB3100>grand prize for this vault floor</color> before starting the treasure hunt.]],
[1003872]=[[3. After unlocking the grand prize in a floor's Coffer, you can proceed to the next floor of the vault. Each Specials event allows for treasure hunting in up to 200 floors.]],
[1003873]=[[4. Reach vault floors <size=28>5/10/20/30/40/60/80/100</size> to claim floor achievement rewards. Unclaimed rewards will be sent via mail at the end of the event.]],
[1003874]=[[5. Specials Quests reset at 00:00 each day, and any unclaimed rewards will be sent via mail upon reset.]],
[1003875]=[[6. Each Coffer Draw randomly grants one remaining reward based on weighted odds.]],
[1003876]=[[7. All rewards obtained will go to the Reward Bag. Unclaimed rewards will be mailed after the event ends.]],
[1003877]=[[<color=#444FEC>Treasure Hunt Common Rewards Probability</color>]],
[1003878]=[[Upgrade Crystal ×10 (10)]],
[1003879]=[[Upgrade Crystal ×20 (10)]],
[1003880]=[[Gear Specials Event Reward Reissue]],
[1003881]=[[Dear Lord, your unclaimed Coffer rewards in the Gear Specials event have been sent via mail.]],
[1003882]=[[Gear Specials Floor Achievement Reward Reissue]],
[1003883]=[[Dear Lord, your unclaimed floor achievement rewards in the Gear Specials event have been sent via mail.]],
[1003884]=[[Gear Specials Reward Reissue]],
[1003885]=[[Dear Lord, your unclaimed quest rewards in the Gear Specials event have been sent via mail.]],
[1003886]=[[Insufficient Specials Keys.]],
[1003887]=[[Please select the grand prize for this vault floor.]],
[1003888]=[[Upgrade Crystal ×50 (10)]],
[1003889]=[[Gold Level Chest ×1 (10)]],
[1003890]=[[Gold Level Chest ×2 (10)]],
[1003891]=[[Gold Level Chest ×5 (10)]],
[1003892]=[[Epic Gear Crystal ×1 (10)]],
[1003893]=[[Epic Gear Crystal ×2 (10)]],
[1003894]=[[Legendary Gear Crystal ×1 (10)]],
[1003895]=[[<color=#DB3100>Treasure Hunt Grand Prize Probability</color>]],
[1003896]=[[1. The grand prize of each vault floor starts with a base weight of 1; weight increases after a certain number of treasure hunts on the same floor.]],
[1003897]=[[After 3 hunts: the grand prize weight becomes 5.]],
[1003898]=[[After 6 hunts: the grand prize weight becomes 10.]],
[1003899]=[[After 9 hunts: the grand prize weight becomes 30.]],
[1003900]=[[2. The grand prize is guaranteed after a certain number of hunts, varying by floor.]],
[1003901]=[[<color=#DB3100>6 hunts</color>: Floors 1，6，11，16，21，26，31，36，41，46，51，56，61，66，71，76，81，86，91，96]],
[1003902]=[[<color=#DB3100>7 hunts</color>: Floors 2，7，12，17，22，27，32，37，42，47，52，57，62，67，72，77，82，87，92，97]],
[1003903]=[[<color=#DB3100>9 hunts</color>: Floors 3，8，13，18，23，28，33，38，43，48，53，58，63，68，73，78，83，88，93，98]],
[1003904]=[[<color=#DB3100>6 hunts</color>: Floors 4，9，14，19，24，29，34，39，44，49，54，59，64，69，74，79，84，89，94，99]],
[1003905]=[[<color=#DB3100>11 hunts</color>: Floors 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95, 100]],
[1003906]=[[You've obtained the grand prize. The next vault floor is unlocked with all rewards reset.]],
[1003907]=[[Tap a Coffer to hunt]],
[1003908]=[[Congratulations! You've completed the treasure hunt in all 200 vault floors!]],
[1004000]='Used',
[1004001]=[[Rescue Mystic Beast]],
[1004002]=[[Rescue Caravan]],
[1004003]=[[Defeat Zombies]],
[1004004]=[[Zombie Siege]],
[1004005]=[[Zombie Horde]],
[1004006]=[[Prerequisite Quest]],
[1004007]='Rescue',
[1004008]='Rescue',
[1004009]=[[Challenge Boss]],
[1004010]=[[Claim Rewards]],
[1004011]=[[Complete the following quest to unlock: Mystic Beast Assistance]],
[1004012]=[[Complete the following quest to unlock: Trade Wagon]],
[1004013]=[[Unlock by completing the quest: Challenge Boss]],
[1004014]=[[Successful rescue grants massive rewards]],
[1004015]=[[Complete the prerequisite quest to unlock]],
[1004016]=[[Challenge Boss to win massive rewards]],
[1004017]=[[Look! That's the Mystic Beast "Borias"! It's surrounded by a horde of zombies. We need to rush to its aid!]],
[1004018]=[[Help! We're a merchant caravan. Save us, and we can trade goods for you. Please!]],
[1004019]=[[A zombie the size of a city tower up ahead? If it wakes up, the city walls won't hold!]],
[1004020]=[[Darn it! First a giant zombie and now a horde of zombies attacking the castle gate?]],
[1004021]=[[(The giant zombie roars deafeningly, followed by the howls of remaining minions.)]],
[1004022]=[[Oh no! The zombie horde is sending a signal! Brace yourself for more of them!]],
[1004023]=[[(Boom! A thunderous crash...) Look! What is that giant beast attacking the castle gate? Take it down, now!]],
[1004024]=[[Upgrade Mystic Beast to Lv. 15]],
[1004025]=[[(The zombies suddenly let out a piercing howl!) They seem to be sending a message, and more are gathering toward the city. We must eliminate zombies in the surrounding area immediately!]],
[1004026]=[[The zombies are gathering in greater numbers! We need to harness the power of the Mystic Beast to clear them out quickly, otherwise the city will be in danger!]],
[1004027]=[[The zombie horde has reached its peak! This is the most dangerous moment. Once we take down this wave, the scattered ones won't be a threat anymore!]],
[1004028]=[[The Union declares war on <color=#319f38>any</color> City.]],
[1004029]=[[Relocate to the Union rally point]],
[1004030]=[[Ugh, it hurts. Is there no end to them?]],
[1004031]=[[I'm under attack!]],
[1004032]=[[I'm injured. Need help here!]],
[1004033]=[[Please help us! We're a caravan!]],
[1004034]=[[Hold on. Rescue will come any minute!]],
[1004035]=[[I smell something delicious!]],
[1004036]=[[The city is under attack!]],
[1004037]=[[The city is about to be breached!]],
[1004038]=[[Complete the following quests to unlock: Claim Rewards]],
[1004039]=[[Zombie Siege: Phase 1]],
[1004040]=[[Zombie Siege: Phase 2]],
[1004041]=[[Zombie Siege: Phase 3]],
[1004042]=[[Save the City: Prologue]],
[1004043]=[[Save the City: The Journey]],
[1004044]=[[Save the City: Finale]],
[1004045]=[[My Lord, the Night Watch risked their lives to deliver this bloodstained note (wax seal clawed open). The city is overrun by zombies! They request urgent aid!]],
[1004046]=[[My Lord! Scouts report the next city is about to fall to the horde. Many survivors are hiding in the ruins! We must march out now!]],
[1004047]=[[Nearly all of the nearby cities have fallen to the zombie horde. We must give it our all to save one more—let's hope it's not too late. May the Holy Light guide us!]],
[1004048]=[[Someone help us!]],
[1004049]=[[Hold on, reinforcements are on the way!]],
[1004050]=[[Save the City Rewards Reissued]],
[1004051]=[[Dear Lord, {%s1} has ended. The following are the rewards you have earned but not yet claimed. Please collect them promptly.]],
[1004052]=[[The Union builds Siege Camp]],
[1004053]=[[Dispatch reinforcements to the Siege Camp]],
[1004054]=[[Siege Camps can only be built after war is declared on a city.]],
[1004055]=[[Siege Camps can only be built after war is declared on a city by R5/R4 members.]],
[1004056]=[[Zombie Siege: Phase 4]],
[1004057]=[[Zombie Siege: Phase 5]],
[1004058]=[[Zombie Siege: Phase 6]],
[1004059]=[[My Lord, our counterattack has stirred up even more trouble. The zombies are howling wildly, and their assault is fiercer than ever. We'll have to grit our teeth and hold the line!]],
[1004060]=[[My bravest Lord, your leadership has lit a fire in our troops! The line's holding—barely—but we're ready for the next, fiercer wave of the zombie horde.]],
[1004061]=[[Guess they didn't learn their lesson. Ready when you are, my Lord—let's grind them into dust!]],
[1004062]=[[Hold to quickly increase quantity]],
[1004063]=[[Hold to quickly decrease quantity]],
[1004200]='Used',
[1004201]='Locked',
[1004202]='Ongoing...',
[1004203]='Completed',
[1004204]=[[Boss respawns in: {%s1}]],
[1004205]=[[Countdown: {%s1}]],
[1004206]=[[{%s1} Opens]],
[1004207]='Contested',
[1004208]=[[Starts in: {%s1}]],
[1004209]=[[Ongoing: {%s1}]],
[1004210]=[[The event has ended, please redeem items as soon as possible.]],
[1004211]='Search',
[1004212]='Go',
[1004213]='View',
[1004214]=[[Boss remaining time: {%s1}]],
[1004215]=[[Please select an attack time]],
[1004216]=[[Preparing: {%s1}]],
[1004217]=[[Not participated]],
[1004218]=[[Event Calendar]],
[1004219]='Today',
[1004220]='Tomorrow',
[1004221]=[[Local Time]],
[1004222]=[[Server Time]],
[1004223]=[[Wait for an R4 member or the Leader to start]],
[1004224]='Important',
[1004225]=[[{%s1} In Progress]],
[1004226]=[[No Events Available]],
[1004227]=[[Switched to Server Time]],
[1004228]=[[Switched to Local Time]],
[1004229]='Ongoing',
[1004230]=[[Coming Soon]],
[1004231]=[[Event Ongoing]],
[1004232]=[[Event starts at {%s1}]],
[1004233]=[[War declaration in progress]],
[1004234]=[[Waiting for Union to declare war]],
[1004235]=[[Please register for the event]],
[1004236]=[[Registering: {%s1}]],
[1004237]=[[Matchmaking: {%s1}]],
[1004238]=[[Battle starts in: {%s1}]],
[1004239]=[[In Battle: {%s1}]],
[1004240]=[[Event Ongoing]],
[1004260]=[[Overlord Incoming]],
[1004261]=[[Castles are more prone to attacks from Zombie Overlords during the event...]],
[1004262]=[[Total number of Zombie Overlords defeated across the server reaches {%s1}]],
[1004263]=[[Number of Lords participating in the attack on Zombie Overlords reaches {%s1}]],
[1004264]=[[First defeat of Lv. {%s1} Zombie Overlord in the server]],
[1004265]=[[Union Threat]],
[1004266]=[[Warzone Threat]],
[1004267]=[[Threat eliminated]],
[1004268]=[[Overlord Incoming has begun. Strike now!]],
[1004269]=[[Eligibility Requirements
1. Lords with Castle Lv. 5 or higher are eligible to participate.

Event Rules
1. Castles are strikingly more prone to attacks from Zombie Overlords during the event. Be prepared!
2. Zombie Overlords will have more concentrated spawn times on the day of the event.
3. Castles will not be forced to move during the event due to Zombie Overlord attacks.
4. When a Zombie Overlord breaches a Castle during the event, the death rate of idle soldiers is reduced.

Rewards
1. First-Kill rewards for Zombie Overlords are now available for the first 3 kills during the event.
2. All qualified Lords may claim rewards once the server-wide missions are completed.
3. At the end of the event, rewards will be distributed based on player rankings for total damage dealt to Zombie Overlords.]],
[1004270]=[[Royal City Guardian]],
[1004271]=[[Overlord Incoming Ranking Rewards]],
[1004272]=[[Dear Lord, congrats! You ranked {%s1} in the Overlord Incoming event. Here are your ranking rewards!]],
[1004273]=[[The next wave of Zombie Overlords will arrive in {%s1}.]],
[1004361]=[[Mystic Beast Value Pack I]],
[1004362]=[[Mystic Beast Value Pack II]],
[1004363]=[[Mystic Beast Value Pack III]],
[1004364]=[[Mystic Beast Value Pack IV]],
[1004365]=[[Mystic Beast Value Pack V]],
[1004366]=[[Fast Development Pack I]],
[1004367]=[[Fast Development Pack II]],
[1004368]=[[Fast Development Pack III]],
[1004369]=[[Fast Development Pack IV]],
[1004370]=[[Fast Development Pack V]],
[1004371]=[[Scientific Research Pack I]],
[1004372]=[[Scientific Research Pack II]],
[1004373]=[[Scientific Research Pack III]],
[1004374]=[[Scientific Research Pack IV]],
[1004375]=[[Scientific Research Pack V]],
[1004376]=[[Super Value EXP Pack I]],
[1004377]=[[Super Value EXP Pack II]],
[1004378]=[[Super Value EXP Pack III]],
[1004379]=[[Super Value EXP Pack IV]],
[1004380]=[[Super Value EXP Pack V]],
[1004381]=[[Resource Grand Pack I]],
[1004382]=[[Resource Grand Pack II]],
[1004383]=[[Resource Grand Pack III]],
[1004384]=[[Resource Grand Pack IV]],
[1004385]=[[Resource Grand Pack V]],
[1004386]=[[Training Pack I]],
[1004387]=[[Training Pack II]],
[1004388]=[[Training Pack III]],
[1004389]=[[Training Pack IV]],
[1004390]=[[Training Pack V]],
[1004391]=[[Sera Pack I]],
[1004392]=[[Sera Pack II]],
[1004393]=[[Sera Pack III]],
[1004394]=[[Sera Pack IV]],
[1004395]=[[Sera Pack V]],
[1004396]=[[Fenixia Pack I]],
[1004397]=[[Fenixia Pack II]],
[1004398]=[[Fenixia Pack III]],
[1004399]=[[Fenixia Pack IV]],
[1004400]=[[Fenixia Pack V]],
[1004401]=[[Andrew Pack I]],
[1004402]=[[Andrew Pack II]],
[1004403]=[[Andrew Pack III]],
[1004404]=[[Andrew Pack IV]],
[1004405]=[[Andrew Pack V]],
[1004406]=[[Daphne Pack I]],
[1004407]=[[Daphne Pack II]],
[1004408]=[[Daphne Pack III]],
[1004409]=[[Daphne Pack IV]],
[1004410]=[[Daphne Pack V]],
[1004411]=[[Belial Pack I]],
[1004412]=[[Belial Pack II]],
[1004413]=[[Belial Pack III]],
[1004414]=[[Belial Pack IV]],
[1004415]=[[Belial Pack V]],
[1004416]=[[Yord Pack I]],
[1004417]=[[Yord Pack II]],
[1004418]=[[Yord Pack III]],
[1004419]=[[Yord Pack IV]],
[1004420]=[[Yord Pack V]],
[1004421]=[[Rexar Pack I]],
[1004422]=[[Rexar Pack II]],
[1004423]=[[Rexar Pack III]],
[1004424]=[[Rexar Pack IV]],
[1004425]=[[Rexar Pack V]],
[1004426]=[[Sophia Pack I]],
[1004427]=[[Sophia Pack II]],
[1004428]=[[Sophia Pack III]],
[1004429]=[[Sophia Pack IV]],
[1004430]=[[Sophia Pack V]],
[1004431]=[[Golden Marshal Pack I]],
[1004432]=[[Golden Marshal Pack II]],
[1004433]=[[Golden Marshal Pack III]],
[1004434]=[[Golden Marshal Pack IV]],
[1004435]=[[Enhancement Value Pack I]],
[1004436]=[[Enhancement Value Pack II]],
[1004437]=[[Enhancement Value Pack III]],
[1004438]=[[Enhancement Value Pack IV]],
[1004439]=[[Enhancement Value Pack V]],
[1004440]=[[Master's Hammer Pack I]],
[1004441]=[[Master's Hammer Pack II]],
[1004442]=[[Master's Hammer Pack III]],
[1004443]=[[Master's Hammer Pack IV]],
[1004444]=[[Master's Hammer Pack V]],
[1004445]=[[Lv.7 Castle Value Pack I]],
[1004446]=[[Lv.7 Castle Value Pack II]],
[1004447]=[[Lv. 10 Castle Value Pack I]],
[1004448]=[[Lv. 10 Castle Value Pack II]],
[1004449]=[[Lv. 13 Castle Value Pack I]],
[1004450]=[[Lv. 13 Castle Value Pack II]],
[1004451]=[[Lv. 16 Castle Value Pack I]],
[1004452]=[[Lv. 16 Castle Value Pack II]],
[1004453]=[[Lv. 19 Castle Value Pack I]],
[1004454]=[[Lv. 19 Castle Value Pack II]],
[1004455]=[[Lv. 21 Castle Value Pack I]],
[1004456]=[[Lv. 21 Castle Value Pack II]],
[1004457]=[[Lv. 23 Castle Value Pack I]],
[1004458]=[[Lv. 23 Castle Value Pack II]],
[1004459]=[[Lv. 25 Castle Value Pack I]],
[1004460]=[[Lv. 25 Castle Value Pack II]],
[1004461]=[[Lv. 26 Castle Value Pack I]],
[1004462]=[[Lv. 26 Castle Value Pack II]],
[1004463]=[[Lv. 28 Castle Value Pack I]],
[1004464]=[[Lv. 28 Castle Value Pack II]],
[1004465]=[[Castle Development Pack I]],
[1004466]=[[Castle Development Pack II]],
[1004467]=[[Castle Development Pack III]],
[1004468]=[[Castle Development Pack IV]],
[1004469]=[[Castle Development Pack V]],
[1004470]=[[Castle Development Pack I]],
[1004471]=[[Castle Development Pack II]],
[1004472]=[[Castle Development Pack III]],
[1004473]=[[Castle Development Pack IV]],
[1004474]=[[Castle Development Pack V]],
[1004475]=[[Mystic Beast Potion Pack I]],
[1004476]=[[Mystic Beast Potion Pack II]],
[1004477]=[[Mystic Beast Potion Pack III]],
[1004478]=[[Mystic Beast Potion Pack IV]],
[1004479]=[[Mystic Beast Potion Pack V]],
[1004480]=[[Mystic Beast Mark Pack I]],
[1004481]=[[Mystic Beast Mark Pack II]],
[1004482]=[[Mystic Beast Mark Pack III]],
[1004483]=[[Mystic Beast Mark Pack IV]],
[1004484]=[[Mystic Beast Mark Pack V]],
[1004485]=[[Hero Training Pack I]],
[1004486]=[[Hero Training Pack II]],
[1004487]=[[Hero Training Pack III]],
[1004488]=[[Hero Training Pack IV]],
[1004489]=[[Hero Training Pack V]],
[1004490]=[[Technology Pack I]],
[1004491]=[[Technology Pack I]],
[1004492]=[[Technology Pack II]],
[1004493]=[[Technology Pack III]],
[1004494]=[[Technology Pack IV]],
[1004495]=[[Technology Pack V]],
[1004496]=[[Technology Pack II]],
[1004497]=[[Technology Pack III]],
[1004498]=[[Technology Pack IV]],
[1004499]=[[Technology Pack V]],
[1004500]=[[Gear Enhancement Pack I]],
[1004501]=[[Gear Enhancement Pack II]],
[1004502]=[[Gear Enhancement Pack III]],
[1004503]=[[Gear Enhancement Pack IV]],
[1004504]=[[Gear Enhancement Pack V]],
[1004505]=[[Training Pack I]],
[1004506]=[[Training Pack II]],
[1004507]=[[Training Pack III]],
[1004508]=[[Training Pack IV]],
[1004509]=[[Training Pack V]],
[1004510]=[[Training Pack I]],
[1004511]=[[Training Pack II]],
[1004512]=[[Training Pack III]],
[1004513]=[[Training Pack IV]],
[1004514]=[[Training Pack V]],
[1004515]=[[Survivor Recruitment Pack I]],
[1004516]=[[Survivor Recruitment Pack II]],
[1004517]=[[Survivor Recruitment Pack III]],
[1004518]=[[Survivor Recruitment Pack IV]],
[1004519]=[[Survivor Recruitment Pack V]],
[1004520]=[[Hero Shard Selection Pack I]],
[1004521]=[[Hero Shard Selection Pack II]],
[1004522]=[[Hero Shard Selection Pack III]],
[1004523]=[[Hero Shard Selection Pack IV]],
[1004524]=[[Hero Shard Selection Pack V]],
[1004525]=[[Upgrade Pass I]],
[1004526]=[[Upgrade Pass II]],
[1004527]=[[Hero Pass - Dragonic]],
[1004528]=[[Hero Pass - Andrew]],
[1004529]=[[Hero Pass：Daphne]],
[1004530]=[[Hero Pass - Yord]],
[1004531]=[[Hero Pass - Rexar]],
[1004532]=[[Hero Pass - Sophia]],
[1004533]=[[Hero Pass - Sparta]],
[1004534]=[[Hero Pass - Monica]],
[1004535]=[[Hero Pass - Valkyr]],
[1004536]=[[Hero Pass - Alvarez]],
[1004537]=[[Hero Pass - Kataras]],
[1004538]=[[Hero Pass - Mirana]],
[1004539]=[[Lord Sprint Pack I]],
[1004540]=[[Lord Sprint Pack II]],
[1004541]=[[Lord Sprint Pack III]],
[1004542]=[[Lord Sprint Pack IV]],
[1004543]=[[Lord Sprint Pack V]],
[1004544]=[[Divine Beast Crystal Gift Pack I]],
[1004545]=[[Divine Beast Crystal Gift Pack II]],
[1004546]=[[Divine Beast Crystal Gift Pack III]],
[1004547]=[[Divine Beast Advanced Gift Pack I]],
[1004548]=[[Divine Beast Advanced Gift Pack II]],
[1004549]=[[Divine Beast Advanced Gift Pack III]],
[1004550]=[[Divine Beast Advanced Gift Pack IV]],
[1004551]=[[Divine Beast Advanced Gift Pack V]],
[1004552]=[[Weekly Crystal Gift Pack]],
[1004553]=[[Mystic Beast Value Pack I]],
[1004554]=[[Mystic Beast Value Pack II]],
[1004555]=[[Mystic Beast Value Pack III]],
[1004556]=[[Mystic Beast Value Pack IV]],
[1004557]=[[Mystic Beast Value Pack V]],
[1005001]=[[Join our Facebook community!]],
[1005002]=[[Join our Discord community!]],
[1005003]=[[Follow Us for Rewards]],
[1005004]=[[Stay ahead with the latest news, take part in exciting community events, and win amazing rewards! Follow any of our community accounts to claim Diamonds!]],
[1005005]=[[Join our community!]],
[1005006]=[[1. First-time visits to our Facebook and Discord communities grant rewards.]],
[1005007]=[[2. After claiming, you can revisit our communities anytime via this event page.]],
[1005021]=[[Push Notifications]],
[1005022]=[[Enable notifications to receive important messages]],
[1005023]=[[Stamina Fully Recovered]],
[1005024]=[[Building Upgrade]],
[1005025]=[[Technology Research]],
[1005026]=[[Resource Output]],
[1005027]=[[Soldier Training]],
[1005028]=[[AFK Rewards]],
[1005029]=[[Under Attack]],
[1005030]='Scouted',
[1005031]=[[Emergency Announcement]],
[1005032]=[[Alliance Attack on City Begins]],
[1005033]=[[New Private Message]],
[1005034]=[[Activate Shield in Union Duel]],
[1005035]=[[War Declared on Union City]],
[1005036]=[[Union City Under Attack]],
[1005037]=[[Officer Appointed]],
[1005038]=[[R4 / R5 Chat Notification]],
[1005039]=[[Arms Race Start]],
[1005040]=[[Gathering Complete]],
[1005041]=[[Union Airship Captain]],
[1005042]=[[Shield Expiration]],
[1005043]=[[You'll be notified when your stamina is fully restored]],
[1005044]=[[You'll be notified when a building upgrade is complete]],
[1005045]=[[You'll be notified when technology research is complete]],
[1005046]=[[You'll be notified upon full resource output]],
[1005047]=[[You'll be notified when Soldier Training is complete]],
[1005048]=[[You'll be notified upon full AFK rewards]],
[1005049]=[[You'll be notified when your castle is under attack]],
[1005050]=[[You'll be notified when your castle is scouted]],
[1005051]=[[You'll be notified when your Union Leader posts an emergency announcement]],
[1005052]=[[You'll be notified when your Union begins an attack on cities]],
[1005053]=[[You'll be notified when you receive a private message]],
[1005054]=[[You'll be reminded to activate a shield before the "Defeat Enemies" day in Union Duel]],
[1005055]=[[You'll be notified when war is declared on a Union City]],
[1005056]=[[You'll be notified when a Union City is under attack]],
[1005057]=[[You'll be notified when a new Officer takes office]],
[1005058]=[[You'll be notified when there's a message in your Union's R4 / R5 channel]],
[1005059]=[[You'll be notified when a new session of Arms Race begins]],
[1005060]=[[You'll be notified when your team finishes gathering]],
[1005061]=[[You'll be notified when you become an Airship Captain]],
[1005062]=[[You'll be notified 10 minutes before your shield expires]],
[1005063]=[[Push notifications are not available in the current version. Please update via the app store!]],
[1005064]=[[Visitor Notice]],
[1005065]=[[My Lord, some of your resources went uncollected and were wasted.
Turn on notifications to avoid further losses.]],
[1005066]='Open',
[1005067]=[[Stamina Regen]],
[1005068]=[[Building Upgrade]],
[1005069]='Research',
[1005070]=[[Resource Output]],
[1005071]=[[Soldier Training]],
[1005072]=[[AFK Rewards]],
[1005073]=[[Under Attack]],
[1005074]='Scouted',
[1005075]=[[Union Announcements]],
[1005076]=[[City Assault]],
[1005077]=[[Private Chat]],
[1005078]=[[Union Duel]],
[1005079]=[[Officer Appointed]],
[1005080]=[[R4 / R5 Message]],
[1005081]=[[Arms Race]],
[1005082]=[[Gathering Complete]],
[1005083]=[[Union Airship]],
[1005084]='Shield',
[1005085]=[[My Lord, your stamina is fully restored!]],
[1005086]=[[My Lord, your building upgrade is complete. Please log in to check it out!]],
[1005087]=[[My Lord, your technology research is complete. Please log in to check it out!]],
[1005088]=[[My Lord, your resource output has reached the limit. Please log in to collect them!]],
[1005089]=[[My Lord, your soldier training is complete.]],
[1005090]=[[My Lord, your AFK Rewards are full. Please log in to claim them!]],
[1005091]=[[My Lord, your castle is under attack! Please log in to defend it!]],
[1005092]=[[My Lord, your castle has been scouted.]],
[1005093]=[[My Lord, your Union Leader has issued an urgent announcement. Please log in to view it!]],
[1005094]=[[My Lord, your Union is attacking a city. Please log in to join the battle!]],
[1005095]=[[My Lord, another Lord has messaged you. Please log in to read it!]],
[1005096]=[[My Lord, the Union Duel – Defeat the Enemy event is about to begin. Please log in and activate a Shield to protect your castle.]],
[1005097]=[[My Lord, your Union's city has been declared war on by the enemy. Rally your forces quickly to face the challenge!]],
[1005098]=[[My Lord, your Union's city is under attack! Support your allies and unite in effort to defend our lands!]],
[1005099]=[[Congratulations! %s has been appointed as %s in your Union.]],
[1005100]=[[New message in the R4 / R5 channel! Log in to see what it's about.]],
[1005101]=[[A new Arms Race has begun! Don't forget to participate, My Lord!]],
[1005102]=[[My Lord, your team has finished gathering. Please log in to check it out!]],
[1005103]=[[Dear Lord, you have been appointed as the captain of the alliance spaceship, please remember to check online]],
[1005104]=[[Warning: Your Shield will expire in 10 minutes! Get ready to defend your castle!]],
[1005105]=[[Union Boss Activation]],
[1005106]=[[You will be notified when the Union Boss is activated.]],
[1005107]=[[Pet Treasure Hunt]],
[1005108]=[[You will receive a notification when your puppy finds a treasure]],
[1005109]=[[Someone @mentioned you. Check it out!]],
[1005110]=[[Mention Notification]],
[1005111]=[[You will receive a push notification when you are @mentioned.]],
[1005112]=[[Warzone Showdown]],
[1005113]=[[War Zone Showdown-Royal City Battle Opening Notice]],
[1005150]=[[Seven-day optional card]],
[1005151]=[[During the event, if you purchase a seven-day optional card at any time, you can get all the rewards. Multiple optional cards can be purchased at the same time.]],
[1005152]=[[Seven-day value card]],
[1005153]=[[Seven-day special offer card]],
[1005154]=[[Valid for {%s} days]],
[1005155]=[[Buy now and get it now]],
[1005156]=[[Collect every day during the event]],
[1005157]=[[1. Each optional card can be purchased after the reward is selected. The selected reward cannot be changed after purchase.]],
[1005158]=[[2. If you purchase and claim it at any time during the event, you can get all daily rewards.]],
[1005159]=[[3. The optional card reward can be claimed once a day, and a total of 7 times can be claimed during the event and will be sent via email.]],
[1005160]=[[4. After the event, unclaimed daily rewards will be calculated based on the number of unclaimed days.]],
[1005161]=[[The reward is full and cannot be selected.]],
[1005162]=[[Please select a reward first]],
[1005163]=[[Seven-day optional card daily rewards]],
[1005164]=[[Dear lord, the {%s1} you purchased has {%s2} days of rewards yet to be claimed, please check~]],
[1005165]=[[You can receive {%s1} days of rewards]],
[1005166]=[[Available after %s]],
[1005167]=[[Rewards already purchased cannot be exchanged]],
[1005250]=[[Gift Box]],
[1005251]='List',
[1005252]=[[Remaining claims today: {%s1}/{%s2}]],
[1005253]=[[Diamond Gift Box]],
[1005254]='Send',
[1005255]=[[Owned: {%s1}]],
[1005256]='Claimed',
[1005257]='Open',
[1005258]=[[Claim History]],
[1005259]=[[No Gift Boxes.]],
[1005260]=[[So lucky! You got the most rewards from the Gift Box. Thanks, {%s1}, for sending the Diamond Gift Box!]],
[1005261]=[[May this Gift Box bring you endless luck.]],
[1005262]=[[You think they're awesome!]],
[1005263]='Like',
[1005264]=[[Count: {%s1}/{%s2}]],
[1005265]=[[Do not copy to World Channel]],
[1005266]=[[Grab it to get Diamonds!
Purchase event packs during festivals for extra Surprise Boxes. Share them in Union Channel or World Channel!]],
[1005267]=[[1. Items obtained from events can be shared in Chat channels.
2. Once sent, Gift Boxes remain active for 8 hours. After that, they will expire and cannot be claimed anymore.
3. You can claim up to 10 Gift Boxes from others per day.
4. After successfully sending a Gift Box, you'll automatically claim one share of the rewards. This doesn't count toward your daily claim limit.
5. When sending a Gift Box, there's a 50% chance it will also be shared in World Channel (if the "Copy Gift Box" option is enabled).
6. You can't view or claim Diamond Gift Boxes while in cross-server state. Claimed boxes may randomly reward 100 to 2,000 Diamonds.]],
[1005268]=[[With the "Copy Gift Box" option enabled, sending a Gift Box has a 50% chance to share a copy in World Channel]],
[1005269]=[[You're the best! (You can't like yourself yet.)]],
[1005270]=[[All content of Lord {%s1}'s Gift Box has been claimed.]],
[1005271]=[[All claimed]],
[1005272]=[[Too late]],
[1005273]=[[Cannot view or claim Gift Boxes in a cross-server state.]],
[1005274]=[[Only available to the winning Kingdom!]],
[1005275]=[[This Gift Box belongs to another Kingdom and cannot be claimed.]],
[1005276]=[[Conqueror Gift Box]],
[1005277]=[[Victor's Glory]],
[1005321]=[[Unlock Alliance Mail]],
[1005322]=[[Send pictures]],
[1005323]=[[Train speed increase]],
[1005324]=[[R4 expansion]],
[1005325]=[[Train Defense Enhancement]],
[1005326]=[[Collect all]],
[1005327]=[[Train Intermediate Defense]],
[1005328]=[[Unlock VIP passengers on the train]],
[1005330]=[[R4 and R5 can send alliance emails]],
[1005331]=[[You can send pictures in the alliance channel]],
[1005332]=[[Increase train speed by 10%]],
[1005333]=[[R4 player limit increased by 2]],
[1005334]=[[The number of train robberies decreased by 1]],
[1005335]=[[Enable the "Get All" advanced gift function]],
[1005336]=[[The number of times a commander can fail to attack a train is adjusted to 3]],
[1005337]=[[The train conductor can invite an ally to become a VIP passenger on the train!]],
[1005340]=[[{} Level Unlock]],
[1005341]=[[The conductor can invite an alliance member to ride the carriage as a VIP passenger. \n\nThere are two types of VIP passengers: \n1. Special guest: Super lucky! Invite your friends and family to ride the carriage and you can get a prize! \n2. Guardian angel: The guardian angel will guard the carriage on behalf of the conductor and resist robbery by others! \n\nSpecial note: The reward for VIP passengers is composed of the rewards of the two carriages designated by the conductor. If you need to adjust, please contact the conductor. The conductor's reward remains unchanged.]],
[1005401]='War',
[1005402]='Member',
[1005403]='Message',
[1005404]='Events',
[1005410]=[[<color=#317bc7>{%s1}</color> adjusted the rank of <color=#317bc7>{%s2}</color> from <color=#319f38>R{%s3}</color> to <color=#319f38>R{%s4}</color>]],
[1005411]=[[<color=#317bc7>{%s1}</color> adjusted the rank of <color=#317bc7>{%s2}</color> from <color=#319f38>R{%s3}</color> to <color=#319f38>R{%s4}</color>]],
[1005412]=[[<color=#317bc7>{%s1}</color> approved <color=#317bc7>{%s2}</color>'s application to join the Union]],
[1005413]=[[<color=#317bc7>{%s1}</color> appointed <color=#317bc7>{%s2}</color> as <color=#319f38>{%s3}</color>]],
[1005414]=[[<color=#317bc7>{%s1}</color> removed the title <color=#319f38>{%s3}</color> from <color=#317bc7>{%s2}</color>]],
[1005415]=[[<color=#317bc7>{%s1}</color> removed <color=#e63838>{%s2}</color> from the Union]],
[1005416]=[[<color=#e63838>{%s1}</color> has been automatically removed from the Union due to long-term inactivity]],
[1005417]=[[<color=#e63838>{%s1}</color> left the Union]],
[1005418]=[[<color=#317bc7>{%s1}</color> transferred the <color=#319f38>R5</color> position to <color=#317bc7>{%s2}</color>]],
[1005419]=[[<color=#319f38>{%s1}</color> joined the Union]],
[1005420]=[[<color=#317bc7>{%s1}</color> modified the Union slogan]],
[1005421]=[[<color=#317bc7>{%s1}</color> set up a new alliance gathering place<color=#319f38><a href=Logjump>X:{%s2}Y:{%s3}</a></color>]],
[1005422]=[[<color=#317bc7>{%s1}</color> modified the conditions for joining the Union. It now requires <color=#319f38>Castle Level ≥{%s2}</color>]],
[1005423]=[[<color=#317bc7>{%s1}</color> modified the conditions for joining the Union. It now requires <color=#319f38>CP ≥{%s2}</color>]],
[1005424]=[[<color=#317bc7>{%s1}</color> modified the conditions for joining the Union. Every player can <color=#319f38>join directly</color>]],
[1005425]=[[<color=#317bc7>{%s1}</color> modified the conditions for joining the Union. It now requires <color=#319f38>application to join</color>]],
[1005430]=[[<color=#317bc7>{%s1}</color> scheduled a <color=#319f38>Difficulty {%s3}</color> Union Boss after server time {%s2}]],
[1005431]=[[<color=#317bc7>{%s1}</color> has started the Union Boss challenge]],
[1005432]=[[Automatically start the Union Boss challenge]],
[1005433]=[[<color=#317bc7>{%s1}</color> appointed <color=#317bc7>{%s2}</color> as Captain]],
[1005434]=[[<color=#317bc7>{%s1}</color> chose to participate in the Desert Storm battlefield at <color=#e63838>server time {%s2}</color>]],
[1005435]=[[<color=#317bc7>{%s1}</color> chose to <color=#e63838>abstain</color> from the Desert Storm battlefield]],
[1005436]=[[<color=#317bc7>{%s1}</color> plans to search for <color=#319f38>Lv. {%s3}</color> Zombie Strongholds at <color=#e63838>server time {%s2}</color>]],
[1005437]=[[<color=#317bc7>{%s1}</color> <color=#e63838>cancelled</color> the search for Zombie Strongholds]],
[1005438]=[[<color=#317bc7>{%s1}</color> modified the plan and the search for <color=#319f38>Lv. {%s3}</color> Zombie Strongholds will begin at <color=#e63838>server time {%s2}</color>]],
[1005439]=[[<color=#317bc7>{%s1}</color> has found a <color=#319f38>Lv. {%s2}</color> Zombie Stronghold. Please prepare to attack.]],
[1005440]=[[<color=#317bc7>{%s1}</color> declares war on <color=#319f38>{%s2} level {%s3}<a href=Logjump>X:{%s4}Y:{%s5}</a></color>, please go and participate in the siege]],
[1005441]=[[<color=#317bc7>{%s1}</color> declared war on <color=#e63838>[{%s2}]{%s3}</color>, the Alliance occupied <color=#319f38>{%s4} level {%s5}<a href=Logjump>X:{%s6}Y:{%s7}</a></color>. Please go and join the siege.]],
[1005442]=[[<color=#317bc7>{%s1}</color>Cancel declaration of war<color=#319f38>{%s2}Level{%s3}<a href=Logjump>X:{%s4}Y:{%s5}</a></color>]],
[1005443]=[[<color=#319f38>{%s1}Level{%s2}<a href=Logjump>X:{%s3}Y:{%s4}</a></color> has been declared at war by the <color=#e63838>[{%s5}]{%s6}</color> Alliance. Please go and participate in the defense.]],
[1005444]=[[Successfully occupied <color=#319f38>{%s1} level {%s2}<a href=Logjump>X:{%s3}Y:{%s4}</a></color>]],
[1005445]=[[Successfully seized the <color=#e63838>[{%s1}]{%s2}</color> Alliance-occupied <color=#319f38>{%s3} Level {%s4}<a href=Logjump>X:{%s5}Y:{%s6}</a></color>]],
[1005446]=[[<color=#319f38>{%s1}Level{%s2}<a href=Logjump>X:{%s3}Y:{%s4}</a></color> was captured by the <color=#e63838>[{%s5}]{%s6}</color> Alliance. We lost the city.]],
[1005501]=[[Zombie's Treasure]],
[1005502]=[[Clear tiles to earn stars]],
[1005503]='Go',
[1005504]='Claim',
[1005505]='Claimed',
[1005506]=[[Collect <color=#fff553>{%s1}</color> more star(s) to claim your reward!]],
[1005507]=[[Clear Adventure Journey stages to claim]],
[1005551]=[[7-Day Login]],
[1005552]=[[7-Day Login]],
[1005553]=[[Day 1]],
[1005554]=[[Day 2]],
[1005555]=[[Day 3]],
[1005556]=[[Day 4]],
[1005557]=[[Day 5]],
[1005558]=[[Day 6]],
[1005559]=[[Day 7]],
[1005560]='Hero',
[1005561]=[[10x Summon]],
[1005562]=[[Log in tomorrow to receive]],
[1005563]=[[Login for %s more day(s) to claim]],
[1005564]='Claim',
[1005565]=[[Come back tomorrow]],
[1005566]=[[Reward available after %s]],
[1006000]=[[Storm Rescue]],
[1006001]='Rewards',
[1006002]=[[Rewards Preview]],
[1006010]='Leaderboard',
[1006011]=[[Start Challenge]],
[1006012]=[[Participate in 3 event levels to claim all rewards!]],
[1006020]='Rules',
[1006021]=[[1. Each event will have 5 levels. After completing each level, you can choose to proceed to the next level or exit and return to the event interface. However, please note that if you exit midway, you will have to start from the first level again next time you try the challenge.]],
[1006022]=[[2. If you pass 5 levels in a row or fail a level in the middle, you will enter the level settlement.]],
[1006023]=[[3. Participate in 3 event levels to claim all rewards!]],
[1006030]=[[Complete Storm Rescue Stages ({%s1}/1)]],
[1006031]=[[Complete Storm Rescue Stages ({%s1}/2)]],
[1006032]=[[Complete Storm Rescue Stages ({%s1}/3)]],
[1006040]=[[Goals Rewards]],
[1006041]='Claim',
[1006042]='Claimed',
[1006045]=[[Storm Rescue {%s1}/5]],
[1006046]=[[Stage Time:]],
[1006050]='Exit',
[1006051]=[[Next Stage]],
[1006080]='Leaderboard',
[1006081]=[[Union Ranking]],
[1006082]=[[Warzone Ranking]],
[1006083]=[[Peak Ranking]],
[1006090]=[[Stage {%s1}]],
[1006091]=[[Not ranked]],
[1006100]=[[Ranking Rewards]],
[1006101]=[[Warzone Ranking Rewards]],
[1006102]=[[Peak Ranking Rewards]],
[1006200]=[[Storm Rescue Rewards Reissued]],
[1006201]=[[Dear Lord, the Storm Rescue event has ended. The following are the goals rewards you have earned but not yet claimed. Please collect them promptly.]],
[1006501]='',
[1006502]='',
[1006503]='',
[1006504]='',
[1006505]='',
[1006506]='',
[1006507]='',
[1006508]='',
[1006509]='',
[1006510]='',
[1006511]='',
[1006512]='',
[1006513]='',
[1006514]='',
[1006515]='',
[1006516]='',
[1006517]='',
[1006518]='',
[1006519]='',
[1006520]='',
[1006521]='',
[1006522]='',
[1006523]='',
[1006524]='',
[1006525]='',
[1006526]='',
[1006527]='',
[1006528]='',
[1006529]='',
[1006530]='',
[1006531]='',
[1006532]='',
[1006533]='',
[1006534]='',
[1006535]='',
[1006536]='',
[1006537]='',
[1006538]='',
[1006539]='',
[1006540]='',
[1006541]='',
[1006542]='',
[1006543]='',
[1006544]='',
[1006545]='',
[1006546]='',
[1006547]='',
[1006548]='',
[1006549]='',
[1006550]='',
[1006551]='',
[1006552]='',
[1006553]='',
[1006554]='',
[1006555]='',
[1006556]='',
[1006557]='',
[1006558]='',
[1006559]='',
[1006560]='',
[1006561]='',
[1006562]='',
[1006563]='',
[1006564]='',
[1006565]='',
[1006566]='',
[1006567]='',
[1006568]='',
[1006569]='',
[1006570]='',
[1006571]='',
[1006572]='',
[1006573]='',
[1006574]='',
[1006575]='',
[1006576]='',
[1006577]='',
[1006578]='',
[1006579]='',
[1006580]='',
[1006581]='',
[1006582]='',
[1006583]='',
[1006584]='',
[1006585]='',
[1006586]='',
[1006587]='',
[1006588]='',
[1006589]='',
[1006590]='',
[1006591]='',
[1006592]='',
[1006593]='',
[1006594]='',
[1006595]='',
[1006596]='',
[1006597]='',
[1006598]='',
[1006599]='',
[1006600]='',
[1006601]='',
[1006602]='',
[1006603]='',
[1006604]='',
[1006605]='',
[1006606]='',
[1006607]='',
[1006608]='',
[1006701]='',
[1006702]='',
[1006703]='',
[1006704]='',
[1006705]='',
[1006706]='',
[1006707]='',
[1006708]='',
[1006709]='',
[1006710]='',
[1006711]='',
[1006712]='',
[1006713]='',
[1006714]='',
[1006715]='',
[1006716]='',
[1006717]='',
[1006718]='',
[1006719]='',
[1006720]='',
[1006721]='',
[1006722]='',
[1006723]='',
[1006724]='',
[1006725]='',
[1006726]='',
[1006727]='',
[1006728]='',
[1006729]='',
[1006730]='',
[1007100]='Lv.%d',
[1007101]=[[Swipe to move.]],
[1007102]=[[Boss Warning!]],
[1007103]='Quests',
[1007104]=[[Defeat Zombies: %d]],
[1007105]=[[Defeat Boss: %d]],
[1007106]=[[Boss Incoming: %s]],
[1007107]=[[Participate in Storm Rescue (%s/1)]],
[1007201]=[[Update Gift]],
[1007202]=[[Dear Lord\nWe have prepared a new version of the client for you, with more exciting gameplay and more benefits waiting for you to explore\nIn order to ensure your gaming experience, you can download the latest version of the app store by <color=#FF7404>Go to the app store</color> and you will be rewarded by email after successful update and login]],
[1007203]=[[Download Now]],
[1007204]=[[Log in after updating to receive your rewards!]],
[1007205]=[[Update Gift Rewards]],
[1007206]=[[Dear Lord, your game client has been updated to the latest version. Thank you for your support. Please accept this gift as a token of our appreciation!]],
[1007207]=[[Update Rewards]],
[1007301]=[[You are quite popular!]],
[1007302]=[[<color=#6fe978>{%s1}</color> people think you are great!]],
[1007303]=[[So cool!]],
[1007304]=[[thinks you are awesome!]],
[1007305]=[[You can only like 1 presidential email per day]],
[1007306]=[[You can only like 1 union email per day]],
[1007307]=[[You have liked this email]],
[1007308]=[[Liked, You've got {%s1}!]],
[1007309]=[[You can only like {%s1} times a day]],
[1007310]=[[You are the best! (You can’t like yourself.)]],
[1007311]=[[No Likes yet.]],
[1007321]=[[I think you are awesome!]],
[1007322]=[[I think your castle is amazing!]],
[1007323]=[[Thank you for helping me complete the intelligence quest!]],
[1007324]=[[Thank you for helping me collect the Acorn Tavern quest reward!]],
[1007325]=[[Your Gift Box is fantastic!]],
[1007326]=[[Great presidential email you sent!]],
[1007327]=[[Great union emails you send!]],
[1007328]=[[I think you are the best Airship Captain!]],
[1007329]=[[Thank you for exchanging treasure map shard with me!]],
[1007330]=[[Your contribution to the Kingdom is incredible!]],
[1007331]=[[Thanks for helping me put out the fire!]],
[1007332]=[[Thank you for curing my zombie poison!]],
[1007401]=[[Lord Treasury]],
[1007402]=[[Lord Tour]],
[1007403]=[[Lord Ranking]],
[1007404]=[[My overall ranking]],
[1007405]=[[{%s1} day(s) {%s2}:{%s3}:{%s4}]],
[1007406]=[[Get the key!]],
[1007407]=[[Participate in the 7-day Lord Tour event to obtain the keys to open the treasury!]],
[1007408]=[[Day 1]],
[1007409]=[[Day 2]],
[1007410]=[[Day 3]],
[1007411]=[[Day 4]],
[1007412]=[[Day 5]],
[1007413]=[[Day 6]],
[1007414]=[[Day 7]],
[1007415]=[[Lord's Treasure Chest]],
[1007416]=[[How to open the treasure chest]],
[1007417]=[[Collect 5 keys to unlock]],
[1007418]=[[How to get the keys]],
[1007419]=[[Tour events according to schedule]],
[1007420]=[[Accumulate 100,000 prestige points every day and you can get 1 key]],
[1007421]='Open',
[1007422]=[[Open the treasure chest to get the following rewards]],
[1007423]=[[Get prestige points]],
[1007424]='Claim',
[1007425]='Go',
[1007426]='Expired',
[1007427]='Locked',
[1007428]='%s',
[1007429]=[[Open after %s]],
[1007430]=[[Phase Ended]],
[1007431]=[[Complete <color=#FFD800><size=30>Intelligence Quests</size></color> to earn massive points.]],
[1007432]=[[<color=#FFD800><size=30>Upgrade Buildings</size></color> to earn massive points.]],
[1007433]=[[<color=#FFD800><size=30>Research & Upgrade</size></color> to earn massive points.]],
[1007434]=[[<color=#FFD800><size=30>Upgrade Heroes</size></color> to earn massive points.]],
[1007435]=[[Use <color=#FFD800><size=30>Any Speedups</size></color> to earn massive points.]],
[1007436]=[[<color=#FFD800><size=30>Kill Enemies</size></color> to earn massive points.]],
[1007437]=[[Complete <color=#FFD800><size=30>Events from the First 6 Days</size></color> to earn massive points.]],
[1007438]=[[Event Rules:]],
[1007439]=[[Lord's Treasury Rewards Reissue]],
[1007440]=[[Dear Lord, you have unclaimed treasure chest rewards in the Lord's Treasury. They have been delivered via email. Please check.]],
[1007441]=[[Lord Tour Rewards Reissue]],
[1007442]=[[Dear Lord, you have unclaimed tour schedule rewards in the Lord Tour. They have been delivered via email. Please check.]],
[1007443]=[[Lord Ranking-Daily Ranking Rewards]],
[1007444]=[[Dear Lord, you have accumulated prestige points in the tour schedule on the Day {%s1} of the Lord Ranking Event and achieved rank {%s2}. This is your ranking reward. Please check it.]],
[1007445]=[[Lord Ranking-Overall Ranking Rewards]],
[1007446]=[[Dear Lord, you have accumulated prestige points in the Lord Ranking Event and achieved rank {%s1}. This is your ranking reward. Please check it.]],
[1007447]=[[Daily Pack]],
[1007448]=[[Replenish your stamina! Help complete more intelligence quests]],
[1007449]=[[Speed ​​up the recruitment! Recruit more survivors!]],
[1007450]=[[Replenish your stamina! Get more speed-up resources!]],
[1007451]=[[More EXP! More heroes to recruit!]],
[1007452]=[[Speed ​​up the training! Train more soldiers!]],
[1007453]=[[Improve your strength! Help defeat more enemies]],
[1007454]=[[Super valuable resources! Help complete more tour events!]],
[1007455]=[[Event Rules:]],
[1007456]=[[1. During the event, participate in the daily Lord Tour event, gain prestige points, and receive Lord Tour progress rewards.]],
[1007457]=[[2. The daily tour events are different and last for 24 hours. The completed schedule will no longer accumulate prestige points.]],
[1007458]=[[3. You can get [Treasury Key] from the tour progress reward, which is used to open the lord's treasury.]],
[1007459]=[[4. Collect 5 keys to open the treasury and get rewards. It can only be opened once during the event.]],
[1007460]=[[Tour Schedule:]],
[1007461]=[[Day 1: Intelligence event, consume stamina]],
[1007462]=[[Day 2: Construction Speed-up, Survivor Recruitment]],
[1007463]=[[Day 3: Research Speed-up, Intelligence events]],
[1007464]=[[Day 4: Consume EXP, recruit heroes]],
[1007465]=[[Day 5: Soldier Training, general speed-up]],
[1007466]=[[Day 6: Kill the enemy, dispatch the wagon]],
[1007467]=[[Day 7: Earn points for the events of the previous 6 days]],
[1007468]=[[3. At the end of each day's tour, the daily rankings will be settled and the ranking rewards will be delivered via email; at the end of the event, the overall rankings will be settled and the ranking rewards will be delivered via email.]],
[1007469]=[[4. The prestige progress rewards that have not been claimed during the daily tour will be delivered via email after the event ends.]],
[1007470]=[[5. During the event, points can only be obtained by purchasing packs with diamonds.]],
[1007471]='Ranking',
[1007472]=[[Ranking Rewards]],
[1007473]=[[Not ranked]],
[1007474]=[[Join the 7-day tour and get great rewards!]],
[1007475]=[[Rewards Preview]],
[1007476]=[[Start countdown]],
[1007477]=[[Obtain the Lord's Key]],
[1007478]=[[Go to the Treasury]],
[1007479]=[[Exclusive Skin]],
[1007601]=[[Customization Makeover]],
[1007602]=[[Complete customization quests to claim rewards.]],
[1007603]='Unachieved',
[1007604]='Claim',
[1007605]='Go',
[1007606]=[[Use a custom avatar!]],
[1007607]=[[Change to a custom name!]],
[1007608]=[[Customization Makeover Rewards Reissue]],
[1007609]=[[Dear Lord, your unclaimed rewards from the Customization Makeover event have been reissued via mail. Check them out!]],
[1007701]=[[Allies have put out the fire]],
[1007702]=[[Cannot put out fire for non-allies]],
[1007703]=[[Do you want to consume <color=#ff5656>{%s1} diamonds</color> to help your allies put out the fire?]],
[1007704]=[[Helping to put out the fire]],
[1007705]=[[<color=#317bc7>{%s1}</color> thanks you for your like!]],
[1007706]=[[Help, my town is on fire]],
[1007721]=[[Guess who just put out the fire in your town? It was me!]],
[1007722]=[[Please call me the little fire-fighting hero!]],
[1007723]=[[If it weren't for my help, your town would have been burned to ashes!]],
[1007724]=[[The fire is out! Come and praise me for being your timely help!]],
[1007725]=[[The fire's out—not a single ember left! Someone give me a "Firefighter of the Year" award!]],
[1007726]=[[If I hadn't acted quickly, your town would probably be nothing but charcoal now!]],
[1007727]=[[Next time there’s a fire, call me. I’ll be there whenever you call!]],
[1007728]=[[Things like putting out fires are a piece of cake for me!]],
[1007801]=[[Rise of the Mighty]],
[1007802]=[[Upgrade your heroes—only the mighty survive the apocalypse!]],
[1007803]=[[1. Complete quests to instantly claim rewards.]],
[1007804]=[[2. Any unclaimed rewards will be sent via mail at the end of the event.]],
[1008001]=[[Sera's Trial]],
[1008005]=[[Event ends in: <color=#FFCF4C>%s</color>]],
[1008006]=[[Daily Chest]],
[1008007]=[[Training Objective]],
[1008008]=[[Complete Training Objectives for bountiful rewards]],
[1008009]=[[<color=#B6F8FD>Stage Unlocks in: </color>%s]],
[1008050]='Claim',
[1008051]='Claimed',
[1008052]='Go',
[1008060]='Challenge',
[1008061]='Training',
[1008065]='Stage',
[1008066]=[[Stage {%s1}]],
[1008067]=[[Sera's Ultimate damage multiplier <color=#008E0B>+55%</color>]],
[1008068]=[[Operation unavailable for the hero]],
[1008069]=[[No claimable rewards]],
[1008071]=[[Value Pack]],
[1008072]=[[Recommended value packs]],
[1008073]=[[Recharge Gifts]],
[1008100]='Leaderboard',
[1008101]=[[Rewards Preview]],
[1008102]='Rewards',
[1008103]=[[Rewards claimed.]],
[1008104]=[[Claimable Rewards]],
[1008120]=[[Normal Mode]],
[1008121]=[[Infinite Mode]],
[1008131]=[[No. {%s1}]],
[1008132]=[[No. {%s1}-No. {%s2}]],
[1008150]=[[Stage Requirement]],
[1008151]=[[Up to 3 heroes can be deployed]],
[1008161]=[[Recommended Sera's CP]],
[1008162]='Trial',
[1008163]='Get',
[1008164]=[[Pick All]],
[1008165]='Chest',
[1008166]=[[Picked Up]],
[1008167]='Obtained',
[1008168]=[[Available upon stage completion]],
[1008169]=[[Claimed Rewards]],
[1008180]=[[Exiting now will initiate settlement and may forfeit some of the rewards. Confirm?]],
[1008200]=[[Clear {%s1} more stage(s)]],
[1008201]=[[Wave: {%s1}]],
[1008202]=[[Quest Complete]],
[1008203]=[[Best: <color=#FFDD22>{%s1}</color>]],
[1008204]=[[Current: <color=#FFDD22>{%s1}</color>]],
[1008205]=[[Zombie Horde]],
[1008206]=[[Zombie Horde: {%s1}]],
[1008220]=[[Low hero CP. Please adjust your lineup or train your heroes before trying again.]],
[1008251]=[[Sera's Trial Settlement Rewards]],
[1008252]=[[Dear Lord, [Sera's Trial] has ended. Rewards have been issued via mail based on your performance.]],
[1008253]=[[Sera's Trial Reward Reissue]],
[1008254]=[[Dear Lord, [Sera's Trial] has ended. Your unclaimed rewards have been reissued via mail.]],
[1008260]=[[Clear Normal Mode Stage {%s1} to unlock]],
[1008280]=[[Infinite Mode is now unlocked! Challenge it now!]],
[1008285]=[[Subsequent levels are not unlocked]],
[1008286]=[[Insufficient combat power to continue the challenge]],
[1008300]='Rules',
[1008301]=[[1. The event features two modes: Normal Mode and Infinite Mode. Infinite Mode only becomes available once Normal Mode Stage 6 is cleared.]],
[1008302]=[[2. Up to 3 heroes can be deployed in the event stages, and one slot is always reserved for Bow of Tomorrow - Sera. Except for some Normal Mode stages that offer hero trial, one of your deployment slots will remain empty if you do not own Bow of Tomorrow - Sera.]],
[1008303]=[[3. Bow of Tomorrow - Sera receives a huge boost in the event: Ultimate damage multiplier <color=#008E0B>+55%</color>.]],
[1008304]=[[4. Defeat monsters in event stages to pick up rewards! However, these rewards can only be claimed after clearing the required stages or waves.]],
[1008305]=[[5. During the event, limited-time training quests for Bow of Tomorrow - Sera will be available. Complete them to claim rewards.]],
[1008306]=[[6. A leaderboard will track players' highest wave cleared in the Infinite Mode during the event. Rewards will be distributed based on rankings at the end of the event. The higher the ranking, the better the rewards.]],
[1008501]=[[Elite Monthly Card]],
[1008502]='Duration',
[1008503]=[[Days remaining]],
[1008504]='Perks',
[1008505]=[[Purchase to get]],
[1008506]=[[Daily Claim]],
[1008507]=[[Elite Monthly Card Daily Rewards Reissue Mail]],
[1008508]=[[Dear Lord, you have unclaimed ongoing rewards from your Elite Monthly Card. We are sending them to you via mail—please check.]],
[1008509]=[[Castle Skin – Winter Wonderland]],
[1008510]=[[March Speed Increase]],
[1008511]=[[DMG boost to Monsters]],
[1008512]=[[DMG boost to All Enemies]],
[1008513]=[[The following Elite Monthly Card perks have been activated.]],
[1008551]=[[Star Upgrade: Shield value increased to {%s1}]],
[1008552]=[[Shield value increased by {%s1}]],
[1008553]=[[Star Upgrade: Increase attack power to {%s1}]],
[1008554]=[[The attack power increase effect is increased by an additional {%s1}]],
[1008555]=[[Star Upgrade: Damage increase effect increased to {%s1}]],
[1008556]=[[Damage increase effect increased by an additional {%s1}]],
[1008557]=[[Star Upgrade: Defense enhancement effect increased to {%s1}]],
[1008558]=[[Defense enhancement effect increased by an additional {%s1}]],
[1008559]=[[Star Upgrade: Shield value increased to {%s1}]],
[1008560]=[[Key star upgrade: After the shield disappears, reduce all damage received by all allied heroes by {%s1}]],
[1008561]=[[Shield value is increased by an additional {%s1}, and all damage received by all allied heroes is reduced by an additional {%s2}]],
[1008562]=[[Star Upgrade: Increase attack power to {%s1}]],
[1008563]=[[Key star upgrade: Increase the number of attack targets of the beast's skills by {%s1}]],
[1008564]=[[The attack power increase effect is increased by an additional {%s1}]],
[1008565]=[[Star Upgrade: Damage increase effect increased to {%s1}]],
[1008566]=[[Key star upgrade: Reduce the damage caused by the active skills of all enemy heroes by {%s1}]],
[1008567]=[[The damage increase effect is increased by an additional {%s1}, and the damage caused by the active skills of all enemy heroes is reduced by an additional {%s2}]],
[1008568]=[[Star Upgrade: Defense enhancement effect increased to {%s1}]],
[1008569]=[[Key star upgrade: Front-line heroes gain {%s1} defense boost]],
[1008570]=[[The defense boost effect is increased by an additional {%s1}, and the defense boost effect of front-line heroes is increased by an additional {%s2}]],
[1008571]=[[At the start of battle, your front-line hero gains a shield equal to {%s1} of the beast's health, lasting {%s2} turns.]],
[1008572]=[[After the Divine Beast uses a skill, your back-row heroes gain attack power equal to {%s1} of the Divine Beast's attack, lasting for {%s2} rounds.]],
[1008573]=[[After the Divine Beast uses a skill, the damage caused by your back-row heroes increases by {%s1} and lasts for {%s2} rounds.]],
[1008574]=[[At the start of battle, your front-row hero gains defense equal to the Divine Beast's defense {%s1}, lasting {%s2} turns.]],
[1008575]=[[At the start of battle, your Forest Hero gains {%s1}+{%s3} shield points, lasting {%s2} turns.]],
[1008576]=[[After the mythical beast uses a skill, the attack power of your forest hero increases by {%s1}+{%s3} and lasts for {%s2} turns.]],
[1008577]=[[After the Divine Beast uses a skill, the damage caused by your back-row Forest Heroes increases by {%s1}+{%s3} for {%s2} turns.]],
[1008578]=[[At the start of battle, the defense of your front-row Forest Heroes is increased by {%s1}+{%s3}, lasting for {%s2} turns.]],
[1008579]=[[At the start of combat, your human hero gains {%s1}+{%s3} shield points, lasting {%s2} rounds.]],
[1008580]=[[After the mythical beast uses a skill, the attack power of allied human heroes increases by {%s1}+{%s3}, lasting for {%s2} turns.]],
[1008581]=[[After the Divine Beast uses a skill, the damage caused by human heroes in the back row is increased by {%s1}+{%s3} for {%s2} turns.]],
[1008582]=[[At the start of battle, the defense of your front-row human heroes is increased by {%s1}+{%s3}, lasting for {%s2} turns.]],
[1008583]=[[At the start of combat, your Night Elf hero gains {%s1}+{%s3} shield points, lasting {%s2} rounds.]],
[1008584]=[[After the mythical beast uses a skill, the attack power of your Night Elf hero increases by {%s1}+{%s3} and lasts for {%s2} rounds.]],
[1008585]=[[After the Divine Beast uses a skill, the damage caused by the Night Elf heroes in the back row is increased by {%s1}+{%s3} and lasts for {%s2} rounds.]],
[1008586]=[[At the start of battle, the defense of your front-row Night Elf heroes increases by {%s1}+{%s3}, lasting for {%s2} rounds.]],
[1008587]=[[At the start of combat, your Forest Hero gains a {%s1}+{%s4} shield, which lasts for {%s2} turns. After the shield disappears, it reduces all damage taken by the target hero by {%s3}+{%s5}, which lasts until the end of combat.]],
[1008588]=[[After the Divine Beast uses a skill, the attack power of your Forest Hero increases by {%s1}+{%s4}, lasting for {%s2} turns; the number of targets attacked by the Divine Beast's skills increases by {%s3}.]],
[1008589]=[[After the Divine Beast uses a skill, the damage dealt by your back-row Forest Heroes is increased by {%s1}+{%s5} for {%s2} turns. It also reduces the damage dealt by all enemy heroes' active skills by {%s3}+{%s6} for {%s4} turns.]],
[1008590]=[[At the start of the battle, the defense of your forest heroes increases by {%s1}+{%s3}, and the defense of your front-line heroes increases by an additional {%s2}+{%s4}, which lasts until the end of the battle.]],
[1008591]=[[At the start of combat, your human hero gains a {%s1}+{%s4} shield, which lasts for {%s2} turns. After the shield disappears, all damage taken by all your heroes is reduced by {%s3}+{%s5}, which lasts until the end of the combat.]],
[1008592]=[[After the Divine Beast uses a skill, the attack power of allied human heroes increases by {%s1}+{%s4}, lasting for {%s2} turns; the number of targets attacked by the Divine Beast's skills increases by {%s3}.]],
[1008593]=[[After the Divine Beast uses a skill, the damage caused by allied back-row human heroes is increased by {%s1}+{%s5} for {%s2} turns. At the same time, the damage caused by the active skills of all enemy heroes is reduced by {%s3}+{%s6} for {%s4} turns.]],
[1008594]=[[At the start of the battle, the defense of your human heroes increases by {%s1}+{%s3}, and the defense of your front-line heroes increases by an additional {%s2}+{%s4}, which lasts until the end of the battle.]],
[1008595]=[[At the start of combat, your Night Elf heroes gain a {%s1}+{%s4} shield, which lasts for {%s2} turns. After the shield disappears, all damage taken by all your heroes is reduced by {%s3}+{%s5}, which lasts until the end of combat.]],
[1008596]=[[After the Divine Beast uses a skill, the attack power of your Night Elf hero increases by {%s1}+{%s4}, lasting for {%s2} turns; the number of targets attacked by the Divine Beast's skills increases by {%s3}.]],
[1008597]=[[After the Divine Beast uses a skill, the damage caused by the Night Elf heroes in the back row is increased by {%s1}+{%s5} for {%s2} turns. At the same time, the damage caused by the active skills of all enemy heroes is reduced by {%s3}+{%s6} for {%s4} turns.]],
[1008598]=[[At the start of the battle, the defense of your Night Elf heroes increases by {%s1}+{%s3}, and the defense of your front-line heroes increases by an additional {%s2}+{%s4}, which lasts until the end of the battle.]],
[1008601]=[[Assign R4 to several Union members.]],
[1008602]=[[Post an announcement to remind members to join Assault rallies for rewards]],
[1008603]=[[Post an announcement to remind members to join Arms Race]],
[1008604]=[[Post an announcement to remind members to join City Race]],
[1008605]=[[Post an announcement to remind members to join Royal City Clash]],
[1008606]=[[Schedule the attack time for the Union Boss.]],
[1008607]=[[Post an announcement to remind members to dispatch wagons]],
[1008608]=[[Post an announcement to remind members to join today's Union Duel]],
[1008609]=[[Member Appointment]],
[1008610]=[[Everyone, please participate in today's Union Duel! Remember to complete your Intelligence Quests and upgrade your heroes! Let's secure this win together!]],
[1008611]=[[Everyone, please participate in today's Union Duel! Remember to dispatch S+ wagons and use speed-ups for building upgrades! Let's secure this win together!]],
[1008612]=[[Everyone, please participate in today's Union Duel! Remember to complete your Intelligence Quests and use speed-ups for technology research! Let's secure this win together!]],
[1008613]=[[Everyone, please participate in today's Union Duel! Use Hero Recruitment Vouchers, level up, and ascend your heroes for maximum strength! Let's secure this win together!]],
[1008614]=[[Everyone, please participate in today's Union Duel! Remember to complete your Intelligence Quests and use speed-ups for soldier training/upgrades! Let's secure this win together!]],
[1008615]=[[Everyone, please participate in today's Union Duel! Today is the final battle. Activate your shields immediately if you don't want to engage the enemy. For those down for a fight, attack players from other Unions to earn points, or send garrisons to allies' castles! Let's secure this win together!]],
[1008616]=[[Everyone, remember to participate in today's Arms Race for S+ shards to upgrade your hero!]],
[1008617]=[[Everyone, remember to refresh your wagons until S+ ones are available and dispatch them for extra rewards!]],
[1008618]=[[Everyone, we're attacking the city today! Prepare and give it your best shot!]],
[1008619]=[[The reward limit has been reached]],
[1008620]=[[Everyone, initiate rallies against the Boss! Participants and initiators will receive big rewards!]],
[1008621]=[[You cannot claim rewards within 24 hours after changing alliances]],
[1008622]='R4/R5',
[1008623]=[[Language Selection]],
[1008624]=[[Default to text language after posting.]],
[1008625]=[[Quest refreshed. Rewards are unavailable.]],
[1008626]=[[Upgrade Castle]],
[1008627]=[[Hero Upgrade]],
[1008628]=[[Everyone, focus on upgrading your Castle to be stronger and unlock more events!]],
[1008629]=[[Everyone, level up and ascend your heroes to be stronger!]],
[1008630]=[[Everyone, remember to attack the Union Boss! Higher total damage grants better rewards!]],
[1008631]=[[Post an announcement to remind members to increase hero CP]],
[1008632]=[[Post an announcement to remind members to attack the Union Boss]],
[1008633]=[[Post an announcement to remind members to upgrade their Castles]],
[1008634]=[[Rewards are unavailable due to changes in your Union position.]],
[1008635]=[[Declare war on the city in advance so that members can prepare in advance]],
[1008636]=[[1. After completing the mission, all R4/R5 rewards are available.
2. Rewards cannot be claimed within 24 hours of switching alliances.
3. The maximum number of Alliance Mission Rewards you can claim is 11. Once the limit is reached, you will no longer be able to claim rewards today.
4. Rewards must be claimed proactively; unclaimed rewards will not be reissued.
5. Mission progress synchronization may sometimes be delayed.]],
[1008701]=[[Realm Revival]],
[1008702]=[[Revival Quest]],
[1008703]=[[Revival Pass]],
[1008704]=[[Verna's Blessing]],
[1008705]=[[Join the revival plan!]],
[1008706]=[[Day 1]],
[1008707]=[[Day 2]],
[1008708]=[[Day 3]],
[1008709]=[[Day 4]],
[1008710]=[[Day 5]],
[1008711]=[[Day 6]],
[1008712]=[[Day 7]],
[1008713]=[[Participate in the 7-day Revival Quest to earn]],
[1008714]='VIP',
[1008715]=[[Realm Revival Rules]],
[1008716]=[[Revival Chest]],
[1008717]=[[Accumulate Hammer of Renaissance to get rewards]],
[1008718]=[[Available Rewards]],
[1008719]=[[How to Obtain Revival Hammers]],
[1008720]=[[Participate in the revival mission every day to get the revival hammer]],
[1008721]=[[Revival Rewards]],
[1008722]=[[Common Reward]],
[1008723]=[[VIP Rewards]],
[1008724]=[[Raise your VIP level for more rewards]],
[1008725]=[[Raise VIP level]],
[1008726]=[[Complete 7-day Revival Quest]],
[1008727]=[[Claim massive exclusive rewards]],
[1008729]=[[Realm Revival Rules]],
[1008730]=[[Revival Grand Prize]],
[1008731]=[[1. Accumulate Revival Hammers to claim Revival Rewards. A higher VIP level yields greater rewards.]],
[1008732]=[[2. Unclaimed available rewards will be reissued via mail at the end of the event.]],
[1008733]=[[3. Complete Revival Quests to obtain Revival Hammers.]],
[1008734]=[[Revival Quest]],
[1008735]=[[1. Quests are unlocked daily during the event. Complete them to earn generous rewards.]],
[1008736]=[[2. Unclaimed rewards will be reissued via mail at the end of the event.]],
[1008737]=[[Revival Pass]],
[1008738]=[[1. Earn Revival Points to claim Revival Pass rewards.]],
[1008739]=[[2. Complete Revival Quests to earn Revival Points.]],
[1008740]=[[3. Unclaimed available rewards will be reissued via mail at the end of the event.]],
[1008745]=[[Realm Revival Reward Reissue]],
[1008746]=[[Dear Lord, your unclaimed Realm Revival grand prize has now been reissued via mail.]],
[1008747]=[[Revival Quest Reward Reissue]],
[1008748]=[[Dear Lord, your unclaimed Revival Quest rewards have now been reissued via mail.]],
[1008749]=[[Revival Pass Reward Reissue]],
[1008750]=[[Dear Lord, your unclaimed quest rewards in Revival Pass have now been reissued via mail.]],
[1008801]=[[Mystic Beast Pass]],
[1008802]=[[Mystic Beast Pass Rewards]],
[1008803]=[[Dear Lord, you have unclaimed Battle Pass rewards in the Mystic Beast Pass event. Please check]],
[1008804]=[[Mystic Beast Pass]],
[1008805]=[[1. During the event, you can accumulate Mystic Beast points to receive Mystic Beast advancement rewards.]],
[1008806]=[[2. Mystic Beast Pass points can be obtained by completing tasks, and purchasing a battle pass can receive additional task reward points]],
[1008807]=[[3. After the event ends, any unclaimed rewards will be resent via email]],
[1008811]=[[Rise of Mystic Beasts]],
[1008812]=[[Complete all quests to claim generous rewards]],
[1008813]=[[Rise of Mystic Beasts Guide]],
[1008814]=[[1. Complete quests during the event to earn rewards. Completing a row or column grants extra rewards, and completing all quests grants generous rewards.]],
[1008815]=[[2. Unclaimed quest rewards will be sent via mail after the event.]],
[1008816]=[[Quest Details]],
[1008817]='Rewards',
[1008818]='Claim',
[1008819]='Go',
[1008820]=[[Mystic Beast]],
[1008821]=[[Rise of Mystic Beasts Reward Reissue]],
[1008822]=[[Dear Lord, your unclaimed rewards from the Rise of Mystic Beasts event have been reissued via mail. Check them out!]],
[1008823]=[[Buildable at Lv. 10 Mystic Beast]],
[1008830]=[[Complete 100 Intelligence Quests]],
[1008831]=[[Upgrade a Mystic Beast to Lv.10]],
[1008832]=[[Perform Union Donation 100 times]],
[1008833]=[[Complete 1 Intelligence Quest.]],
[1008834]=[[Log in for 1 day]],
[1008835]=[[Upgrade a Mystic Beast to Lv.3]],
[1008836]=[[Craft Crystal Cores 1 time at the Crystal Core Research Center]],
[1008837]=[[Upgrade a Mystic Beast to Lv.5]],
[1008838]=[[Perform Union Donation 20 times]],
[1008839]=[[Log in for 5 days in a row]],
[1008840]=[[Upgrade a Mystic Beast to Lv.20]],
[1008841]=[[Craft Crystal Cores 2 times at the Crystal Core Research Center]],
[1008842]=[[Dispatch 10 Trade Wagons]],
[1008843]=[[Help Allies 200 times]],
[1008844]=[[Speed up 3,000 minutes]],
[1008845]=[[Complete all 15 Rise of Mystic Beasts quests]],
[1008861]=[[Mystic Beast Sign-In]],
[1008862]=[[Day 1]],
[1008863]=[[Day 2]],
[1008864]=[[Day 3]],
[1008865]=[[Day 4]],
[1008866]=[[Day 5]],
[1008867]=[[Day 6]],
[1008868]=[[Day 7]],
[1008869]=[[Log in every day to claim abundant rewards!]],
[1008870]=[[Reward available after %s]],
[1008891]='Nameplate',
[1008892]=[[Default Nameplate]],
[1008893]=[[Test nameplate (7 days)]],
[1008894]=[[Test results]],
[1009001]=[[Union Manager]],
[1009002]=[[Union Manager will transfer Leadership in {%s1} hour(s)]],
[1009003]=[[Union Manager will transfer Leadership in {%s1} minute(s)]],
[1009004]=[[Union Manager is transferring and cannot be contacted temporarily.]],
[1009005]=[[Union Manager is transferring and cannot be viewed temporarily.]],
[1009006]=[[Allies, search the Elite Zombie [Klass] in the [World] and initiate rallies to defeat it for massive Union Contribution and Hero Shards.\n[Union Contribution]: Can be exchanged for a variety of valuable items at the Union Store.]],
[1009007]=[[Allies, let's grow stronger together!\n[Castle Level]: Upgrading your Castle not only increases power but also unlocks new features and rewards.\n[Hero Training]: Leveling up your heroes and increasing their stars greatly boosts your strength.]],
[1009101]=[[Storm Arena]],
[1009102]=[[The gameplay will be available in {%s1}! Stay tuned!]],
[1009103]=[[Reaching the castle level of {%s1} unlocks {%s2} gameplay]],
[1009104]=[[Show your strength!]],
[1009105]=[[Battle Record]],
[1009106]='Advanced',
[1009107]='Intermediate',
[1009108]=[[Combat Power Ranking]],
[1009109]=[[Not on the list]],
[1009110]=[[Go to Ascension]],
[1009111]=[[Heroes whose combat power rankings meet the requirements can enter this arena!]],
[1009112]=[[Hero Power Ranking {%s1}]],
[1009113]=[[Daily Treasure Chest]],
[1009114]=[[Number of challenges]],
[1009115]=[[All members of the alliance of the player who won the {%s1}th place in Storm Arena will receive a corresponding alliance reward]],
[1009116]=[[Today's challenge times have been used up, please try again tomorrow]],
[1009117]=[[Free Refresh]],
[1009118]=[[Challenge {%s1} again to receive daily treasure chest rewards]],
[1009119]=[[Complete {%s1} challenges to unlock quick battles]],
[1009120]='promotion',
[1009121]=[[Your ranking has been promoted to the top <color=#E63838><size=30>{%s1}</size></color>!]],
[1009122]=[[Do you want to advance to the <color=#2877CA><size=30>High-Level Arena</size></color>?]],
[1009123]=[[Advanced Arena Rewards Preview]],
[1009124]=[[The promotion quota will be retained during this event. Close the pop-up window and click the promotion button to be promoted.]],
[1009125]=[[Initial ranking]],
[1009126]=[[Ranking drop]],
[1009127]=[[Rising rankings]],
[1009128]=[[There is a delay in updating the data in the list]],
[1009200]=[[Halloween Party]],
[1009201]=[[Halloween Sign-in]],
[1009202]=[[Lucky Pumpkin]],
[1009203]=[[Ghost Shop]],
[1009204]=[[Pumpkin Battle Pass]],
[1009205]=[[Carnival Party]],
[1009206]=[[Ghost Party]],
[1009207]=[[Ghost's Gift]],
[1009208]=[[Pumpkin Wars]],
[1009209]=[[Daily Pumpkin Gift Pack]],
[1009210]=[[Log in every day to receive gifts from the Pumpkin Ghost]],
[1009211]=[[Day 1]],
[1009212]=[[Day 2]],
[1009213]=[[Day 3]],
[1009214]=[[Day 4]],
[1009215]=[[Day 5]],
[1009216]=[[Day 6]],
[1009217]=[[Day 7]],
[1009218]=[[Reward available after %s]],
[1009219]=[[Come back tomorrow]],
[1009220]='Claim',
[1009230]=[[Lucky Pumpkin Description]],
[1009231]=[[Reward Preview]],
[1009232]=[[Gameplay Instructions]],
[1009233]=[[There are 3 identical patterns]],
[1009234]=[[There are 2 identical patterns]],
[1009235]=[[Any 3 different patterns]],
[1009236]=[[<color=#FFCE1A>Pumpkin Machine Lottery</color>]],
[1009237]=[[1. During the event, lords can use <color=#FFCE1A>【Pumpkin Coins】</color> to draw prizes, and each draw will earn rewards.]],
[1009238]=[[2. In the lottery results, the more identical patterns you have, the better the reward.]],
[1009239]=[[3. Use the 5x mode to draw, the consumption and reward will be 5 times the initial]],
[1009240]=[[4. Accumulate the number of lucky draws to get rich rewards\n]],
[1009241]=[[<color=#FFCE1A>Item Recovery and Reward Reissue</color>]],
[1009242]=[[1. At the end of the event, <color=#FFCE1A>[Pumpkin Coins]</color> will be recycled into acceleration items in proportion and will be distributed via email.]],
[1009243]=[[2. Recycling ratio: Pumpkin Coin*1 is recycled into 1 minute universal acceleration*30]],
[1009244]=[[3. At the end of the event, rewards for completed but unclaimed sessions will be reissued via email.]],
[1009245]='<color=#FFCE1A>Other</color>',
[1009246]=[[1. During the event, you can get a surprise box by purchasing a pumpkin gift pack]],
[1009247]=[[If you draw the same pattern, you will get a bigger reward.]],
[1009248]='1x',
[1009249]='5x',
[1009250]='lottery',
[1009251]='x1',
[1009252]='x5',
[1009253]=[[Remaining times today]],
[1009254]=[[Skip animation]],
[1009255]='Record',
[1009256]=[[1x lottery]],
[1009257]=[[5x lottery]],
[1009258]=[[Battle Pass Acquisition]],
[1009259]=[[Free Pumpkin Gift Bag]],
[1009260]=[[Premium Pumpkin Gift Pack]],
[1009261]=[[Rare Pumpkin Pack]],
[1009262]=[[Premium Pumpkin Gift Pack]],
[1009263]=[[Deluxe Pumpkin Gift Pack]],
[1009264]=[[Super Pumpkin Gift Pack]],
[1009265]=[[You can still purchase %s times]],
[1009266]=[[Lucky Pumpkin Reward Reissue]],
[1009267]=[[Dear Lord, you have unclaimed rewards for the Halloween Party-Lucky Pumpkin event. We will now resend them via email. Please check.]],
[1009268]=[[Lucky Pumpkin Item Recycling]],
[1009269]=[[Dear Lord, your <color=#FFCE1A>【Pumpkin Coins】</color>*<color=#FFCE1A>*%s1</color> in the Halloween Party - Lucky Pumpkin event has been exchanged for %s3 at a rate of %s2. Please check.]],
[1009270]=[[Epic Pumpkin Bundle]],
[1009271]=[[Legendary Pumpkin Pack]],
[1009272]=[[Lucky comes! Get the following rewards]],
[1009280]=[[Great value]],
[1009281]='rare',
[1009282]='return',
[1009283]=[[Redemption Tips]],
[1009284]=[[Remaining activities:]],
[1009285]=[[Sold out]],
[1009286]=[[Attribute: All Attack +5%]],
[1009287]=[[Ghost Shop Description]],
[1009288]=[[1. You can use mysterious candies to exchange for rare items in this event.]],
[1009289]=[[2. At the end of the event, <color=#FFCE1A>[Mysterious Candy]</color> will be recycled into speed-up items in proportion and will be distributed via email.]],
[1009290]=[[3. Recycling ratio: Mysterious Candy*1 recycling is 1 minute acceleration*100]],
[1009291]=[[Ghost Shop Prop Recycling]],
[1009292]=[[Dear Lord, your <color=#FFCE1A>[Mysterious Candy]</color>*<color=#FFCE1A>*%s1</color> in the Halloween Party - Ghost Shop event has been exchanged for %s3 at a rate of %s2. Please check.]],
[1009295]=[[Feast Ranking]],
[1009296]=[[Submit the Sweet Pumpkin Upgrade Banquet and get great rewards!]],
[1009297]='submit',
[1009298]=[[Next level unlocked: %s]],
[1009299]=[[There are no parties in the league]],
[1009300]=[[My Party]],
[1009301]=[[Party in progress]],
[1009302]='hold',
[1009303]=[[You are late. The reward has been claimed.]],
[1009304]=[[It's too late. The party is over.]],
[1009305]=[[Submit and get rewards]],
[1009306]=[[Owned by: %s]],
[1009307]='personal',
[1009308]='alliance',
[1009309]='Rankings',
[1009310]=[[Reward Preview]],
[1009311]=[[Not yet joined the alliance]],
[1009312]=[[Banquet Rewards]],
[1009313]=[[Carnival Feast Description]],
[1009314]=[[<color=#FFCE1A>Carnival Party</color>]],
[1009315]=[[1. Each time you submit a <color=#FFCE1A>Sweet Pumpkin</color>, you will receive 10 <color=#FFCE1A>Deliciousness</color> points. When the deliciousness reaches the specified value, the banquet level will be upgraded.]],
[1009316]=[[2. The banquet level will gradually unlock the upper limit as the event lasts.]],
[1009317]=[[3. During the event, the deliciousness submitted by individuals and alliances will be ranked separately. If the scores are the same, the one with the shorter warrior will be ranked higher. At the end of the event, rewards will be distributed according to the ranking and delivered by email.]],
[1009318]=[[<color=#FFCE1A>Ghost Party</color>]],
[1009319]=[[1. Use <color=#FFCE1A>Ghost's Token</color> to hold a ghost party. The party lasts for 15 minutes. During this time, you and other lords in the alliance can participate in the party and receive party rewards.]],
[1009320]=[[2. Each party can be attended by up to 20 people, and each person can receive a maximum of 10 queue rewards per day.]],
[1009321]=[[<color=#FFCE1A>Item Recovery and Reward Reissue</color>]],
[1009322]=[[1. After the event, the Sweet Pumpkin will be recycled into a 1-minute acceleration prop. For example, 1 Sweet Pumpkin will be recycled into 10 1-minute acceleration props, which will be distributed via email.]],
[1009323]=[[2. After the event, the ghost's token will be recycled into 1 minute of acceleration. The ratio is 1 ghost token*100, which will be recycled into 1 minute of acceleration. It will be distributed via email.]],
[1009324]=[[Carnival Feast Reward Reissue]],
[1009325]=[[Dear Lord, you have unclaimed banquet level rewards in the Halloween Party-Carnival Feast event. We are now reissuing them via email. Please check.]],
[1009326]=[[Sweet Pumpkin Prop Recycling]],
[1009327]=[[Dear Lord, your <color=#FFCE1A>[Sweet Pumpkin]</color>*<color=#FFCE1A>*%s1</color> in the Halloween Party - Carnival Feast is exchanged for %s3 at a rate of %s2. Please check.]],
[1009328]=[[Ghost Keepsake Item Recovery]],
[1009329]=[[Dear Lord, your <color=#FFCE1A>[Ghost's Token]</color>*<color=#FFCE1A>*%s1</color> in the Halloween Party - Carnival Feast has been exchanged for %s3 at a rate of %s2. Please check.]],
[1009340]=[[Participate in the following gameplay to get sweet pumpkin]],
[1009341]='Go',
[1009342]=[[Participate in the lucky draw to get <color=#A370B5> (not included in today's limit)</color>]],
[1009343]=[[Pumpkin Battle Pass]],
[1009344]=[[Upgrading the Pumpkin Battle Pass will earn you <color=#A370B5> (not counted towards today's limit)</color>]],
[1009345]=[[Intelligence mission]],
[1009346]=[[Completing intelligence missions can earn]],
[1009347]=[[Elite Zombies]],
[1009348]=[[Defeat the elite zombies to get]],
[1009349]='zombies',
[1009350]=[[Defeat the Iron Ore/Food/Gold Zombie to get]],
[1009351]=[[World Boss]],
[1009352]=[[Attacking the World Boss can get]],
[1009353]=[[Dark Night Trial]],
[1009354]=[[Complete the challenge to get]],
[1009355]=[[Alliance Boss]],
[1009356]=[[Participate in the Alliance Boss to get]],
[1009357]=[[Verna's Trial]],
[1009358]=[[Completing Verna's challenge will earn you]],
[1009359]=[[Lucky Pumpkin]],
[1009370]=[[Advanced Pumpkin Battle Pass]],
[1009371]=[[Deluxe Pumpkin Battle Pass]],
[1009372]=[[Pumpkin Battle Pass Help]],
[1009373]=[[1. During the event, accumulate mischief points to upgrade the battle pass level and get rewards]],
[1009374]=[[2. Unlock advanced and deluxe battle passes for richer rewards]],
[1009375]=[[3. After the event ends, any unclaimed rewards will be resent via email]],
[1009376]=[[Pumpkin Battle Pass Reward Reissue]],
[1009377]=[[Dear Lord, you have unclaimed battle pass rewards in the Halloween Party-Pumpkin Battle Pass event. We have resent them via email. Please check.]],
[1009380]=[[Ghost's Gift Reward Reissue]],
[1009381]=[[Dear Lord, you have unclaimed rewards in the Halloween Party-Ghost Gift event. We are now sending them via email. Please check.]],
[1009801]=[[Violent BOSS]],
[1009802]='Lv.{%s1}{%s2}',
[1009803]=[[My Rank]],
[1009804]=[[Current damage:]],
[1009805]=[[No. {%s}:]],
[1009806]=[[No Rank]],
[1009807]=[[Server {%s} point appears]],
[1009808]=[[Coming soon]],
[1009809]=[[Wait for the BOSS to appear]],
[1009810]=[[Has appeared]],
[1009811]=[[The BOSS has appeared, go and contribute damage!]],
[1009812]='weak',
[1009813]=[[Already weak, go refresh the damage to win the ranking!]],
[1009814]=[[Rewards Preview]],
[1009815]=[[Deal {%s1}/{%s2} damage to the BOSS]],
[1009816]=[[You can get rewards immediately after the BOSS is weakened. Otherwise, the rewards will be sent via email after the event ends.]],
[1009817]=[[DMG Ranking]],
[1009818]=[[Displays the current highest damage ranking]],
[1009819]=[[Injury details]],
[1009820]=[[Damage to {%s}]],
[1009821]=[[Total DMG]],
[1009822]=[[You have not joined the alliance yet]],
[1009823]=[[Alliance Damage Rankings]],
[1009824]=[[No data yet]],
[1009825]=[[Refresh the highest damage and win the ranking!]],
[1009826]=[[Battle Plan]],
[1009827]=[[Full-Scale Attack]],
[1009828]=[[The current formation is on an expedition, and the hero cannot be changed]],
[1009829]=[[There are currently no teams available to go]],
[1009830]=[[The team currently able to participate in the battle is {%s}. Do you want to continue the expedition?]],
[1009831]=[[The violent BOSS is attacking the kingdom, please fight him immediately.]],
[1009832]='Rules',
[1009833]=[[【Event Background】]],
[1009834]=[[Three mysterious altars suddenly appeared around Congress. The powerful BOSSes within the altars awakened and stormed the Congress. These BOSSes were so powerful that a single lord could not withstand them, requiring the combined efforts of all lords in the server to defeat them.]],
[1009835]=[[【BOSS Information】]],
[1009836]=[[1. At 0:00, 6:00, and 12:00 on the server, a violent BOSS will wake up from the altar.]],
[1009837]=[[2. Only Lords with a base level ≥ 8 can participate in the attack; there is no limit on the number of attacks on each violent BOSS.]],
[1009838]=[[3. Each BOSS has different characteristics. Attacking its weaknesses can cause more damage.]],
[1009839]=[[4. After attacking the violent BOSS to a certain amount of damage, the BOSS will enter a weak state. You can get rich rewards by executing the [Defeat] command on the weak BOSS.]],
[1009840]=[[5. Even if the BOSS has entered a weakened state, the Lord can still continue to attack and accumulate damage. All damage will be counted towards the total damage ranking to compete for ranking rewards.]],
[1009841]=[[【Special Mechanism】]],
[1009842]=[[1. When the situation is urgent, the lord can choose "All-out Attack" - all teams can attack at the same time and focus on hitting the violent BOSS.]],
[1009843]=[[2. The "All Army Attack" feature will be released in stages. Please refer to the event prompts for details.]],
[1009844]=[[【Reward Description】]],
[1009845]=[[1. At 24:00 on the server, any BOSSes that haven't been eliminated will flee. Players who damage any violent BOSS during the event will receive participation rewards via email; the more damage you deal to the BOSS, the greater the reward.]],
[1009846]=[[2. The top 100 lords who cause the most damage to the BOSS will receive additional ranking rewards.]],
[1009847]=[[3. If you successfully defeat three BOSSes in this event, a stronger BOSS will appear in the next event, and the rewards will be more generous.]],
[1009848]=[[4. If you fail to defeat any violent BOSS in this event, the Legion of Doom will send a lower-level BOSS to participate in the battle, and you will not be able to obtain more generous subsequent rewards.]],
[1009849]=[[Weakness Progress]],
[1009851]=[[BOSS is weakened]],
[1009852]=[[Damaged by a human hero]],
[1009853]=[[Damaged by Forest Heroes]],
[1009854]=[[Damaged by Night Heroes]],
[1009855]=[[Individual ranking]],
[1009856]=[[League Rankings]],
[1009857]=[[Two-Headed Ogre]],
[1009858]=[[Frenzied Zombie]],
[1009859]=[[Undead Dragon]],
[1009860]=[[Rage BOSS Ranking Rewards]],
[1009861]=[[Congratulations on ranking {%s1}th in total damage in the Rage BOSS event. Below is your ranking reward, please check it out!]],
[1009862]=[[Rage BOSS Server-wide Rewards]],
[1009863]=[[Congratulations on conquering <color=#FF4D2A>{%s1}</color>{%s2}/{%s3} HP today. Thank you for your heroic efforts. Here are your rewards, please check!]],
[2030001]='',
[2030002]='',
[2030003]='',
[2030004]='',
[2030005]='',
[2030006]='',
[2030007]='',
[2030008]='',
[2030009]='',
[2030010]='',
[2030011]='',
[2030012]='',
[2030014]='',
[2030015]='',
[2130001]='',
[2130002]='',
[2130003]='',
[2130004]='',
[2130005]='',
[2130006]='',
[2130007]='',
[2130008]='',
[2130009]='',
[2130010]='',
[2130011]='',
[2130012]='',
[2130014]='',
[2130015]='',
[30800001]=[[Hot Pursuit]],
[105900001]=[[Rapid Slashes]],
[112200001]=[[Leave None]],
[113200001]=[[Participate in 3 event levels to claim all rewards!]],
[113200003]=[[1. Each event will have 5 levels. After completing each level, you can choose to proceed to the next level or exit and return to the event interface. However, please note that if you exit midway, you will have to start from the first level again next time you try the challenge.]],
[113200004]=[[2. If you pass 5 levels in a row or fail a level in the middle, you will enter the level settlement.]],
[113200005]=[[3. Participate in 3 event levels to claim all rewards!]],
[32400001]='Interlocking/Rings',
[32410001]=[[Tap to Start]],
[32410002]=[[Spin the rings to unlock all colored rings.]],
[32410003]='Stage',
[104600001]=[[Kill Arrow]],
[104610001]=[[Tap the block to move it toward its arrow to eliminate it.]],
[104610002]=[[Drag the screen to rotate the block.]],
[104610003]=[[Tap the Reset button to reset the direction of the arrows. Has a 15s cooldown.]],
[104610004]=[[Item blocks grant various effects when triggered.]],
[104610005]=[[Get a 10x combo to trigger a random item effect.]],
[104610006]=[[Get a 30x combo to trigger an Angel's Blessing that eliminates multiple blocks at once.]],
[104610007]=[[Ice blocks cannot be eliminated by tapping them. They must be eliminated with items.]],
[104610008]=[[Demon blocks must be eliminated 3 times as fast as possible or bad things will happen!]],
[104610009]=[[Tap any blank space to close.]],
[104610010]=[[Tap the arrow button below to rotate the block model.]],
[104610011]='Level%s',
[114300001]='Connect/Dots',
[114310001]='Stage',
[114310002]='Tips',
[114310003]=[[Connect dots of the same color with disjoint lines.]],
[114310004]=[[Tap to view the answer]],
[114310005]='Confirm',
[115700001]=[[No Escape]],
[100200001]=[[Coloring squares]],
[100000001]=[[Cat Worm]],
[120700001]='Life/Saver',
[14500001]=[[Ball maze for kids]],
[119900001]='Block/Defense'}