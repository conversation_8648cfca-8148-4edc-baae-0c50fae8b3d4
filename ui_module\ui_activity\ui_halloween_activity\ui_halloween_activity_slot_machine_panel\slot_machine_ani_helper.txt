-- SlotMachine.lua

-- 引入必要的 C# 类库
local GameObject = CS.UnityEngine.GameObject
local Vector2 = CS.UnityEngine.Vector2
local Vector3 = CS.UnityEngine.Vector3
local LeanTween = CS.LeanTween
local LeanTweenType = CS.LeanTweenType
local Button = CS.UnityEngine.UI.Button
local VerticalLayoutGroup = CS.UnityEngine.UI.VerticalLayoutGroup

local require = require

local logger = require "logger"

local math = math


local halloween_slot_item = require "halloween_slot_item"


---@class SlotMachine
local SlotMachine = {}
SlotMachine.__index = SlotMachine


local UI_CONST = {
    PER_ITEM_POS_Y = 178.41,
}


function SlotMachine.new()
    local self = setmetatable({}, SlotMachine)

    -- =============== 参数配置 ===============
    self.columns = {}            -- { [1] = column1_transform, [2] = column2_transform, ... }
    self.isSpinning = false      -- 是否正在旋转

    self.spinButton = nil
    self.iconIdList = {1, 2, 3, 4, 5, 6, 7} -- 图标ID列表
    self.patternIdList = {1, 2, 3, 4, 5, 6, 7}
    
    self.iconHeight = UI_CONST.PER_ITEM_POS_Y        -- 单个图标的高度 (需要与你的Prefab或设置一致)
    self.visibleIcons = 3        -- 可见区域的图标数量

    -- 动画参数
    self.baseSpinDuration = 2.0  -- 第一列的基础旋转时间
    self.columnStopDelay = 0.5   -- 每列之间停止的延迟时间
    self.spinLoops = 5           --

    -- 内部变量
    self.iconsPerColumn = {}     -- { [1] = {icon_image1, icon_image2, ...}, ... }
    self.columnLayouts = {}      -- 存储每列的 VerticalLayoutGroup

    return self
end

---@public pattern中的0表示随机值，这里把它实际roll出来
---@param patternData table  {n1,n2,0} ...
---@return table {n1, n2, n3}
function SlotMachine.RollPattern(patternData, randomList)
    -- 创建结果表的副本，避免修改原始数据
    local result = {}
    for i, v in ipairs(patternData) do
        result[i] = tonumber(v)
    end
    
    -- 收集已经存在的值（非0的值）
    local usedValues = {}
    for _, value in ipairs(result) do
        if value ~= 0 then
            usedValues[value] = true
        end
    end
    
    -- 创建可用的随机值列表（排除已使用的值）
    local availableValues = {}
    for _, value in ipairs(randomList) do
        if not usedValues[value] then
            table.insert(availableValues, value)
        end
    end
    
    -- 遍历结果，替换所有的0
    for i, value in ipairs(result) do
        if value == 0 then
            -- 如果没有可用的随机值了，可以根据需要处理这种情况
            if #availableValues == 0 then
                logger.Error("没有足够的可用随机值来替换所有的0")
            end
            
            -- 从可用值中随机选择一个
            local randomIndex = math.random(1, #availableValues)
            local selectedValue = availableValues[randomIndex]
            
            -- 设置结果值
            result[i] = selectedValue
            
            -- 从可用值列表中移除已选择的值，确保不重复
            table.remove(availableValues, randomIndex)
        end
    end
    
    return result
end

function SlotMachine.LoadSprite(name, cb)
    ---@type GWAssetMgr
    local gw_asset_mgr = require "gw_asset_mgr"
    gw_asset_mgr:LoadGoodsIcon(name, function(sprite)
        cb(sprite)
    end)
end

-- 初始化
function SlotMachine:Init(iconIdList, patternIdList, columns_gameobject, spin_button_gameobject)
    self.iconIdList = iconIdList
    self.patternIdList = patternIdList

    self.pattern_icon_id_dict = {}
    for i = 1, #patternIdList do
        local pattern_id = patternIdList[i]
        local icon_id = iconIdList[i]
        self.pattern_icon_id_dict[pattern_id] = icon_id
    end


    for i = 1, #columns_gameobject do
        self.columns[i] = columns_gameobject[i].transform
        self.columnLayouts[i] = columns_gameobject[i]:GetComponent(typeof(VerticalLayoutGroup))

        self.iconsPerColumn[i] = {}
        local icon_transforms = columns_gameobject[i].transform
        for j = 0, icon_transforms.childCount - 1 do
            local icon_item = icon_transforms:GetChild(j):GetComponent("RectTransform")
            table.insert(self.iconsPerColumn[i], icon_item)
        end
    end

    self.spinButton = spin_button_gameobject:GetComponent(typeof(Button))

    --print("老虎机初始化完成!")

    --self:RenderState(cur_state)
end

function SlotMachine:GetIconIdByPattern(pattern)
    return self.pattern_icon_id_dict[pattern]
end

function SlotMachine:SetStartState(last_patterns)
    last_patterns = last_patterns or {}
    local new_state = {column_state_list = {}}

    for i = 1, #self.iconsPerColumn do
        local column_state = {
            pattern_id_list = {},
            icon_id_list = {},
            pos_index = 1,
        }
        local per_column_num = #self.iconsPerColumn[i]
        for j = 1, per_column_num do
            -- 第二个设置上次状态
            if j == 2 then
                column_state.pattern_id_list[j] = last_patterns[i] or math.random(1, #self.patternIdList)
            else
                column_state.pattern_id_list[j] = math.random(1, #self.patternIdList)
            end


            column_state.icon_id_list[j] = self:GetIconIdByPattern(column_state.pattern_id_list[j])
        end


        -- local patternID = tonumber(patternGroup[i])
        -- local patternCfg = game_scheme:SlotPattern_0(patternID, act_id)

        new_state.column_state_list[i] = column_state
    end

    self:RenderState(new_state)
end

function SlotMachine:RenderState(state)
    local column_state_list = state.column_state_list
    for i = 1, #column_state_list do
        local column_state = column_state_list[i]
        self:RenderColumn(i, column_state.icon_id_list, column_state.pos_index)
    end
end

function SlotMachine:RenderColumn(column_index, icon_id_list, pos_index)
    local icon_transforms = self.iconsPerColumn[column_index]
    for i = 1, #icon_transforms do
        local icon_id = icon_id_list[i]
        local _i = i
        SlotMachine.LoadSprite(icon_id, function(sprite)
            halloween_slot_item.Set(icon_transforms[_i], sprite)
        end)
    end

    local origin_pos = self.columns[column_index]:GetComponent("RectTransform").anchoredPosition
    self.columns[column_index]:GetComponent("RectTransform").anchoredPosition = Vector2(origin_pos.x, UI_CONST.PER_ITEM_POS_Y * pos_index - 1)
end


-- 点击“抽奖”按钮时调用
function SlotMachine:OnSpin(iconIdList, endcb)
    if self.isSpinning then
        --print("正在旋转中，请勿重复点击...")
        return
    end

    --print("开始抽奖!")
    self.isSpinning = true
    self.spinButton.interactable = false

    -- 依次启动每一列的滚动
    for i = 1, #self.columns do
        -- 计算每一列的最终停止时间
        local stopDuration = self.baseSpinDuration + (i - 1) * self.columnStopDelay
        self:StartColumnRoll(i, stopDuration, iconIdList[i])
    end

    -- 在最后一列停止后，解锁按钮
    local totalDuration = self.baseSpinDuration + (#self.columns - 1) * self.columnStopDelay
    LeanTween.delayedCall(totalDuration, function()
        self.isSpinning = false
        self.spinButton.interactable = true
        --print("抽奖结束!")

        endcb()
    end)
end

-- 启动单列的滚动动画
function SlotMachine:StartColumnRoll(columnIndex, duration, resultID)
    local column = self.columns[columnIndex]
    local icons = self.iconsPerColumn[columnIndex]
    local totalIconCount = #icons

    -- 1. 禁用LayoutGroup，防止与LeanTween冲突
    if self.columnLayouts[columnIndex] and self.columnLayouts[columnIndex].enabled then
        self.columnLayouts[columnIndex].enabled = false
    end

    local finalIconIndex = totalIconCount - 2

    for i = 1, totalIconCount do
        local icon = icons[i]

        local ramdom_id = self.iconIdList[math.random(1, #self.iconIdList)]
        local icon_id = ramdom_id
        if i == finalIconIndex + 1 then
            icon_id = resultID
        end
        SlotMachine.LoadSprite(icon_id, function(sprite)
            halloween_slot_item.Set(icon, sprite)
        end)
    end
    
    -- 3. 计算动画的起始和结束位置
    -- 结束位置Y：让目标图标居中。假设锚点在上部(top)，初始Y是0。
    -- 目标图标之前的图标总高度 = (finalIconIndex - 1 - math.floor(self.visibleIcons/2)) * self.iconHeight
    local endY = self.iconHeight * (finalIconIndex - 1)
    
    -- 起始位置Y：在结束位置的基础上，向上移动 N 圈的高度，形成快速滚动效果
    local reelHeight = totalIconCount * self.iconHeight
    local startY = 0
    
    -- 4. 重置到起始位置
    column.anchoredPosition = Vector2(column.anchoredPosition.x, startY)

    -- 5. 执行LeanTween动画
    LeanTween.moveLocalY(column.gameObject, endY, duration)
        :setEase(LeanTweenType.easeOutQuint) -- 使用 easeOutQuint 效果，开始快，结束慢，非常真实
        :setOnComplete(function()
            --print("第 " .. columnIndex .. " 列停止")
            -- 动画结束后重新启用LayoutGroup以方便编辑
            -- if self.columnLayouts[columnIndex] then
            --     self.columnLayouts[columnIndex].enabled = true
            -- end
        end)
end

return SlotMachine