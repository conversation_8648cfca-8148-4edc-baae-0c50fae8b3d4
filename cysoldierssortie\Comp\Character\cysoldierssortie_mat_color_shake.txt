local cysoldierssortie_base_feedback = require("cysoldierssortie_base_feedback")
local cysoldierssortie_mat_color_shake = bc_Class("cysoldierssortie_mat_color_shake",cysoldierssortie_base_feedback) --类名用小游戏名加后缀保证全局唯一
local cysoldierssortie_dotween_extern = require("cysoldierssortie_dotween_extern")
local cysoldierssortie_urp_ecs = cysoldierssortie_urp_ecs
local entity_manager = require("entity_manager")

local URP = CS.UnityEngine.Rendering.GraphicsSettings.renderPipelineAsset ~= nil
local SelfLightColorID = CS.UnityEngine.Shader.PropertyToID("_SelfLightColor")
local floor = math.floor
local util = require("util")
local SetMaterialFloat = util.IsCSharpClass(CS.GPUAnimationBaker.Engine.ShaderIDs)

function cysoldierssortie_mat_color_shake.__init(self,data)
    cysoldierssortie_base_feedback.__init(self,data)
    self._strength = data.strength
    self._duration = data.duration
    self._color = data.color
    
    if URP and (not cysoldierssortie_urp_ecs or not entity_manager.URP22) then
        SetMaterialFloat = false
    end
end

function  cysoldierssortie_mat_color_shake.__delete(self)
    cysoldierssortie_base_feedback.__delete(self)
    if self._tweener then
        self._tweener = nil
    end
end

function cysoldierssortie_mat_color_shake:StartFeedBack(transform,obj)
    cysoldierssortie_base_feedback.StartFeedBack(self,transform)
    if not self._initOnce then
        if self._entity then
            if SetMaterialFloat then
                self._colorPacked = floor(self._color.r * 127) * 256 * 256 * 256 + floor(self._color.g * 127) * 256 * 256 + floor(self._color.b * 127) * 256
                self._entity:SetMaterialFloat(SelfLightColorID, self._colorPacked + 127)
            else
                self._entity:SetRenderPropertiesColor("_SelfLightColor",self._color)
                self._entity:SetRenderPropertiesFloat("_EnableSelfLight",1)
            end
        else
            obj:SetRenderPropertiesColor("_SelfLightColor",self._color)
            obj:SetRenderPropertiesFloat("_EnableSelfLight",1)
        end
        self._initOnce = true
    end
    
    if not self._tweener then
        self._tweener =  cysoldierssortie_dotween_extern.DoValueVibration(
        function(newValue)
            if self._entity then
                if SetMaterialFloat then
                    self._entity:SetMaterialFloat(SelfLightColorID, self._colorPacked + floor(newValue * 127))
                else
                    self._entity:SetRenderPropertiesFloat("_SelfLightness",newValue)
                end
            else
                obj:SetRenderPropertiesFloat("_SelfLightness",newValue)
            end
        end,
                0,self._strength,self._duration,function()
            self._tweener = nil
        end,transform)
    end
end

return cysoldierssortie_mat_color_shake