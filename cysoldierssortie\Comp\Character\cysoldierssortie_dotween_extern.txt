local bc_dotween_extern = require "bc_dotween_extern"
local cysoldierssortie_dotween_extern = bc_Class("cysoldierssortie_dotween_extern", bc_dotween_extern)
local Tweening = CS.DG.Tweening
local bc_CS_Quaternion = bc_CS_Quaternion
local bc_CS_Vector3 = bc_CS_Vector3
local typeof = typeof
local CreateInstance = CS.System.Array.CreateInstance

function cysoldierssortie_dotween_extern.DOScaleVibration(transform , endValue , duration , vibrato,random, completeCallback, ease)
    local tweener = transform:DOShakeScale(duration,endValue,vibrato,random)
    if ease then
        tweener:SetEase(ease)
    else
        tweener:SetEase(Tweening.Ease.Linear)
    end
    if completeCallback ~= nil then
        tweener:OnComplete(completeCallback)
    end
    -- 生命周期绑定到Mono对象上
    tweener:SetLink(transform.gameObject)
    tweener:SetA<PERSON><PERSON>ill(true)
    return tweener
end

function cysoldierssortie_dotween_extern.DoValueVibration(getFunc,startValue,endValue,duration,completeCallback,linkTransf)
    local tweener = Tweening.DOTween.To(
            function(newValue)
                if getFunc then
                    getFunc(newValue)
                end
            end,
            startValue,
            endValue,
            duration*0.3):
    OnComplete(function()
        local backTweener = Tweening.DOTween.Sequence()
                                    :AppendInterval(duration*0.2)
                                    :Append(Tweening.DOTween.To(
                function(newValue)
                    if getFunc then
                        getFunc(newValue)
                    end
                    --mat:SetFloat("_SelfLightness", newValue)
                end,
                endValue,
                startValue,
                duration*0.5):SetAutoKill(true)) -- Fade back

        if completeCallback ~= nil then
            backTweener:OnComplete(completeCallback)
        end
    end)
    -- 生命周期绑定到Mono对象上
    if linkTransf then
        tweener:SetLink(linkTransf.gameObject)
    end
    tweener:SetAutoKill(true)
    return tweener
end

function cysoldierssortie_dotween_extern.DoJump(transform , targetPos ,jumpHeight,jumpNum, duration , completeCallback, ease)
    local tweener = transform:DOJump(targetPos,jumpHeight,jumpNum,duration)
    if ease then
        tweener:SetEase(ease)
    else
        tweener:SetEase(Tweening.Ease.Linear)
    end
    if completeCallback ~= nil then
        tweener:OnComplete(completeCallback)
    end
    -- DoJump是个Sequence, 生命周期要绑定到Mono对象上
    tweener:SetLink(transform.gameObject)
    tweener:SetAutoKill(true)
    return tweener
end

function cysoldierssortie_dotween_extern.DoMoveY(transform , endValue , duration , completeCallback, ease)
    local tweener = transform:DOMoveY(endValue, duration, false)
    if ease then
        tweener:SetEase(ease)
    else
        tweener:SetEase(Tweening.Ease.Linear)
    end
    if completeCallback ~= nil then
        tweener:OnComplete(completeCallback)
    end
    tweener:SetAutoKill(true)
    return tweener
end

function cysoldierssortie_dotween_extern.DoLocalMoveZ(transform , endValue , duration , completeCallback, ease)
    local tweener = transform:DOLocalMoveZ(endValue, duration)
    if ease then
        tweener:SetEase(ease)
    else
        tweener:SetEase(Tweening.Ease.Linear)
    end
    if completeCallback ~= nil then
        tweener:OnComplete(completeCallback)
    end
    tweener:SetAutoKill(true)
    return tweener
end

function cysoldierssortie_dotween_extern.DoLocalMoveY(transform , endValue , duration , completeCallback, ease)
    local tweener = transform:DOLocalMoveY(endValue, duration)
    if ease then
        tweener:SetEase(ease)
    else
        tweener:SetEase(Tweening.Ease.Linear)
    end
    if completeCallback ~= nil then
        tweener:OnComplete(completeCallback)
    end
    tweener:SetAutoKill(true)
    return tweener
end

function cysoldierssortie_dotween_extern.DoRotate(transform , endValue , duration , completeCallback, ease)
    local tweener = transform:DORotate(endValue, duration)
    if ease then
        tweener:SetEase(ease)
    else
        tweener:SetEase(Tweening.Ease.Linear)
    end
    if completeCallback ~= nil then
        tweener:OnComplete(completeCallback)
    end
    tweener:SetAutoKill(true)
    return tweener
end

function cysoldierssortie_dotween_extern.DoLocalRotate(transform , endValue , duration , completeCallback, ease,loop)
    local tweener = transform:DOLocalRotate(endValue, duration)
    if loop then
        tweener:SetLoops(-1,Tweening.LoopType.Restart)
    end
    if ease then
        tweener:SetEase(ease)
    else
        tweener:SetEase(Tweening.Ease.Linear)
    end
    if completeCallback ~= nil then
        tweener:OnComplete(completeCallback)
    end
    
    tweener:SetAutoKill(true)
    return tweener
end

function cysoldierssortie_dotween_extern.DoFieldOfView(cam , endValue , duration , completeCallback, ease)
    local tweener = cam:DOFieldOfView(endValue, duration)
    if ease then
        tweener:SetEase(ease)
    else
        tweener:SetEase(Tweening.Ease.Linear)
    end
    if completeCallback ~= nil then
        tweener:OnComplete(completeCallback)
    end
    tweener:SetAutoKill(true)
    return tweener
end

function cysoldierssortie_dotween_extern.DoLookAt(trans , towards , duration , completeCallback, ease)
    local tweener = trans:DOLookAt(towards, duration)
    if ease then
        tweener:SetEase(ease)
    else
        tweener:SetEase(Tweening.Ease.Linear)
    end
    if completeCallback ~= nil then
        tweener:OnComplete(completeCallback)
    end
    tweener:SetAutoKill(true)
    return tweener
end

function cysoldierssortie_dotween_extern.DoCameraShake(cam,duration,strength,directionBias,vibrato,randomness,completeCallback,ease)
    local tweener = cam:DOShakePosition(duration, strength*directionBias,vibrato,randomness,true)
    if ease then
        tweener:SetEase(ease)
    else
        tweener:SetEase(Tweening.Ease.Linear)
    end
    if completeCallback ~= nil then
        tweener:OnComplete(completeCallback)
    end
    tweener:SetAutoKill(true)
    return tweener
end

function cysoldierssortie_dotween_extern.DoValue(getFunc,startValue,endValue,duration,completeCallback,ease)
    local tweener = Tweening.DOTween.To(
            function(newValue)
                if getFunc then
                    getFunc(newValue)
                end
            end,
            startValue,
            endValue,
            duration)
    
    if ease then
        tweener:SetEase(ease)
    else
        tweener:SetEase(Tweening.Ease.Linear)
    end
    if completeCallback ~= nil then
        tweener:OnComplete(completeCallback)
    end
    tweener:SetAutoKill(true)
    return tweener
end

function cysoldierssortie_dotween_extern.DoLookRotation(trans , targetDir , duration , completeCallback, ease)
    local targetRotation = bc_CS_Quaternion.LookRotation(targetDir.normalized,bc_CS_Vector3.up)
    local tweener = trans:DORotateQuaternion(targetRotation, duration)
    if ease then
        tweener:SetEase(ease)
    else
        tweener:SetEase(Tweening.Ease.Linear)
    end
    if completeCallback ~= nil then
        tweener:OnComplete(completeCallback)
    end
    tweener:SetAutoKill(true)
    return tweener
end

function cysoldierssortie_dotween_extern.DoPath(trans , pathPoints , duration , completeCallback, ease)
    local pathArray = CreateInstance(typeof(bc_CS_Vector3),#pathPoints)
    for i=1,#pathPoints do
        pathArray[i-1] = pathPoints[i]
    end
    local tweener = trans:DOPath(pathArray,duration)
    if ease then
        tweener:SetEase(ease)
    else
        tweener:SetEase(Tweening.Ease.Linear)
    end
    if completeCallback ~= nil then
        tweener:OnComplete(completeCallback)
    end
    tweener:SetAutoKill(true)
    return tweener
end

return cysoldierssortie_dotween_extern