local print = print
local require = require
local tonumber = tonumber
local tostring = tostring
local type = type

local error_code_pb = require "error_code_pb"
local msg_pb = require "msg_pb"
local activity_pb = require "activity_pb"
local frame_pb = require "frame_pb"
local roleplate_pb = require "roleplate_pb"
local title_pb = require "title_pb"
local prop_pb = require "prop_pb"
local package_pb = require "package_pb"
local DroneCenter_pb = require "DroneCenter_pb"
local GWG = GWG

local net = require "net"
local net_route = require "net_route"
local xManMsg_pb = require "xManMsg_pb"

local data_personalInfo = require "data_personalInfo"
local const_personalInfo = require "const_personalInfo"
local event_personalInfo = require "event_personalInfo"
local game_scheme = require "game_scheme"

local event_define = require "event_define"

local flow_text = require "flow_text"
local lang = require "lang"
local event = require "event"
local util = require "util"
local red_const = require("red_const")
local red_system = require "red_system"
local os = os

local net_halloween_slot_machine_panel = {}

local function OnErrorCode(enErr)
    flow_text.Clear()
    flow_text.Add(util.GetErrorLangText(enErr))
end

---[请求抽奖]
function net_halloween_slot_machine_panel.MSG_SLOT_MACHINE_DRAW_REQ(act_id, count)
    flow_text.Clear()
    

    local msg = activity_pb.TMSG_ZONE_ROLE_UPDATE_NAME_REQ()
    msg.nActivityID = act_id
    msg.nDrawsCnt = count
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_SLOT_MACHINE_DRAW_REQ, msg)
end

function net_halloween_slot_machine_panel.MSG_SLOT_MACHINE_DRAW_RSP(msg)
    if msg and msg.err == error_code_pb.enErr_NoError then
        event.Trigger(event_define.EVENT_HALLOWEEN_SLOT_MECHINE_DRAW_RESULT, {
            result_index = msg.nIndex,
            count = msg.nDrawsCnt,
        })
    else
        OnErrorCode(msg.err)
    end
end


local MessageTable = {
    --- 
    { msg_pb.MSG_SLOT_MACHINE_DRAW_REQ, net_halloween_slot_machine_panel.MSG_SLOT_MACHINE_DRAW_RSP, activity_pb.TMSG_SLOT_MACHINE_DRAW_RSP },
    
}

net_route.RegisterMsgHandlers(MessageTable)

return net_halloween_slot_machine_panel