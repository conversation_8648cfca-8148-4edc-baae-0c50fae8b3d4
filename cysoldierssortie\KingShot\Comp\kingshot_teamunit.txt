---@class kingshot_teamunit : fusion_gopoolitem
local unit = bc_Class("kingshot_teamunit", require("fusion_gopoolitem"))
---@type kingshot_scene_mgr
unit.sceneMgr = nil
---@type kingshot_team
unit.teamCtrl = nil
unit.DataSrc = nil
---@type kingshot_TeamConfig
unit.config = nil
---@type kingshot_enemyData
unit.teamData = nil
---@type number 记录队伍状态 0=集合状态，1=解散，2=成员死完
unit.ActionFlag = nil

unit.characterArray = nil
---@type number 队伍存活数量
unit.characterCount = nil

unit.tween_TeamRot = nil

unit.arrived = false
unit.runtimeDistance = 0

---@type kingshot_hero_circle
unit.heroCircle = nil

local wanderDurRandom = { 5, 8 }
local borderOff = 5
local heroCircleClass = require "kingshot_hero_circle"

function unit:__init(...)
    self:Ctor(...)
    self.DataSrc = {}
    local neeRefer = self.gameObject:GetComponent(KingShot_Define.TypeOf.NeeReferCollection)
    neeRefer:Bind(self.DataSrc)
end

function unit:Init(sceneMgr, ctrl, data, config)
    self.ActionFlag = 0
    self.DataSrc.NavMeshAgent.enabled = false
    self.sceneMgr = sceneMgr
    self.teamCtrl = ctrl
    local actorMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.ActorInstanceMgr)
    self.teamData = data
    self.config = config

    -- Initialize spawn state tracking
    self.isInitialized = false
    self.hasSpawned = false

    -- Set initial spawn position from configuration
    if self.teamData.Pos then
        KingShot_Define.SetTransformPositionXYZ(self.transform, self.teamData.Pos.x, self.teamData.Pos.y,
                self.teamData.Pos.z)
        -- Debug logging for spawn position
        Fusion.Log("[MONSTER_SPAWN] TeamID:", self.teamData.TeamID, "spawned at position:",
                    self.teamData.Pos.x, self.teamData.Pos.y, self.teamData.Pos.z,
                    "PathName:", self.teamData.PathName or "NO_PATH")
    else
        Fusion.Log("[MONSTER_SPAWN] ERROR: TeamID:", self.teamData.TeamID, "has no spawn position!")
    end

    self.DataSrc.NavMeshAgent.speed = self.config.MoveSpeed
    self.gameObject:SetActive(true)

    self.characterArray = {}
    local pointArray = self:SpawnPointWithCount(self.config.UnitCount)
    local tmpIndex = 0
        local localPos={x=0,y=0,z=0}
    for _, data in ipairs(self.config.UnitDatas) do
        for i = 1, data.Count, 1 do
            tmpIndex = tmpIndex + 1
            local character = actorMgr:CreateCharacter(data.UnitId, localPos, self.transform)
            character.troopClash_IgnoreBattleUpdate = true
            -- character:ResetCooldown()
         
            self.characterArray[tmpIndex] = character
            self.teamCtrl.TeamUnitWithCharacter[character] = self

        end
    end
    self:CreateHeroCircle()
    self.characterCount = tmpIndex
    -- 添加纵向的偏移，避免队伍堆叠
    self.runtimeDistance = math.random(0,5)
    -- 添加随机横向偏移，避免队伍堆叠
    self.sideOffset = (math.random() - 0.5) * 3 -- 生成-1.5到1.5的随机偏移

    -- Mark as initialized after all setup is complete
    self.isInitialized = true

    -- Debug logging for character creation
    Fusion.Log("[MONSTER_SPAWN] TeamID:", self.teamData.TeamID, "created", tmpIndex-1, "characters",
                "at final position:", self.transform.position.x, self.transform.position.y, self.transform.position.z)

    --self:Wandering()
    cysoldierssortie_DelayCallOnce(2, function()
        if self.isInitialized then
            self.DataSrc.NavMeshAgent.enabled = true
        end
    end)
end

function unit:SpawnPointWithCount(count)
    local pointArray = {}
    local golden_angle = math.pi * (3 - math.sqrt(5)) -- 黄金角的弧度值，大约是2.39996
    local max_radius = self.config.Radius
    for i = 0, count - 1, 1 do
        -- 基础参数计算
        local base_radius = math.sqrt(i / count) * max_radius
        local base_theta = i * golden_angle
        -- 添加半径扰动 (±5%)
        local radius_jitter = 1 + (math.random() - 0.5) * 0.1
        local final_radius = math.min(base_radius * radius_jitter, max_radius)
        -- 添加角度扰动 (±15度)
        local angle_jitter = math.rad((math.random() - 0.5) * 30)
        local final_theta = base_theta + angle_jitter
        -- 坐标计算
        local x = final_radius * math.cos(final_theta)
        local z = final_radius * math.sin(final_theta)
        pointArray[i + 1] = { x = x, z = z }
    end
    return pointArray
end

---新的游荡任务
function unit:Wandering()
    local curPos = self.transform.position
    local moveLength = math.random(wanderDurRandom[1], wanderDurRandom[2]) * self.config.MoveSpeed
    local playerDir = self.sceneMgr.playerCtrl:GetCenterVec3() - curPos
    playerDir.y = 0
    local tmpRot = KingShot_Define.CS.Quaternion.LookRotation(playerDir.normalized) *
        KingShot_Define.CS.Quaternion.Euler(0, math.random(-70, 70), 0)
    local tarPos = curPos + (tmpRot * KingShot_Define.CacheVector3.Forward).normalized * moveLength
    self.DataSrc.NavMeshAgent:SetDestination(tarPos)
end

---沿着设定的path行走
function unit:MoveAlongPath(deltaTime)
    -- Only start path movement after proper spawning
    if not self.hasSpawned then
        return
    end

    local path = self.sceneMgr:GetBakedPath(self.teamData.PathName)
    if not self.arrived and path then
        -- Log path movement start (only once)
        if not self.pathMovementStarted then
            self.pathMovementStarted = true
            Fusion.Log("[MONSTER_MOVEMENT] TeamID:", self.teamData.TeamID,
                        "starting path movement along:", self.teamData.PathName,
                        "from position:", self.transform.position.x, self.transform.position.y, self.transform.position.z)
        end

        -- 记录当前位置用于计算移动方向
        local currentPos = self.transform.position
        local oldRuntimeDistance = self.runtimeDistance

        -- 更新距离
        self.runtimeDistance = self.runtimeDistance + self.config.MoveSpeed * deltaTime
        local adjustedDistance = path.PathDistance * 0.999
        self.runtimeDistance = math.clamp(self.runtimeDistance,0,adjustedDistance)
        -- 检查是否到达终点
        if self.runtimeDistance >= adjustedDistance then
            if not self.arrived then
                self.arrived = true
                Fusion.Log("[MONSTER_MOVEMENT] TeamID:", self.teamData.TeamID,
                            "arrived at path destination. Final position:",
                            self.transform.position.x, self.transform.position.y, self.transform.position.z)
            end
        end

        -- 获取新位置
        -- 获取路径上的基础位置
        local basePos = path:GetPositionAtDistance(self.runtimeDistance)
        -- 添加横向偏移避免堆叠
        local sideOffset = self.sideOffset
        local lookAheadDistance = math.min(self.runtimeDistance + 0.1, adjustedDistance)
        local lookAheadPos = path:GetPositionAtDistance(lookAheadDistance)
        local forwardDir = (lookAheadPos - basePos).normalized
        local rightDir = KingShot_Define.CS.Vector3.Cross(forwardDir, KingShot_Define.CacheVector3.Up)
        -- 最终位置 = 基础位置 + 横向偏移
        local newPos = basePos + rightDir * sideOffset

        -- Log significant position changes (for debugging teleportation)
        local positionChange = (newPos - currentPos).magnitude
        if positionChange > 5.0 then -- Log if monster moves more than 5 units in one frame
            Fusion.Log("[MONSTER_MOVEMENT] WARNING: TeamID:", self.teamData.TeamID,
                        "large position change detected:", positionChange,
                        "from:", currentPos.x, currentPos.y, currentPos.z,
                        "to:", newPos.x, newPos.y, newPos.z,
                        "runtime distance:", oldRuntimeDistance, "->", self.runtimeDistance)
        end
        --先不做重叠处理 0915
        --KingShot_Define.SetTransformPositionXYZ(self.characterArray[1].transform, newPos.x, newPos.y, newPos.z)
        
        -- 获取稍远一点的位置来计算前进方向
        local lookAheadDistance = math.min(self.runtimeDistance + 0.1, adjustedDistance)
        local lookAheadPos = path:GetPositionAtDistance(lookAheadDistance)
        -- 计算前进方向
        local moveDirection = lookAheadPos - newPos

        -- 只有当移动方向有效时才设置朝向
        if moveDirection.magnitude > 0.001 then
            moveDirection.y = 0 -- 保持水平朝向
            local lookRotation = KingShot_Define.CS.Quaternion.LookRotation(moveDirection)
            self.transform.rotation = lookRotation
        end
    end
end

function unit:Update(deltaTime, viewCenterPos, pX, pZ)
    if self.ActionFlag ~= 0 then
        return
    end

    if self.isInitialized and not self.hasSpawned and self.characterArray and #self.characterArray > 0 then
        if self.teamData.Pos then
            KingShot_Define.SetTransformPositionXYZ(self.characterArray[1].transform,
                    self.teamData.Pos.x, self.teamData.Pos.y, self.teamData.Pos.z)
            self.hasSpawned = true

            Fusion.Log("[MONSTER_MOVEMENT] TeamID:", self.teamData.TeamID,
                        "positioned first character at spawn point:",
                        self.teamData.Pos.x, self.teamData.Pos.y, self.teamData.Pos.z)
        else
            Fusion.Log("[MONSTER_MOVEMENT] ERROR: TeamID:", self.teamData.TeamID,
                        "cannot position character - no spawn position!")
            self.hasSpawned = true -- Prevent infinite error logging
        end
    end
    --怪物太远，重置位置
    local resetWander = false
    local curX, curY, curZ = KingShot_Define.GetTransformPositionXYZ(self.transform)
    --if math.abs(curX - viewCenterPos.x) - self.config.Radius - borderOff > self.sceneMgr.cameraCtrl.camBorderSizeHalf.x
    --    or math.abs(curZ - viewCenterPos.z) - self.config.Radius - borderOff > self.sceneMgr.cameraCtrl.camBorderSizeHalf.y then
    --    resetWander = true
    --    local newPos = self.sceneMgr:GetSpawnTeamPos()
    --    KingShot_Define.SetTransformPositionXYZ(self.transform, newPos.x, newPos.y, newPos.z)
    --end
    if resetWander or
        (self.DataSrc.NavMeshAgent.hasPath and not self.DataSrc.NavMeshAgent.pathPending
            and self.DataSrc.NavMeshAgent.remainingDistance <= self.DataSrc.NavMeshAgent.stoppingDistance) then
        --self:Wandering()
    end
    self:MoveAlongPath(deltaTime)
    for _, v in ipairs(self.characterArray) do
        v:UpdateAI()
        if v._character_entity and bc_IsNotNull(v._character_entity.modelGo) and not v._character_entity.modelGo.activeSelf then
            v._character_entity.modelGo:SetActive(true)
        end
    end

    local character =  self.characterArray[1]
    local targetGo = character:GetTargetGo()
    if targetGo~=nil then
        self:Dissolution()
    end
    
    --local disFromPlayer = KingShot_Define.Func_GetDistance(pX, pZ, curX, curZ) -
    --    self.sceneMgr.playerCtrl.RadiusBind.value - self.config.Radius
    --if disFromPlayer < self.config.SearchRange then
    --    self:Dissolution()
    --end
    
    -- 更新HeroCircle
    if self.heroCircle then
        self.heroCircle:Update()
    end
end

--队伍解散
function unit:Dissolution()
    if self.ActionFlag == 0 then
        self.ActionFlag = 1
        --self.DataSrc.NavMeshAgent.enabled = false
        for _, v in ipairs(self.characterArray) do
            v.troopClash_IgnoreBattleUpdate = false
        end
    end
end

---队伍成员死亡
---@return boolean 是否全部死亡
function unit:CharacterDie(character)
    self.characterCount = self.characterCount - 1
    local allDieFlag = self.characterCount < 1
    if allDieFlag then
        --小队成员全部死亡，掉落道具
        if self.config.DropId ~= nil then
            self.sceneMgr.propCtrl:DropProps(character.transform.position, self.config.DropId)
        end
    end
    return allDieFlag
end

function unit:CreateHeroCircle()
    -- 如果已经创建过了，就不再创建
    if self.heroCircle then
        return
    end

    local firstCharacter = self.characterArray[1]
    -- 确保有场景管理器和角色
    if not self.sceneMgr or not firstCharacter then
        return
    end

    -- 加载HeroCircle预制体
    local prefabGo = KingShot_Define.CS.GameObject.Instantiate(self.sceneMgr.resMgr.HeroCirclePrefab)
    if prefabGo then
        -- 设置父物体为角色
        prefabGo.transform:SetParent(firstCharacter.transform)

        -- 创建HeroCircle实例
        self.heroCircle = heroCircleClass.New(prefabGo.transform, firstCharacter.transform, KingShot_Define.HeroCircleType.Monster, self.sceneMgr)
    else
        print("Failed to load HeroCircle prefab")
    end
end

function unit:Recycle()
    -- Reset all state variables
    self.isInitialized = false
    self.hasSpawned = false
    self.pathMovementStarted = false
    self.arrived = false
    self.runtimeDistance = 0
    self.ActionFlag = 0

    if self.teamData then
        Fusion.Log("[MONSTER_SPAWN] TeamID:", self.teamData.TeamID, "recycled and reset")
    end

    if self.heroCircle then
        self.heroCircle:Delete()
        self.heroCircle = nil
    end
end

function unit:__delete()
    self.arrived = false
    self.runtimeDistance = 0
    if self.heroCircle then
        self.heroCircle:Delete()
        self.heroCircle = nil
    end
    self:Dispose()
   
end

return unit
