# 智能位置分散功能测试说明

## 功能概述
智能位置分散功能解决了Timer模式下同时生成多个怪物队伍时的位置重叠问题。当策划配置的多个队伍位置重叠或距离过近时，系统会自动重新分配位置，避免NavMeshAgent开启后的相互推挤。

## 实现原理

### 1. 冲突检测
- 使用并查集算法检测位置冲突的队伍
- 安全距离设置为6单位（可配置）
- 将冲突的队伍自动分组

### 2. 位置重新分配
- 计算冲突组的重心位置
- 使用圆形分布算法重新排列队伍位置
- 确保每个队伍间距离大于安全阈值

### 3. 配置参数
```lua
local SMART_POSITIONING_CONFIG = {
    SAFE_DISTANCE = 6.0,        -- 队伍间安全距离
    MIN_RADIUS = 6.0,           -- 圆形分布最小半径
    RADIUS_PER_TEAM = 2.0,      -- 每个队伍增加的半径
    ENABLE_LOGGING = true,      -- 是否启用详细日志
}
```

## 测试场景

### 场景1：完全重叠位置
配置7-8个队伍在完全相同的位置(x=100, z=100)同时生成
**期望结果**：队伍会以圆形分布重新排列，间距约6单位

### 场景2：部分重叠位置
配置队伍在接近但不完全相同的位置生成
**期望结果**：只有距离小于6单位的队伍会被重新分配

### 场景3：无冲突位置
配置队伍在距离较远的位置生成
**期望结果**：位置保持不变，直接生成

## 日志监控
启用详细日志后，可以在控制台看到以下信息：
- `[SMART_POSITIONING] Processing X teams for smart positioning`
- `[SMART_POSITIONING] Redistributing positions for X conflicting teams`
- `[SMART_POSITIONING] TeamID: X repositioned to: X, Y, Z`

## 性能考虑
- 算法复杂度：O(n²)，其中n为同时生成的队伍数量
- 通常n较小（7-8个），性能影响可忽略
- 只在检测到多个队伍同时生成时才启用

## 兼容性
- 不影响单个队伍生成的逻辑
- 保持原有SpawnTeam函数接口不变
- 只在Timer模式下的批量生成时生效

## 调试建议
1. 开启详细日志：`ENABLE_LOGGING = true`
2. 调整安全距离：根据实际需求修改`SAFE_DISTANCE`
3. 观察重新分配的位置是否合理
4. 检查NavMeshAgent开启后是否还有推挤现象
