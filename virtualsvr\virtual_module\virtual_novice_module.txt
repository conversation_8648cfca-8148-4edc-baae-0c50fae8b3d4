﻿---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by ha<PERSON><PERSON><PERSON>.
--- DateTime: 2025/9/3 17:22
--- Desc : 新手模块

local require = require
local challenge_pb = require "challenge_pb"
local log = require "log"
local msg_pb = require "msg_pb"
local xManMsg_pb = require "xManMsg_pb"
local virtual_net = require "virtual_net"
local activity_pb = require "activity_pb"
local prop_pb = require "prop_pb"
module("virtual_novice_module")

function OnVirtualServerStarted()
    log.Error("virtual_novice_module __ OnVirtualServerStarted")
end

function OnVirtualServerStoped()
    log.Error("virtual_novice_module __ OnVirtualServerStoped")
end

function MSG_SET_HOOKLEVELCHALLENGE_RESULT_REQ(msg)
    --玩家战斗回复
    local virtual_main_level_module = require "virtual_main_level_module"
    virtual_main_level_module.MSG_PLAYER_ENTER_BATTLE_RSP(msg)
    --玩家Topic数据更新
    local virtual_player_module = require "virtual_player_module"
    virtual_player_module.MSG_SET_HOOKLEVELCHALLENGE_RESULT_REQ(msg)
    --设置主线关卡挑战回复
    local repMsg = challenge_pb.TMSG_SET_HOOKLEVELCHALLENGE_RESULT_RSP()
    repMsg.errCode = 0
    virtual_net.SendMessageToClient(xManMsg_pb.MSG_SET_HOOKLEVELCHALLENGE_RESULT_RSP,repMsg)
end

virtual_net.RegisterMsgHandler(xManMsg_pb.MSG_SET_HOOKLEVELCHALLENGE_RESULT_REQ, MSG_SET_HOOKLEVELCHALLENGE_RESULT_REQ, challenge_pb.MSG_SET_HOOKLEVELCHALLENGE_RESULT_REQ)