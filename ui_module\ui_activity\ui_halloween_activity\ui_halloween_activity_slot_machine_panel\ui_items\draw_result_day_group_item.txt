local log = require "log"
local time_util = require "time_util"
local string = string

local game_scheme = require("game_scheme")
local draw_result_day_group_item = {}

function draw_result_day_group_item.Render(rtf_item, act_id, data_list)
    rtf_item = rtf_item:GetComponent("RectTransform")
    local btn_title = rtf_item:Find("btn_title"):GetComponent("Button")
    local img_arrow_up = rtf_item:Find("btn_title/Image/img_arrow_up"):GetComponent("Image")
    local img_arrow_down = rtf_item:Find("btn_title/Image/img_arrow_down"):GetComponent("Image")
    local down_content = rtf_item:Find("down_content")
    btn_title.onClick:AddListener(function()
        down_content:SetActive(not down_content.gameObject.activeSelf)
        img_arrow_up:SetActive(down_content.gameObject.activeSelf)
        img_arrow_down:SetActive(not down_content.gameObject.activeSelf)
    end)

    down_content:SetActive(false)
    img_arrow_up:SetActive(down_content.gameObject.activeSelf)
    img_arrow_down:SetActive(not down_content.gameObject.activeSelf)

    local txt_title = btn_title:GetComponent("RectTransform"):Find("txt_title"):GetComponent("Text")
    -- local server_date = time_util.GetLocalDate(data.time_stamp or 0)
    -- local server_date_string = string.format("%s-%s %02d:%02d:%02d", server_date.month, server_date.day, server_date.hour, server_date.min, server_date.sec)
    txt_title.text = data_list.date

    local item_prefab = rtf_item:Find("down_content/item_prefab")
    item_prefab:SetActive(false)
    for i = 1, #data_list.items do
        local item = CS.UnityEngine.GameObject.Instantiate(item_prefab)
        item:SetActive(true)
        item.transform:SetParent(down_content, false)
        local draw_result_single_history_item = require "draw_result_single_history_item"

        local cfg = game_scheme:SlotGame_0(data_list.items[i].result_id, act_id)
        if not cfg then
            log.Error("[draw_result_day_group_item] no cfg for result_id: %d", data_list.items[i].result_id)
        else
            draw_result_single_history_item.Render(item, cfg, act_id, data_list.items[i])
        end
    end
end

function draw_result_day_group_item.Extend(rtf_item, bool_extend)
    rtf_item = rtf_item:GetComponent("RectTransform")
    local img_arrow_up = rtf_item:Find("btn_title/Image/img_arrow_up"):GetComponent("Image")
    local img_arrow_down = rtf_item:Find("btn_title/Image/img_arrow_down"):GetComponent("Image")
    local down_content = rtf_item:Find("down_content")

    bool_extend = bool_extend == true
    down_content:SetActive(bool_extend)
    img_arrow_up:SetActive(down_content.gameObject.activeSelf)
    img_arrow_down:SetActive(not down_content.gameObject.activeSelf)
end


return draw_result_day_group_item