-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local V=protobuf.EnumValueDescriptor
local F=protobuf.FieldDescriptor
local D=protobuf.Descriptor
local E=protobuf.EnumDescriptor
local M=protobuf.Message
local error_code_pb=require("error_code_pb")
module('alliance_pb')


V1M=V(4,"EAllianceGetMemberType_All",0,1)
V2M=V(4,"EAllianceGetMemberType_Offline",1,2)
V3M=V(4,"EAllianceGetMemberType_Online",2,3)
E1M=E(3,"EAllianceGetMemberType",".CSMsg.EAllianceGetMemberType")
V4M=V(4,"EAllianceMemberType_Create",0,1)
V5M=V(4,"EAllianceMemberType_Enter",1,2)
V6M=V(4,"EAllianceMemberType_Leave",2,3)
V7M=V(4,"EAllianceMemberType_Dismiss",3,4)
E2M=E(3,"EAllianceMemberType",".CSMsg.EAllianceMemberType")
V8M=V(4,"EAlliaceLeaveType_KickOut",0,1)
V9M=V(4,"EAlliaceLeaveType_Self",1,2)
V10M=V(4,"EAlliaceLeaveType_AutoClear",2,3)
E3M=E(3,"EAlliaceLeaveType",".CSMsg.EAlliaceLeaveType")
V11M=V(4,"EAllianceCheckType_Mail",0,1)
V12M=V(4,"EAllianceCheckType_Announcement",1,2)
V13M=V(4,"EAllianceCheckType_MailTitle",2,3)
E4M=E(3,"EAllianceCheckType",".CSMsg.EAllianceCheckType")
V14M=V(4,"EAllianceGiftBoxType_Normal",0,1)
V15M=V(4,"EAllianceGiftBoxType_Present",1,2)
V16M=V(4,"EAllianceGiftBoxType_Marauder",2,3)
E5M=E(3,"EAllianceGiftBoxType",".CSMsg.EAllianceGiftBoxType")
V17M=V(4,"EAllianceGiftType_Loot",0,1)
V18M=V(4,"EAllianceGiftType_Ally",1,2)
E6M=E(3,"EAllianceGiftType",".CSMsg.EAllianceGiftType")
V19M=V(4,"EAllianceGiftSendType_Login",0,1)
V20M=V(4,"EAllianceGiftSendType_Produce",1,2)
E7M=E(3,"EAllianceGiftSendType",".CSMsg.EAllianceGiftSendType")
V21M=V(4,"emAlliancePermission_LeagueSet",0,1)
V22M=V(4,"emAlliancePermission_Dismiss",1,2)
V23M=V(4,"emAlliancePermission_TransferCEO",2,3)
V24M=V(4,"emAlliancePermission_ChangeAnnouncement",3,4)
V25M=V(4,"emAlliancePermission_RallyInvitation",4,5)
V26M=V(4,"emAlliancePermission_SetRallyPoint",5,6)
V27M=V(4,"emAlliancePermission_SendMail",6,7)
V28M=V(4,"emAlliancePermission_ExpelMember",7,8)
V29M=V(4,"emAlliancePermission_UpgradeTechnology",8,9)
V30M=V(4,"emAlliancePermission_RecommendTechnology",9,10)
V31M=V(4,"emAlliancePermission_Markers",10,11)
V32M=V(4,"emAlliancePermission_DeclareWar",11,12)
V33M=V(4,"emAlliancePermission_RemovalTeam",12,13)
V34M=V(4,"emAlliancePermission_AbandonCity",13,14)
V35M=V(4,"emAlliancePermission_AdjustingAuthority",14,15)
V36M=V(4,"emAlliancePermission_ViewMember",15,16)
V37M=V(4,"emAlliancePermission_Exit",16,17)
V38M=V(4,"emAlliancePermission_PositionOpt",17,18)
V39M=V(4,"emAlliancePermission_ProcApplication",18,19)
V40M=V(4,"emAlliancePermission_SendInviteLink",19,20)
V41M=V(4,"emAlliancePermission_AutoResearch",20,21)
V42M=V(4,"emAlliancePermission_AllianceBoss",21,22)
V43M=V(4,"emAlliancePermission_WholeArmyAttack",22,23)
V44M=V(4,"emAlliancePermission_AppointDriver",23,25)
V45M=V(4,"emAlliancePermission_ZombieApocalypse",24,26)
V46M=V(4,"emAlliancePermission_Share",25,27)
E8M=E(3,"EAlliancePermission",".CSMsg.EAlliancePermission")
V47M=V(4,"EOptType_Agree",0,1)
V48M=V(4,"EOptType_Refuse",1,2)
E9M=E(3,"EOptType",".CSMsg.EOptType")
V49M=V(4,"emAllianceNameType_Name",0,1)
V50M=V(4,"emAllianceNameType_ShortName",1,2)
E10M=E(3,"EAllianceNameType",".CSMsg.EAllianceNameType")
V51M=V(4,"emAllianceReqType_Input",0,1)
V52M=V(4,"emAllianceReqType_Random",1,2)
V53M=V(4,"emAllianceReqType_Announce",2,3)
V54M=V(4,"emAllianceReqType_Mail",3,4)
V55M=V(4,"emAllianceReqType_Content",4,5)
V56M=V(4,"emAllianceReqType_MailTitle",5,6)
V57M=V(4,"emCongressReqType_Manifesto",6,7)
V58M=V(4,"emCongressReqType_MailSend",7,8)
V59M=V(4,"emCongressReqType_MailTitle",8,9)
V60M=V(4,"emAllianceReqType_AllianceDeclare",9,10)
E11M=E(3,"EAllianceReqType",".CSMsg.EAllianceReqType")
V61M=V(4,"emAllianceOneKey_Yes",0,1)
V62M=V(4,"emAllianceOneKey_No",1,2)
E12M=E(3,"EAllianceOneKey",".CSMsg.EAllianceOneKey")
V63M=V(4,"emAllianceApplyType_None",0,0)
V64M=V(4,"emAllianceApplyType_Auto",1,1)
V65M=V(4,"emAllianceApplyType_Apply",2,2)
E13M=E(3,"EAllianceApplyType",".CSMsg.EAllianceApplyType")
V66M=V(4,"emAlliancePosition_NoOfficial",0,0)
V67M=V(4,"emAlliancePosition_zhanshen",1,1)
V68M=V(4,"emAlliancePosition_zhaomuguan",2,2)
V69M=V(4,"emAlliancePosition_nvshen",3,3)
V70M=V(4,"emAlliancePosition_waijiaoguan",4,4)
E14M=E(3,"EAlliancePosition",".CSMsg.EAlliancePosition")
V71M=V(4,"emAlliancePositionOpt_Remove",0,1)
V72M=V(4,"emAlliancePositionOpt_Commission",1,2)
E15M=E(3,"EAlliancePositionOpt",".CSMsg.EAlliancePositionOpt")
V73M=V(4,"emAllianceAuthority_R1",0,1)
V74M=V(4,"emAllianceAuthority_R2",1,2)
V75M=V(4,"emAllianceAuthority_R3",2,3)
V76M=V(4,"emAllianceAuthority_R4",3,4)
V77M=V(4,"emAllianceAuthority_R5",4,5)
E16M=E(3,"EAllianceAuthority",".CSMsg.EAllianceAuthority")
V78M=V(4,"emAllianceRecommendOpt_Set",0,1)
V79M=V(4,"emAllianceRecommendOpt_Cancel",1,2)
E17M=E(3,"EAllianceRecommendOpt",".CSMsg.EAllianceRecommendOpt")
V80M=V(4,"emAllianceRSwitchOpt_Open",0,1)
V81M=V(4,"emAllianceRSwitchOpt_Close",1,2)
E18M=E(3,"EAllianceSwitchOpt",".CSMsg.EAllianceSwitchOpt")
V82M=V(4,"emAllianceDonateOpt_Coin",0,1)
V83M=V(4,"emAllianceDonateOpt_Diamond",1,2)
E19M=E(3,"EAllianceDonateOpt",".CSMsg.EAllianceDonateOpt")
V84M=V(4,"emAllianceClearType_rejectclear",0,1)
V85M=V(4,"emAllianceClearType_conditioned1",1,2)
E20M=E(3,"EAllianceClearType",".CSMsg.EAllianceClearType")
V86M=V(4,"EAllianceJoinType_default",0,0)
V87M=V(4,"EAllianceJoinType_invitation",1,1)
E21M=E(3,"EAllianceJoinType",".CSMsg.EAllianceJoinType")
V88M=V(4,"emMail_AllianceAuthority_R1",0,1)
V89M=V(4,"emMail_AllianceAuthority_R2",1,2)
V90M=V(4,"emMail_AllianceAuthority_R3",2,4)
V91M=V(4,"emMail_AllianceAuthority_R4",3,8)
V92M=V(4,"emMail_AllianceAuthority_R5",4,16)
E22M=E(3,"TAllianceRoleAuthority",".CSMsg.TAllianceRoleAuthority")
V93M=V(4,"emllianceOptType_Leave",0,0)
V94M=V(4,"emllianceOptType_Create",1,1)
V95M=V(4,"emllianceOptType_Join",2,2)
V96M=V(4,"emllianceOptType_Login",3,3)
V97M=V(4,"emllianceOptType_Expel",4,4)
V98M=V(4,"emllianceOptType_Change",5,5)
V99M=V(4,"emllianceOptType_autoResearch",6,6)
V100M=V(4,"emllianceOptType_AutoChange",7,7)
V101M=V(4,"emllianceOptType_Invitation",8,8)
V102M=V(4,"emllianceOptType_Share",9,9)
E23M=E(3,"EAllianceOptType",".CSMsg.EAllianceOptType")
V103M=V(4,"emllianceORoleRank_CE",0,1)
V104M=V(4,"emllianceORoleRank_Kill",1,2)
V105M=V(4,"emllianceORoleRank_EveryDay",2,3)
V106M=V(4,"emllianceORoleRank_EveryWeek",3,4)
E24M=E(3,"EAllianceRoleRank",".CSMsg.EAllianceRoleRank")
V107M=V(4,"EAllianceHelpType_Build",0,1)
V108M=V(4,"EAllianceHelpType_Heal",1,2)
V109M=V(4,"EAllianceHelpType_Study",2,3)
E25M=E(3,"EAllianceHelpType",".CSMsg.EAllianceHelpType")
V110M=V(4,"EAllianceChangeType_R5ToOthers",0,1)
V111M=V(4,"EAllianceChangeType_Expel",1,2)
V112M=V(4,"EAllianceChangeType_R5ToMe",2,3)
E26M=E(3,"EAllianceChangeType",".CSMsg.EAllianceChangeType")
V113M=V(4,"EAllianceR4R5_TaskType_Announcement",0,37201)
V114M=V(4,"EAllianceR4R5_TaskType_SetAllianceBossTime",1,37202)
V115M=V(4,"EAllianceR4R5_TaskType_SetR4",2,37203)
V116M=V(4,"EAllianceR4R5_TaskType_DeclareCity",3,37204)
E27M=E(3,"EAllianceR4R5_TaskType",".CSMsg.EAllianceR4R5_TaskType")
V117M=V(4,"EAllianceR4R5_TaskState_Doing",0,0)
V118M=V(4,"EAllianceR4R5_TaskState_CanGetReward",1,1)
V119M=V(4,"EAllianceR4R5_TaskState_CantGetReward",2,2)
E28M=E(3,"EAllianceR4R5_TaskState",".CSMsg.EAllianceR4R5_TaskState")
V120M=V(4,"EAllianceRecordType_War",0,1)
V121M=V(4,"EAllianceRecordType_Member",1,2)
V122M=V(4,"EAllianceRecordType_Info",2,3)
V123M=V(4,"EAllianceRecordType_Activity",3,4)
E29M=E(3,"EAllianceRecordType",".CSMsg.EAllianceRecordType")
V124M=V(4,"emAllianceLogType_UpAuthority",0,1)
V125M=V(4,"emAllianceLogType_DwAuthority",1,2)
V126M=V(4,"emAllianceLogType_SetPosition",2,3)
V127M=V(4,"emAllianceLogType_CancelPosition",3,4)
V128M=V(4,"emAllianceLogType_AllowJoin",4,5)
V129M=V(4,"emAllianceLogType_KickOut",5,6)
V130M=V(4,"emAllianceLogType_SystemKickOut",6,7)
V131M=V(4,"emAllianceLogType_ExitAlliance",7,8)
V132M=V(4,"emAllianceLogType_TranCeo",8,9)
V133M=V(4,"emAllianceLogType_Declaration",9,10)
V134M=V(4,"emAllianceLogType_AllianceMark",10,11)
V135M=V(4,"emAllianceLogType_SetJoinLevel",11,12)
V136M=V(4,"emAllianceLogType_SetJoinPower",12,13)
V137M=V(4,"emAllianceLogType_BossOpenPre",13,14)
V138M=V(4,"emAllianceLogType_BossOpen",14,15)
V139M=V(4,"emAllianceLogType_BossAutoOpen",15,16)
V140M=V(4,"emAllianceLogType_AppointConductor",16,17)
V141M=V(4,"emAllianceLogType_DesertPrebook",17,18)
V142M=V(4,"emAllianceLogType_DesertAbstain",18,19)
V143M=V(4,"emAllianceLogType_ZombiePrebook",19,20)
V144M=V(4,"emAllianceLogType_ZombieCancel",20,21)
V145M=V(4,"emAllianceLogType_ZombieUpdate",21,22)
V146M=V(4,"emAllianceLogType_ZombieOpen",22,23)
V147M=V(4,"emAllianceLogType_CityDeclareNoPeople",23,24)
V148M=V(4,"emAllianceLogType_CityDeclareHasPeople",24,25)
V149M=V(4,"emAllianceLogType_CityDeclareCancel",25,26)
V150M=V(4,"emAllianceLogType_CityBeDeclared",26,27)
V151M=V(4,"emAllianceLogType_CityOccupyNoPeople",27,28)
V152M=V(4,"emAllianceLogType_CityOccupyHasPeople",28,29)
V153M=V(4,"emAllianceLogType_CityBeOccupied",29,30)
V154M=V(4,"emAllianceLogType_JoinAlliance",30,31)
V155M=V(4,"emAllianceLogType_SetJoinNoApply",31,32)
V156M=V(4,"emAllianceLogType_SetJoinApply",32,33)
E30M=E(3,"EAllianceLogType",".CSMsg.EAllianceLogType")
F1D=F(2,"allianceId",".CSMsg.TAllianceBase.allianceId",1,0,2,false,0,5,1)
F2D=F(2,"allianceName",".CSMsg.TAllianceBase.allianceName",2,1,1,false,"",9,9)
F3D=F(2,"shortName",".CSMsg.TAllianceBase.shortName",3,2,1,false,"",9,9)
F4D=F(2,"announcement",".CSMsg.TAllianceBase.announcement",4,3,1,false,"",9,9)
F5D=F(2,"flag",".CSMsg.TAllianceBase.flag",5,4,1,false,0,5,1)
F6D=F(2,"count",".CSMsg.TAllianceBase.count",6,5,1,false,0,5,1)
F7D=F(2,"apply",".CSMsg.TAllianceBase.apply",7,6,1,false,false,8,7)
F8D=F(2,"language",".CSMsg.TAllianceBase.language",8,7,1,false,0,5,1)
F9D=F(2,"applySet",".CSMsg.TAllianceBase.applySet",9,8,1,false,nil,14,8)
F10D=F(2,"lvLimit",".CSMsg.TAllianceBase.lvLimit",10,9,1,false,0,5,1)
F11D=F(2,"ceLimit",".CSMsg.TAllianceBase.ceLimit",11,10,1,false,0,5,1)
F12D=F(2,"clearSet",".CSMsg.TAllianceBase.clearSet",12,11,1,false,nil,14,8)
F13D=F(2,"clearHours",".CSMsg.TAllianceBase.clearHours",13,12,1,false,0,5,1)
F14D=F(2,"newCreate",".CSMsg.TAllianceBase.newCreate",14,13,1,false,false,8,7)
F15D=F(2,"impeachCount",".CSMsg.TAllianceBase.impeachCount",15,14,1,false,0,5,1)
F16D=F(2,"power",".CSMsg.TAllianceBase.power",16,15,1,false,0,5,1)
F17D=F(2,"iAnnNextSetTime",".CSMsg.TAllianceBase.iAnnNextSetTime",17,16,1,false,0,5,1)
F18D=F(2,"arrFlag",".CSMsg.TAllianceBase.arrFlag",18,17,3,false,{},5,1)
F19D=F(2,"r5Info",".CSMsg.TAllianceBase.r5Info",19,18,1,false,nil,11,10)
F20D=F(2,"giftLv",".CSMsg.TAllianceBase.giftLv",20,19,1,false,0,5,1)
F21D=F(2,"giftExp",".CSMsg.TAllianceBase.giftExp",21,20,1,false,0,5,1)
F22D=F(2,"emailCount",".CSMsg.TAllianceBase.emailCount",22,21,1,false,0,5,1)
F23D=F(2,"emailLastTime",".CSMsg.TAllianceBase.emailLastTime",23,22,1,false,0,5,1)
F24D=F(2,"autoResearch",".CSMsg.TAllianceBase.autoResearch",24,23,1,false,0,5,1)
F25D=F(2,"achievementCloseTime",".CSMsg.TAllianceBase.achievementCloseTime",25,24,1,false,0,5,1)
F26D=F(2,"createTime",".CSMsg.TAllianceBase.createTime",26,25,1,false,0,13,3)
F27D=F(2,"arrMedal",".CSMsg.TAllianceBase.arrMedal",27,26,3,false,{},11,10)
F28D=F(2,"lastMassTime",".CSMsg.TAllianceBase.lastMassTime",28,27,1,false,0,13,3)
F29D=F(2,"topIndex",".CSMsg.TAllianceBase.topIndex",29,28,1,false,0,13,3)
F30D=F(2,"newPower",".CSMsg.TAllianceBase.newPower",30,29,1,false,0,3,2)
F31D=F(2,"bRobotFlag",".CSMsg.TAllianceBase.bRobotFlag",31,30,1,false,0,5,1)
F32D=F(2,"nationalFlagID",".CSMsg.TAllianceBase.nationalFlagID",32,31,1,false,0,13,3)
F33D=F(2,"TimeZoneId",".CSMsg.TAllianceBase.TimeZoneId",33,32,1,false,0,5,1)
M1G=D(1,"TAllianceBase",".CSMsg.TAllianceBase",false,{},{},nil,{})
F34D=F(2,"roleId",".CSMsg.TAlliancePersonBase.roleId",1,0,2,false,0,5,1)
F35D=F(2,"faceId",".CSMsg.TAlliancePersonBase.faceId",2,1,1,false,0,5,1)
F36D=F(2,"level",".CSMsg.TAlliancePersonBase.level",3,2,1,false,0,5,1)
F37D=F(2,"strName",".CSMsg.TAlliancePersonBase.strName",4,3,1,false,"",9,9)
F38D=F(2,"personCE",".CSMsg.TAlliancePersonBase.personCE",5,4,1,false,0,5,1)
F39D=F(2,"logoutTime",".CSMsg.TAlliancePersonBase.logoutTime",6,5,1,false,0,5,1)
F40D=F(2,"passStage",".CSMsg.TAlliancePersonBase.passStage",7,6,1,false,0,5,1)
F41D=F(2,"frameID",".CSMsg.TAlliancePersonBase.frameID",8,7,1,false,0,5,1)
F42D=F(2,"serverID",".CSMsg.TAlliancePersonBase.serverID",9,8,1,false,0,5,1)
F43D=F(2,"authority",".CSMsg.TAlliancePersonBase.authority",10,9,1,false,nil,14,8)
F44D=F(2,"position",".CSMsg.TAlliancePersonBase.position",11,10,1,false,nil,14,8)
F45D=F(2,"freeMoveCnt",".CSMsg.TAlliancePersonBase.freeMoveCnt",12,11,1,false,0,5,1)
F46D=F(2,"joinAllianceTime",".CSMsg.TAlliancePersonBase.joinAllianceTime",13,12,1,false,0,5,1)
F47D=F(2,"joinFirstAllianceTime",".CSMsg.TAlliancePersonBase.joinFirstAllianceTime",14,13,1,false,0,5,1)
F48D=F(2,"sex",".CSMsg.TAlliancePersonBase.sex",15,14,1,false,0,5,1)
F49D=F(2,"FreeMassCnt",".CSMsg.TAlliancePersonBase.FreeMassCnt",16,15,1,false,0,5,1)
F50D=F(2,"faceStr",".CSMsg.TAlliancePersonBase.faceStr",17,16,1,false,"",9,9)
M4G=D(1,"TAlliancePersonBase",".CSMsg.TAlliancePersonBase",false,{},{},nil,{})
F51D=F(2,"roleId",".CSMsg.TAllianceBoxBase.roleId",1,0,2,false,0,5,1)
F52D=F(2,"roleName",".CSMsg.TAllianceBoxBase.roleName",2,1,1,false,"",9,9)
F53D=F(2,"createTime",".CSMsg.TAllianceBoxBase.createTime",3,2,2,false,0,5,1)
F54D=F(2,"expireTime",".CSMsg.TAllianceBoxBase.expireTime",4,3,2,false,0,5,1)
F55D=F(2,"boxId",".CSMsg.TAllianceBoxBase.boxId",5,4,2,false,"",9,9)
F56D=F(2,"spoilsID",".CSMsg.TAllianceBoxBase.spoilsID",6,5,2,false,0,5,1)
F57D=F(2,"boxType",".CSMsg.TAllianceBoxBase.boxType",7,6,2,false,nil,14,8)
F58D=F(2,"experience",".CSMsg.TAllianceBoxBase.experience",8,7,2,false,0,5,1)
F59D=F(2,"boxChooseId",".CSMsg.TAllianceBoxBase.boxChooseId",10,8,1,false,0,5,1)
F60D=F(2,"anonymous",".CSMsg.TAllianceBoxBase.anonymous",9,9,1,false,0,5,1)
F61D=F(2,"rewardId",".CSMsg.TAllianceBoxBase.rewardId",11,10,1,false,0,5,1)
F62D=F(2,"giftLv",".CSMsg.TAllianceBoxBase.giftLv",12,11,1,false,0,5,1)
F63D=F(2,"rechargeID",".CSMsg.TAllianceBoxBase.rechargeID",13,12,1,false,0,5,1)
M8G=D(1,"TAllianceBoxBase",".CSMsg.TAllianceBoxBase",false,{},{},nil,{})
F64D=F(2,"effectIDs",".CSMsg.TAllianceTechnologyBase.effectIDs",1,0,2,false,0,5,1)
F65D=F(2,"rank",".CSMsg.TAllianceTechnologyBase.rank",2,1,2,false,0,5,1)
F66D=F(2,"experience",".CSMsg.TAllianceTechnologyBase.experience",3,2,2,false,0,5,1)
F67D=F(2,"time",".CSMsg.TAllianceTechnologyBase.time",4,3,1,false,0,5,1)
F68D=F(2,"recommend",".CSMsg.TAllianceTechnologyBase.recommend",5,4,1,false,0,5,1)
F69D=F(2,"state",".CSMsg.TAllianceTechnologyBase.state",6,5,1,false,0,5,1)
M10G=D(1,"TAllianceTechnologyBase",".CSMsg.TAllianceTechnologyBase",false,{},{},nil,{})
F70D=F(2,"allianceId",".CSMsg.TAllianceTechnologyInfo.allianceId",1,0,2,false,0,5,1)
F71D=F(2,"tTechnologyInfo",".CSMsg.TAllianceTechnologyInfo.tTechnologyInfo",2,1,3,false,{},11,10)
M11G=D(1,"TAllianceTechnologyInfo",".CSMsg.TAllianceTechnologyInfo",false,{},{},nil,{})
F72D=F(2,"stPersonBase",".CSMsg.TAllianceApplyPerson.stPersonBase",1,0,2,false,nil,11,10)
F73D=F(2,"iApplyTime",".CSMsg.TAllianceApplyPerson.iApplyTime",2,1,1,false,0,5,1)
M12G=D(1,"TAllianceApplyPerson",".CSMsg.TAllianceApplyPerson",false,{},{},nil,{})
F74D=F(2,"languageId",".CSMsg.TMSG_ALLIANCE_RECOMMEND_REQ.languageId",1,0,2,false,0,5,1)
M13G=D(1,"TMSG_ALLIANCE_RECOMMEND_REQ",".CSMsg.TMSG_ALLIANCE_RECOMMEND_REQ",false,{},{},nil,{})
F75D=F(2,"arrAlliances",".CSMsg.TMSG_ALLIANCE_RECOMMEND_RSP.arrAlliances",1,0,3,false,{},11,10)
F76D=F(2,"leaveAllianceTime",".CSMsg.TMSG_ALLIANCE_RECOMMEND_RSP.leaveAllianceTime",2,1,1,false,0,5,1)
F77D=F(2,"st1",".CSMsg.TMSG_ALLIANCE_RECOMMEND_RSP.st1",3,2,1,false,nil,11,10)
F78D=F(2,"st2",".CSMsg.TMSG_ALLIANCE_RECOMMEND_RSP.st2",4,3,1,false,nil,11,10)
F79D=F(2,"st5",".CSMsg.TMSG_ALLIANCE_RECOMMEND_RSP.st5",5,4,1,false,nil,11,10)
F80D=F(2,"st6",".CSMsg.TMSG_ALLIANCE_RECOMMEND_RSP.st6",6,5,1,false,nil,11,10)
M14G=D(1,"TMSG_ALLIANCE_RECOMMEND_RSP",".CSMsg.TMSG_ALLIANCE_RECOMMEND_RSP",false,{},{},nil,{})
F81D=F(2,"stBase",".CSMsg.TRepeatAllianceBase.stBase",1,0,3,false,{},11,10)
M15G=D(1,"TRepeatAllianceBase",".CSMsg.TRepeatAllianceBase",false,{},{},nil,{})
F82D=F(2,"strName",".CSMsg.TMSG_ALLIANCE_SEARCH_REQ.strName",1,0,2,false,"",9,9)
M16G=D(1,"TMSG_ALLIANCE_SEARCH_REQ",".CSMsg.TMSG_ALLIANCE_SEARCH_REQ",false,{},{},nil,{})
F83D=F(2,"stBase",".CSMsg.TMSG_ALLIANCE_SEARCH_RSP.stBase",1,0,3,false,{},11,10)
F84D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_SEARCH_RSP.errorCode",2,1,1,false,nil,14,8)
F85D=F(2,"AllianceNums",".CSMsg.TMSG_ALLIANCE_SEARCH_RSP.AllianceNums",3,2,1,false,0,5,1)
F86D=F(2,"leaveAllianceTime",".CSMsg.TMSG_ALLIANCE_SEARCH_RSP.leaveAllianceTime",4,3,1,false,0,5,1)
M17G=D(1,"TMSG_ALLIANCE_SEARCH_RSP",".CSMsg.TMSG_ALLIANCE_SEARCH_RSP",false,{},{},nil,{})
F87D=F(2,"allianceId",".CSMsg.TMSG_ALLIANCE_INFO_REQ.allianceId",1,0,2,false,0,5,1)
M19G=D(1,"TMSG_ALLIANCE_INFO_REQ",".CSMsg.TMSG_ALLIANCE_INFO_REQ",false,{},{},nil,{})
F88D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_INFO_RSP.errorCode",1,0,1,false,nil,14,8)
F89D=F(2,"allianceId",".CSMsg.TMSG_ALLIANCE_INFO_RSP.allianceId",2,1,2,false,0,5,1)
F90D=F(2,"stBase",".CSMsg.TMSG_ALLIANCE_INFO_RSP.stBase",3,2,1,false,nil,11,10)
M20G=D(1,"TMSG_ALLIANCE_INFO_RSP",".CSMsg.TMSG_ALLIANCE_INFO_RSP",false,{},{},nil,{})
F91D=F(2,"allianceId",".CSMsg.TMSG_ALLIANCE_ROLE_INFO_REQ.allianceId",1,0,2,false,0,5,1)
M21G=D(1,"TMSG_ALLIANCE_ROLE_INFO_REQ",".CSMsg.TMSG_ALLIANCE_ROLE_INFO_REQ",false,{},{},nil,{})
F92D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_ROLE_INFO_RSP.errorCode",1,0,1,false,nil,14,8)
F93D=F(2,"allianceId",".CSMsg.TMSG_ALLIANCE_ROLE_INFO_RSP.allianceId",2,1,2,false,0,5,1)
F94D=F(2,"arrMembers",".CSMsg.TMSG_ALLIANCE_ROLE_INFO_RSP.arrMembers",3,2,3,false,{},11,10)
M22G=D(1,"TMSG_ALLIANCE_ROLE_INFO_RSP",".CSMsg.TMSG_ALLIANCE_ROLE_INFO_RSP",false,{},{},nil,{})
F95D=F(2,"allianceId",".CSMsg.TMSG_ALLIANCE_QUICK_ADD_REQ.allianceId",1,0,2,false,0,5,1)
M23G=D(1,"TMSG_ALLIANCE_QUICK_ADD_REQ",".CSMsg.TMSG_ALLIANCE_QUICK_ADD_REQ",false,{},{},nil,{})
F96D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_QUICK_ADD_RSP.errorCode",1,0,2,false,nil,14,8)
F97D=F(2,"leaveAllianceTime",".CSMsg.TMSG_ALLIANCE_QUICK_ADD_RSP.leaveAllianceTime",2,1,1,false,0,5,1)
F98D=F(2,"allianceId",".CSMsg.TMSG_ALLIANCE_QUICK_ADD_RSP.allianceId",3,2,1,false,0,5,1)
M24G=D(1,"TMSG_ALLIANCE_QUICK_ADD_RSP",".CSMsg.TMSG_ALLIANCE_QUICK_ADD_RSP",false,{},{},nil,{})
F99D=F(2,"allianceId",".CSMsg.TMSG_ALLIANCE_APPLY_REQ.allianceId",1,0,2,false,0,5,1)
F100D=F(2,"joinType",".CSMsg.TMSG_ALLIANCE_APPLY_REQ.joinType",2,1,1,false,nil,14,8)
M25G=D(1,"TMSG_ALLIANCE_APPLY_REQ",".CSMsg.TMSG_ALLIANCE_APPLY_REQ",false,{},{},nil,{})
F101D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_APPLY_RSP.errorCode",1,0,2,false,nil,14,8)
F102D=F(2,"allianceId",".CSMsg.TMSG_ALLIANCE_APPLY_RSP.allianceId",2,1,1,false,0,5,1)
F103D=F(2,"leaveAllianceTime",".CSMsg.TMSG_ALLIANCE_APPLY_RSP.leaveAllianceTime",3,2,1,false,0,5,1)
F104D=F(2,"applyType",".CSMsg.TMSG_ALLIANCE_APPLY_RSP.applyType",4,3,1,false,nil,14,8)
M27G=D(1,"TMSG_ALLIANCE_APPLY_RSP",".CSMsg.TMSG_ALLIANCE_APPLY_RSP",false,{},{},nil,{})
F105D=F(2,"checkType",".CSMsg.TMSG_ALLIANCE_CHECKNAME_REQ.checkType",1,0,2,false,nil,14,8)
F106D=F(2,"checkName",".CSMsg.TMSG_ALLIANCE_CHECKNAME_REQ.checkName",2,1,2,false,"",9,9)
M28G=D(1,"TMSG_ALLIANCE_CHECKNAME_REQ",".CSMsg.TMSG_ALLIANCE_CHECKNAME_REQ",false,{},{},nil,{})
F107D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_CHECKNAME_RSP.errorCode",1,0,2,false,nil,14,8)
F108D=F(2,"checkType",".CSMsg.TMSG_ALLIANCE_CHECKNAME_RSP.checkType",2,1,2,false,nil,14,8)
M30G=D(1,"TMSG_ALLIANCE_CHECKNAME_RSP",".CSMsg.TMSG_ALLIANCE_CHECKNAME_RSP",false,{},{},nil,{})
M31G=D(1,"TMSG_ALLIANCE_RANDOM_NAME_REQ",".CSMsg.TMSG_ALLIANCE_RANDOM_NAME_REQ",false,{},{},{},{})
F109D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_RANDOM_NAME_RSP.errorCode",1,0,2,false,nil,14,8)
F110D=F(2,"randomName",".CSMsg.TMSG_ALLIANCE_RANDOM_NAME_RSP.randomName",2,1,1,false,"",9,9)
M32G=D(1,"TMSG_ALLIANCE_RANDOM_NAME_RSP",".CSMsg.TMSG_ALLIANCE_RANDOM_NAME_RSP",false,{},{},nil,{})
F111D=F(2,"name",".CSMsg.TMSG_ALLIANCE_CREATE_REQ.name",1,0,2,false,"",9,9)
F112D=F(2,"shortName",".CSMsg.TMSG_ALLIANCE_CREATE_REQ.shortName",2,1,2,false,"",9,9)
F113D=F(2,"flag",".CSMsg.TMSG_ALLIANCE_CREATE_REQ.flag",3,2,2,false,0,5,1)
F114D=F(2,"language",".CSMsg.TMSG_ALLIANCE_CREATE_REQ.language",4,3,2,false,0,5,1)
F115D=F(2,"nationalFlagID",".CSMsg.TMSG_ALLIANCE_CREATE_REQ.nationalFlagID",5,4,1,false,0,13,3)
M33G=D(1,"TMSG_ALLIANCE_CREATE_REQ",".CSMsg.TMSG_ALLIANCE_CREATE_REQ",false,{},{},nil,{})
F116D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_CREATE_RSP.errorCode",1,0,2,false,nil,14,8)
F117D=F(2,"leaveAllianceTime",".CSMsg.TMSG_ALLIANCE_CREATE_RSP.leaveAllianceTime",2,1,1,false,0,5,1)
M34G=D(1,"TMSG_ALLIANCE_CREATE_RSP",".CSMsg.TMSG_ALLIANCE_CREATE_RSP",false,{},{},nil,{})
F118D=F(2,"roleId",".CSMsg.TMSG_ALLIANCE_CHANGECEO_REQ.roleId",1,0,2,false,0,5,1)
M35G=D(1,"TMSG_ALLIANCE_CHANGECEO_REQ",".CSMsg.TMSG_ALLIANCE_CHANGECEO_REQ",false,{},{},nil,{})
F119D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_CHANGECEO_RSP.errorCode",1,0,2,false,nil,14,8)
F120D=F(2,"roleId",".CSMsg.TMSG_ALLIANCE_CHANGECEO_RSP.roleId",2,1,1,false,0,5,1)
M36G=D(1,"TMSG_ALLIANCE_CHANGECEO_RSP",".CSMsg.TMSG_ALLIANCE_CHANGECEO_RSP",false,{},{},nil,{})
F121D=F(2,"roleId",".CSMsg.TMSG_ALLIANCE_AUTHORITY_REQ.roleId",1,0,2,false,0,5,1)
F122D=F(2,"authority",".CSMsg.TMSG_ALLIANCE_AUTHORITY_REQ.authority",2,1,2,false,nil,14,8)
F123D=F(2,"position",".CSMsg.TMSG_ALLIANCE_AUTHORITY_REQ.position",3,2,1,false,nil,14,8)
M37G=D(1,"TMSG_ALLIANCE_AUTHORITY_REQ",".CSMsg.TMSG_ALLIANCE_AUTHORITY_REQ",false,{},{},nil,{})
F124D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_AUTHORITY_RSP.errorCode",1,0,2,false,nil,14,8)
F125D=F(2,"roleId",".CSMsg.TMSG_ALLIANCE_AUTHORITY_RSP.roleId",2,1,1,false,0,5,1)
F126D=F(2,"authority",".CSMsg.TMSG_ALLIANCE_AUTHORITY_RSP.authority",3,2,1,false,nil,14,8)
F127D=F(2,"position",".CSMsg.TMSG_ALLIANCE_AUTHORITY_RSP.position",4,3,1,false,nil,14,8)
M38G=D(1,"TMSG_ALLIANCE_AUTHORITY_RSP",".CSMsg.TMSG_ALLIANCE_AUTHORITY_RSP",false,{},{},nil,{})
F128D=F(2,"roleId",".CSMsg.TMSG_ALLIANCE_EXPEL_REQ.roleId",1,0,2,false,0,5,1)
M39G=D(1,"TMSG_ALLIANCE_EXPEL_REQ",".CSMsg.TMSG_ALLIANCE_EXPEL_REQ",false,{},{},nil,{})
F129D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_EXPEL_RSP.errorCode",1,0,2,false,nil,14,8)
F130D=F(2,"roleId",".CSMsg.TMSG_ALLIANCE_EXPEL_RSP.roleId",2,1,1,false,0,5,1)
M40G=D(1,"TMSG_ALLIANCE_EXPEL_RSP",".CSMsg.TMSG_ALLIANCE_EXPEL_RSP",false,{},{},nil,{})
F131D=F(2,"rewardIds",".CSMsg.TAllianceBoxRewards.rewardIds",1,0,2,false,0,5,1)
F132D=F(2,"boxId",".CSMsg.TAllianceBoxRewards.boxId",2,1,2,false,"",9,9)
M41G=D(1,"TAllianceBoxRewards",".CSMsg.TAllianceBoxRewards",false,{},{},nil,{})
F133D=F(2,"giftType",".CSMsg.TMSG_ALLIANCE_GIFT_GET_REQ.giftType",1,0,2,false,nil,14,8)
F134D=F(2,"oneKey",".CSMsg.TMSG_ALLIANCE_GIFT_GET_REQ.oneKey",2,1,2,false,nil,14,8)
F135D=F(2,"boxId",".CSMsg.TMSG_ALLIANCE_GIFT_GET_REQ.boxId",3,2,2,false,"",9,9)
M42G=D(1,"TMSG_ALLIANCE_GIFT_GET_REQ",".CSMsg.TMSG_ALLIANCE_GIFT_GET_REQ",false,{},{},nil,{})
F136D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_GIFT_GET_RSP.errorCode",1,0,2,false,nil,14,8)
F137D=F(2,"giftType",".CSMsg.TMSG_ALLIANCE_GIFT_GET_RSP.giftType",2,1,1,false,nil,14,8)
F138D=F(2,"oneKey",".CSMsg.TMSG_ALLIANCE_GIFT_GET_RSP.oneKey",3,2,1,false,nil,14,8)
F139D=F(2,"boxReward",".CSMsg.TMSG_ALLIANCE_GIFT_GET_RSP.boxReward",4,3,3,false,{},11,10)
F140D=F(2,"giftLv",".CSMsg.TMSG_ALLIANCE_GIFT_GET_RSP.giftLv",5,4,1,false,0,5,1)
F141D=F(2,"giftExp",".CSMsg.TMSG_ALLIANCE_GIFT_GET_RSP.giftExp",6,5,1,false,0,5,1)
M45G=D(1,"TMSG_ALLIANCE_GIFT_GET_RSP",".CSMsg.TMSG_ALLIANCE_GIFT_GET_RSP",false,{},{},nil,{})
F142D=F(2,"switchOpt",".CSMsg.TMSG_ALLIANCE_ANONYMOUS_REQ.switchOpt",1,0,2,false,nil,14,8)
M46G=D(1,"TMSG_ALLIANCE_ANONYMOUS_REQ",".CSMsg.TMSG_ALLIANCE_ANONYMOUS_REQ",false,{},{},nil,{})
F143D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_ANONYMOUS_RSP.errorCode",1,0,2,false,nil,14,8)
F144D=F(2,"switchOpt",".CSMsg.TMSG_ALLIANCE_ANONYMOUS_RSP.switchOpt",2,1,1,false,nil,14,8)
M48G=D(1,"TMSG_ALLIANCE_ANONYMOUS_RSP",".CSMsg.TMSG_ALLIANCE_ANONYMOUS_RSP",false,{},{},nil,{})
F145D=F(2,"sendType",".CSMsg.TMSG_ALLIANCE_GIFT_NTF.sendType",1,0,2,false,nil,14,8)
F146D=F(2,"arrLootBox",".CSMsg.TMSG_ALLIANCE_GIFT_NTF.arrLootBox",2,1,3,false,{},11,10)
F147D=F(2,"arrAllyBox",".CSMsg.TMSG_ALLIANCE_GIFT_NTF.arrAllyBox",3,2,3,false,{},11,10)
M49G=D(1,"TMSG_ALLIANCE_GIFT_NTF",".CSMsg.TMSG_ALLIANCE_GIFT_NTF",false,{},{},nil,{})
M51G=D(1,"TMSG_ALLIANCE_TECHNOLOGY_REQ",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_REQ",false,{},{},{},{})
F148D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_RSP.errorCode",1,0,2,false,nil,14,8)
F149D=F(2,"developInfo",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_RSP.developInfo",2,1,3,false,{},11,10)
F150D=F(2,"WarInfo",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_RSP.WarInfo",3,2,3,false,{},11,10)
F151D=F(2,"everyDayContribute",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_RSP.everyDayContribute",4,3,1,false,0,5,1)
F152D=F(2,"RecommendID",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_RSP.RecommendID",5,4,1,false,0,5,1)
M52G=D(1,"TMSG_ALLIANCE_TECHNOLOGY_RSP",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_RSP",false,{},{},nil,{})
F153D=F(2,"effectIDs",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_RECOMMEND_REQ.effectIDs",1,0,2,false,0,5,1)
F154D=F(2,"opt",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_RECOMMEND_REQ.opt",2,1,2,false,nil,14,8)
M53G=D(1,"TMSG_ALLIANCE_TECHNOLOGY_RECOMMEND_REQ",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_RECOMMEND_REQ",false,{},{},nil,{})
F155D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_RECOMMEND_RSP.errorCode",1,0,2,false,nil,14,8)
F156D=F(2,"effectIDs",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_RECOMMEND_RSP.effectIDs",2,1,1,false,0,5,1)
F157D=F(2,"opt",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_RECOMMEND_RSP.opt",3,2,1,false,nil,14,8)
M55G=D(1,"TMSG_ALLIANCE_TECHNOLOGY_RECOMMEND_RSP",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_RECOMMEND_RSP",false,{},{},nil,{})
F158D=F(2,"effectIDs",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_DETAILS_REQ.effectIDs",1,0,2,false,0,5,1)
M56G=D(1,"TMSG_ALLIANCE_TECHNOLOGY_DETAILS_REQ",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_DETAILS_REQ",false,{},{},nil,{})
F159D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_DETAILS_RSP.errorCode",1,0,2,false,nil,14,8)
F160D=F(2,"effectIDs",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_DETAILS_RSP.effectIDs",2,1,1,false,0,5,1)
F161D=F(2,"baseInfo",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_DETAILS_RSP.baseInfo",3,2,1,false,nil,11,10)
M57G=D(1,"TMSG_ALLIANCE_TECHNOLOGY_DETAILS_RSP",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_DETAILS_RSP",false,{},{},nil,{})
F162D=F(2,"effectIDs",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_STUDY_REQ.effectIDs",1,0,2,false,0,5,1)
M58G=D(1,"TMSG_ALLIANCE_TECHNOLOGY_STUDY_REQ",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_STUDY_REQ",false,{},{},nil,{})
F163D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_STUDY_RSP.errorCode",1,0,2,false,nil,14,8)
F164D=F(2,"effectIDs",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_STUDY_RSP.effectIDs",2,1,1,false,0,5,1)
F165D=F(2,"upResultTime",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_STUDY_RSP.upResultTime",3,2,1,false,0,5,1)
M59G=D(1,"TMSG_ALLIANCE_TECHNOLOGY_STUDY_RSP",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_STUDY_RSP",false,{},{},nil,{})
F166D=F(2,"effectIDs",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_DONATE_REQ.effectIDs",1,0,2,false,0,5,1)
F167D=F(2,"opt",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_DONATE_REQ.opt",2,1,2,false,nil,14,8)
M60G=D(1,"TMSG_ALLIANCE_TECHNOLOGY_DONATE_REQ",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_DONATE_REQ",false,{},{},nil,{})
F168D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_DONATE_RSP.errorCode",1,0,2,false,nil,14,8)
F169D=F(2,"effectIDs",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_DONATE_RSP.effectIDs",2,1,1,false,0,5,1)
F170D=F(2,"opt",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_DONATE_RSP.opt",3,2,1,false,nil,14,8)
F171D=F(2,"Multiplier",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_DONATE_RSP.Multiplier",4,3,1,false,0,5,1)
F172D=F(2,"TechExperience",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_DONATE_RSP.TechExperience",5,4,1,false,0,5,1)
F173D=F(2,"DonateVal",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_DONATE_RSP.DonateVal",6,5,1,false,0,5,1)
M62G=D(1,"TMSG_ALLIANCE_TECHNOLOGY_DONATE_RSP",".CSMsg.TMSG_ALLIANCE_TECHNOLOGY_DONATE_RSP",false,{},{},nil,{})
F174D=F(2,"content",".CSMsg.TMSG_ALLIANCE_MODIFY_ANNOUNCEMENT_REQ.content",1,0,2,false,"",9,9)
M63G=D(1,"TMSG_ALLIANCE_MODIFY_ANNOUNCEMENT_REQ",".CSMsg.TMSG_ALLIANCE_MODIFY_ANNOUNCEMENT_REQ",false,{},{},nil,{})
F175D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_MODIFY_ANNOUNCEMENT_RSP.errorCode",1,0,2,false,nil,14,8)
F176D=F(2,"content",".CSMsg.TMSG_ALLIANCE_MODIFY_ANNOUNCEMENT_RSP.content",2,1,1,false,"",9,9)
M64G=D(1,"TMSG_ALLIANCE_MODIFY_ANNOUNCEMENT_RSP",".CSMsg.TMSG_ALLIANCE_MODIFY_ANNOUNCEMENT_RSP",false,{},{},nil,{})
M65G=D(1,"TMSG_ALLIANCE_APPLICATION_LIST_REQ",".CSMsg.TMSG_ALLIANCE_APPLICATION_LIST_REQ",false,{},{},{},{})
F177D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_APPLICATION_LIST_RSP.errorCode",1,0,2,false,nil,14,8)
F178D=F(2,"roleInfo",".CSMsg.TMSG_ALLIANCE_APPLICATION_LIST_RSP.roleInfo",2,1,3,false,{},11,10)
M66G=D(1,"TMSG_ALLIANCE_APPLICATION_LIST_RSP",".CSMsg.TMSG_ALLIANCE_APPLICATION_LIST_RSP",false,{},{},nil,{})
F179D=F(2,"opt",".CSMsg.TMSG_ALLIANCE_HANDLE_APPLICATION_REQ.opt",1,0,2,false,nil,14,8)
F180D=F(2,"roleId",".CSMsg.TMSG_ALLIANCE_HANDLE_APPLICATION_REQ.roleId",2,1,2,false,0,5,1)
M67G=D(1,"TMSG_ALLIANCE_HANDLE_APPLICATION_REQ",".CSMsg.TMSG_ALLIANCE_HANDLE_APPLICATION_REQ",false,{},{},nil,{})
F181D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_HANDLE_APPLICATION_RSP.errorCode",1,0,2,false,nil,14,8)
F182D=F(2,"roleId",".CSMsg.TMSG_ALLIANCE_HANDLE_APPLICATION_RSP.roleId",2,1,1,false,0,5,1)
M69G=D(1,"TMSG_ALLIANCE_HANDLE_APPLICATION_RSP",".CSMsg.TMSG_ALLIANCE_HANDLE_APPLICATION_RSP",false,{},{},nil,{})
F183D=F(2,"flag",".CSMsg.TMSG_ALLIANCE_MODIFY_FLAG_REQ.flag",1,0,2,false,0,5,1)
M70G=D(1,"TMSG_ALLIANCE_MODIFY_FLAG_REQ",".CSMsg.TMSG_ALLIANCE_MODIFY_FLAG_REQ",false,{},{},nil,{})
F184D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_MODIFY_FLAG_RSP.errorCode",1,0,2,false,nil,14,8)
F185D=F(2,"flag",".CSMsg.TMSG_ALLIANCE_MODIFY_FLAG_RSP.flag",2,1,1,false,0,5,1)
M71G=D(1,"TMSG_ALLIANCE_MODIFY_FLAG_RSP",".CSMsg.TMSG_ALLIANCE_MODIFY_FLAG_RSP",false,{},{},nil,{})
F186D=F(2,"modifyType",".CSMsg.TMSG_ALLIANCE_MODIFY_NAME_REQ.modifyType",1,0,2,false,nil,14,8)
F187D=F(2,"modifyName",".CSMsg.TMSG_ALLIANCE_MODIFY_NAME_REQ.modifyName",2,1,2,false,"",9,9)
M72G=D(1,"TMSG_ALLIANCE_MODIFY_NAME_REQ",".CSMsg.TMSG_ALLIANCE_MODIFY_NAME_REQ",false,{},{},nil,{})
F188D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_MODIFY_NAME_RSP.errorCode",1,0,2,false,nil,14,8)
F189D=F(2,"modifyType",".CSMsg.TMSG_ALLIANCE_MODIFY_NAME_RSP.modifyType",2,1,1,false,nil,14,8)
F190D=F(2,"modifyName",".CSMsg.TMSG_ALLIANCE_MODIFY_NAME_RSP.modifyName",3,2,1,false,"",9,9)
M73G=D(1,"TMSG_ALLIANCE_MODIFY_NAME_RSP",".CSMsg.TMSG_ALLIANCE_MODIFY_NAME_RSP",false,{},{},nil,{})
F191D=F(2,"language",".CSMsg.TMSG_ALLIANCE_MODIFY_LANGUAGE_REQ.language",1,0,2,false,0,5,1)
M74G=D(1,"TMSG_ALLIANCE_MODIFY_LANGUAGE_REQ",".CSMsg.TMSG_ALLIANCE_MODIFY_LANGUAGE_REQ",false,{},{},nil,{})
F192D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_MODIFY_LANGUAGE_RSP.errorCode",1,0,2,false,nil,14,8)
F193D=F(2,"language",".CSMsg.TMSG_ALLIANCE_MODIFY_LANGUAGE_RSP.language",2,1,1,false,0,5,1)
M75G=D(1,"TMSG_ALLIANCE_MODIFY_LANGUAGE_RSP",".CSMsg.TMSG_ALLIANCE_MODIFY_LANGUAGE_RSP",false,{},{},nil,{})
F194D=F(2,"applySet",".CSMsg.TMSG_ALLIANCE_MODIFY_JOIN_CONDITIONS_REQ.applySet",1,0,1,false,nil,14,8)
F195D=F(2,"power",".CSMsg.TMSG_ALLIANCE_MODIFY_JOIN_CONDITIONS_REQ.power",2,1,1,false,0,5,1)
F196D=F(2,"headOffice",".CSMsg.TMSG_ALLIANCE_MODIFY_JOIN_CONDITIONS_REQ.headOffice",3,2,1,false,0,5,1)
F197D=F(2,"clearSet",".CSMsg.TMSG_ALLIANCE_MODIFY_JOIN_CONDITIONS_REQ.clearSet",4,3,1,false,nil,14,8)
M76G=D(1,"TMSG_ALLIANCE_MODIFY_JOIN_CONDITIONS_REQ",".CSMsg.TMSG_ALLIANCE_MODIFY_JOIN_CONDITIONS_REQ",false,{},{},nil,{})
F198D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_MODIFY_JOIN_CONDITIONS_RSP.errorCode",1,0,2,false,nil,14,8)
F199D=F(2,"applySet",".CSMsg.TMSG_ALLIANCE_MODIFY_JOIN_CONDITIONS_RSP.applySet",2,1,1,false,nil,14,8)
F200D=F(2,"power",".CSMsg.TMSG_ALLIANCE_MODIFY_JOIN_CONDITIONS_RSP.power",3,2,1,false,0,5,1)
F201D=F(2,"headOffice",".CSMsg.TMSG_ALLIANCE_MODIFY_JOIN_CONDITIONS_RSP.headOffice",4,3,1,false,0,5,1)
F202D=F(2,"clearSet",".CSMsg.TMSG_ALLIANCE_MODIFY_JOIN_CONDITIONS_RSP.clearSet",5,4,1,false,nil,14,8)
M77G=D(1,"TMSG_ALLIANCE_MODIFY_JOIN_CONDITIONS_RSP",".CSMsg.TMSG_ALLIANCE_MODIFY_JOIN_CONDITIONS_RSP",false,{},{},nil,{})
F203D=F(2,"content",".CSMsg.TMSG_ALLIANCE_MAIL_SEND_REQ.content",1,0,2,false,"",9,9)
F204D=F(2,"title",".CSMsg.TMSG_ALLIANCE_MAIL_SEND_REQ.title",2,1,2,false,"",9,9)
F205D=F(2,"sendAuthority",".CSMsg.TMSG_ALLIANCE_MAIL_SEND_REQ.sendAuthority",3,2,2,false,0,5,1)
M78G=D(1,"TMSG_ALLIANCE_MAIL_SEND_REQ",".CSMsg.TMSG_ALLIANCE_MAIL_SEND_REQ",false,{},{},nil,{})
F206D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_MAIL_SEND_RSP.errorCode",1,0,2,false,nil,14,8)
M79G=D(1,"TMSG_ALLIANCE_MAIL_SEND_RSP",".CSMsg.TMSG_ALLIANCE_MAIL_SEND_RSP",false,{},{},nil,{})
F207D=F(2,"sid",".CSMsg.TSandboxNCOccupyAndDeclareWar.sid",1,0,2,false,0,4,4)
F208D=F(2,"regionID",".CSMsg.TSandboxNCOccupyAndDeclareWar.regionID",2,1,2,false,0,13,3)
F209D=F(2,"nType",".CSMsg.TSandboxNCOccupyAndDeclareWar.nType",3,2,2,false,0,13,3)
M80G=D(1,"TSandboxNCOccupyAndDeclareWar",".CSMsg.TSandboxNCOccupyAndDeclareWar",false,{},{},nil,{})
F210D=F(2,"infoList",".CSMsg.TSandboxNCInfo.infoList",1,0,3,false,{},11,10)
F211D=F(2,"declarewarNum",".CSMsg.TSandboxNCInfo.declarewarNum",2,1,1,false,0,5,1)
F212D=F(2,"declarewarTime",".CSMsg.TSandboxNCInfo.declarewarTime",3,2,1,false,0,13,3)
M81G=D(1,"TSandboxNCInfo",".CSMsg.TSandboxNCInfo",false,{},{},nil,{})
F213D=F(2,"x",".CSMsg.TAllianceOneMarkInfo.x",1,0,2,false,0,13,3)
F214D=F(2,"y",".CSMsg.TAllianceOneMarkInfo.y",2,1,2,false,0,13,3)
F215D=F(2,"sRemark",".CSMsg.TAllianceOneMarkInfo.sRemark",3,2,2,false,"",9,9)
F216D=F(2,"bServerID",".CSMsg.TAllianceOneMarkInfo.bServerID",4,3,2,false,0,13,3)
F217D=F(2,"nIndex",".CSMsg.TAllianceOneMarkInfo.nIndex",5,4,2,false,0,13,3)
F218D=F(2,"nSymbol",".CSMsg.TAllianceOneMarkInfo.nSymbol",6,5,1,false,0,13,3)
F219D=F(2,"sid",".CSMsg.TAllianceOneMarkInfo.sid",7,6,1,false,0,4,4)
F220D=F(2,"name",".CSMsg.TAllianceOneMarkInfo.name",8,7,1,false,"",9,9)
F221D=F(2,"entitylevel",".CSMsg.TAllianceOneMarkInfo.entitylevel",9,8,1,false,0,4,4)
F222D=F(2,"entityName",".CSMsg.TAllianceOneMarkInfo.entityName",10,9,1,false,0,4,4)
F223D=F(2,"quality",".CSMsg.TAllianceOneMarkInfo.quality",11,10,1,false,0,4,4)
M82G=D(1,"TAllianceOneMarkInfo",".CSMsg.TAllianceOneMarkInfo",false,{},{},nil,{})
F224D=F(2,"info",".CSMsg.TAllianceMarkInfo.info",1,0,3,false,{},11,10)
M83G=D(1,"TAllianceMarkInfo",".CSMsg.TAllianceMarkInfo",false,{},{},nil,{})
F225D=F(2,"dbid",".CSMsg.TAlliance_InvitePlayerInfo.dbid",1,0,2,false,0,5,1)
F226D=F(2,"expirationTime",".CSMsg.TAlliance_InvitePlayerInfo.expirationTime",2,1,2,false,0,5,1)
M84G=D(1,"TAlliance_InvitePlayerInfo",".CSMsg.TAlliance_InvitePlayerInfo",false,{},{},nil,{})
F227D=F(2,"optType",".CSMsg.TMSG_ALLIANCE_UPDATE_NTF.optType",1,0,1,false,nil,14,8)
F228D=F(2,"allianceId",".CSMsg.TMSG_ALLIANCE_UPDATE_NTF.allianceId",2,1,2,false,0,5,1)
F229D=F(2,"stBase",".CSMsg.TMSG_ALLIANCE_UPDATE_NTF.stBase",3,2,1,false,nil,11,10)
F230D=F(2,"arrMembers",".CSMsg.TMSG_ALLIANCE_UPDATE_NTF.arrMembers",4,3,3,false,{},11,10)
F231D=F(2,"arrMemberDelRoleId",".CSMsg.TMSG_ALLIANCE_UPDATE_NTF.arrMemberDelRoleId",5,4,3,false,{},5,1)
F232D=F(2,"arrApplyPersons",".CSMsg.TMSG_ALLIANCE_UPDATE_NTF.arrApplyPersons",6,5,3,false,{},11,10)
F233D=F(2,"arrApplyDelRoleId",".CSMsg.TMSG_ALLIANCE_UPDATE_NTF.arrApplyDelRoleId",7,6,3,false,{},5,1)
F234D=F(2,"effectIDs",".CSMsg.TMSG_ALLIANCE_UPDATE_NTF.effectIDs",8,7,1,false,0,5,1)
F235D=F(2,"upResultTime",".CSMsg.TMSG_ALLIANCE_UPDATE_NTF.upResultTime",9,8,1,false,0,5,1)
F236D=F(2,"NCInfo",".CSMsg.TMSG_ALLIANCE_UPDATE_NTF.NCInfo",10,9,1,false,"",9,9)
F237D=F(2,"MarkInfo",".CSMsg.TMSG_ALLIANCE_UPDATE_NTF.MarkInfo",11,10,1,false,"",9,9)
F238D=F(2,"roleInfos",".CSMsg.TMSG_ALLIANCE_UPDATE_NTF.roleInfos",12,11,3,false,{},11,10)
F239D=F(2,"records",".CSMsg.TMSG_ALLIANCE_UPDATE_NTF.records",13,12,1,false,"",9,9)
F240D=F(2,"invitation_reward_times",".CSMsg.TMSG_ALLIANCE_UPDATE_NTF.invitation_reward_times",14,13,1,false,0,5,1)
F241D=F(2,"share_edTime",".CSMsg.TMSG_ALLIANCE_UPDATE_NTF.share_edTime",15,14,1,false,0,5,1)
M85G=D(1,"TMSG_ALLIANCE_UPDATE_NTF",".CSMsg.TMSG_ALLIANCE_UPDATE_NTF",false,{},{},nil,{})
M87G=D(1,"TMSG_ALLIANCE_EXIT_REQ",".CSMsg.TMSG_ALLIANCE_EXIT_REQ",false,{},{},{},{})
F242D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_EXIT_RSP.errorCode",1,0,2,false,nil,14,8)
M88G=D(1,"TMSG_ALLIANCE_EXIT_RSP",".CSMsg.TMSG_ALLIANCE_EXIT_RSP",false,{},{},nil,{})
F243D=F(2,"allianceId",".CSMsg.TAllianceRankBase.allianceId",1,0,2,false,0,5,1)
F244D=F(2,"rankId",".CSMsg.TAllianceRankBase.rankId",2,1,2,false,0,5,1)
F245D=F(2,"power",".CSMsg.TAllianceRankBase.power",3,2,2,false,0,5,1)
F246D=F(2,"allianceName",".CSMsg.TAllianceRankBase.allianceName",4,3,1,false,"",9,9)
F247D=F(2,"shortName",".CSMsg.TAllianceRankBase.shortName",5,4,1,false,"",9,9)
F248D=F(2,"flag",".CSMsg.TAllianceRankBase.flag",6,5,1,false,0,5,1)
F249D=F(2,"killNum",".CSMsg.TAllianceRankBase.killNum",7,6,1,false,0,5,1)
F250D=F(2,"newPower",".CSMsg.TAllianceRankBase.newPower",8,7,1,false,0,3,2)
F251D=F(2,"nationalFlagID",".CSMsg.TAllianceRankBase.nationalFlagID",9,8,1,false,0,13,3)
M89G=D(1,"TAllianceRankBase",".CSMsg.TAllianceRankBase",false,{},{},nil,{})
F252D=F(2,"roleId",".CSMsg.TAllianceRoleRankBase.roleId",1,0,2,false,0,5,1)
F253D=F(2,"rankId",".CSMsg.TAllianceRoleRankBase.rankId",2,1,2,false,0,5,1)
F254D=F(2,"score",".CSMsg.TAllianceRoleRankBase.score",3,2,2,false,0,5,1)
F255D=F(2,"faceId",".CSMsg.TAllianceRoleRankBase.faceId",4,3,1,false,0,5,1)
F256D=F(2,"level",".CSMsg.TAllianceRoleRankBase.level",5,4,1,false,0,5,1)
F257D=F(2,"strName",".CSMsg.TAllianceRoleRankBase.strName",6,5,1,false,"",9,9)
F258D=F(2,"frameID",".CSMsg.TAllianceRoleRankBase.frameID",7,6,1,false,0,5,1)
F259D=F(2,"authority",".CSMsg.TAllianceRoleRankBase.authority",8,7,1,false,nil,14,8)
F260D=F(2,"position",".CSMsg.TAllianceRoleRankBase.position",9,8,1,false,nil,14,8)
F261D=F(2,"scoreTime",".CSMsg.TAllianceRoleRankBase.scoreTime",10,9,1,false,0,5,1)
F262D=F(2,"faceStr",".CSMsg.TAllianceRoleRankBase.faceStr",11,10,1,false,"",9,9)
M90G=D(1,"TAllianceRoleRankBase",".CSMsg.TAllianceRoleRankBase",false,{},{},nil,{})
F263D=F(2,"ShowNum",".CSMsg.TMSG_ALLIANCE_POWERRANK_REQ.ShowNum",1,0,1,false,0,5,1)
F264D=F(2,"toWorldId",".CSMsg.TMSG_ALLIANCE_POWERRANK_REQ.toWorldId",2,1,1,false,0,5,1)
M91G=D(1,"TMSG_ALLIANCE_POWERRANK_REQ",".CSMsg.TMSG_ALLIANCE_POWERRANK_REQ",false,{},{},nil,{})
F265D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_POWERRANK_RSP.errorCode",1,0,2,false,nil,14,8)
F266D=F(2,"arrRoleBase",".CSMsg.TMSG_ALLIANCE_POWERRANK_RSP.arrRoleBase",2,1,3,false,{},11,10)
F267D=F(2,"worldId",".CSMsg.TMSG_ALLIANCE_POWERRANK_RSP.worldId",3,2,1,false,0,5,1)
M92G=D(1,"TMSG_ALLIANCE_POWERRANK_RSP",".CSMsg.TMSG_ALLIANCE_POWERRANK_RSP",false,{},{},nil,{})
F268D=F(2,"ShowNum",".CSMsg.TMSG_ALLIANCE_KILLNUM_REQ.ShowNum",1,0,1,false,0,5,1)
F269D=F(2,"toWorldId",".CSMsg.TMSG_ALLIANCE_KILLNUM_REQ.toWorldId",2,1,1,false,0,5,1)
M93G=D(1,"TMSG_ALLIANCE_KILLNUM_REQ",".CSMsg.TMSG_ALLIANCE_KILLNUM_REQ",false,{},{},nil,{})
F270D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_KILLNUM_RSP.errorCode",1,0,2,false,nil,14,8)
F271D=F(2,"arrRoleBase",".CSMsg.TMSG_ALLIANCE_KILLNUM_RSP.arrRoleBase",2,1,3,false,{},11,10)
F272D=F(2,"worldId",".CSMsg.TMSG_ALLIANCE_KILLNUM_RSP.worldId",3,2,1,false,0,5,1)
M94G=D(1,"TMSG_ALLIANCE_KILLNUM_RSP",".CSMsg.TMSG_ALLIANCE_KILLNUM_RSP",false,{},{},nil,{})
F273D=F(2,"rankType",".CSMsg.TMSG_ALLIANCE_ROLERANK_REQ.rankType",1,0,2,false,nil,14,8)
M95G=D(1,"TMSG_ALLIANCE_ROLERANK_REQ",".CSMsg.TMSG_ALLIANCE_ROLERANK_REQ",false,{},{},nil,{})
F274D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_ROLERANK_RSP.errorCode",1,0,2,false,nil,14,8)
F275D=F(2,"rankType",".CSMsg.TMSG_ALLIANCE_ROLERANK_RSP.rankType",2,1,1,false,nil,14,8)
F276D=F(2,"arrRoleBase",".CSMsg.TMSG_ALLIANCE_ROLERANK_RSP.arrRoleBase",3,2,3,false,{},11,10)
M97G=D(1,"TMSG_ALLIANCE_ROLERANK_RSP",".CSMsg.TMSG_ALLIANCE_ROLERANK_RSP",false,{},{},nil,{})
F277D=F(2,"strKey",".CSMsg.TAllianceHelp_KeyValue.strKey",1,0,2,false,"",9,9)
F278D=F(2,"strValue",".CSMsg.TAllianceHelp_KeyValue.strValue",2,1,2,false,"",9,9)
M98G=D(1,"TAllianceHelp_KeyValue",".CSMsg.TAllianceHelp_KeyValue",false,{},{},nil,{})
F279D=F(2,"roleID",".CSMsg.TAllianceHelp_Role.roleID",1,0,2,false,0,5,1)
F280D=F(2,"strName",".CSMsg.TAllianceHelp_Role.strName",2,1,1,false,"",9,9)
F281D=F(2,"faceID",".CSMsg.TAllianceHelp_Role.faceID",3,2,1,false,0,5,1)
F282D=F(2,"frameID",".CSMsg.TAllianceHelp_Role.frameID",4,3,1,false,0,5,1)
F283D=F(2,"faceStr",".CSMsg.TAllianceHelp_Role.faceStr",5,4,1,false,"",9,9)
M99G=D(1,"TAllianceHelp_Role",".CSMsg.TAllianceHelp_Role",false,{},{},nil,{})
F284D=F(2,"listKeyValue",".CSMsg.TAllianceHelp_List.listKeyValue",1,0,2,false,nil,11,10)
F285D=F(2,"helpID",".CSMsg.TAllianceHelp_List.helpID",2,1,2,false,0,5,1)
F286D=F(2,"timeBeg",".CSMsg.TAllianceHelp_List.timeBeg",3,2,2,false,0,3,2)
F287D=F(2,"helpTimes",".CSMsg.TAllianceHelp_List.helpTimes",4,3,2,false,0,5,1)
F288D=F(2,"helpLimits",".CSMsg.TAllianceHelp_List.helpLimits",5,4,2,false,0,5,1)
F289D=F(2,"helpSecs",".CSMsg.TAllianceHelp_List.helpSecs",6,5,2,false,0,5,1)
M100G=D(1,"TAllianceHelp_List",".CSMsg.TAllianceHelp_List",false,{},{},nil,{})
F290D=F(2,"roleInfo",".CSMsg.TAllianceHelp_Role_List.roleInfo",1,0,2,false,nil,11,10)
F291D=F(2,"listInfo",".CSMsg.TAllianceHelp_Role_List.listInfo",2,1,2,false,nil,11,10)
M101G=D(1,"TAllianceHelp_Role_List",".CSMsg.TAllianceHelp_Role_List",false,{},{},nil,{})
M102G=D(1,"TMSG_ALLIANCE_HELP_SELF_REQ",".CSMsg.TMSG_ALLIANCE_HELP_SELF_REQ",false,{},{},{},{})
F292D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_HELP_SELF_RSP.errorCode",1,0,2,false,nil,14,8)
F293D=F(2,"helpScores",".CSMsg.TMSG_ALLIANCE_HELP_SELF_RSP.helpScores",2,1,2,false,0,5,1)
F294D=F(2,"listKeyValue",".CSMsg.TMSG_ALLIANCE_HELP_SELF_RSP.listKeyValue",3,2,3,false,{},11,10)
F295D=F(2,"beHealTimes",".CSMsg.TMSG_ALLIANCE_HELP_SELF_RSP.beHealTimes",4,3,2,false,0,5,1)
M103G=D(1,"TMSG_ALLIANCE_HELP_SELF_RSP",".CSMsg.TMSG_ALLIANCE_HELP_SELF_RSP",false,{},{},nil,{})
M104G=D(1,"TMSG_ALLIANCE_HELP_LIST_REQ",".CSMsg.TMSG_ALLIANCE_HELP_LIST_REQ",false,{},{},{},{})
F296D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_HELP_LIST_RSP.errorCode",1,0,2,false,nil,14,8)
F297D=F(2,"helpInfo",".CSMsg.TMSG_ALLIANCE_HELP_LIST_RSP.helpInfo",2,1,3,false,{},11,10)
F298D=F(2,"allianceID",".CSMsg.TMSG_ALLIANCE_HELP_LIST_RSP.allianceID",3,2,2,false,0,5,1)
M105G=D(1,"TMSG_ALLIANCE_HELP_LIST_RSP",".CSMsg.TMSG_ALLIANCE_HELP_LIST_RSP",false,{},{},nil,{})
F299D=F(2,"del_strKey",".CSMsg.TMSG_ALLIANCE_HELP_LIST_NTF.del_strKey",1,0,3,false,{},9,9)
F300D=F(2,"new_helpInfo",".CSMsg.TMSG_ALLIANCE_HELP_LIST_NTF.new_helpInfo",2,1,3,false,{},11,10)
F301D=F(2,"update_List",".CSMsg.TMSG_ALLIANCE_HELP_LIST_NTF.update_List",3,2,3,false,{},11,10)
M106G=D(1,"TMSG_ALLIANCE_HELP_LIST_NTF",".CSMsg.TMSG_ALLIANCE_HELP_LIST_NTF",false,{},{},nil,{})
F302D=F(2,"strKey",".CSMsg.TMSG_ALLIANCE_HELP_START_REQ.strKey",1,0,2,false,"",9,9)
M107G=D(1,"TMSG_ALLIANCE_HELP_START_REQ",".CSMsg.TMSG_ALLIANCE_HELP_START_REQ",false,{},{},nil,{})
F303D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_HELP_START_RSP.errorCode",1,0,2,false,nil,14,8)
F304D=F(2,"listKeyValue",".CSMsg.TMSG_ALLIANCE_HELP_START_RSP.listKeyValue",2,1,1,false,nil,11,10)
M108G=D(1,"TMSG_ALLIANCE_HELP_START_RSP",".CSMsg.TMSG_ALLIANCE_HELP_START_RSP",false,{},{},nil,{})
F305D=F(2,"dbID",".CSMsg.TAllianceHelp_Start_Zone2Micro.dbID",1,0,2,false,0,5,1)
F306D=F(2,"strKeyValue",".CSMsg.TAllianceHelp_Start_Zone2Micro.strKeyValue",2,1,2,false,nil,11,10)
F307D=F(2,"nType",".CSMsg.TAllianceHelp_Start_Zone2Micro.nType",3,2,2,false,nil,14,8)
F308D=F(2,"timeEnd",".CSMsg.TAllianceHelp_Start_Zone2Micro.timeEnd",4,3,2,false,0,3,2)
F309D=F(2,"nAddHelpTimes",".CSMsg.TAllianceHelp_Start_Zone2Micro.nAddHelpTimes",5,4,1,false,0,5,1)
F310D=F(2,"nAddHelpSecs",".CSMsg.TAllianceHelp_Start_Zone2Micro.nAddHelpSecs",6,5,1,false,0,5,1)
M109G=D(1,"TAllianceHelp_Start_Zone2Micro",".CSMsg.TAllianceHelp_Start_Zone2Micro",false,{},{},nil,{})
M111G=D(1,"TMSG_ALLIANCE_HELP_CLICK_REQ",".CSMsg.TMSG_ALLIANCE_HELP_CLICK_REQ",false,{},{},{},{})
F311D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_HELP_CLICK_RSP.errorCode",1,0,2,false,nil,14,8)
F312D=F(2,"helpScores",".CSMsg.TMSG_ALLIANCE_HELP_CLICK_RSP.helpScores",2,1,2,false,0,5,1)
M112G=D(1,"TMSG_ALLIANCE_HELP_CLICK_RSP",".CSMsg.TMSG_ALLIANCE_HELP_CLICK_RSP",false,{},{},nil,{})
F313D=F(2,"helper",".CSMsg.TMSG_ALLIANCE_HELP_CLICK_NTF.helper",1,0,2,false,nil,11,10)
F314D=F(2,"info",".CSMsg.TMSG_ALLIANCE_HELP_CLICK_NTF.info",2,1,3,false,{},11,10)
F315D=F(2,"beHealTimes",".CSMsg.TMSG_ALLIANCE_HELP_CLICK_NTF.beHealTimes",3,2,2,false,0,5,1)
M113G=D(1,"TMSG_ALLIANCE_HELP_CLICK_NTF",".CSMsg.TMSG_ALLIANCE_HELP_CLICK_NTF",false,{},{},nil,{})
F316D=F(2,"checkType",".CSMsg.TMSG_ALLIANCE_CHECKCONTENT_REQ.checkType",1,0,2,false,nil,14,8)
F317D=F(2,"checkContent",".CSMsg.TMSG_ALLIANCE_CHECKCONTENT_REQ.checkContent",2,1,2,false,"",9,9)
M114G=D(1,"TMSG_ALLIANCE_CHECKCONTENT_REQ",".CSMsg.TMSG_ALLIANCE_CHECKCONTENT_REQ",false,{},{},nil,{})
F318D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_CHECKCONTENT_RSP.errorCode",1,0,2,false,nil,14,8)
F319D=F(2,"checkType",".CSMsg.TMSG_ALLIANCE_CHECKCONTENT_RSP.checkType",2,1,1,false,nil,14,8)
F320D=F(2,"checkContent",".CSMsg.TMSG_ALLIANCE_CHECKCONTENT_RSP.checkContent",3,2,1,false,"",9,9)
M116G=D(1,"TMSG_ALLIANCE_CHECKCONTENT_RSP",".CSMsg.TMSG_ALLIANCE_CHECKCONTENT_RSP",false,{},{},nil,{})
F321D=F(2,"type",".CSMsg.TMSG_ALLIANCE_CHANGE_NTF.type",1,0,2,false,nil,14,8)
M117G=D(1,"TMSG_ALLIANCE_CHANGE_NTF",".CSMsg.TMSG_ALLIANCE_CHANGE_NTF",false,{},{},nil,{})
F322D=F(2,"achID",".CSMsg.TAllianceAchievement_BaseRole.achID",1,0,2,false,0,5,1)
F323D=F(2,"type",".CSMsg.TAllianceAchievement_BaseRole.type",2,1,2,false,0,5,1)
F324D=F(2,"finishCnt",".CSMsg.TAllianceAchievement_BaseRole.finishCnt",3,2,2,false,0,5,1)
F325D=F(2,"reqCnt",".CSMsg.TAllianceAchievement_BaseRole.reqCnt",4,3,2,false,0,5,1)
F326D=F(2,"state",".CSMsg.TAllianceAchievement_BaseRole.state",5,4,2,false,0,5,1)
F327D=F(2,"isGetReward",".CSMsg.TAllianceAchievement_BaseRole.isGetReward",6,5,2,false,0,5,1)
F328D=F(2,"unlockTime",".CSMsg.TAllianceAchievement_BaseRole.unlockTime",7,6,2,false,0,5,1)
F329D=F(2,"overTime",".CSMsg.TAllianceAchievement_BaseRole.overTime",8,7,2,false,0,5,1)
F330D=F(2,"preTask",".CSMsg.TAllianceAchievement_BaseRole.preTask",9,8,2,false,0,5,1)
F331D=F(2,"nextTask",".CSMsg.TAllianceAchievement_BaseRole.nextTask",10,9,2,false,0,5,1)
M119G=D(1,"TAllianceAchievement_BaseRole",".CSMsg.TAllianceAchievement_BaseRole",false,{},{},nil,{})
F332D=F(2,"baseInfo",".CSMsg.TAllianceAchievement_BaseRoleList.baseInfo",1,0,3,false,{},11,10)
M120G=D(1,"TAllianceAchievement_BaseRoleList",".CSMsg.TAllianceAchievement_BaseRoleList",false,{},{},nil,{})
F333D=F(2,"baseInfo",".CSMsg.TAllianceAchievement_WholeAchievementInfo.baseInfo",1,0,2,false,nil,11,10)
F334D=F(2,"isAcquired",".CSMsg.TAllianceAchievement_WholeAchievementInfo.isAcquired",2,1,1,false,0,5,1)
F335D=F(2,"acquiredTime",".CSMsg.TAllianceAchievement_WholeAchievementInfo.acquiredTime",3,2,1,false,0,5,1)
M121G=D(1,"TAllianceAchievement_WholeAchievementInfo",".CSMsg.TAllianceAchievement_WholeAchievementInfo",false,{},{},nil,{})
F336D=F(2,"dbid",".CSMsg.TMSG_ALLIANCE_ACHIEVEMENT_REQ.dbid",1,0,2,false,0,5,1)
M122G=D(1,"TMSG_ALLIANCE_ACHIEVEMENT_REQ",".CSMsg.TMSG_ALLIANCE_ACHIEVEMENT_REQ",false,{},{},nil,{})
F337D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_ACHIEVEMENT_RSP.errorCode",1,0,2,false,nil,14,8)
M123G=D(1,"TMSG_ALLIANCE_ACHIEVEMENT_RSP",".CSMsg.TMSG_ALLIANCE_ACHIEVEMENT_RSP",false,{},{},nil,{})
F338D=F(2,"dbid",".CSMsg.TMSG_ALLIANCE_ACHIEVEMENT_NTF.dbid",1,0,2,false,0,5,1)
F339D=F(2,"achInfo",".CSMsg.TMSG_ALLIANCE_ACHIEVEMENT_NTF.achInfo",2,1,3,false,{},11,10)
M124G=D(1,"TMSG_ALLIANCE_ACHIEVEMENT_NTF",".CSMsg.TMSG_ALLIANCE_ACHIEVEMENT_NTF",false,{},{},nil,{})
F340D=F(2,"achID",".CSMsg.TMSG_ALLIANCE_ACHIEVEMENT_REWARD_REQ.achID",2,0,2,false,0,5,1)
M125G=D(1,"TMSG_ALLIANCE_ACHIEVEMENT_REWARD_REQ",".CSMsg.TMSG_ALLIANCE_ACHIEVEMENT_REWARD_REQ",false,{},{},nil,{})
F341D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_ACHIEVEMENT_REWARD_RSP.errorCode",1,0,2,false,nil,14,8)
F342D=F(2,"achID",".CSMsg.TMSG_ALLIANCE_ACHIEVEMENT_REWARD_RSP.achID",2,1,1,false,0,5,1)
F343D=F(2,"rewardId",".CSMsg.TMSG_ALLIANCE_ACHIEVEMENT_REWARD_RSP.rewardId",3,2,3,false,{},5,1)
M126G=D(1,"TMSG_ALLIANCE_ACHIEVEMENT_REWARD_RSP",".CSMsg.TMSG_ALLIANCE_ACHIEVEMENT_REWARD_RSP",false,{},{},nil,{})
F344D=F(2,"dbid",".CSMsg.TMSG_ALLIANCE_INVITE_REQ.dbid",1,0,2,false,0,5,1)
M127G=D(1,"TMSG_ALLIANCE_INVITE_REQ",".CSMsg.TMSG_ALLIANCE_INVITE_REQ",false,{},{},nil,{})
F345D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_INVITE_RSP.errorCode",1,0,2,false,nil,14,8)
M128G=D(1,"TMSG_ALLIANCE_INVITE_RSP",".CSMsg.TMSG_ALLIANCE_INVITE_RSP",false,{},{},nil,{})
F346D=F(2,"allianceId",".CSMsg.TAlliance_Invite_Info.allianceId",1,0,2,false,0,5,1)
F347D=F(2,"allianceName",".CSMsg.TAlliance_Invite_Info.allianceName",2,1,1,false,"",9,9)
F348D=F(2,"shortName",".CSMsg.TAlliance_Invite_Info.shortName",3,2,1,false,"",9,9)
F349D=F(2,"flag",".CSMsg.TAlliance_Invite_Info.flag",4,3,1,false,0,5,1)
F350D=F(2,"count",".CSMsg.TAlliance_Invite_Info.count",5,4,1,false,0,5,1)
F351D=F(2,"language",".CSMsg.TAlliance_Invite_Info.language",6,5,1,false,0,5,1)
F352D=F(2,"power",".CSMsg.TAlliance_Invite_Info.power",7,6,1,false,0,5,1)
F353D=F(2,"time",".CSMsg.TAlliance_Invite_Info.time",8,7,1,false,0,5,1)
F354D=F(2,"newPower",".CSMsg.TAlliance_Invite_Info.newPower",9,8,1,false,0,3,2)
M129G=D(1,"TAlliance_Invite_Info",".CSMsg.TAlliance_Invite_Info",false,{},{},nil,{})
F355D=F(2,"allianceInfo",".CSMsg.TMSG_ALLIANCE_INVITE_NTF.allianceInfo",1,0,3,false,{},11,10)
M130G=D(1,"TMSG_ALLIANCE_INVITE_NTF",".CSMsg.TMSG_ALLIANCE_INVITE_NTF",false,{},{},nil,{})
F356D=F(2,"isAccept",".CSMsg.TMSG_ALLIANCE_ISACCEPT_INVITE_REQ.isAccept",1,0,2,false,0,5,1)
F357D=F(2,"allianceID",".CSMsg.TMSG_ALLIANCE_ISACCEPT_INVITE_REQ.allianceID",2,1,2,false,0,5,1)
M131G=D(1,"TMSG_ALLIANCE_ISACCEPT_INVITE_REQ",".CSMsg.TMSG_ALLIANCE_ISACCEPT_INVITE_REQ",false,{},{},nil,{})
F358D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_ISACCEPT_INVITE_RSP.errorCode",1,0,2,false,nil,14,8)
F359D=F(2,"isAccept",".CSMsg.TMSG_ALLIANCE_ISACCEPT_INVITE_RSP.isAccept",2,1,1,false,0,5,1)
F360D=F(2,"allianceID",".CSMsg.TMSG_ALLIANCE_ISACCEPT_INVITE_RSP.allianceID",3,2,1,false,0,5,1)
M132G=D(1,"TMSG_ALLIANCE_ISACCEPT_INVITE_RSP",".CSMsg.TMSG_ALLIANCE_ISACCEPT_INVITE_RSP",false,{},{},nil,{})
M133G=D(1,"TMSG_ALLIANCE_INVITE_INFO_REQ",".CSMsg.TMSG_ALLIANCE_INVITE_INFO_REQ",false,{},{},{},{})
F361D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_INVITE_INFO_RSP.errorCode",1,0,2,false,nil,14,8)
M134G=D(1,"TMSG_ALLIANCE_INVITE_INFO_RSP",".CSMsg.TMSG_ALLIANCE_INVITE_INFO_RSP",false,{},{},nil,{})
F362D=F(2,"opt",".CSMsg.TMSG_ALLIANCE_AUTO_RESEARCH_REQ.opt",1,0,1,false,0,5,1)
M135G=D(1,"TMSG_ALLIANCE_AUTO_RESEARCH_REQ",".CSMsg.TMSG_ALLIANCE_AUTO_RESEARCH_REQ",false,{},{},nil,{})
F363D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_AUTO_RESEARCH_RSP.errorCode",1,0,2,false,nil,14,8)
F364D=F(2,"opt",".CSMsg.TMSG_ALLIANCE_AUTO_RESEARCH_RSP.opt",2,1,1,false,0,5,1)
M136G=D(1,"TMSG_ALLIANCE_AUTO_RESEARCH_RSP",".CSMsg.TMSG_ALLIANCE_AUTO_RESEARCH_RSP",false,{},{},nil,{})
F365D=F(2,"allianceId",".CSMsg.TMSG_NEWPALYER_FREEMOVE_NTF.allianceId",1,0,2,false,0,5,1)
M137G=D(1,"TMSG_NEWPALYER_FREEMOVE_NTF",".CSMsg.TMSG_NEWPALYER_FREEMOVE_NTF",false,{},{},nil,{})
F366D=F(2,"flag",".CSMsg.TDestoryCity_PlayerInfo.flag",1,0,2,false,0,5,1)
F367D=F(2,"baseInfo",".CSMsg.TDestoryCity_PlayerInfo.baseInfo",2,1,2,false,nil,11,10)
F368D=F(2,"areaId",".CSMsg.TDestoryCity_PlayerInfo.areaId",3,2,1,false,0,5,1)
M138G=D(1,"TDestoryCity_PlayerInfo",".CSMsg.TDestoryCity_PlayerInfo",false,{},{},nil,{})
F369D=F(2,"dbid",".CSMsg.TDestoryCity_BaseInfo.dbid",1,0,2,false,0,5,1)
F370D=F(2,"roleName",".CSMsg.TDestoryCity_BaseInfo.roleName",2,1,2,false,"",9,9)
F371D=F(2,"shortName",".CSMsg.TDestoryCity_BaseInfo.shortName",3,2,1,false,"",9,9)
M139G=D(1,"TDestoryCity_BaseInfo",".CSMsg.TDestoryCity_BaseInfo",false,{},{},nil,{})
F372D=F(2,"dbid",".CSMsg.TMSG_OFFLINE_CITY_DESTOIRY_REWARD_NTF.dbid",1,0,2,false,0,5,1)
F373D=F(2,"moveCityTime",".CSMsg.TMSG_OFFLINE_CITY_DESTOIRY_REWARD_NTF.moveCityTime",2,1,2,false,0,5,1)
F374D=F(2,"enemyCnt",".CSMsg.TMSG_OFFLINE_CITY_DESTOIRY_REWARD_NTF.enemyCnt",3,2,2,false,0,5,1)
F375D=F(2,"byAttackCnt",".CSMsg.TMSG_OFFLINE_CITY_DESTOIRY_REWARD_NTF.byAttackCnt",4,3,2,false,0,5,1)
F376D=F(2,"maxAttackInfo",".CSMsg.TMSG_OFFLINE_CITY_DESTOIRY_REWARD_NTF.maxAttackInfo",5,4,2,false,nil,11,10)
F377D=F(2,"byMemberInfo",".CSMsg.TMSG_OFFLINE_CITY_DESTOIRY_REWARD_NTF.byMemberInfo",6,5,3,false,{},11,10)
F378D=F(2,"byCeoInfo",".CSMsg.TMSG_OFFLINE_CITY_DESTOIRY_REWARD_NTF.byCeoInfo",7,6,1,false,nil,11,10)
F379D=F(2,"maxAttackCnt",".CSMsg.TMSG_OFFLINE_CITY_DESTOIRY_REWARD_NTF.maxAttackCnt",8,7,2,false,0,5,1)
M140G=D(1,"TMSG_OFFLINE_CITY_DESTOIRY_REWARD_NTF",".CSMsg.TMSG_OFFLINE_CITY_DESTOIRY_REWARD_NTF",false,{},{},nil,{})
F380D=F(2,"allianceId",".CSMsg.TMSG_ALLIANCE_FREE_MOVE_CITY_NTF.allianceId",1,0,2,false,0,5,1)
F381D=F(2,"dbid",".CSMsg.TMSG_ALLIANCE_FREE_MOVE_CITY_NTF.dbid",2,1,2,false,0,5,1)
F382D=F(2,"freeMoveCnt",".CSMsg.TMSG_ALLIANCE_FREE_MOVE_CITY_NTF.freeMoveCnt",3,2,2,false,0,5,1)
M141G=D(1,"TMSG_ALLIANCE_FREE_MOVE_CITY_NTF",".CSMsg.TMSG_ALLIANCE_FREE_MOVE_CITY_NTF",false,{},{},nil,{})
F383D=F(2,"lv",".CSMsg.TMedalLvProgressInfo.lv",1,0,2,false,0,5,1)
F384D=F(2,"progress",".CSMsg.TMedalLvProgressInfo.progress",2,1,2,false,0,5,1)
F385D=F(2,"myProgress",".CSMsg.TMedalLvProgressInfo.myProgress",3,2,2,false,0,5,1)
M142G=D(1,"TMedalLvProgressInfo",".CSMsg.TMedalLvProgressInfo",false,{},{},nil,{})
F386D=F(2,"id",".CSMsg.TAllianceMedal.id",1,0,2,false,0,5,1)
F387D=F(2,"lv",".CSMsg.TAllianceMedal.lv",2,1,1,false,0,5,1)
F388D=F(2,"time",".CSMsg.TAllianceMedal.time",3,2,1,false,0,5,1)
F389D=F(2,"progress",".CSMsg.TAllianceMedal.progress",4,3,1,false,0,5,1)
F390D=F(2,"arrProgress",".CSMsg.TAllianceMedal.arrProgress",5,4,3,false,{},11,10)
F391D=F(2,"rewarded",".CSMsg.TAllianceMedal.rewarded",6,5,1,false,0,5,1)
F392D=F(2,"warned",".CSMsg.TAllianceMedal.warned",7,6,1,false,false,8,7)
M5G=D(1,"TAllianceMedal",".CSMsg.TAllianceMedal",false,{},{},nil,{})
F393D=F(2,"rank",".CSMsg.TAllianceMedalRank.rank",1,0,2,false,0,5,1)
F394D=F(2,"dwRoleID",".CSMsg.TAllianceMedalRank.dwRoleID",2,1,2,false,0,5,1)
F395D=F(2,"value",".CSMsg.TAllianceMedalRank.value",3,2,2,false,0,5,1)
F396D=F(2,"lastRank",".CSMsg.TAllianceMedalRank.lastRank",4,3,1,false,0,5,1)
M143G=D(1,"TAllianceMedalRank",".CSMsg.TAllianceMedalRank",false,{},{},nil,{})
F397D=F(2,"id",".CSMsg.TAllianceMedalSingleRedis.id",1,0,2,false,0,5,1)
F398D=F(2,"lv",".CSMsg.TAllianceMedalSingleRedis.lv",2,1,2,false,0,5,1)
F399D=F(2,"time",".CSMsg.TAllianceMedalSingleRedis.time",3,2,1,false,0,5,1)
F400D=F(2,"progress",".CSMsg.TAllianceMedalSingleRedis.progress",4,3,1,false,0,5,1)
F401D=F(2,"resetTime",".CSMsg.TAllianceMedalSingleRedis.resetTime",5,4,1,false,0,5,1)
F402D=F(2,"auto",".CSMsg.TAllianceMedalSingleRedis.auto",6,5,1,false,0,5,1)
F403D=F(2,"lastWarnTime",".CSMsg.TAllianceMedalSingleRedis.lastWarnTime",7,6,1,false,0,5,1)
M144G=D(1,"TAllianceMedalSingleRedis",".CSMsg.TAllianceMedalSingleRedis",false,{},{},nil,{})
F404D=F(2,"leagueID",".CSMsg.TAllianceMedalDataRedis.leagueID",1,0,2,false,0,5,1)
F405D=F(2,"arrMedal",".CSMsg.TAllianceMedalDataRedis.arrMedal",2,1,3,false,{},11,10)
M145G=D(1,"TAllianceMedalDataRedis",".CSMsg.TAllianceMedalDataRedis",false,{},{},nil,{})
F406D=F(2,"id",".CSMsg.TAllianceMedalResetRoleInfo.id",1,0,2,false,0,5,1)
F407D=F(2,"medalResetLv",".CSMsg.TAllianceMedalResetRoleInfo.medalResetLv",2,1,1,false,0,5,1)
F408D=F(2,"medalResetTime",".CSMsg.TAllianceMedalResetRoleInfo.medalResetTime",3,2,1,false,0,5,1)
M146G=D(1,"TAllianceMedalResetRoleInfo",".CSMsg.TAllianceMedalResetRoleInfo",false,{},{},nil,{})
F409D=F(2,"roleid",".CSMsg.TAllianceMedalRoleRedis.roleid",1,0,2,false,0,5,1)
F410D=F(2,"activity",".CSMsg.TAllianceMedalRoleRedis.activity",2,1,1,false,0,5,1)
F411D=F(2,"cityLv",".CSMsg.TAllianceMedalRoleRedis.cityLv",3,2,1,false,0,5,1)
F412D=F(2,"duelScore",".CSMsg.TAllianceMedalRoleRedis.duelScore",4,3,1,false,0,5,1)
F413D=F(2,"weekResetTime",".CSMsg.TAllianceMedalRoleRedis.weekResetTime",5,4,1,false,0,5,1)
F414D=F(2,"rewarded1",".CSMsg.TAllianceMedalRoleRedis.rewarded1",6,5,1,false,0,5,1)
F415D=F(2,"rewarded2",".CSMsg.TAllianceMedalRoleRedis.rewarded2",7,6,1,false,0,5,1)
F416D=F(2,"warned",".CSMsg.TAllianceMedalRoleRedis.warned",8,7,1,false,0,5,1)
F417D=F(2,"dayResetTime",".CSMsg.TAllianceMedalRoleRedis.dayResetTime",9,8,1,false,0,5,1)
F418D=F(2,"arrResetInfo",".CSMsg.TAllianceMedalRoleRedis.arrResetInfo",10,9,3,false,{},11,10)
M147G=D(1,"TAllianceMedalRoleRedis",".CSMsg.TAllianceMedalRoleRedis",false,{},{},nil,{})
M148G=D(1,"TMSG_ALLIANCE_MEDAL_BASE_REQ",".CSMsg.TMSG_ALLIANCE_MEDAL_BASE_REQ",false,{},{},{},{})
F419D=F(2,"id",".CSMsg.TMSG_ALLIANCE_MEDAL_BASE_RSP.id",1,0,2,false,0,5,1)
F420D=F(2,"arrMedal",".CSMsg.TMSG_ALLIANCE_MEDAL_BASE_RSP.arrMedal",2,1,3,false,{},11,10)
M149G=D(1,"TMSG_ALLIANCE_MEDAL_BASE_RSP",".CSMsg.TMSG_ALLIANCE_MEDAL_BASE_RSP",false,{},{},nil,{})
F421D=F(2,"id",".CSMsg.TMSG_ALLIANCE_MEDAL_BASE_NTF.id",1,0,2,false,0,5,1)
F422D=F(2,"arrMedal",".CSMsg.TMSG_ALLIANCE_MEDAL_BASE_NTF.arrMedal",2,1,3,false,{},11,10)
M150G=D(1,"TMSG_ALLIANCE_MEDAL_BASE_NTF",".CSMsg.TMSG_ALLIANCE_MEDAL_BASE_NTF",false,{},{},nil,{})
F423D=F(2,"id",".CSMsg.TMSG_ALLIANCE_MEDAL_DETAIL_REQ.id",1,0,2,false,0,5,1)
M151G=D(1,"TMSG_ALLIANCE_MEDAL_DETAIL_REQ",".CSMsg.TMSG_ALLIANCE_MEDAL_DETAIL_REQ",false,{},{},nil,{})
F424D=F(2,"errorcode",".CSMsg.TMSG_ALLIANCE_MEDAL_DETAIL_RSP.errorcode",1,0,2,false,nil,14,8)
F425D=F(2,"id",".CSMsg.TMSG_ALLIANCE_MEDAL_DETAIL_RSP.id",2,1,2,false,0,5,1)
F426D=F(2,"arrRank",".CSMsg.TMSG_ALLIANCE_MEDAL_DETAIL_RSP.arrRank",3,2,3,false,{},11,10)
F427D=F(2,"auto",".CSMsg.TMSG_ALLIANCE_MEDAL_DETAIL_RSP.auto",4,3,1,false,false,8,7)
F428D=F(2,"arrProgress",".CSMsg.TMSG_ALLIANCE_MEDAL_DETAIL_RSP.arrProgress",5,4,3,false,{},11,10)
M152G=D(1,"TMSG_ALLIANCE_MEDAL_DETAIL_RSP",".CSMsg.TMSG_ALLIANCE_MEDAL_DETAIL_RSP",false,{},{},nil,{})
F429D=F(2,"id",".CSMsg.TMSG_ALLIANCE_MEDAL_REWARD_REQ.id",1,0,2,false,0,5,1)
F430D=F(2,"lv",".CSMsg.TMSG_ALLIANCE_MEDAL_REWARD_REQ.lv",2,1,2,false,0,5,1)
M153G=D(1,"TMSG_ALLIANCE_MEDAL_REWARD_REQ",".CSMsg.TMSG_ALLIANCE_MEDAL_REWARD_REQ",false,{},{},nil,{})
F431D=F(2,"id",".CSMsg.TMSG_ALLIANCE_MEDAL_REWARD_RSP.id",1,0,2,false,0,5,1)
F432D=F(2,"lv",".CSMsg.TMSG_ALLIANCE_MEDAL_REWARD_RSP.lv",2,1,2,false,0,5,1)
F433D=F(2,"errorcode",".CSMsg.TMSG_ALLIANCE_MEDAL_REWARD_RSP.errorcode",3,2,2,false,nil,14,8)
M154G=D(1,"TMSG_ALLIANCE_MEDAL_REWARD_RSP",".CSMsg.TMSG_ALLIANCE_MEDAL_REWARD_RSP",false,{},{},nil,{})
F434D=F(2,"id",".CSMsg.TMSG_ALLIANCE_MEDAL_WARNDEL_REQ.id",1,0,2,false,0,5,1)
M155G=D(1,"TMSG_ALLIANCE_MEDAL_WARNDEL_REQ",".CSMsg.TMSG_ALLIANCE_MEDAL_WARNDEL_REQ",false,{},{},nil,{})
F435D=F(2,"id",".CSMsg.TMSG_ALLIANCE_MEDAL_WARNDEL_RSP.id",1,0,2,false,0,5,1)
F436D=F(2,"errorcode",".CSMsg.TMSG_ALLIANCE_MEDAL_WARNDEL_RSP.errorcode",2,1,2,false,nil,14,8)
M156G=D(1,"TMSG_ALLIANCE_MEDAL_WARNDEL_RSP",".CSMsg.TMSG_ALLIANCE_MEDAL_WARNDEL_RSP",false,{},{},nil,{})
F437D=F(2,"id",".CSMsg.TMSG_ALLIANCE_MEDAL_WARNAUTO_REQ.id",1,0,2,false,0,5,1)
F438D=F(2,"auto",".CSMsg.TMSG_ALLIANCE_MEDAL_WARNAUTO_REQ.auto",2,1,2,false,false,8,7)
M157G=D(1,"TMSG_ALLIANCE_MEDAL_WARNAUTO_REQ",".CSMsg.TMSG_ALLIANCE_MEDAL_WARNAUTO_REQ",false,{},{},nil,{})
F439D=F(2,"id",".CSMsg.TMSG_ALLIANCE_MEDAL_WARNAUTO_RSP.id",1,0,2,false,0,5,1)
F440D=F(2,"auto",".CSMsg.TMSG_ALLIANCE_MEDAL_WARNAUTO_RSP.auto",2,1,2,false,false,8,7)
F441D=F(2,"errorcode",".CSMsg.TMSG_ALLIANCE_MEDAL_WARNAUTO_RSP.errorcode",3,2,2,false,nil,14,8)
M158G=D(1,"TMSG_ALLIANCE_MEDAL_WARNAUTO_RSP",".CSMsg.TMSG_ALLIANCE_MEDAL_WARNAUTO_RSP",false,{},{},nil,{})
F442D=F(2,"id",".CSMsg.TMSG_ALLIANCE_MEDAL_WARN_REQ.id",1,0,2,false,0,5,1)
M159G=D(1,"TMSG_ALLIANCE_MEDAL_WARN_REQ",".CSMsg.TMSG_ALLIANCE_MEDAL_WARN_REQ",false,{},{},nil,{})
F443D=F(2,"id",".CSMsg.TMSG_ALLIANCE_MEDAL_WARN_RSP.id",1,0,2,false,0,5,1)
F444D=F(2,"errorcode",".CSMsg.TMSG_ALLIANCE_MEDAL_WARN_RSP.errorcode",2,1,2,false,nil,14,8)
M160G=D(1,"TMSG_ALLIANCE_MEDAL_WARN_RSP",".CSMsg.TMSG_ALLIANCE_MEDAL_WARN_RSP",false,{},{},nil,{})
F445D=F(2,"firstJoinTime",".CSMsg.TMSG_ALLIANCE_FIRST_JOINTIME_NTF.firstJoinTime",1,0,2,false,0,4,4)
M161G=D(1,"TMSG_ALLIANCE_FIRST_JOINTIME_NTF",".CSMsg.TMSG_ALLIANCE_FIRST_JOINTIME_NTF",false,{},{},nil,{})
F446D=F(2,"operate",".CSMsg.TMSG_ALLIANCE_MARK_REQ.operate",1,0,2,false,0,13,3)
F447D=F(2,"info",".CSMsg.TMSG_ALLIANCE_MARK_REQ.info",2,1,2,false,nil,11,10)
M162G=D(1,"TMSG_ALLIANCE_MARK_REQ",".CSMsg.TMSG_ALLIANCE_MARK_REQ",false,{},{},nil,{})
F448D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_MARK_RSP.errorCode",1,0,2,false,nil,14,8)
F449D=F(2,"operate",".CSMsg.TMSG_ALLIANCE_MARK_RSP.operate",2,1,1,false,0,13,3)
F450D=F(2,"info",".CSMsg.TMSG_ALLIANCE_MARK_RSP.info",3,2,1,false,nil,11,10)
M163G=D(1,"TMSG_ALLIANCE_MARK_RSP",".CSMsg.TMSG_ALLIANCE_MARK_RSP",false,{},{},nil,{})
F451D=F(2,"languageId",".CSMsg.TMSG_ALLIANCE_ONE_CLICK_JOIN_REQ.languageId",1,0,2,false,0,5,1)
F452D=F(2,"devLanguageId",".CSMsg.TMSG_ALLIANCE_ONE_CLICK_JOIN_REQ.devLanguageId",2,1,1,false,0,5,1)
M164G=D(1,"TMSG_ALLIANCE_ONE_CLICK_JOIN_REQ",".CSMsg.TMSG_ALLIANCE_ONE_CLICK_JOIN_REQ",false,{},{},nil,{})
F453D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_ONE_CLICK_JOIN_RSP.errorCode",1,0,2,false,nil,14,8)
F454D=F(2,"leaveAllianceTime",".CSMsg.TMSG_ALLIANCE_ONE_CLICK_JOIN_RSP.leaveAllianceTime",2,1,1,false,0,5,1)
F455D=F(2,"allianceId",".CSMsg.TMSG_ALLIANCE_ONE_CLICK_JOIN_RSP.allianceId",3,2,1,false,0,5,1)
M165G=D(1,"TMSG_ALLIANCE_ONE_CLICK_JOIN_RSP",".CSMsg.TMSG_ALLIANCE_ONE_CLICK_JOIN_RSP",false,{},{},nil,{})
F456D=F(2,"roleId",".CSMsg.TAllianceRecordPlayInfo.roleId",1,0,2,false,0,5,1)
F457D=F(2,"roleName",".CSMsg.TAllianceRecordPlayInfo.roleName",2,1,2,false,"",9,9)
F458D=F(2,"shortName",".CSMsg.TAllianceRecordPlayInfo.shortName",3,2,1,false,"",9,9)
F459D=F(2,"x",".CSMsg.TAllianceRecordPlayInfo.x",4,3,1,false,0,5,1)
F460D=F(2,"y",".CSMsg.TAllianceRecordPlayInfo.y",5,4,1,false,0,5,1)
F461D=F(2,"allianceId",".CSMsg.TAllianceRecordPlayInfo.allianceId",6,5,1,false,0,5,1)
M166G=D(1,"TAllianceRecordPlayInfo",".CSMsg.TAllianceRecordPlayInfo",false,{},{},nil,{})
F462D=F(2,"recordId",".CSMsg.TAllianceRecordInfo.recordId",1,0,2,false,0,4,4)
F463D=F(2,"isWin",".CSMsg.TAllianceRecordInfo.isWin",2,1,1,false,0,5,1)
F464D=F(2,"AtkInfo",".CSMsg.TAllianceRecordInfo.AtkInfo",3,2,1,false,nil,11,10)
F465D=F(2,"DefInfo",".CSMsg.TAllianceRecordInfo.DefInfo",4,3,1,false,nil,11,10)
M167G=D(1,"TAllianceRecordInfo",".CSMsg.TAllianceRecordInfo",false,{},{},nil,{})
F466D=F(2,"arrRecordInfo",".CSMsg.TAllianceRecordList.arrRecordInfo",1,0,3,false,{},11,10)
M168G=D(1,"TAllianceRecordList",".CSMsg.TAllianceRecordList",false,{},{},nil,{})
F467D=F(2,"recordId",".CSMsg.TMSG_ALLIANCE_RECORD_REQ.recordId",1,0,2,false,0,4,4)
M169G=D(1,"TMSG_ALLIANCE_RECORD_REQ",".CSMsg.TMSG_ALLIANCE_RECORD_REQ",false,{},{},nil,{})
F468D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_RECORD_RSP.errorCode",1,0,2,false,nil,14,8)
F469D=F(2,"records",".CSMsg.TMSG_ALLIANCE_RECORD_RSP.records",2,1,1,false,"",9,9)
M170G=D(1,"TMSG_ALLIANCE_RECORD_RSP",".CSMsg.TMSG_ALLIANCE_RECORD_RSP",false,{},{},nil,{})
M171G=D(1,"TMSG_ALLIANCE_MASS_REQ",".CSMsg.TMSG_ALLIANCE_MASS_REQ",false,{},{},{},{})
F470D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_MASS_RSP.errorCode",1,0,2,false,nil,14,8)
F471D=F(2,"x",".CSMsg.TMSG_ALLIANCE_MASS_RSP.x",2,1,1,false,0,13,3)
F472D=F(2,"y",".CSMsg.TMSG_ALLIANCE_MASS_RSP.y",3,2,1,false,0,13,3)
M172G=D(1,"TMSG_ALLIANCE_MASS_RSP",".CSMsg.TMSG_ALLIANCE_MASS_RSP",false,{},{},nil,{})
F473D=F(2,"x",".CSMsg.TMSG_ALLIANCE_MASS_NTF.x",1,0,1,false,0,13,3)
F474D=F(2,"y",".CSMsg.TMSG_ALLIANCE_MASS_NTF.y",2,1,1,false,0,13,3)
F475D=F(2,"freeMassCnt",".CSMsg.TMSG_ALLIANCE_MASS_NTF.freeMassCnt",3,2,1,false,0,13,3)
F476D=F(2,"lastMassTime",".CSMsg.TMSG_ALLIANCE_MASS_NTF.lastMassTime",4,3,1,false,0,5,1)
M173G=D(1,"TMSG_ALLIANCE_MASS_NTF",".CSMsg.TMSG_ALLIANCE_MASS_NTF",false,{},{},nil,{})
F477D=F(2,"allianceId",".CSMsg.TMSG_ALLIANCE_MASS_CNT_NTF.allianceId",1,0,2,false,0,5,1)
F478D=F(2,"dbid",".CSMsg.TMSG_ALLIANCE_MASS_CNT_NTF.dbid",2,1,2,false,0,5,1)
F479D=F(2,"freeMassCnt",".CSMsg.TMSG_ALLIANCE_MASS_CNT_NTF.freeMassCnt",3,2,2,false,0,5,1)
M174G=D(1,"TMSG_ALLIANCE_MASS_CNT_NTF",".CSMsg.TMSG_ALLIANCE_MASS_CNT_NTF",false,{},{},nil,{})
F480D=F(2,"roleId",".CSMsg.TAlliance_Intelligence_PlayInfo.roleId",1,0,2,false,0,5,1)
F481D=F(2,"roleName",".CSMsg.TAlliance_Intelligence_PlayInfo.roleName",2,1,2,false,"",9,9)
F482D=F(2,"shortName",".CSMsg.TAlliance_Intelligence_PlayInfo.shortName",3,2,1,false,"",9,9)
F483D=F(2,"x",".CSMsg.TAlliance_Intelligence_PlayInfo.x",4,3,1,false,0,5,1)
F484D=F(2,"y",".CSMsg.TAlliance_Intelligence_PlayInfo.y",5,4,1,false,0,5,1)
F485D=F(2,"allianceId",".CSMsg.TAlliance_Intelligence_PlayInfo.allianceId",6,5,1,false,0,5,1)
F486D=F(2,"faceId",".CSMsg.TAlliance_Intelligence_PlayInfo.faceId",7,6,1,false,0,5,1)
F487D=F(2,"frameId",".CSMsg.TAlliance_Intelligence_PlayInfo.frameId",8,7,1,false,0,5,1)
F488D=F(2,"faceStr",".CSMsg.TAlliance_Intelligence_PlayInfo.faceStr",9,8,1,false,"",9,9)
M175G=D(1,"TAlliance_Intelligence_PlayInfo",".CSMsg.TAlliance_Intelligence_PlayInfo",false,{},{},nil,{})
F489D=F(2,"indexId",".CSMsg.TAlliance_Intelligence_Info.indexId",1,0,2,false,"",9,9)
F490D=F(2,"AtkInfo",".CSMsg.TAlliance_Intelligence_Info.AtkInfo",2,1,1,false,nil,11,10)
F491D=F(2,"DefInfo",".CSMsg.TAlliance_Intelligence_Info.DefInfo",3,2,1,false,nil,11,10)
F492D=F(2,"startTime",".CSMsg.TAlliance_Intelligence_Info.startTime",4,3,1,false,0,5,1)
F493D=F(2,"endTime",".CSMsg.TAlliance_Intelligence_Info.endTime",5,4,1,false,0,5,1)
M176G=D(1,"TAlliance_Intelligence_Info",".CSMsg.TAlliance_Intelligence_Info",false,{},{},nil,{})
F494D=F(2,"arrIntelligenceInfo",".CSMsg.TAllianceIntelligenceList.arrIntelligenceInfo",1,0,3,false,{},11,10)
M177G=D(1,"TAllianceIntelligenceList",".CSMsg.TAllianceIntelligenceList",false,{},{},nil,{})
M178G=D(1,"TMSG_ALLIANCE_INTELLIGENCE_REQ",".CSMsg.TMSG_ALLIANCE_INTELLIGENCE_REQ",false,{},{},{},{})
F495D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_INTELLIGENCE_RSP.errorCode",1,0,2,false,nil,14,8)
F496D=F(2,"intelligences",".CSMsg.TMSG_ALLIANCE_INTELLIGENCE_RSP.intelligences",2,1,1,false,"",9,9)
M179G=D(1,"TMSG_ALLIANCE_INTELLIGENCE_RSP",".CSMsg.TMSG_ALLIANCE_INTELLIGENCE_RSP",false,{},{},nil,{})
F497D=F(2,"intelligences",".CSMsg.TMSG_ALLIANCE_INTELLIGENCE_NTF.intelligences",1,0,1,false,"",9,9)
M180G=D(1,"TMSG_ALLIANCE_INTELLIGENCE_NTF",".CSMsg.TMSG_ALLIANCE_INTELLIGENCE_NTF",false,{},{},nil,{})
M181G=D(1,"TMSG_ALLIANCE_NEEDPOPUP_NTF",".CSMsg.TMSG_ALLIANCE_NEEDPOPUP_NTF",false,{},{},{},{})
F498D=F(2,"nums",".CSMsg.TMSG_ALLIANCE_SURPASS_RALLYPOINT_NUMS_NTF.nums",1,0,1,false,0,5,1)
M182G=D(1,"TMSG_ALLIANCE_SURPASS_RALLYPOINT_NUMS_NTF",".CSMsg.TMSG_ALLIANCE_SURPASS_RALLYPOINT_NUMS_NTF",false,{},{},nil,{})
F499D=F(2,"chat",".CSMsg.TMSG_ALLIANCE_SHARE_REQ.chat",1,0,2,false,"",12,9)
M183G=D(1,"TMSG_ALLIANCE_SHARE_REQ",".CSMsg.TMSG_ALLIANCE_SHARE_REQ",false,{},{},nil,{})
F500D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_SHARE_RSP.errorCode",1,0,2,false,nil,14,8)
M184G=D(1,"TMSG_ALLIANCE_SHARE_RSP",".CSMsg.TMSG_ALLIANCE_SHARE_RSP",false,{},{},nil,{})
F501D=F(2,"rewardId",".CSMsg.TMSG_ALLIANCE_INVITATION_REWARD_NTF.rewardId",1,0,3,false,{},5,1)
M185G=D(1,"TMSG_ALLIANCE_INVITATION_REWARD_NTF",".CSMsg.TMSG_ALLIANCE_INVITATION_REWARD_NTF",false,{},{},nil,{})
M186G=D(1,"TMSG_ALLIANCE_INVITATION_REQ",".CSMsg.TMSG_ALLIANCE_INVITATION_REQ",false,{},{},{},{})
F502D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_INVITATION_RSP.errorCode",1,0,2,false,nil,14,8)
M187G=D(1,"TMSG_ALLIANCE_INVITATION_RSP",".CSMsg.TMSG_ALLIANCE_INVITATION_RSP",false,{},{},nil,{})
F503D=F(2,"fireRoleId",".CSMsg.TMSG_ALLIANCE_OUTFIRE_REQ.fireRoleId",1,0,2,false,0,5,1)
M188G=D(1,"TMSG_ALLIANCE_OUTFIRE_REQ",".CSMsg.TMSG_ALLIANCE_OUTFIRE_REQ",false,{},{},nil,{})
F504D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_OUTFIRE_RSP.errorCode",1,0,2,false,nil,14,8)
M189G=D(1,"TMSG_ALLIANCE_OUTFIRE_RSP",".CSMsg.TMSG_ALLIANCE_OUTFIRE_RSP",false,{},{},nil,{})
F505D=F(2,"fireRoleId",".CSMsg.TMSG_ALLIANCE_OUTFIRE_NTF.fireRoleId",1,0,1,false,0,5,1)
F506D=F(2,"outFireRoleId",".CSMsg.TMSG_ALLIANCE_OUTFIRE_NTF.outFireRoleId",2,1,1,false,0,5,1)
F507D=F(2,"allianceId",".CSMsg.TMSG_ALLIANCE_OUTFIRE_NTF.allianceId",3,2,1,false,0,5,1)
F508D=F(2,"FireCnt",".CSMsg.TMSG_ALLIANCE_OUTFIRE_NTF.FireCnt",4,3,1,false,0,5,1)
F509D=F(2,"sandboxSid",".CSMsg.TMSG_ALLIANCE_OUTFIRE_NTF.sandboxSid",5,4,1,false,0,13,3)
M190G=D(1,"TMSG_ALLIANCE_OUTFIRE_NTF",".CSMsg.TMSG_ALLIANCE_OUTFIRE_NTF",false,{},{},nil,{})
F510D=F(2,"indexId",".CSMsg.TAlliance_R4R5Todo_Base.indexId",1,0,2,false,0,5,1)
F511D=F(2,"taskId",".CSMsg.TAlliance_R4R5Todo_Base.taskId",2,1,2,false,0,5,1)
F512D=F(2,"taskSid",".CSMsg.TAlliance_R4R5Todo_Base.taskSid",3,2,2,false,0,5,1)
F513D=F(2,"atyType",".CSMsg.TAlliance_R4R5Todo_Base.atyType",4,3,2,false,0,5,1)
F514D=F(2,"state",".CSMsg.TAlliance_R4R5Todo_Base.state",5,4,2,false,nil,14,8)
F515D=F(2,"authority",".CSMsg.TAlliance_R4R5Todo_Base.authority",6,5,2,false,0,5,1)
F516D=F(2,"curGetNum",".CSMsg.TAlliance_R4R5Todo_Base.curGetNum",7,6,2,false,0,5,1)
F517D=F(2,"curFinishNum",".CSMsg.TAlliance_R4R5Todo_Base.curFinishNum",8,7,2,false,0,5,1)
F518D=F(2,"externParam1",".CSMsg.TAlliance_R4R5Todo_Base.externParam1",9,8,1,false,0,5,1)
M191G=D(1,"TAlliance_R4R5Todo_Base",".CSMsg.TAlliance_R4R5Todo_Base",false,{},{},nil,{})
F519D=F(2,"allianceId",".CSMsg.TAlliance_R4R5Todo_List.allianceId",1,0,2,false,0,5,1)
F520D=F(2,"arrTask",".CSMsg.TAlliance_R4R5Todo_List.arrTask",2,1,3,false,{},11,10)
M193G=D(1,"TAlliance_R4R5Todo_List",".CSMsg.TAlliance_R4R5Todo_List",false,{},{},nil,{})
F521D=F(2,"allianceId",".CSMsg.TMSG_ALLIANCE_R4R5TODO_NTF.allianceId",1,0,1,false,0,5,1)
F522D=F(2,"arrTask",".CSMsg.TMSG_ALLIANCE_R4R5TODO_NTF.arrTask",2,1,3,false,{},11,10)
F523D=F(2,"rewardState",".CSMsg.TMSG_ALLIANCE_R4R5TODO_NTF.rewardState",3,2,1,false,0,5,1)
F524D=F(2,"isClear",".CSMsg.TMSG_ALLIANCE_R4R5TODO_NTF.isClear",4,3,1,false,false,8,7)
M194G=D(1,"TMSG_ALLIANCE_R4R5TODO_NTF",".CSMsg.TMSG_ALLIANCE_R4R5TODO_NTF",false,{},{},nil,{})
F525D=F(2,"indexId",".CSMsg.TMSG_ALLIANCE_R4R5TODO_REWARD_REQ.indexId",1,0,2,false,0,5,1)
F526D=F(2,"taskSid",".CSMsg.TMSG_ALLIANCE_R4R5TODO_REWARD_REQ.taskSid",2,1,2,false,0,5,1)
M195G=D(1,"TMSG_ALLIANCE_R4R5TODO_REWARD_REQ",".CSMsg.TMSG_ALLIANCE_R4R5TODO_REWARD_REQ",false,{},{},nil,{})
F527D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCE_R4R5TODO_REWARD_RSP.errorCode",1,0,2,false,nil,14,8)
F528D=F(2,"indexId",".CSMsg.TMSG_ALLIANCE_R4R5TODO_REWARD_RSP.indexId",2,1,1,false,0,5,1)
F529D=F(2,"taskSid",".CSMsg.TMSG_ALLIANCE_R4R5TODO_REWARD_RSP.taskSid",3,2,1,false,0,5,1)
M196G=D(1,"TMSG_ALLIANCE_R4R5TODO_REWARD_RSP",".CSMsg.TMSG_ALLIANCE_R4R5TODO_REWARD_RSP",false,{},{},nil,{})
F530D=F(2,"indexId",".CSMsg.TAllianceLog_Info.indexId",1,0,2,false,0,7,3)
F531D=F(2,"type",".CSMsg.TAllianceLog_Info.type",2,1,2,false,0,5,1)
F532D=F(2,"logStr",".CSMsg.TAllianceLog_Info.logStr",3,2,1,false,"",9,9)
F533D=F(2,"logNum",".CSMsg.TAllianceLog_Info.logNum",4,3,1,false,0,6,4)
M197G=D(1,"TAllianceLog_Info",".CSMsg.TAllianceLog_Info",false,{},{},nil,{})
F534D=F(2,"msgType",".CSMsg.TAllianceLog_Base.msgType",1,0,2,false,0,5,1)
F535D=F(2,"arrInfo",".CSMsg.TAllianceLog_Base.arrInfo",2,1,3,false,{},11,10)
F536D=F(2,"time",".CSMsg.TAllianceLog_Base.time",3,2,2,false,0,6,4)
M198G=D(1,"TAllianceLog_Base",".CSMsg.TAllianceLog_Base",false,{},{},nil,{})
F537D=F(2,"logType",".CSMsg.TMSG_ALLIANCELOG_RECORD_REQ.logType",1,0,2,false,nil,14,8)
M199G=D(1,"TMSG_ALLIANCELOG_RECORD_REQ",".CSMsg.TMSG_ALLIANCELOG_RECORD_REQ",false,{},{},nil,{})
F538D=F(2,"errorCode",".CSMsg.TMSG_ALLIANCELOG_RECORD_RSP.errorCode",1,0,2,false,nil,14,8)
F539D=F(2,"logType",".CSMsg.TMSG_ALLIANCELOG_RECORD_RSP.logType",2,1,1,false,nil,14,8)
F540D=F(2,"arrBaseInfo",".CSMsg.TMSG_ALLIANCELOG_RECORD_RSP.arrBaseInfo",3,2,3,false,{},9,9)
M201G=D(1,"TMSG_ALLIANCELOG_RECORD_RSP",".CSMsg.TMSG_ALLIANCELOG_RECORD_RSP",false,{},{},nil,{})

E1M.values = {V1M,V2M,V3M}
E2M.values = {V4M,V5M,V6M,V7M}
E3M.values = {V8M,V9M,V10M}
E4M.values = {V11M,V12M,V13M}
E5M.values = {V14M,V15M,V16M}
E6M.values = {V17M,V18M}
E7M.values = {V19M,V20M}
E8M.values = {V21M,V22M,V23M,V24M,V25M,V26M,V27M,V28M,V29M,V30M,V31M,V32M,V33M,V34M,V35M,V36M,V37M,V38M,V39M,V40M,V41M,V42M,V43M,V44M,V45M,V46M}
E9M.values = {V47M,V48M}
E10M.values = {V49M,V50M}
E11M.values = {V51M,V52M,V53M,V54M,V55M,V56M,V57M,V58M,V59M,V60M}
E12M.values = {V61M,V62M}
E13M.values = {V63M,V64M,V65M}
E14M.values = {V66M,V67M,V68M,V69M,V70M}
E15M.values = {V71M,V72M}
E16M.values = {V73M,V74M,V75M,V76M,V77M}
E17M.values = {V78M,V79M}
E18M.values = {V80M,V81M}
E19M.values = {V82M,V83M}
E20M.values = {V84M,V85M}
E21M.values = {V86M,V87M}
E22M.values = {V88M,V89M,V90M,V91M,V92M}
E23M.values = {V93M,V94M,V95M,V96M,V97M,V98M,V99M,V100M,V101M,V102M}
E24M.values = {V103M,V104M,V105M,V106M}
E25M.values = {V107M,V108M,V109M}
E26M.values = {V110M,V111M,V112M}
E27M.values = {V113M,V114M,V115M,V116M}
E28M.values = {V117M,V118M,V119M}
E29M.values = {V120M,V121M,V122M,V123M}
E30M.values = {V124M,V125M,V126M,V127M,V128M,V129M,V130M,V131M,V132M,V133M,V134M,V135M,V136M,V137M,V138M,V139M,V140M,V141M,V142M,V143M,V144M,V145M,V146M,V147M,V148M,V149M,V150M,V151M,V152M,V153M,V154M,V155M,V156M}
F9D.enum_type=M2G
F12D.enum_type=M3G
F19D.message_type=M4G
F27D.message_type=M5G
M1G.fields={F1D, F2D, F3D, F4D, F5D, F6D, F7D, F8D, F9D, F10D, F11D, F12D, F13D, F14D, F15D, F16D, F17D, F18D, F19D, F20D, F21D, F22D, F23D, F24D, F25D, F26D, F27D, F28D, F29D, F30D, F31D, F32D, F33D}
F43D.enum_type=M6G
F44D.enum_type=M7G
M4G.fields={F34D, F35D, F36D, F37D, F38D, F39D, F40D, F41D, F42D, F43D, F44D, F45D, F46D, F47D, F48D, F49D, F50D}
F57D.enum_type=M9G
M8G.fields={F51D, F52D, F53D, F54D, F55D, F56D, F57D, F58D, F59D, F60D, F61D, F62D, F63D}
M10G.fields={F64D, F65D, F66D, F67D, F68D, F69D}
F71D.message_type=M10G
M11G.fields={F70D, F71D}
F72D.message_type=M4G
M12G.fields={F72D, F73D}
M13G.fields={F74D}
F75D.message_type=M1G
F77D.message_type=M1G
F78D.message_type=M1G
F79D.message_type=M1G
F80D.message_type=M1G
M14G.fields={F75D, F76D, F77D, F78D, F79D, F80D}
F81D.message_type=M1G
M15G.fields={F81D}
M16G.fields={F82D}
F83D.message_type=M1G
F84D.enum_type=error_code_pb.E1M
M17G.fields={F83D, F84D, F85D, F86D}
M19G.fields={F87D}
F88D.enum_type=error_code_pb.E1M
F90D.message_type=M1G
M20G.fields={F88D, F89D, F90D}
M21G.fields={F91D}
F92D.enum_type=error_code_pb.E1M
F94D.message_type=M4G
M22G.fields={F92D, F93D, F94D}
M23G.fields={F95D}
F96D.enum_type=error_code_pb.E1M
M24G.fields={F96D, F97D, F98D}
F100D.enum_type=M26G
M25G.fields={F99D, F100D}
F101D.enum_type=error_code_pb.E1M
F104D.enum_type=M2G
M27G.fields={F101D, F102D, F103D, F104D}
F105D.enum_type=M29G
M28G.fields={F105D, F106D}
F107D.enum_type=error_code_pb.E1M
F108D.enum_type=M29G
M30G.fields={F107D, F108D}
F109D.enum_type=error_code_pb.E1M
M32G.fields={F109D, F110D}
M33G.fields={F111D, F112D, F113D, F114D, F115D}
F116D.enum_type=error_code_pb.E1M
M34G.fields={F116D, F117D}
M35G.fields={F118D}
F119D.enum_type=error_code_pb.E1M
M36G.fields={F119D, F120D}
F122D.enum_type=M6G
F123D.enum_type=M7G
M37G.fields={F121D, F122D, F123D}
F124D.enum_type=error_code_pb.E1M
F126D.enum_type=M6G
F127D.enum_type=M7G
M38G.fields={F124D, F125D, F126D, F127D}
M39G.fields={F128D}
F129D.enum_type=error_code_pb.E1M
M40G.fields={F129D, F130D}
M41G.fields={F131D, F132D}
F133D.enum_type=M43G
F134D.enum_type=M44G
M42G.fields={F133D, F134D, F135D}
F136D.enum_type=error_code_pb.E1M
F137D.enum_type=M43G
F138D.enum_type=M44G
F139D.message_type=M41G
M45G.fields={F136D, F137D, F138D, F139D, F140D, F141D}
F142D.enum_type=M47G
M46G.fields={F142D}
F143D.enum_type=error_code_pb.E1M
F144D.enum_type=M47G
M48G.fields={F143D, F144D}
F145D.enum_type=M50G
F146D.message_type=M8G
F147D.message_type=M8G
M49G.fields={F145D, F146D, F147D}
F148D.enum_type=error_code_pb.E1M
F149D.message_type=M10G
F150D.message_type=M10G
M52G.fields={F148D, F149D, F150D, F151D, F152D}
F154D.enum_type=M54G
M53G.fields={F153D, F154D}
F155D.enum_type=error_code_pb.E1M
F157D.enum_type=M54G
M55G.fields={F155D, F156D, F157D}
M56G.fields={F158D}
F159D.enum_type=error_code_pb.E1M
F161D.message_type=M10G
M57G.fields={F159D, F160D, F161D}
M58G.fields={F162D}
F163D.enum_type=error_code_pb.E1M
M59G.fields={F163D, F164D, F165D}
F167D.enum_type=M61G
M60G.fields={F166D, F167D}
F168D.enum_type=error_code_pb.E1M
F170D.enum_type=M61G
M62G.fields={F168D, F169D, F170D, F171D, F172D, F173D}
M63G.fields={F174D}
F175D.enum_type=error_code_pb.E1M
M64G.fields={F175D, F176D}
F177D.enum_type=error_code_pb.E1M
F178D.message_type=M4G
M66G.fields={F177D, F178D}
F179D.enum_type=M68G
M67G.fields={F179D, F180D}
F181D.enum_type=error_code_pb.E1M
M69G.fields={F181D, F182D}
M70G.fields={F183D}
F184D.enum_type=error_code_pb.E1M
M71G.fields={F184D, F185D}
F186D.enum_type=M29G
M72G.fields={F186D, F187D}
F188D.enum_type=error_code_pb.E1M
F189D.enum_type=M29G
M73G.fields={F188D, F189D, F190D}
M74G.fields={F191D}
F192D.enum_type=error_code_pb.E1M
M75G.fields={F192D, F193D}
F194D.enum_type=M2G
F197D.enum_type=M3G
M76G.fields={F194D, F195D, F196D, F197D}
F198D.enum_type=error_code_pb.E1M
F199D.enum_type=M2G
F202D.enum_type=M3G
M77G.fields={F198D, F199D, F200D, F201D, F202D}
M78G.fields={F203D, F204D, F205D}
F206D.enum_type=error_code_pb.E1M
M79G.fields={F206D}
M80G.fields={F207D, F208D, F209D}
F210D.message_type=M80G
M81G.fields={F210D, F211D, F212D}
M82G.fields={F213D, F214D, F215D, F216D, F217D, F218D, F219D, F220D, F221D, F222D, F223D}
F224D.message_type=M82G
M83G.fields={F224D}
M84G.fields={F225D, F226D}
F227D.enum_type=M86G
F229D.message_type=M1G
F230D.message_type=M4G
F232D.message_type=M12G
F238D.message_type=M84G
M85G.fields={F227D, F228D, F229D, F230D, F231D, F232D, F233D, F234D, F235D, F236D, F237D, F238D, F239D, F240D, F241D}
F242D.enum_type=error_code_pb.E1M
M88G.fields={F242D}
M89G.fields={F243D, F244D, F245D, F246D, F247D, F248D, F249D, F250D, F251D}
F259D.enum_type=M6G
F260D.enum_type=M7G
M90G.fields={F252D, F253D, F254D, F255D, F256D, F257D, F258D, F259D, F260D, F261D, F262D}
M91G.fields={F263D, F264D}
F265D.enum_type=error_code_pb.E1M
F266D.message_type=M89G
M92G.fields={F265D, F266D, F267D}
M93G.fields={F268D, F269D}
F270D.enum_type=error_code_pb.E1M
F271D.message_type=M89G
M94G.fields={F270D, F271D, F272D}
F273D.enum_type=M96G
M95G.fields={F273D}
F274D.enum_type=error_code_pb.E1M
F275D.enum_type=M96G
F276D.message_type=M90G
M97G.fields={F274D, F275D, F276D}
M98G.fields={F277D, F278D}
M99G.fields={F279D, F280D, F281D, F282D, F283D}
F284D.message_type=M98G
M100G.fields={F284D, F285D, F286D, F287D, F288D, F289D}
F290D.message_type=M99G
F291D.message_type=M100G
M101G.fields={F290D, F291D}
F292D.enum_type=error_code_pb.E1M
F294D.message_type=M98G
M103G.fields={F292D, F293D, F294D, F295D}
F296D.enum_type=error_code_pb.E1M
F297D.message_type=M101G
M105G.fields={F296D, F297D, F298D}
F300D.message_type=M101G
F301D.message_type=M100G
M106G.fields={F299D, F300D, F301D}
M107G.fields={F302D}
F303D.enum_type=error_code_pb.E1M
F304D.message_type=M98G
M108G.fields={F303D, F304D}
F306D.message_type=M98G
F307D.enum_type=M110G
M109G.fields={F305D, F306D, F307D, F308D, F309D, F310D}
F311D.enum_type=error_code_pb.E1M
M112G.fields={F311D, F312D}
F313D.message_type=M99G
F314D.message_type=M100G
M113G.fields={F313D, F314D, F315D}
F316D.enum_type=M115G
M114G.fields={F316D, F317D}
F318D.enum_type=error_code_pb.E1M
F319D.enum_type=M115G
M116G.fields={F318D, F319D, F320D}
F321D.enum_type=M118G
M117G.fields={F321D}
M119G.fields={F322D, F323D, F324D, F325D, F326D, F327D, F328D, F329D, F330D, F331D}
F332D.message_type=M119G
M120G.fields={F332D}
F333D.message_type=M119G
M121G.fields={F333D, F334D, F335D}
M122G.fields={F336D}
F337D.enum_type=error_code_pb.E1M
M123G.fields={F337D}
F339D.message_type=M121G
M124G.fields={F338D, F339D}
M125G.fields={F340D}
F341D.enum_type=error_code_pb.E1M
M126G.fields={F341D, F342D, F343D}
M127G.fields={F344D}
F345D.enum_type=error_code_pb.E1M
M128G.fields={F345D}
M129G.fields={F346D, F347D, F348D, F349D, F350D, F351D, F352D, F353D, F354D}
F355D.message_type=M129G
M130G.fields={F355D}
M131G.fields={F356D, F357D}
F358D.enum_type=error_code_pb.E1M
M132G.fields={F358D, F359D, F360D}
F361D.enum_type=error_code_pb.E1M
M134G.fields={F361D}
M135G.fields={F362D}
F363D.enum_type=error_code_pb.E1M
M136G.fields={F363D, F364D}
M137G.fields={F365D}
F367D.message_type=M139G
M138G.fields={F366D, F367D, F368D}
M139G.fields={F369D, F370D, F371D}
F376D.message_type=M138G
F377D.message_type=M138G
F378D.message_type=M138G
M140G.fields={F372D, F373D, F374D, F375D, F376D, F377D, F378D, F379D}
M141G.fields={F380D, F381D, F382D}
M142G.fields={F383D, F384D, F385D}
F390D.message_type=M142G
M5G.fields={F386D, F387D, F388D, F389D, F390D, F391D, F392D}
M143G.fields={F393D, F394D, F395D, F396D}
M144G.fields={F397D, F398D, F399D, F400D, F401D, F402D, F403D}
F405D.message_type=M144G
M145G.fields={F404D, F405D}
M146G.fields={F406D, F407D, F408D}
F418D.message_type=M146G
M147G.fields={F409D, F410D, F411D, F412D, F413D, F414D, F415D, F416D, F417D, F418D}
F420D.message_type=M5G
M149G.fields={F419D, F420D}
F422D.message_type=M5G
M150G.fields={F421D, F422D}
M151G.fields={F423D}
F424D.enum_type=error_code_pb.E1M
F426D.message_type=M143G
F428D.message_type=M142G
M152G.fields={F424D, F425D, F426D, F427D, F428D}
M153G.fields={F429D, F430D}
F433D.enum_type=error_code_pb.E1M
M154G.fields={F431D, F432D, F433D}
M155G.fields={F434D}
F436D.enum_type=error_code_pb.E1M
M156G.fields={F435D, F436D}
M157G.fields={F437D, F438D}
F441D.enum_type=error_code_pb.E1M
M158G.fields={F439D, F440D, F441D}
M159G.fields={F442D}
F444D.enum_type=error_code_pb.E1M
M160G.fields={F443D, F444D}
M161G.fields={F445D}
F447D.message_type=M82G
M162G.fields={F446D, F447D}
F448D.enum_type=error_code_pb.E1M
F450D.message_type=M82G
M163G.fields={F448D, F449D, F450D}
M164G.fields={F451D, F452D}
F453D.enum_type=error_code_pb.E1M
M165G.fields={F453D, F454D, F455D}
M166G.fields={F456D, F457D, F458D, F459D, F460D, F461D}
F464D.message_type=M166G
F465D.message_type=M166G
M167G.fields={F462D, F463D, F464D, F465D}
F466D.message_type=M167G
M168G.fields={F466D}
M169G.fields={F467D}
F468D.enum_type=error_code_pb.E1M
M170G.fields={F468D, F469D}
F470D.enum_type=error_code_pb.E1M
M172G.fields={F470D, F471D, F472D}
M173G.fields={F473D, F474D, F475D, F476D}
M174G.fields={F477D, F478D, F479D}
M175G.fields={F480D, F481D, F482D, F483D, F484D, F485D, F486D, F487D, F488D}
F490D.message_type=M175G
F491D.message_type=M175G
M176G.fields={F489D, F490D, F491D, F492D, F493D}
F494D.message_type=M176G
M177G.fields={F494D}
F495D.enum_type=error_code_pb.E1M
M179G.fields={F495D, F496D}
M180G.fields={F497D}
M182G.fields={F498D}
M183G.fields={F499D}
F500D.enum_type=error_code_pb.E1M
M184G.fields={F500D}
M185G.fields={F501D}
F502D.enum_type=error_code_pb.E1M
M187G.fields={F502D}
M188G.fields={F503D}
F504D.enum_type=error_code_pb.E1M
M189G.fields={F504D}
M190G.fields={F505D, F506D, F507D, F508D, F509D}
F514D.enum_type=M192G
M191G.fields={F510D, F511D, F512D, F513D, F514D, F515D, F516D, F517D, F518D}
F520D.message_type=M191G
M193G.fields={F519D, F520D}
F522D.message_type=M191G
M194G.fields={F521D, F522D, F523D, F524D}
M195G.fields={F525D, F526D}
F527D.enum_type=error_code_pb.E1M
M196G.fields={F527D, F528D, F529D}
M197G.fields={F530D, F531D, F532D, F533D}
F535D.message_type=M197G
M198G.fields={F534D, F535D, F536D}
F537D.enum_type=M200G
M199G.fields={F537D}
F538D.enum_type=error_code_pb.E1M
F539D.enum_type=M200G
M201G.fields={F538D, F539D, F540D}

EAlliaceLeaveType_AutoClear = 3
EAlliaceLeaveType_KickOut = 1
EAlliaceLeaveType_Self = 2
EAllianceChangeType_Expel = 2
EAllianceChangeType_R5ToMe = 3
EAllianceChangeType_R5ToOthers = 1
EAllianceCheckType_Announcement = 2
EAllianceCheckType_Mail = 1
EAllianceCheckType_MailTitle = 3
EAllianceGetMemberType_All = 1
EAllianceGetMemberType_Offline = 2
EAllianceGetMemberType_Online = 3
EAllianceGiftBoxType_Marauder = 3
EAllianceGiftBoxType_Normal = 1
EAllianceGiftBoxType_Present = 2
EAllianceGiftSendType_Login = 1
EAllianceGiftSendType_Produce = 2
EAllianceGiftType_Ally = 2
EAllianceGiftType_Loot = 1
EAllianceHelpType_Build = 1
EAllianceHelpType_Heal = 2
EAllianceHelpType_Study = 3
EAllianceJoinType_default = 0
EAllianceJoinType_invitation = 1
EAllianceMemberType_Create = 1
EAllianceMemberType_Dismiss = 4
EAllianceMemberType_Enter = 2
EAllianceMemberType_Leave = 3
EAllianceR4R5_TaskState_CanGetReward = 1
EAllianceR4R5_TaskState_CantGetReward = 2
EAllianceR4R5_TaskState_Doing = 0
EAllianceR4R5_TaskType_Announcement = 37201
EAllianceR4R5_TaskType_DeclareCity = 37204
EAllianceR4R5_TaskType_SetAllianceBossTime = 37202
EAllianceR4R5_TaskType_SetR4 = 37203
EAllianceRecordType_Activity = 4
EAllianceRecordType_Info = 3
EAllianceRecordType_Member = 2
EAllianceRecordType_War = 1
EOptType_Agree = 1
EOptType_Refuse = 2
TAllianceAchievement_BaseRole =M(M119G)
TAllianceAchievement_BaseRoleList =M(M120G)
TAllianceAchievement_WholeAchievementInfo =M(M121G)
TAllianceApplyPerson =M(M12G)
TAllianceBase =M(M1G)
TAllianceBoxBase =M(M8G)
TAllianceBoxRewards =M(M41G)
TAllianceHelp_KeyValue =M(M98G)
TAllianceHelp_List =M(M100G)
TAllianceHelp_Role =M(M99G)
TAllianceHelp_Role_List =M(M101G)
TAllianceHelp_Start_Zone2Micro =M(M109G)
TAllianceIntelligenceList =M(M177G)
TAllianceLog_Base =M(M198G)
TAllianceLog_Info =M(M197G)
TAllianceMarkInfo =M(M83G)
TAllianceMedal =M(M5G)
TAllianceMedalDataRedis =M(M145G)
TAllianceMedalRank =M(M143G)
TAllianceMedalResetRoleInfo =M(M146G)
TAllianceMedalRoleRedis =M(M147G)
TAllianceMedalSingleRedis =M(M144G)
TAllianceOneMarkInfo =M(M82G)
TAlliancePersonBase =M(M4G)
TAllianceRankBase =M(M89G)
TAllianceRecordInfo =M(M167G)
TAllianceRecordList =M(M168G)
TAllianceRecordPlayInfo =M(M166G)
TAllianceRoleRankBase =M(M90G)
TAllianceTechnologyBase =M(M10G)
TAllianceTechnologyInfo =M(M11G)
TAlliance_Intelligence_Info =M(M176G)
TAlliance_Intelligence_PlayInfo =M(M175G)
TAlliance_InvitePlayerInfo =M(M84G)
TAlliance_Invite_Info =M(M129G)
TAlliance_R4R5Todo_Base =M(M191G)
TAlliance_R4R5Todo_List =M(M193G)
TDestoryCity_BaseInfo =M(M139G)
TDestoryCity_PlayerInfo =M(M138G)
TMSG_ALLIANCELOG_RECORD_REQ =M(M199G)
TMSG_ALLIANCELOG_RECORD_RSP =M(M201G)
TMSG_ALLIANCE_ACHIEVEMENT_NTF =M(M124G)
TMSG_ALLIANCE_ACHIEVEMENT_REQ =M(M122G)
TMSG_ALLIANCE_ACHIEVEMENT_REWARD_REQ =M(M125G)
TMSG_ALLIANCE_ACHIEVEMENT_REWARD_RSP =M(M126G)
TMSG_ALLIANCE_ACHIEVEMENT_RSP =M(M123G)
TMSG_ALLIANCE_ANONYMOUS_REQ =M(M46G)
TMSG_ALLIANCE_ANONYMOUS_RSP =M(M48G)
TMSG_ALLIANCE_APPLICATION_LIST_REQ =M(M65G)
TMSG_ALLIANCE_APPLICATION_LIST_RSP =M(M66G)
TMSG_ALLIANCE_APPLY_REQ =M(M25G)
TMSG_ALLIANCE_APPLY_RSP =M(M27G)
TMSG_ALLIANCE_AUTHORITY_REQ =M(M37G)
TMSG_ALLIANCE_AUTHORITY_RSP =M(M38G)
TMSG_ALLIANCE_AUTO_RESEARCH_REQ =M(M135G)
TMSG_ALLIANCE_AUTO_RESEARCH_RSP =M(M136G)
TMSG_ALLIANCE_CHANGECEO_REQ =M(M35G)
TMSG_ALLIANCE_CHANGECEO_RSP =M(M36G)
TMSG_ALLIANCE_CHANGE_NTF =M(M117G)
TMSG_ALLIANCE_CHECKCONTENT_REQ =M(M114G)
TMSG_ALLIANCE_CHECKCONTENT_RSP =M(M116G)
TMSG_ALLIANCE_CHECKNAME_REQ =M(M28G)
TMSG_ALLIANCE_CHECKNAME_RSP =M(M30G)
TMSG_ALLIANCE_CREATE_REQ =M(M33G)
TMSG_ALLIANCE_CREATE_RSP =M(M34G)
TMSG_ALLIANCE_EXIT_REQ =M(M87G)
TMSG_ALLIANCE_EXIT_RSP =M(M88G)
TMSG_ALLIANCE_EXPEL_REQ =M(M39G)
TMSG_ALLIANCE_EXPEL_RSP =M(M40G)
TMSG_ALLIANCE_FIRST_JOINTIME_NTF =M(M161G)
TMSG_ALLIANCE_FREE_MOVE_CITY_NTF =M(M141G)
TMSG_ALLIANCE_GIFT_GET_REQ =M(M42G)
TMSG_ALLIANCE_GIFT_GET_RSP =M(M45G)
TMSG_ALLIANCE_GIFT_NTF =M(M49G)
TMSG_ALLIANCE_HANDLE_APPLICATION_REQ =M(M67G)
TMSG_ALLIANCE_HANDLE_APPLICATION_RSP =M(M69G)
TMSG_ALLIANCE_HELP_CLICK_NTF =M(M113G)
TMSG_ALLIANCE_HELP_CLICK_REQ =M(M111G)
TMSG_ALLIANCE_HELP_CLICK_RSP =M(M112G)
TMSG_ALLIANCE_HELP_LIST_NTF =M(M106G)
TMSG_ALLIANCE_HELP_LIST_REQ =M(M104G)
TMSG_ALLIANCE_HELP_LIST_RSP =M(M105G)
TMSG_ALLIANCE_HELP_SELF_REQ =M(M102G)
TMSG_ALLIANCE_HELP_SELF_RSP =M(M103G)
TMSG_ALLIANCE_HELP_START_REQ =M(M107G)
TMSG_ALLIANCE_HELP_START_RSP =M(M108G)
TMSG_ALLIANCE_INFO_REQ =M(M19G)
TMSG_ALLIANCE_INFO_RSP =M(M20G)
TMSG_ALLIANCE_INTELLIGENCE_NTF =M(M180G)
TMSG_ALLIANCE_INTELLIGENCE_REQ =M(M178G)
TMSG_ALLIANCE_INTELLIGENCE_RSP =M(M179G)
TMSG_ALLIANCE_INVITATION_REQ =M(M186G)
TMSG_ALLIANCE_INVITATION_REWARD_NTF =M(M185G)
TMSG_ALLIANCE_INVITATION_RSP =M(M187G)
TMSG_ALLIANCE_INVITE_INFO_REQ =M(M133G)
TMSG_ALLIANCE_INVITE_INFO_RSP =M(M134G)
TMSG_ALLIANCE_INVITE_NTF =M(M130G)
TMSG_ALLIANCE_INVITE_REQ =M(M127G)
TMSG_ALLIANCE_INVITE_RSP =M(M128G)
TMSG_ALLIANCE_ISACCEPT_INVITE_REQ =M(M131G)
TMSG_ALLIANCE_ISACCEPT_INVITE_RSP =M(M132G)
TMSG_ALLIANCE_KILLNUM_REQ =M(M93G)
TMSG_ALLIANCE_KILLNUM_RSP =M(M94G)
TMSG_ALLIANCE_MAIL_SEND_REQ =M(M78G)
TMSG_ALLIANCE_MAIL_SEND_RSP =M(M79G)
TMSG_ALLIANCE_MARK_REQ =M(M162G)
TMSG_ALLIANCE_MARK_RSP =M(M163G)
TMSG_ALLIANCE_MASS_CNT_NTF =M(M174G)
TMSG_ALLIANCE_MASS_NTF =M(M173G)
TMSG_ALLIANCE_MASS_REQ =M(M171G)
TMSG_ALLIANCE_MASS_RSP =M(M172G)
TMSG_ALLIANCE_MEDAL_BASE_NTF =M(M150G)
TMSG_ALLIANCE_MEDAL_BASE_REQ =M(M148G)
TMSG_ALLIANCE_MEDAL_BASE_RSP =M(M149G)
TMSG_ALLIANCE_MEDAL_DETAIL_REQ =M(M151G)
TMSG_ALLIANCE_MEDAL_DETAIL_RSP =M(M152G)
TMSG_ALLIANCE_MEDAL_REWARD_REQ =M(M153G)
TMSG_ALLIANCE_MEDAL_REWARD_RSP =M(M154G)
TMSG_ALLIANCE_MEDAL_WARNAUTO_REQ =M(M157G)
TMSG_ALLIANCE_MEDAL_WARNAUTO_RSP =M(M158G)
TMSG_ALLIANCE_MEDAL_WARNDEL_REQ =M(M155G)
TMSG_ALLIANCE_MEDAL_WARNDEL_RSP =M(M156G)
TMSG_ALLIANCE_MEDAL_WARN_REQ =M(M159G)
TMSG_ALLIANCE_MEDAL_WARN_RSP =M(M160G)
TMSG_ALLIANCE_MODIFY_ANNOUNCEMENT_REQ =M(M63G)
TMSG_ALLIANCE_MODIFY_ANNOUNCEMENT_RSP =M(M64G)
TMSG_ALLIANCE_MODIFY_FLAG_REQ =M(M70G)
TMSG_ALLIANCE_MODIFY_FLAG_RSP =M(M71G)
TMSG_ALLIANCE_MODIFY_JOIN_CONDITIONS_REQ =M(M76G)
TMSG_ALLIANCE_MODIFY_JOIN_CONDITIONS_RSP =M(M77G)
TMSG_ALLIANCE_MODIFY_LANGUAGE_REQ =M(M74G)
TMSG_ALLIANCE_MODIFY_LANGUAGE_RSP =M(M75G)
TMSG_ALLIANCE_MODIFY_NAME_REQ =M(M72G)
TMSG_ALLIANCE_MODIFY_NAME_RSP =M(M73G)
TMSG_ALLIANCE_NEEDPOPUP_NTF =M(M181G)
TMSG_ALLIANCE_ONE_CLICK_JOIN_REQ =M(M164G)
TMSG_ALLIANCE_ONE_CLICK_JOIN_RSP =M(M165G)
TMSG_ALLIANCE_OUTFIRE_NTF =M(M190G)
TMSG_ALLIANCE_OUTFIRE_REQ =M(M188G)
TMSG_ALLIANCE_OUTFIRE_RSP =M(M189G)
TMSG_ALLIANCE_POWERRANK_REQ =M(M91G)
TMSG_ALLIANCE_POWERRANK_RSP =M(M92G)
TMSG_ALLIANCE_QUICK_ADD_REQ =M(M23G)
TMSG_ALLIANCE_QUICK_ADD_RSP =M(M24G)
TMSG_ALLIANCE_R4R5TODO_NTF =M(M194G)
TMSG_ALLIANCE_R4R5TODO_REWARD_REQ =M(M195G)
TMSG_ALLIANCE_R4R5TODO_REWARD_RSP =M(M196G)
TMSG_ALLIANCE_RANDOM_NAME_REQ =M(M31G)
TMSG_ALLIANCE_RANDOM_NAME_RSP =M(M32G)
TMSG_ALLIANCE_RECOMMEND_REQ =M(M13G)
TMSG_ALLIANCE_RECOMMEND_RSP =M(M14G)
TMSG_ALLIANCE_RECORD_REQ =M(M169G)
TMSG_ALLIANCE_RECORD_RSP =M(M170G)
TMSG_ALLIANCE_ROLERANK_REQ =M(M95G)
TMSG_ALLIANCE_ROLERANK_RSP =M(M97G)
TMSG_ALLIANCE_ROLE_INFO_REQ =M(M21G)
TMSG_ALLIANCE_ROLE_INFO_RSP =M(M22G)
TMSG_ALLIANCE_SEARCH_REQ =M(M16G)
TMSG_ALLIANCE_SEARCH_RSP =M(M17G)
TMSG_ALLIANCE_SHARE_REQ =M(M183G)
TMSG_ALLIANCE_SHARE_RSP =M(M184G)
TMSG_ALLIANCE_SURPASS_RALLYPOINT_NUMS_NTF =M(M182G)
TMSG_ALLIANCE_TECHNOLOGY_DETAILS_REQ =M(M56G)
TMSG_ALLIANCE_TECHNOLOGY_DETAILS_RSP =M(M57G)
TMSG_ALLIANCE_TECHNOLOGY_DONATE_REQ =M(M60G)
TMSG_ALLIANCE_TECHNOLOGY_DONATE_RSP =M(M62G)
TMSG_ALLIANCE_TECHNOLOGY_RECOMMEND_REQ =M(M53G)
TMSG_ALLIANCE_TECHNOLOGY_RECOMMEND_RSP =M(M55G)
TMSG_ALLIANCE_TECHNOLOGY_REQ =M(M51G)
TMSG_ALLIANCE_TECHNOLOGY_RSP =M(M52G)
TMSG_ALLIANCE_TECHNOLOGY_STUDY_REQ =M(M58G)
TMSG_ALLIANCE_TECHNOLOGY_STUDY_RSP =M(M59G)
TMSG_ALLIANCE_UPDATE_NTF =M(M85G)
TMSG_NEWPALYER_FREEMOVE_NTF =M(M137G)
TMSG_OFFLINE_CITY_DESTOIRY_REWARD_NTF =M(M140G)
TMedalLvProgressInfo =M(M142G)
TRepeatAllianceBase =M(M15G)
TSandboxNCInfo =M(M81G)
TSandboxNCOccupyAndDeclareWar =M(M80G)
emAllianceApplyType_Apply = 2
emAllianceApplyType_Auto = 1
emAllianceApplyType_None = 0
emAllianceAuthority_R1 = 1
emAllianceAuthority_R2 = 2
emAllianceAuthority_R3 = 3
emAllianceAuthority_R4 = 4
emAllianceAuthority_R5 = 5
emAllianceClearType_conditioned1 = 2
emAllianceClearType_rejectclear = 1
emAllianceDonateOpt_Coin = 1
emAllianceDonateOpt_Diamond = 2
emAllianceLogType_AllianceMark = 11
emAllianceLogType_AllowJoin = 5
emAllianceLogType_AppointConductor = 17
emAllianceLogType_BossAutoOpen = 16
emAllianceLogType_BossOpen = 15
emAllianceLogType_BossOpenPre = 14
emAllianceLogType_CancelPosition = 4
emAllianceLogType_CityBeDeclared = 27
emAllianceLogType_CityBeOccupied = 30
emAllianceLogType_CityDeclareCancel = 26
emAllianceLogType_CityDeclareHasPeople = 25
emAllianceLogType_CityDeclareNoPeople = 24
emAllianceLogType_CityOccupyHasPeople = 29
emAllianceLogType_CityOccupyNoPeople = 28
emAllianceLogType_Declaration = 10
emAllianceLogType_DesertAbstain = 19
emAllianceLogType_DesertPrebook = 18
emAllianceLogType_DwAuthority = 2
emAllianceLogType_ExitAlliance = 8
emAllianceLogType_JoinAlliance = 31
emAllianceLogType_KickOut = 6
emAllianceLogType_SetJoinApply = 33
emAllianceLogType_SetJoinLevel = 12
emAllianceLogType_SetJoinNoApply = 32
emAllianceLogType_SetJoinPower = 13
emAllianceLogType_SetPosition = 3
emAllianceLogType_SystemKickOut = 7
emAllianceLogType_TranCeo = 9
emAllianceLogType_UpAuthority = 1
emAllianceLogType_ZombieCancel = 21
emAllianceLogType_ZombieOpen = 23
emAllianceLogType_ZombiePrebook = 20
emAllianceLogType_ZombieUpdate = 22
emAllianceNameType_Name = 1
emAllianceNameType_ShortName = 2
emAllianceOneKey_No = 2
emAllianceOneKey_Yes = 1
emAlliancePermission_AbandonCity = 14
emAlliancePermission_AdjustingAuthority = 15
emAlliancePermission_AllianceBoss = 22
emAlliancePermission_AppointDriver = 25
emAlliancePermission_AutoResearch = 21
emAlliancePermission_ChangeAnnouncement = 4
emAlliancePermission_DeclareWar = 12
emAlliancePermission_Dismiss = 2
emAlliancePermission_Exit = 17
emAlliancePermission_ExpelMember = 8
emAlliancePermission_LeagueSet = 1
emAlliancePermission_Markers = 11
emAlliancePermission_PositionOpt = 18
emAlliancePermission_ProcApplication = 19
emAlliancePermission_RallyInvitation = 5
emAlliancePermission_RecommendTechnology = 10
emAlliancePermission_RemovalTeam = 13
emAlliancePermission_SendInviteLink = 20
emAlliancePermission_SendMail = 7
emAlliancePermission_SetRallyPoint = 6
emAlliancePermission_Share = 27
emAlliancePermission_TransferCEO = 3
emAlliancePermission_UpgradeTechnology = 9
emAlliancePermission_ViewMember = 16
emAlliancePermission_WholeArmyAttack = 23
emAlliancePermission_ZombieApocalypse = 26
emAlliancePositionOpt_Commission = 2
emAlliancePositionOpt_Remove = 1
emAlliancePosition_NoOfficial = 0
emAlliancePosition_nvshen = 3
emAlliancePosition_waijiaoguan = 4
emAlliancePosition_zhanshen = 1
emAlliancePosition_zhaomuguan = 2
emAllianceRSwitchOpt_Close = 2
emAllianceRSwitchOpt_Open = 1
emAllianceRecommendOpt_Cancel = 2
emAllianceRecommendOpt_Set = 1
emAllianceReqType_AllianceDeclare = 10
emAllianceReqType_Announce = 3
emAllianceReqType_Content = 5
emAllianceReqType_Input = 1
emAllianceReqType_Mail = 4
emAllianceReqType_MailTitle = 6
emAllianceReqType_Random = 2
emCongressReqType_MailSend = 8
emCongressReqType_MailTitle = 9
emCongressReqType_Manifesto = 7
emMail_AllianceAuthority_R1 = 1
emMail_AllianceAuthority_R2 = 2
emMail_AllianceAuthority_R3 = 4
emMail_AllianceAuthority_R4 = 8
emMail_AllianceAuthority_R5 = 16
emllianceORoleRank_CE = 1
emllianceORoleRank_EveryDay = 3
emllianceORoleRank_EveryWeek = 4
emllianceORoleRank_Kill = 2
emllianceOptType_AutoChange = 7
emllianceOptType_Change = 5
emllianceOptType_Create = 1
emllianceOptType_Expel = 4
emllianceOptType_Invitation = 8
emllianceOptType_Join = 2
emllianceOptType_Leave = 0
emllianceOptType_Login = 3
emllianceOptType_Share = 9
emllianceOptType_autoResearch = 6

