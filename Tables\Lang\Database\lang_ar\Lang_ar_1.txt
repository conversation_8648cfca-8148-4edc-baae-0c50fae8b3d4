return {
[0]='',
[1]='',
[2]='',
[3]='',
[4]='',
[5]='',
[6]='',
[7]='',
[8]='',
[9]=[[اذهب إلى التنزيل]],
[29]='',
[30]='',
[31]='',
[32]='',
[33]='',
[34]='',
[35]='',
[36]='',
[37]='',
[38]='',
[39]='',
[40]='',
[41]='',
[42]='',
[43]='',
[49]='',
[50]='',
[51]='',
[52]='',
[53]='',
[54]='',
[55]='',
[56]='',
[57]='',
[58]='',
[59]='',
[60]='',
[61]='',
[62]='',
[63]='',
[64]='',
[65]='',
[66]='',
[67]='',
[68]='',
[69]='',
[70]='',
[71]='',
[72]='',
[73]='',
[74]='',
[75]='',
[76]='',
[77]='',
[78]='',
[79]='',
[80]='',
[81]='',
[82]='',
[83]='',
[84]='',
[85]='',
[86]='',
[87]='',
[88]='',
[89]='',
[90]='',
[91]='',
[92]='',
[93]='',
[94]='',
[95]='',
[96]='',
[97]='',
[98]='',
[99]='',
[100]='',
[101]='',
[102]='',
[103]='',
[104]='',
[105]='',
[106]='',
[107]='',
[108]='',
[109]='',
[110]='',
[111]='',
[112]='',
[113]='',
[114]='',
[115]='',
[116]='',
[117]='',
[118]='',
[119]='',
[120]='',
[121]='',
[122]='',
[123]='',
[124]='',
[125]='',
[126]='',
[127]='',
[128]='',
[129]='',
[130]='',
[131]='',
[132]='',
[133]='',
[134]='',
[135]='',
[136]='',
[137]='',
[138]='',
[139]='',
[140]='',
[141]='',
[142]='',
[143]='',
[144]='',
[145]='',
[146]='',
[147]='',
[148]='',
[149]='',
[150]='',
[151]='',
[152]='',
[153]='',
[154]='',
[156]='',
[157]='',
[158]='',
[159]='',
[160]='',
[161]='',
[162]='',
[163]='',
[164]='',
[165]='',
[166]='',
[167]='',
[168]='',
[169]='',
[170]='',
[171]='',
[172]='',
[173]='',
[174]='',
[175]='',
[176]='',
[177]='',
[178]='',
[179]='',
[180]='',
[181]='',
[182]='',
[183]='',
[184]='',
[185]=[[ما إذا كنت تريد إضافة الطرف الآخر إلى القائمة السوداء وعدم تلقي رسائل الدردشة من الطرف الآخر]],
[186]='',
[187]='',
[188]='',
[189]='',
[190]='',
[191]='',
[192]='',
[193]='',
[194]='',
[195]='',
[196]='',
[197]='',
[198]='',
[199]='',
[200]='',
[201]='',
[202]='',
[203]='',
[204]='',
[205]='',
[206]='',
[207]='',
[208]='',
[209]='',
[221]='',
[222]='',
[223]='',
[224]='',
[225]='',
[226]='',
[227]='',
[228]='',
[229]='',
[230]='',
[231]='',
[232]='',
[233]='',
[234]='',
[235]='',
[236]='',
[237]='',
[238]='',
[239]='',
[240]='',
[241]='',
[242]='',
[243]='',
[244]='',
[245]='',
[246]='',
[247]='',
[248]='',
[249]='',
[250]='',
[251]='',
[252]='',
[253]='',
[254]='',
[255]='',
[256]='',
[257]='',
[258]='',
[260]='',
[261]='',
[262]='',
[263]='',
[264]='',
[265]='',
[266]='',
[267]='',
[268]='',
[269]='',
[270]='',
[271]='',
[272]='',
[273]='',
[274]='',
[275]='',
[276]='',
[277]='',
[278]='',
[279]='',
[280]='',
[281]='',
[282]='',
[283]='',
[284]='',
[285]='',
[286]='',
[287]='',
[288]='',
[289]='',
[290]='',
[291]='',
[292]='',
[293]='',
[294]='',
[295]='',
[296]='',
[297]='',
[298]='',
[299]='',
[300]='',
[301]='',
[302]='',
[303]='',
[304]='',
[305]='',
[306]='',
[307]='',
[308]='',
[309]='',
[310]='',
[311]='',
[312]='',
[313]='',
[314]='',
[315]='',
[316]='',
[317]='',
[318]='',
[319]='',
[320]='',
[321]='',
[322]='',
[323]='',
[324]='',
[325]='',
[326]='',
[327]='',
[328]='',
[329]='',
[330]='',
[331]='',
[332]='',
[333]='',
[334]='',
[335]='',
[336]='',
[337]='',
[338]='',
[339]='',
[340]='',
[341]='',
[342]='',
[343]='',
[344]='',
[345]='',
[346]='',
[400]='',
[401]='',
[402]='',
[403]='',
[404]='',
[405]='',
[406]='',
[407]='',
[408]='',
[409]='',
[500]='',
[501]='',
[502]='',
[605]='',
[606]='',
[614]='',
[615]='',
[616]='',
[618]='',
[619]='',
[640]=[[تسجيل الدخول مع أبل]],
[651]='',
[652]='',
[653]='',
[654]='',
[655]='',
[656]='',
[657]='',
[658]='',
[659]='',
[660]='',
[661]='',
[663]='',
[664]='',
[665]='',
[667]='',
[668]='',
[669]='',
[670]='',
[671]='',
[672]='',
[673]='',
[674]='',
[675]='',
[676]='',
[677]='',
[689]='',
[690]='',
[691]='',
[692]='',
[693]='',
[694]='',
[695]='',
[696]='',
[697]='',
[698]='',
[699]='',
[732]='English',
[734]='简体中文',
[737]='',
[738]='',
[742]='',
[770]='繁體中文',
[771]='Indonesia',
[772]='Filipino',
[773]=[[Tiếng Việt]],
[774]='ภาษาไทย',
[781]='Portugues',
[782]='한국',
[783]='',
[784]='',
[785]='',
[786]='',
[787]='',
[788]='',
[789]='',
[790]='',
[791]='',
[792]='',
[793]='',
[794]='',
[795]='',
[796]='français',
[797]='Deutsch',
[798]='Malay',
[799]='',
[800]='',
[801]='',
[802]='',
[803]='',
[804]='',
[805]='',
[806]=[[Русский язык]],
[807]='Español',
[808]='',
[809]='',
[810]='',
[811]='',
[812]='',
[813]='',
[814]='',
[815]='',
[816]='',
[817]='',
[818]='',
[819]='हिंदी',
[820]='한국어',
[821]='日本語',
[822]='Türkçe',
[823]='Italiano',
[824]='Polski',
[825]='عربي',
[826]='Nederlands',
[850]=[[Naver Game]],
[851]=[[Naver Cafe]],
[852]='',
[900]='',
[901]='',
[902]='',
[903]='',
[904]='',
[905]='',
[906]='',
[907]='',
[908]='',
[909]='',
[910]='',
[911]='',
[912]='',
[913]='',
[914]='',
[915]='',
[916]='',
[917]='',
[918]='',
[919]='',
[920]='',
[921]='',
[922]='',
[923]='',
[924]='',
[925]='',
[926]='',
[927]='',
[928]='',
[929]='',
[930]='',
[931]='',
[932]='',
[933]='',
[934]='',
[935]='',
[936]='',
[937]='',
[938]='',
[939]='',
[940]='',
[941]='',
[942]='',
[943]='',
[944]='',
[945]='',
[946]='',
[947]='',
[948]='',
[949]='',
[950]='',
[951]='',
[952]='',
[953]='',
[954]='',
[955]='',
[956]='',
[957]='',
[958]='',
[959]='',
[960]='',
[961]='',
[962]='',
[963]='',
[964]='',
[965]='',
[966]='',
[967]='',
[968]='',
[969]='',
[970]='',
[971]='',
[972]='',
[973]='',
[974]='',
[975]='',
[976]='',
[977]='',
[978]='',
[979]='',
[980]='',
[981]='',
[982]='',
[983]='',
[984]='',
[985]='',
[986]='',
[987]='',
[988]='',
[989]='',
[990]='',
[1001]='',
[1002]='',
[1003]='',
[1004]='',
[1005]='',
[1006]='',
[1007]='',
[1008]='',
[1009]='',
[1010]='',
[1011]='',
[1012]='',
[1013]='',
[1014]='',
[1102]='',
[1200]='　',
[1301]='',
[1302]='',
[1303]='',
[1304]='',
[1305]='',
[1306]='',
[1307]='',
[1308]='',
[1309]='',
[1310]='',
[1311]='',
[1312]='',
[1313]='',
[1314]='',
[1315]='',
[1316]='',
[1317]='',
[1318]='',
[1319]='',
[1320]='',
[1321]='',
[1322]='',
[1323]='',
[1324]='',
[1325]='',
[1326]='',
[1351]='',
[1352]='',
[1353]='',
[1354]='',
[1355]='',
[1356]='',
[1357]='',
[1400]='',
[1401]='',
[1402]='',
[1403]='',
[1404]='',
[1405]='',
[1406]='',
[1407]='',
[1408]='',
[1409]='',
[1410]='',
[1411]='',
[1412]='',
[1413]='',
[1414]='',
[1415]='',
[1416]='',
[1417]='',
[1418]='',
[1419]='',
[1420]='',
[1421]='',
[1422]='',
[1423]='',
[1424]='',
[1425]='',
[1426]='',
[1427]='',
[1428]='',
[1429]='',
[1430]='',
[1431]='',
[1432]='',
[1433]='',
[1434]='',
[1435]='',
[1150]='',
[1552]='',
[1553]='',
[1554]='',
[1555]='',
[1556]='',
[1557]='',
[1558]='',
[1559]='',
[1560]='',
[1561]='',
[1562]='',
[1563]='',
[1564]='',
[1565]='',
[1566]='',
[1567]='',
[1568]='',
[1569]='',
[1570]='',
[1571]='',
[1572]='',
[1573]='',
[1574]='',
[1575]='',
[1576]='',
[1577]='',
[1578]='',
[1579]='',
[1580]='',
[1581]='',
[1582]='',
[1583]='',
[1584]='',
[1585]='',
[1586]='',
[1587]='',
[1588]='',
[1589]='',
[1590]='',
[1591]='',
[1592]='',
[1593]='',
[1594]='',
[1595]='',
[1596]='',
[1597]='',
[1598]='Facebook',
[1599]='',
[1600]='',
[1601]='',
[1602]='',
[1603]='',
[1604]='',
[1605]='',
[1606]='',
[1607]='',
[1608]='',
[1609]='',
[1610]='',
[1611]='',
[1612]='',
[1613]='',
[1614]='',
[1615]='',
[1616]='',
[1617]='',
[1618]='',
[1619]='',
[1620]='',
[1621]='',
[1622]='',
[1623]='',
[1624]='',
[1625]='',
[1626]='',
[1627]='',
[1628]='',
[1629]='',
[1630]='',
[1631]='',
[1632]='',
[1633]='',
[1634]='',
[1635]='',
[1636]='',
[1637]='',
[1638]='',
[1639]='',
[1640]='',
[1641]='',
[1642]='',
[1643]='',
[1644]='',
[1645]='',
[1646]='',
[1647]='',
[1648]='',
[1649]='',
[1650]='',
[1651]='',
[1652]='',
[1653]='',
[1654]='',
[1655]='',
[1656]='',
[1657]='',
[1658]='',
[1659]='',
[1660]='',
[1661]='',
[1662]='',
[1663]='',
[1664]='',
[1665]='',
[1666]='',
[1667]='',
[1668]='',
[1669]='',
[1670]='',
[1671]='',
[1672]='',
[1673]='',
[1674]='',
[1675]='',
[1676]='',
[1677]='',
[1678]='',
[1679]='',
[1680]='',
[1681]='',
[1682]='',
[1683]='',
[1684]='',
[1685]='',
[1686]='',
[1687]='',
[1688]='',
[1689]='',
[1690]='',
[1691]='',
[1692]='',
[1693]='',
[1694]='',
[1695]='',
[1696]=[[الإصدار: %s]],
[1697]=[[إصدار الأصول：%s]],
[1698]='ينسخ',
[1699]=[[تم النسخ بنجاح]],
[1700]=[[أحدث إصدار للمورد: %s]],
[1701]='',
[1702]='',
[1703]='',
[1704]='',
[1705]='',
[1706]='',
[1707]='',
[1708]='',
[1709]='',
[1710]='',
[1711]='',
[1712]='',
[1713]='',
[1714]='',
[1715]='',
[1716]='',
[1717]='',
[1718]='',
[1719]='',
[1720]='',
[1721]='',
[1722]='',
[1723]='',
[1724]='',
[1725]='',
[1726]='',
[1727]='',
[1728]='',
[1729]='',
[1730]='',
[1731]='',
[1732]='',
[1733]='',
[1734]='',
[1735]='',
[1736]='',
[1737]='',
[1738]='',
[1739]='',
[1740]='',
[1741]='',
[1742]='',
[1743]='',
[1744]='',
[1745]='',
[1746]='',
[1747]='',
[1748]='',
[1749]='',
[1750]='',
[1751]='',
[1752]='',
[1753]='',
[1754]='',
[1755]='',
[1756]='',
[1757]='',
[1758]='',
[1759]='',
[1760]='',
[1763]='',
[1764]='',
[1766]='',
[1767]='',
[1798]='',
[1799]='',
[1800]='',
[1801]='',
[1802]='',
[1803]='',
[1804]='',
[1805]='',
[1806]='',
[1807]='',
[1808]='',
[1809]='',
[1810]='',
[1811]='',
[1812]='',
[1813]='',
[1814]='',
[1815]='',
[1816]='',
[1817]='',
[1818]='',
[1819]='',
[1820]='',
[1821]='',
[1822]='',
[1823]='',
[1824]='',
[1825]='',
[1826]='',
[1827]='',
[1828]='',
[1829]='',
[1830]='',
[1831]='',
[1832]='',
[1850]='',
[1851]='',
[1890]='',
[1891]='',
[1892]='',
[1893]='',
[1894]='',
[1895]='',
[1896]='',
[1897]='',
[1898]='',
[1899]='',
[1900]='',
[1901]='',
[1902]='',
[1903]='',
[1904]='',
[1905]='',
[1906]='',
[1907]='',
[1908]='',
[1909]='',
[1910]='',
[1911]='',
[1912]='',
[1913]='',
[1914]='',
[1915]='',
[1916]='',
[1917]='',
[1918]='',
[1919]='',
[1920]='',
[1921]='',
[1922]='',
[1923]='',
[1924]='',
[1925]='',
[1926]='',
[1927]='',
[1928]='',
[1929]='',
[1930]='',
[1931]='',
[1932]='',
[1933]='',
[1934]='',
[1935]='',
[1936]='',
[1937]='',
[1938]='',
[1939]='',
[1940]='',
[1941]='',
[1942]='',
[1943]='',
[1944]='',
[1945]='',
[1946]='',
[1947]='',
[1948]='',
[1949]='',
[1950]='',
[1951]='',
[1952]='',
[1953]='',
[1954]='',
[1955]='',
[1956]='',
[1957]='',
[1958]='',
[1959]='',
[1960]='',
[1961]='',
[1962]='',
[1963]='',
[1964]='',
[1965]='',
[1966]='',
[1967]='',
[1968]='',
[1969]='',
[1970]='',
[1971]='',
[1972]='',
[1973]='',
[1974]='',
[1975]='',
[1976]='',
[1977]='',
[1978]='',
[1979]='',
[1980]='',
[1981]='',
[1982]='',
[1983]='',
[1984]='',
[1985]='',
[1986]='',
[1987]='',
[1988]='',
[1989]='',
[1990]='',
[1991]='',
[1992]='',
[1993]='',
[1994]='',
[1995]='',
[1996]='',
[1997]='',
[1998]='',
[1999]='',
[2000]='',
[2001]='',
[2002]='',
[2003]='',
[2004]='',
[2005]='',
[2006]='',
[2007]='',
[2008]='',
[2011]='',
[2012]='',
[2013]='',
[2014]='',
[2015]='',
[2016]='',
[2017]='',
[2018]='',
[2019]='',
[2020]='',
[2021]='',
[2022]='',
[2023]='',
[2024]='',
[2025]='',
[2026]='',
[2027]='',
[2028]='',
[2029]='',
[2030]='',
[2031]='',
[2032]='',
[2033]='',
[2034]='',
[2035]='',
[2036]='',
[2037]='',
[2038]='',
[2039]='',
[2040]='',
[2041]='',
[2042]='',
[2043]='',
[2044]='',
[2045]='',
[2046]='',
[2047]='',
[2048]='',
[2049]='',
[2050]='',
[2051]='',
[2052]='',
[2053]='',
[2054]='',
[2055]='',
[2056]='',
[2057]='',
[2058]='',
[2100]='',
[2101]='',
[2102]='',
[2103]='',
[2104]='',
[2105]='',
[2106]='',
[2107]='',
[2108]='',
[2109]='',
[2110]='',
[2111]='',
[2112]='',
[2113]='',
[2114]='',
[2130]='',
[2131]='',
[2132]='',
[2133]='',
[2134]='',
[2135]='',
[2136]='',
[2141]='',
[2142]='',
[2143]='',
[2144]='',
[2145]='',
[2146]='',
[2147]='',
[2148]='',
[2149]='',
[2150]='',
[2151]='',
[2152]='',
[2153]='',
[2154]='',
[2155]='',
[2156]='',
[2157]='',
[2158]='',
[2159]='',
[2160]='',
[2161]='',
[2200]='',
[2201]='',
[2202]='',
[2203]='',
[2301]='',
[2302]='',
[2303]='',
[2304]='',
[2305]='',
[2306]='',
[2307]='',
[2308]='',
[2309]='',
[2310]='',
[2311]='',
[2312]='',
[2313]='',
[2314]='',
[2315]='',
[2316]='',
[2317]='',
[2318]='',
[2319]='',
[2320]='',
[2321]='',
[2322]='',
[2323]='',
[2324]='',
[2325]='',
[2326]='',
[2327]='',
[2328]='',
[2329]='',
[2330]='',
[2331]='',
[2332]='',
[2333]='',
[2334]='',
[2335]='',
[2336]='',
[2337]='',
[2338]='',
[2339]='',
[2340]='',
[2341]='',
[2342]='',
[2343]='',
[2344]='',
[2345]='',
[2346]='',
[2347]='',
[2348]='',
[2349]='',
[2350]='',
[2351]='',
[2352]='',
[2353]='',
[2354]='',
[2355]='',
[2356]='',
[2357]='',
[2358]='',
[2359]='',
[2360]='',
[2361]='',
[2362]='',
[2363]='',
[2364]='',
[2365]='',
[2366]='',
[2367]='',
[2368]='',
[2369]='',
[2370]='',
[2371]='',
[2372]='',
[2373]='',
[2374]='',
[2375]='',
[2376]='',
[2377]='',
[2378]='',
[2379]='',
[2380]='',
[2381]='',
[2382]='',
[2383]='',
[2384]='',
[2385]='',
[2386]='',
[2387]='',
[2388]='',
[2389]='',
[2390]='',
[2391]='',
[2392]='',
[2393]='',
[2394]='',
[2401]='',
[2402]='',
[2403]='',
[2404]='',
[2405]='',
[2406]='',
[2407]='',
[2408]='',
[2409]='',
[2410]='',
[2411]='',
[2412]='',
[2413]='',
[2414]='',
[2415]='',
[2416]='',
[2417]='',
[2418]='',
[2419]='',
[2420]='',
[2501]='',
[2502]='',
[2503]='',
[2504]='',
[2505]='',
[2506]='',
[2507]='',
[2508]='',
[2509]='',
[2510]='',
[2511]='',
[2512]='',
[2513]='',
[2514]='',
[2515]='',
[2516]='',
[2517]='',
[2518]='',
[2519]='',
[2520]='',
[2521]='',
[2522]='',
[2523]='',
[2524]='',
[2525]='',
[2526]='',
[2527]='',
[2528]='',
[2529]='',
[2540]='',
[2541]='',
[2542]='',
[2543]='',
[2601]='',
[2602]='',
[2603]='',
[2604]='',
[2605]='',
[2606]='',
[2607]='',
[2701]='',
[2702]='',
[2703]='',
[2704]='',
[2705]='',
[2706]='',
[2707]='',
[2708]='',
[2709]='',
[2710]='',
[2711]='',
[2712]='',
[2713]='',
[2714]='',
[2715]='',
[2716]='',
[2717]='',
[2718]='',
[2719]='',
[2720]='',
[2721]='',
[2722]='',
[2723]='',
[2724]='',
[2725]='',
[2726]='',
[2727]='',
[2728]='',
[2729]='',
[2730]='',
[2731]='',
[2732]='',
[2733]='',
[2734]='',
[2735]='',
[2736]='',
[2737]='',
[2738]='',
[2739]='',
[2740]='',
[2741]='',
[2742]='',
[2743]='',
[2744]='',
[2745]='',
[2746]='',
[2747]='',
[2748]='',
[2749]='',
[2750]='',
[2751]='',
[2760]='',
[2761]='',
[2762]=[[لقد استولى Frost على نيويورك، ولكن لحسن الحظ، يمكنك الآن مساعدة أبطال الطائرة في حل الألغاز وكسب المكافآت للتحضير للمعركة القادمة!]],
[2763]=[[ساعد الحراس الخارقين على هزيمة أعدائهم والحصول على ميزة في المعركة الخارقة التالية!]],
[2764]=[[يهدد تنين الصقيع المدينة، ويجب علينا أن نتخلص بسرعة من العوائق أمامنا ونهزمه.]],
[2765]=[[حرب القوى العظمى تقترب، ساعد هواينغ بليد في الحصول على صندوق الكنز للاستعداد لحرب القوى العظمى القادمة!]],
[2766]=[[لقد استولى Frost على نيويورك، ولكن لحسن الحظ، يمكنك الآن مساعدة الأبطال الخارقين في حل الألغاز وكسب المكافآت للتحضير للمعارك القادمة!]],
[2767]=[[لقد وصل تنين الصقيع، يجب علينا الانطلاق على الفور، الوقت لا ينتظر أحدًا!]],
[2768]=[[ساعد الحراس الخارقين على هزيمة أعدائهم والحصول على ميزة في المعركة الخارقة التالية!]],
[2769]='',
[2770]='',
[2799]='',
[2801]='',
[2802]='',
[2803]='',
[2805]='',
[2806]='',
[2807]='',
[2808]='',
[2809]='',
[2810]='',
[2811]='',
[2812]='',
[2813]='',
[2814]='',
[2815]='',
[2816]='',
[2817]='',
[2818]='',
[2819]='',
[2820]='',
[2821]='',
[2822]='',
[2823]='',
[2824]='',
[2825]='',
[2826]='',
[2827]='',
[2828]='',
[2829]='',
[2830]='',
[2831]='',
[2832]='',
[2833]='',
[2834]='',
[2835]='',
[2836]='',
[2837]='',
[2838]='',
[2839]='',
[2840]='',
[2841]='',
[2842]='',
[2843]='',
[2844]='',
[2845]='',
[2846]='',
[2850]='',
[2851]='',
[2852]='',
[2853]='',
[2854]='',
[2855]='',
[2856]='',
[2857]='',
[2858]='',
[2859]='',
[2860]='',
[2901]='',
[2902]='',
[2903]='',
[2905]='',
[2909]='',
[2910]='',
[2911]='',
[2912]='',
[2913]='',
[2914]='',
[2915]='',
[2916]='',
[2917]='',
[2918]='',
[2919]='',
[2920]='',
[2921]='',
[2922]='',
[2923]='',
[2924]='',
[2925]='',
[2927]='',
[2928]='',
[2929]='',
[2932]='',
[2933]='',
[2934]='',
[2936]='',
[2937]='',
[2938]='',
[2939]='',
[2941]='',
[2942]='',
[2943]='',
[2944]='',
[2950]='',
[2954]='',
[2957]='',
[2960]='',
[2963]='',
[2971]='',
[2973]='',
[2974]='',
[2975]='',
[2978]='',
[2984]='',
[2987]='',
[2990]='',
[3001]='',
[3002]='',
[3005]='',
[3006]='',
[3007]='',
[3009]='',
[3012]='',
[3014]='',
[3015]='',
[3065]='',
[3066]='',
[3069]='',
[3073]='',
[3074]='',
[3076]='',
[3077]='',
[3078]='',
[3079]='',
[3086]='',
[3102]='',
[3103]='',
[3104]='',
[3105]='',
[3107]='',
[3112]='',
[3113]='',
[3124]='',
[3125]='',
[3133]='',
[3160]='',
[3161]='',
[3162]='',
[3163]='',
[3167]='',
[3169]='',
[3170]='',
[3171]='',
[3172]='',
[3173]='',
[3174]='',
[3175]='',
[3176]='',
[3177]='',
[3178]='',
[3179]='',
[3180]='',
[3181]='',
[3182]='',
[3192]='',
[3193]='',
[3194]='',
[3200]='',
[3201]='',
[3202]='',
[3203]='',
[3204]='',
[3205]='',
[3206]='',
[3207]='',
[3208]='',
[3209]='',
[3210]='',
[3211]='',
[3212]='',
[3213]='',
[3298]='',
[3299]='',
[3300]='',
[3301]='',
[3302]='',
[3303]='',
[3304]='',
[3305]='',
[3306]='',
[3307]='',
[3454]='',
[3455]='',
[3456]='',
[3601]='',
[3602]='',
[3603]='',
[3604]='',
[3605]='',
[3606]='',
[3607]='',
[3608]='',
[3609]='',
[3610]='',
[3611]='',
[3612]='',
[3613]='',
[3506]='',
[3507]='',
[3508]='',
[3509]='',
[3510]='',
[3511]='',
[3512]='',
[3513]='',
[3514]='',
[3515]='',
[3516]='',
[3517]='',
[3518]='',
[3519]='',
[3520]='',
[3521]='',
[3522]='',
[3523]='',
[3524]='',
[3525]='',
[3526]='',
[3527]='',
[3528]='',
[3529]='',
[3530]='',
[3531]='',
[3532]='',
[3533]='',
[3534]='',
[3535]='',
[3536]='',
[3537]='',
[3538]='',
[3539]='',
[3540]='',
[3541]='',
[3571]='',
[3572]='',
[3573]='',
[3574]='',
[3721]='',
[3722]='',
[3723]='',
[3724]='',
[3725]='',
[4051]='',
[4052]='',
[4053]='',
[4054]='',
[4055]='',
[4056]='',
[4057]='',
[4058]='',
[4059]='',
[4060]='',
[4061]='',
[4062]='',
[4063]='',
[4064]='',
[4065]='',
[4066]='',
[4067]='',
[4068]='',
[4069]='',
[4070]='',
[4071]='',
[4072]='',
[4073]='',
[4074]='',
[4075]='',
[4076]='',
[4077]='',
[4078]='',
[4101]='',
[4102]='',
[4103]='',
[4104]='',
[4105]='',
[4106]='',
[4107]='',
[4108]='',
[4109]='',
[4110]='',
[4111]='',
[4112]='',
[4113]='',
[4114]='',
[4115]='',
[4116]='',
[4117]='',
[4118]='',
[4119]='',
[4120]='',
[4121]='',
[4122]='',
[4123]='',
[4124]='',
[4125]='',
[4126]='',
[4127]='',
[4128]='',
[4129]='',
[4130]='',
[4131]='',
[4132]='',
[4133]='',
[4134]='',
[4135]='',
[4136]='',
[4137]='',
[4138]='',
[4139]='',
[4140]='',
[4141]='',
[4142]='',
[4143]='',
[4144]='',
[4145]='',
[4146]='',
[4147]='',
[4148]='',
[4149]='',
[4150]='',
[4151]='',
[4152]='',
[4153]='',
[4154]='',
[4155]=[[Lv. ]],
[4156]='',
[4157]='',
[4158]='',
[4200]='',
[4201]='',
[4202]='',
[4203]='',
[4204]='',
[4205]='',
[4206]='',
[4207]='',
[4208]='',
[4209]='',
[4210]='',
[4211]='',
[4212]='',
[4213]='',
[4214]='',
[4215]='',
[4216]='',
[4217]='',
[4218]='',
[4219]='',
[4220]='',
[4221]='',
[4222]='',
[4223]='',
[5000]='',
[5001]='',
[5002]='',
[5003]='',
[5004]='',
[5005]='',
[5006]='',
[5007]='',
[5008]='',
[5009]='',
[5010]='',
[5011]='',
[5012]='',
[5013]='',
[5014]='',
[5015]='',
[5016]='',
[5017]='',
[5018]='',
[5019]='',
[5020]='',
[5021]='',
[5022]='',
[5023]='',
[5024]='',
[5025]='',
[5026]='',
[5027]='',
[5028]='',
[5029]='',
[5030]='',
[5031]='',
[5032]='',
[5033]='',
[5034]='',
[5035]='',
[5036]='',
[5037]='',
[5038]='',
[5039]='',
[5040]='',
[5041]='',
[5042]='',
[5043]='',
[5044]='',
[5045]='',
[5046]='',
[5047]='',
[5048]='',
[5049]='',
[5050]='',
[5051]='',
[5052]='',
[5053]='',
[5054]='',
[5055]='',
[5056]='',
[5057]='',
[5058]='',
[5059]='',
[5060]='',
[5100]='',
[5101]='',
[5102]='',
[5103]='',
[5104]='',
[5105]='',
[5106]='',
[5107]='',
[5108]='',
[5109]='',
[5110]='',
[5111]='',
[5112]='',
[5113]='',
[5114]='',
[5200]='',
[5201]='',
[5202]='',
[5203]='',
[5204]='',
[5205]='',
[5206]='',
[5207]='',
[5208]='',
[5209]='',
[5210]='',
[5211]='',
[5212]='',
[5213]='',
[5214]='',
[5215]='',
[5216]='',
[5217]='',
[5301]='',
[5302]='',
[5303]='',
[5304]='',
[5305]='',
[5306]='',
[5307]='',
[5308]='',
[5309]='',
[5310]='',
[5311]='',
[5312]='',
[5313]='',
[5314]='',
[5315]='',
[5316]='',
[5320]='',
[5321]='',
[5322]='',
[5323]='',
[5324]='',
[5325]='',
[5326]='',
[5327]='',
[5328]='',
[5329]='',
[5330]='',
[5331]='',
[5332]='',
[5333]='',
[5334]='',
[5335]='',
[5336]='',
[5337]='',
[5338]='',
[5339]='',
[5340]='',
[5341]='',
[5342]='',
[5343]='',
[5344]='',
[5403]='',
[5451]='',
[5452]='',
[5453]='',
[5454]='',
[5500]='',
[5501]='',
[5502]='',
[5503]='',
[5504]='',
[5505]='',
[5506]='',
[5507]='',
[5508]='',
[5509]='',
[5510]='',
[5511]='',
[5512]='',
[5513]='',
[5514]='',
[5515]='',
[5683]='',
[5684]='',
[5685]='',
[5686]='',
[5625]='',
[5631]='',
[5632]='',
[5633]='',
[5634]='',
[5635]='',
[5636]='',
[5637]='',
[5638]='',
[5639]='',
[5640]='',
[5641]='',
[5642]='',
[5643]='',
[5644]='',
[5645]='',
[5646]='',
[5647]='',
[5648]='',
[5649]='',
[5650]='',
[5651]='',
[5652]='',
[5653]='',
[5654]='',
[5655]='',
[5656]='',
[5657]='',
[5658]='',
[5659]='',
[5660]='',
[5661]='',
[5662]='',
[5799]='',
[5801]='',
[5802]='',
[5803]='',
[5804]='',
[5805]='',
[5806]='',
[5807]='',
[5808]='',
[5809]='',
[5810]='',
[5811]='',
[5812]='',
[5813]='',
[5814]='',
[5815]='',
[5816]='',
[5817]='',
[5818]='',
[5819]='',
[5820]='',
[5821]='',
[5822]='',
[5823]='',
[5824]='',
[5825]='',
[5826]='',
[5827]='',
[5828]='',
[5829]='',
[5830]='',
[5831]='',
[5832]='',
[5833]='',
[5834]='',
[5835]='',
[5836]='',
[5837]='',
[5838]='',
[5901]='',
[5902]='',
[5903]='',
[5904]='',
[5905]='',
[5906]='',
[5907]='',
[5908]='',
[5909]='',
[5910]='',
[5911]='',
[5912]='',
[5913]='',
[5914]='',
[5915]='',
[5916]='',
[5917]='',
[5918]='',
[5919]='',
[6000]='',
[6001]='',
[6002]='',
[6003]='',
[6004]='',
[6005]='',
[6006]='',
[6007]='',
[6008]='',
[6009]='',
[6010]='',
[6011]='',
[6012]='',
[6013]='',
[6998]='',
[6999]='',
[7000]='',
[7001]='',
[7002]='',
[7003]='',
[7004]='',
[7005]='',
[7006]='',
[7007]='',
[7008]='',
[7009]='',
[7010]='',
[7011]='',
[7012]='',
[7013]='',
[7014]='',
[7015]='',
[7016]='',
[7017]='',
[7018]='',
[7019]='',
[7020]='',
[7021]='',
[7022]='',
[7023]='',
[7024]='',
[7025]='',
[7026]='',
[7027]='',
[7028]='',
[7029]='',
[7030]='',
[7031]='',
[7032]='',
[7033]='',
[7034]='',
[7035]='',
[7036]='',
[7037]='',
[7038]='',
[7039]='',
[7040]='',
[7041]='',
[7042]='',
[7043]='',
[7044]='',
[7045]='',
[7046]='',
[7047]='',
[7048]='',
[7049]='',
[7050]='',
[7051]='',
[7052]='',
[7053]='',
[7054]='',
[7055]='',
[7056]='',
[7057]='',
[7058]='',
[7059]='',
[7060]='',
[7061]='',
[7062]='',
[7063]='',
[7064]='',
[7065]='',
[7066]='',
[7067]='',
[7068]='',
[7069]='',
[7070]='',
[7071]='',
[7072]='',
[7073]='',
[7080]='',
[7081]='',
[7082]='',
[7083]='',
[7084]='',
[7085]='',
[7086]='',
[7087]='',
[7088]='',
[7089]='',
[7090]='',
[7091]='',
[7092]='',
[7093]='',
[7094]='',
[7095]='',
[7101]='',
[7102]='',
[7103]='',
[7104]='',
[7105]='',
[7106]='',
[7107]=[[إلى الأمام]],
[7108]='',
[7109]='',
[7110]='',
[7111]='',
[7112]='',
[7113]='',
[7114]='',
[7115]='',
[7116]='',
[7117]='',
[7118]='',
[7119]='',
[7120]='',
[7121]='',
[7122]='',
[7123]='',
[7124]='',
[7125]='',
[7126]='',
[7127]='',
[7128]='',
[7129]='',
[7130]='',
[7131]='',
[7132]='',
[7133]='',
[7134]='',
[7135]='',
[7136]='',
[7137]='',
[7138]='',
[7139]='',
[7140]='',
[7141]='',
[7142]='',
[7143]='',
[7144]='',
[7145]='',
[7146]='',
[7147]='',
[7148]='',
[7149]='',
[7150]='',
[7151]='',
[7152]='',
[7153]='',
[7154]='',
[7155]='',
[7156]='',
[7157]='',
[7158]='',
[7159]='',
[7160]='',
[7161]='',
[7162]='',
[7163]='',
[7164]='',
[7165]='',
[7166]='',
[7167]='',
[7168]='',
[7169]='',
[7170]='',
[7171]='',
[7172]='',
[7173]='',
[7174]='',
[7175]='',
[7176]='',
[7177]='',
[7178]='',
[7179]='',
[7180]='',
[7181]='',
[7182]='',
[7183]='',
[7184]='',
[7185]='',
[7186]='',
[7187]='',
[7188]='',
[7189]='',
[7190]='',
[7191]='',
[7192]='',
[7193]='',
[7194]='',
[7195]='',
[7196]='',
[7197]='',
[7198]='',
[7199]='',
[7200]='',
[7270]=[[تأثير التقوية]],
[7401]='',
[7402]='',
[7403]='',
[7404]='',
[7405]='',
[7406]='',
[7407]='',
[7408]='',
[7409]='',
[7410]='',
[7411]='',
[7412]='',
[7413]='',
[7414]='',
[7415]='',
[7416]='',
[7417]='',
[7418]='',
[7419]='',
[7420]='',
[7421]='',
[7422]='',
[7423]='',
[7424]='',
[7425]='',
[7426]='',
[7427]='',
[7428]='',
[7429]='',
[7430]='',
[7431]='',
[7432]=[[قلعتك حاليًا في حالة تقاطع بين الخوادم، ولا يُمكنك دخولها. يُرجى العودة إلى خادمك أولًا.]],
[7501]='',
[7502]='',
[7503]='',
[7504]='',
[7505]='',
[7506]='',
[7507]='',
[7508]='',
[7509]='',
[7601]='',
[7602]='',
[7603]='',
[7604]='',
[7605]='',
[7701]='',
[7702]='',
[7703]='',
[7704]='',
[7705]='',
[7706]='',
[7707]='',
[7708]='',
[7709]='',
[7710]='',
[7711]='',
[7712]='',
[7713]='',
[7714]='',
[7715]='',
[7716]='',
[7717]='',
[7718]='',
[7719]='',
[7720]='',
[7721]='',
[7722]='',
[7723]='',
[7724]='',
[7725]='',
[7726]='',
[7727]='',
[7728]='',
[7729]='',
[7730]='',
[7731]='',
[7732]='',
[7733]='',
[7734]='',
[7735]='',
[7736]='',
[7737]='',
[7738]='',
[7739]='',
[7740]='',
[7741]='',
[7742]='',
[7743]='',
[7744]='',
[7745]='',
[7746]='',
[7747]='',
[7748]='',
[7749]='',
[7750]='',
[7751]='',
[7752]='',
[7753]='',
[7754]='',
[7755]='',
[7756]='',
[7757]='',
[7758]='',
[7759]='',
[7760]='',
[7761]='',
[7762]='',
[7763]='',
[7764]='',
[7765]='',
[7766]='',
[7767]='',
[7768]='',
[7769]='',
[7770]='',
[7771]='',
[7772]='',
[7773]='',
[7774]='',
[7775]='',
[7776]='',
[7777]='',
[7778]='',
[7779]='',
[7780]='',
[7781]='',
[7801]='',
[7802]='',
[7803]='',
[7804]='',
[7805]='',
[7806]='',
[7807]='',
[7808]='',
[7809]='',
[7810]='',
[7811]='',
[7812]='',
[7813]='',
[7814]='',
[7815]='',
[7816]='',
[7817]='',
[7818]='',
[7901]='',
[7902]='',
[7903]='',
[7904]='',
[7905]='',
[7906]='',
[7907]='',
[7908]='',
[7909]='',
[7910]='',
[7911]='',
[7912]='',
[7913]='',
[7914]='',
[7915]='',
[7916]='',
[7917]='',
[8001]='',
[8002]='',
[8003]='',
[8004]='',
[8005]='',
[8006]='',
[8007]='',
[8008]='',
[8009]='',
[8010]='',
[8011]='',
[8012]='',
[8013]='',
[8014]='',
[8015]='',
[8016]='',
[8017]='',
[8018]='',
[8019]='',
[8020]='',
[8021]='',
[8022]='',
[8023]='',
[8024]='',
[8025]='',
[8026]='',
[8027]='',
[8028]='',
[8029]='',
[8030]='',
[8031]='',
[8032]='',
[8033]='',
[8034]='',
[8035]='',
[8036]='',
[8037]='',
[8038]='',
[8039]='',
[8040]='',
[8041]='',
[8042]='',
[8043]='',
[8044]='',
[8045]='',
[8046]='',
[8047]='',
[8048]='',
[8049]='',
[8050]='',
[8051]='',
[8052]='',
[8053]=[[تحتاج إلى %s أبطال بجودة %s أو أعلى للتسجيل]],
[8054]='',
[8055]='',
[8056]='',
[8057]='',
[8058]='',
[8059]='',
[8060]='',
[8061]='',
[8062]='',
[8063]='',
[8064]='',
[8065]='',
[8066]='',
[8067]='',
[8068]='',
[8069]='',
[8070]='',
[8071]='',
[8072]='',
[8073]='',
[8074]='',
[8075]='',
[8076]='',
[8077]='',
[8078]='',
[8079]='',
[8080]='',
[8081]='',
[8082]='',
[8083]='',
[8084]='',
[8085]='',
[8086]='',
[8087]='',
[8088]='',
[8089]='',
[8090]='',
[8091]='',
[8092]='',
[8093]='',
[8094]='',
[8095]='',
[8096]='',
[8097]='',
[8098]='',
[8101]='',
[8102]='',
[8103]='',
[8104]='',
[8105]='',
[8106]='',
[8107]='',
[8108]='',
[8109]='',
[8110]='',
[8200]='',
[8201]='',
[8202]='',
[8203]='',
[8204]='',
[8205]='',
[8206]='',
[8207]='',
[8208]='',
[8209]='',
[8210]='',
[8211]='',
[8212]='',
[8213]='',
[8214]='',
[8215]='',
[8216]='',
[8217]='',
[8218]='',
[8219]='',
[8220]='',
[8221]='',
[8222]='',
[8223]='',
[8224]='',
[8225]='',
[8226]='',
[8227]='',
[8228]='',
[8229]='',
[8230]='',
[8231]='',
[8232]='',
[8233]='',
[8234]='',
[8235]='',
[8236]='',
[8237]='',
[8238]='',
[8239]='',
[8240]='',
[8241]='',
[8242]='',
[8243]='',
[8244]='',
[8245]='',
[8246]='',
[8247]='',
[8248]='',
[8249]='',
[8250]='',
[8251]='',
[8252]='',
[8253]='',
[8254]='',
[8255]='',
[8256]='',
[8257]='',
[8258]='',
[8259]='',
[8260]='',
[8261]='',
[8262]='',
[8263]='',
[8264]='',
[8265]='',
[8266]='',
[8267]='',
[8268]='',
[8269]='',
[8270]='',
[8271]='',
[8272]='',
[8273]='',
[8274]='',
[8275]='',
[8276]='',
[8277]='',
[8278]='',
[8279]='',
[8280]='',
[8281]='',
[8282]='',
[8283]='',
[8284]='',
[8285]='',
[8286]='',
[8287]='',
[8288]='',
[8289]='',
[8290]='',
[8291]='',
[8292]='',
[8293]='',
[8294]='',
[8295]='',
[8296]='',
[8297]='',
[8298]='',
[8299]='',
[8300]='',
[8301]='',
[8302]='',
[8303]='',
[8304]='',
[8305]='',
[8306]='',
[8307]='',
[8308]='',
[8309]='',
[8310]='',
[8311]='',
[8312]='',
[8313]='',
[8314]='',
[8315]='',
[8316]='',
[8317]='',
[8318]='',
[8319]='',
[8320]='',
[8400]='',
[8401]='',
[8402]='',
[8403]='',
[8404]='',
[8405]='',
[8406]='',
[8407]='',
[8408]='',
[8409]='',
[8410]='',
[8411]='',
[8412]='',
[8413]='',
[8414]='',
[8415]='',
[8416]='',
[8417]='',
[8418]='',
[8419]='',
[8420]='',
[8421]='',
[8422]='',
[8423]='',
[8424]='',
[8425]='',
[8426]='',
[8427]='',
[8428]='',
[8429]='',
[8430]='',
[8431]='',
[8432]='',
[8433]='',
[8434]='',
[8435]='',
[8436]='',
[8437]='',
[8438]='',
[8439]='',
[8440]='',
[8441]='',
[8442]='',
[8443]='',
[8444]='',
[8445]='',
[8446]='',
[8447]='',
[8448]='',
[8449]='',
[8450]='',
[8451]='',
[8452]='',
[8453]='',
[8454]='',
[8455]='',
[8456]='',
[8457]='',
[8458]='',
[8459]='',
[8460]='',
[8461]='',
[8462]='',
[8463]='',
[8464]='',
[8465]='',
[8466]='',
[8467]='',
[8468]='',
[8469]='',
[8470]='',
[8471]='',
[8472]='',
[8473]='',
[8474]='',
[8475]='',
[8476]='',
[8477]='',
[8478]='',
[8479]='',
[8480]='',
[8481]='',
[8482]='',
[8483]='',
[8484]='',
[8485]='',
[8486]='',
[8487]='',
[8488]='',
[8489]='',
[8490]='',
[8491]='',
[8492]='',
[8493]='',
[8494]='',
[8495]='',
[8496]='',
[8497]='',
[8498]='',
[8499]='',
[8500]='',
[8501]='',
[8502]='',
[8503]='',
[8504]='',
[8505]='',
[8506]='',
[8507]='',
[8508]='',
[8509]='',
[8510]='',
[8511]='',
[8512]='',
[8513]='',
[8514]='',
[8515]='',
[8516]='',
[8517]='',
[8518]='',
[8519]='',
[8520]='',
[8521]='',
[8522]='',
[8523]='',
[8524]='',
[8525]='',
[8526]='',
[8527]='',
[8528]='',
[8529]='',
[8530]='',
[8531]='',
[8532]='',
[8533]='',
[8534]='',
[8535]='',
[8536]='',
[8537]='',
[8538]='',
[8539]='',
[8540]='',
[8541]='',
[8542]='',
[8543]='',
[8544]='',
[8545]='',
[8546]='',
[8547]='',
[8548]='',
[8549]='',
[8550]='',
[8551]='',
[8552]='',
[8553]='',
[8554]='',
[8555]='',
[8556]='',
[8557]='',
[8558]='',
[8559]='',
[8560]='',
[8561]='',
[8562]='',
[8563]='',
[8564]='',
[8565]='',
[8566]='',
[8567]='',
[8568]='',
[8569]='',
[8570]='',
[8571]='',
[8572]='',
[8573]='',
[8574]='',
[8575]='',
[8576]='',
[8577]='',
[8578]='',
[8579]='',
[8580]='',
[8581]='',
[8582]='',
[8583]='',
[8584]='',
[8585]='',
[8586]='',
[8587]='',
[8588]='',
[8589]='',
[8590]='',
[8591]='',
[8592]='',
[8593]='',
[8594]='',
[8595]='',
[8596]='',
[8597]='',
[8598]='',
[8599]='',
[8600]='',
[8601]='',
[8602]='',
[8603]='',
[8604]='',
[8605]='',
[8606]='',
[8607]='',
[8608]='',
[8609]='',
[8610]='',
[8611]='',
[8612]='',
[8613]='',
[8614]='',
[8615]='',
[8616]='',
[8617]='',
[8618]='',
[8619]='',
[8620]='',
[8621]='',
[8622]='',
[8623]='',
[8624]='',
[8625]='',
[8626]='',
[8627]='',
[8628]='',
[8629]='',
[8630]='',
[8631]='',
[8632]='',
[8633]='',
[8634]='',
[8635]='',
[8636]='',
[8637]='',
[8638]='',
[8639]='',
[8640]='',
[8641]='',
[8642]='',
[8643]='',
[8644]='',
[8645]='',
[8646]='',
[8647]='',
[8648]='',
[8649]='',
[8650]='',
[8651]='',
[8652]='',
[8653]='',
[8654]='',
[8655]='',
[8656]='',
[8657]='',
[8658]='',
[8659]='',
[8660]='',
[8661]='',
[8662]='',
[8663]='',
[8664]='',
[8665]='',
[8666]='',
[8667]='',
[8668]='',
[8669]='',
[8670]='',
[8671]='',
[8672]='',
[8673]='',
[8674]='',
[8675]='',
[8676]='',
[8677]='',
[8701]='',
[8702]='',
[8703]='',
[8704]='',
[8705]='',
[8706]='',
[8707]='',
[8708]='',
[8798]='',
[8799]='',
[8800]='',
[8801]='',
[8802]='',
[8803]='',
[8804]='',
[8805]='',
[8806]='',
[8807]='',
[8808]='',
[8809]='',
[8810]='',
[8811]='',
[8812]='',
[8813]='',
[8814]='',
[8815]='',
[8816]='',
[8817]='',
[8818]='',
[8819]='',
[8820]='',
[8821]='',
[8822]='',
[8823]='',
[8824]='',
[8825]='',
[8826]='',
[8827]='',
[8828]='',
[8829]='',
[8830]='',
[8831]='',
[8832]='',
[8833]='',
[8834]='',
[8835]='',
[8836]='',
[8837]='',
[8838]='',
[8839]='',
[8840]='',
[8841]='',
[8842]='',
[8843]='',
[8844]='',
[8845]='',
[8846]='',
[8847]='',
[8848]='',
[8849]='',
[8850]='',
[8901]='',
[8902]='',
[8903]='',
[9000]='',
[9001]='',
[9002]='',
[9003]='',
[9004]='',
[9005]='',
[9006]='',
[9007]='',
[9008]='',
[9009]='',
[9010]='',
[9011]='',
[9012]='',
[9013]='',
[9014]='',
[9015]='',
[9016]='',
[9017]='',
[9018]='',
[9019]='',
[9020]='',
[9021]='',
[9022]='',
[9023]='',
[9024]='',
[9025]='',
[9026]='',
[9027]='',
[9028]='',
[9029]='',
[9030]='',
[9031]='',
[9032]='',
[9033]='',
[9034]='',
[9035]='',
[9036]='',
[9037]='',
[9038]='',
[9039]='',
[9040]='',
[9041]='',
[9042]='',
[9043]='',
[9044]='',
[9045]='',
[9046]='',
[9047]='',
[9048]='',
[9049]='',
[9050]='',
[9051]='',
[9052]='',
[9061]='',
[9062]='',
[9063]='',
[9064]='',
[9065]='',
[9066]='',
[9067]='',
[9068]='',
[9069]='',
[9070]='',
[9071]='',
[9072]='',
[9073]='',
[9074]='',
[9075]='',
[9076]='',
[9077]='',
[9078]='',
[9079]='',
[9080]='',
[9081]='',
[9082]='',
[9083]='',
[9084]='',
[9085]='',
[9086]='',
[9087]='',
[9088]=[[ - ]],
[9089]='',
[9090]='',
[9091]='',
[9092]='',
[9093]='',
[9094]='',
[9095]='',
[9096]='',
[9097]='',
[9098]='',
[9099]='',
[9100]='',
[9101]='',
[9102]='',
[9103]='',
[9104]='',
[9105]='',
[9106]='',
[9107]='',
[9108]='',
[9109]='',
[9110]='',
[9111]='',
[9112]='',
[9113]='',
[9114]='',
[9115]='',
[9116]='',
[9117]='',
[9118]='',
[9119]='',
[9120]='',
[9121]='',
[9122]='',
[9123]='',
[9124]='',
[9125]='',
[9126]='',
[9127]='',
[9128]='',
[9129]='',
[9130]='',
[9131]='',
[9132]='',
[9133]='',
[9134]='',
[9135]='',
[9136]='',
[9137]='',
[9138]='',
[9139]='',
[9140]='',
[9141]='',
[9142]='',
[9143]='',
[9144]='',
[9145]='',
[9146]='',
[9147]='',
[9148]='',
[9149]='',
[9150]='',
[9151]='',
[9152]='',
[9153]=[[إجمالي ما تم الحصول عليه %d من الماسات البلورية الفائقة
+%d من الخبرة النبيلة]],
[9154]='',
[9155]='',
[9156]='',
[9157]='',
[9158]='',
[9159]='',
[9160]='',
[9161]='',
[9162]='',
[9163]='',
[9164]='',
[9165]='',
[9166]='',
[9167]='',
[9168]='',
[9169]='',
[9170]='',
[9171]='',
[9173]='',
[9172]='',
[9176]='',
[9177]='',
[9178]='',
[9179]='',
[9180]='',
[9181]='',
[9182]='',
[9183]='',
[9184]='',
[9185]='',
[9186]='',
[9187]='',
[9188]='',
[9189]='',
[9190]='',
[9191]='',
[9192]='',
[9193]='',
[9194]='',
[9195]='',
[9196]='',
[9197]='',
[9198]='',
[9199]='',
[9200]='',
[9201]='',
[9202]='',
[9203]='',
[9204]='',
[9251]='',
[9261]='',
[9262]='',
[9263]='',
[9264]='',
[9265]='',
[9266]='',
[9267]='',
[9270]='',
[9271]='',
[9272]='',
[9273]='',
[9301]='',
[9302]='',
[9303]='',
[9304]='',
[9305]='',
[9306]='',
[9307]='',
[9308]='',
[9309]='',
[9310]='',
[9311]='',
[9312]='',
[9313]='',
[9314]='',
[9315]='',
[9316]='',
[9317]='',
[9318]='',
[9319]='',
[9320]='',
[9321]='',
[9322]='',
[9323]='',
[9324]='',
[9325]='',
[9326]='',
[9327]='',
[9328]='',
[9329]='',
[9330]='',
[9331]='',
[9332]='',
[9333]='',
[9334]='',
[9335]='',
[9336]='',
[9337]='',
[9338]='',
[9339]='',
[9340]='',
[9341]='',
[9342]='',
[9343]='',
[9344]='',
[9345]='',
[9346]='',
[9347]='',
[9348]='',
[9349]='',
[9350]='',
[9351]='',
[9352]='',
[9353]='',
[9354]='',
[9355]='',
[9356]='',
[9357]='',
[9358]='',
[9359]='',
[9360]='',
[9361]='',
[9362]='',
[9363]='',
[9364]='',
[9365]='',
[9366]='',
[9367]='',
[9368]='',
[9369]='',
[9370]='',
[9371]='',
[9372]='',
[9373]='',
[9374]='',
[9375]='',
[9376]='',
[9377]='',
[9378]='',
[9379]='',
[9380]='',
[9381]='',
[9382]='',
[9383]='',
[9384]='',
[9385]='',
[9386]='',
[9387]='',
[9388]='',
[9389]='',
[9390]='',
[9391]='',
[9392]='',
[9393]='',
[9394]='',
[9395]='',
[9396]='',
[9397]='',
[9398]='',
[9399]='',
[9400]='',
[9401]='',
[9402]='',
[9404]='',
[9405]='',
[9406]='',
[9407]='',
[9408]='',
[9409]='',
[9410]='',
[9411]='',
[9412]='',
[9413]='',
[9414]='',
[9415]='',
[9416]='',
[9417]='',
[9418]='',
[9419]='',
[9420]='',
[9421]='',
[9422]='',
[9423]='',
[9424]='',
[9425]='',
[9426]='',
[9427]='',
[9428]='',
[9429]='',
[9430]='',
[9431]='',
[9432]='',
[9433]='',
[9450]='',
[9451]='',
[9452]='',
[9453]='',
[9454]='',
[9455]='',
[9456]='',
[9457]='',
[9458]='',
[9459]='',
[9460]='',
[9461]='',
[9462]='',
[9463]='',
[9464]='',
[9465]='',
[9466]='',
[9467]='',
[9468]='',
[9469]='',
[9470]='',
[9471]='',
[9472]='',
[9473]='',
[9474]='',
[9475]='',
[9476]='',
[9477]='',
[9478]='',
[9479]='',
[9480]='',
[9481]='',
[9482]='',
[9483]='',
[9484]='',
[9485]='',
[9486]='',
[9487]='',
[9488]='',
[9489]='',
[9490]='',
[9491]='',
[9492]='',
[9493]='',
[9494]='',
[9495]='',
[9496]='',
[9497]='',
[9498]='',
[9500]='',
[9510]='',
[9520]='',
[9521]='',
[9522]='',
[9523]='',
[9524]='',
[9525]='',
[9526]='',
[9527]='',
[9528]='',
[9529]='',
[9530]='',
[9531]='',
[9532]='',
[9533]='',
[9534]='',
[9535]='',
[9536]='',
[9537]='',
[9538]='',
[9539]='',
[9540]='',
[9541]='',
[9542]='',
[9543]='',
[9544]='',
[9545]='',
[9546]='',
[9547]='',
[9548]='',
[9549]='',
[9550]='',
[9551]='',
[9552]='',
[9553]='',
[9554]='',
[9555]='',
[9556]='',
[9557]='',
[9558]='',
[9601]='',
[9602]='',
[9603]='',
[9604]='',
[9605]='',
[9606]='',
[9607]='',
[9608]='',
[9609]='',
[9610]='',
[9611]='',
[9701]='',
[9702]='',
[9703]='',
[9704]='',
[9705]='',
[9706]='',
[9707]='',
[9708]='',
[9709]='',
[9710]='',
[9711]='',
[9712]='',
[9713]='',
[9714]='',
[9715]='',
[9716]='',
[9717]='',
[9718]='',
[9719]='',
[9720]='',
[9721]='',
[9722]='',
[9723]='',
[9724]='',
[9725]='',
[9726]='',
[9727]='',
[9728]='',
[9729]='',
[9730]='',
[9731]='',
[9732]='',
[9733]='',
[9734]='',
[9735]='',
[9736]='',
[9737]='',
[9738]='',
[9739]='',
[9740]='',
[9741]='',
[9742]='',
[9743]='',
[9744]='',
[9745]='',
[9746]='',
[9747]='',
[9748]='',
[9749]='',
[9750]='',
[9751]='',
[9752]='',
[9753]='',
[9754]='',
[9755]='',
[9756]='',
[9757]='',
[9758]=[[العنوان: حياة +٥٪ هجوم +٥٪
إطار الصورة الرمزية: حياة +١٠٪ هجوم +١٠٪]],
[9759]=[[إطار الصورة: الحياة +8% الهجوم +8%]],
[9760]=[[إطار الصورة: الحياة +6% الهجوم +6%]],
[9761]='',
[9762]='',
[9763]='',
[9764]='',
[9765]='',
[9766]='',
[9780]='',
[9781]='',
[9782]='',
[9783]='',
[9784]='',
[9785]='',
[9786]='',
[9787]='',
[9788]='',
[9789]='',
[9790]='',
[9791]='',
[9792]='',
[9793]='',
[9794]='',
[9795]='',
[9796]='',
[9797]='',
[9798]='',
[9799]='',
[9800]='',
[9801]='',
[9802]='',
[9803]='',
[9804]='',
[9805]='',
[9806]='',
[9807]='',
[9808]='',
[9809]='',
[9810]='',
[9811]='',
[9812]='',
[9813]='',
[9814]='',
[9815]='',
[9816]='',
[9817]='',
[9818]='',
[9819]='',
[9820]='',
[9821]='',
[9822]='',
[9823]='',
[9901]='',
[9902]='',
[9903]='',
[9904]='',
[9905]='',
[9906]='',
[9907]='',
[9908]='',
[9999]='',
[15001]='',
[15002]='',
[15003]='',
[15004]='',
[15005]='',
[15006]='',
[15007]='',
[15008]='',
[15009]='',
[15010]='',
[15011]='',
[15012]='',
[15013]='',
[15014]='',
[15015]='',
[15016]='',
[15017]='',
[15018]='',
[15019]='',
[15100]='',
[15101]='',
[15102]='',
[15103]='',
[15104]='',
[15105]='',
[15106]='',
[15107]='',
[15108]='',
[15109]='',
[15110]='',
[15111]='',
[15112]='',
[15113]='',
[15114]='',
[15115]='',
[15116]='',
[15117]='',
[15118]='',
[15119]='',
[15120]='',
[15121]='',
[15122]='',
[15123]='',
[15124]='',
[15125]='',
[15126]='',
[15127]='',
[15128]='',
[15129]='',
[15200]='',
[15201]='',
[15202]='',
[15203]='',
[15204]='',
[15205]='',
[15206]='',
[15207]='',
[15208]='',
[15209]='',
[15210]='',
[15211]='',
[15212]='',
[15213]='',
[15214]='',
[15215]='',
[15216]='',
[15217]='',
[15218]='',
[15219]='',
[15220]='',
[15221]='',
[15222]='',
[15223]='',
[15224]='',
[15250]='',
[15251]='',
[15252]='',
[15253]='',
[15254]='',
[15255]='',
[15256]='',
[15257]='',
[15258]='',
[15259]='',
[15260]='',
[15261]='',
[15262]='',
[15263]='',
[15264]='',
[15265]='',
[15266]='',
[15267]='',
[15268]='',
[15269]='',
[15270]='',
[15271]='',
[15272]='',
[15273]='',
[15274]='',
[15275]='',
[15276]='',
[15277]='',
[15278]=[[انقر لفتح صفحة الفيسبوك الرئيسية]],
[15279]='',
[15280]='',
[15281]='',
[15282]='',
[15283]='',
[15284]='',
[15285]='',
[15286]='',
[15287]='',
[15288]='',
[15289]='',
[15290]='',
[15291]='',
[15292]='',
[15293]='',
[15294]='',
[15295]='',
[15296]='',
[15297]='',
[15298]=[[خدمة العملاء الرسمية]],
[15299]=[[حساب الموظفين الرسمي، لا يمكنه عرض المعلومات الشخصية]],
[15300]='',
[15301]='',
[15302]='',
[15303]='',
[15304]='',
[15305]='',
[15306]='',
[15307]='',
[15308]='',
[15309]='',
[15310]='',
[15311]='',
[15312]='',
[15313]='',
[15314]='',
[15315]='',
[15316]='',
[15317]='',
[15318]='',
[15319]='',
[15320]='',
[15321]='',
[15322]='',
[15323]='',
[15324]='',
[15325]='',
[15326]='',
[15327]='',
[15328]='',
[15329]='',
[15330]='',
[15331]='',
[15332]='',
[15333]='',
[15334]='',
[15335]='',
[15336]='',
[15337]='',
[15338]='',
[15339]='',
[15340]='',
[15341]='',
[15342]='',
[15343]='',
[15344]='',
[15345]='',
[15346]='',
[15347]='',
[15348]='',
[15349]='',
[15350]='',
[15351]='',
[15352]='',
[15353]='',
[15354]='',
[15355]='',
[15356]='',
[15357]='',
[15358]='',
[15359]='',
[15360]='',
[15361]='',
[15362]='',
[15363]='',
[15364]='',
[15365]='',
[15366]='',
[15367]='',
[15368]='',
[15369]='',
[15370]='',
[15371]='',
[15372]='',
[15373]='',
[15374]='',
[15375]='',
[15376]='',
[15377]='',
[15378]='',
[15379]='',
[15380]='',
[15381]='',
[15382]='',
[15383]='',
[15384]='',
[15385]='',
[15386]='',
[15387]='',
[15388]='',
[15389]='',
[15390]='',
[15428]='',
[15429]='',
[15430]='',
[15431]='',
[15432]='',
[15433]='',
[15434]='',
[15435]='',
[15436]='',
[15437]='',
[15438]='',
[15500]='',
[15501]='',
[15502]='',
[15503]='',
[15504]='',
[15505]='',
[15506]='',
[15507]='',
[15508]='',
[15509]='',
[15510]='',
[15511]='',
[15512]='',
[15513]='',
[15514]='',
[15515]='',
[15516]='',
[15517]='',
[15518]='',
[15519]='',
[15520]='',
[15521]='',
[15522]='',
[15523]='',
[15524]='',
[15525]='',
[15526]='',
[15527]='',
[15528]='',
[15529]='',
[15530]='',
[15531]='',
[15532]='',
[15533]='',
[15534]='',
[15535]='',
[15536]='',
[15537]='',
[15538]='',
[15539]='',
[15540]='',
[15541]='',
[15542]='',
[15543]='',
[15544]='',
[15545]='',
[15546]='',
[15547]='',
[15548]='',
[15549]='',
[15550]='',
[15551]='',
[15552]='',
[15553]='',
[15554]='',
[15555]='',
[15601]='',
[15602]='',
[15603]='',
[15604]='',
[15605]='',
[15606]='',
[15607]='',
[15608]='',
[15609]='',
[15610]='',
[15611]='',
[15612]='',
[15613]='',
[15614]='',
[15615]='',
[15616]='',
[15617]='',
[15618]='',
[15619]='',
[15620]='',
[15621]='',
[15622]='',
[15623]='',
[15624]='',
[15625]='',
[15630]='',
[15631]='',
[15632]='',
[15633]='',
[15634]='',
[15635]='',
[15636]='',
[15637]='',
[15638]='',
[15639]='',
[15640]='',
[15641]='',
[15642]='',
[15643]='',
[15644]='',
[15645]='',
[15646]='',
[15647]='',
[15648]='',
[15649]='',
[15650]='',
[15651]='',
[15652]='',
[15653]='',
[15654]='',
[15655]='',
[15656]='',
[15660]='',
[15661]='',
[15662]='',
[15663]='',
[15664]='',
[15665]='',
[15666]='',
[15667]='',
[15668]='',
[15669]='',
[15670]='',
[15671]='',
[15672]='',
[15673]='',
[15674]='',
[15675]='',
[15690]='',
[15691]='',
[15692]='',
[15693]='',
[15694]='',
[15695]='',
[15696]='',
[15697]='',
[15698]='',
[15699]='',
[15795]='',
[15796]='',
[15797]='',
[15798]='',
[15799]='',
[15800]='',
[15801]='',
[15802]='',
[15803]='',
[15804]='',
[15805]='',
[15806]='',
[15807]='',
[15808]='',
[15809]='',
[15810]='',
[15811]='',
[15812]='',
[15813]='',
[15814]='',
[15815]='',
[15816]='',
[15817]='',
[15818]='',
[15819]='',
[15820]='',
[15821]='',
[15822]='',
[15823]='',
[15824]='',
[15825]='',
[15826]='',
[15827]='',
[15828]='',
[15829]='',
[15830]='',
[15831]='',
[15832]='',
[15833]='',
[15834]='',
[15835]='',
[15836]='',
[15837]='',
[15838]='',
[15839]='',
[15840]='',
[15841]='',
[15842]='',
[15843]='',
[15844]='',
[15845]='',
[15846]='',
[15847]='',
[15848]='',
[15849]='',
[15850]='',
[15851]='',
[15852]='',
[15853]='',
[15854]='',
[15855]='',
[15856]='',
[15857]='',
[15858]='',
[15860]='',
[15861]='',
[15868]='',
[15869]='',
[15870]='',
[15872]='',
[15873]='',
[15874]='',
[15875]='',
[15876]='',
[15877]='',
[15878]='',
[15879]='',
[15880]='',
[15881]='',
[15882]='',
[15883]='',
[15884]='',
[15885]='',
[15886]='',
[15887]='',
[15888]='',
[15889]='',
[15890]='',
[15891]='',
[15892]='',
[15893]='',
[15894]='',
[15895]='',
[15896]='',
[15897]='',
[15898]='',
[15899]='',
[15900]='',
[15901]='',
[15902]='',
[15903]='',
[15904]='',
[15905]='',
[15906]='',
[15907]='',
[15908]='',
[15909]='',
[15910]='',
[15911]='',
[15912]='',
[15913]='',
[15914]='',
[15915]='',
[15916]='',
[15917]='',
[15918]='',
[15919]='',
[15920]='',
[15921]='',
[15922]='',
[15923]='',
[15924]='',
[15925]='',
[15926]='',
[15927]='',
[15928]='',
[15929]='',
[15930]='',
[15931]='',
[15932]='',
[15933]='',
[15934]='',
[15935]='',
[15936]='',
[15937]='',
[15938]='',
[15939]='',
[15940]='',
[15941]='',
[15942]='',
[15943]='',
[15944]='',
[15945]='',
[15946]='',
[15947]='',
[15948]='',
[15949]='',
[15950]='',
[15951]='',
[15952]='',
[15953]='',
[15954]='',
[15955]='',
[15956]='',
[15957]='',
[15958]='',
[15959]='',
[15960]='',
[15961]='',
[15962]='',
[15963]='',
[15964]='',
[15965]='',
[15966]='',
[15967]='',
[15968]='',
[15969]='',
[15970]='',
[15971]='',
[15972]='',
[15973]='',
[15974]='',
[15975]='',
[15976]='',
[15977]='',
[15978]='',
[15979]='',
[15980]='',
[15981]='',
[15982]='',
[15983]='',
[15984]='',
[15985]='',
[15986]='',
[15987]='',
[15988]='',
[15989]='',
[15990]='',
[15991]='',
[15992]='',
[15993]='',
[15994]='',
[15995]='',
[15996]='',
[15997]='',
[15998]='',
[15999]='',
[16000]='',
[16001]='',
[16002]='',
[16003]='',
[16004]='',
[16005]='',
[16006]='',
[16007]='',
[16008]='',
[16009]='',
[16010]='',
[16011]='',
[16012]='',
[16013]='',
[16046]='',
[16047]='',
[16048]='',
[16049]='',
[16050]='',
[16051]='',
[16052]='',
[16053]='',
[16054]='',
[16055]='',
[16056]='',
[16057]='',
[16058]='',
[16059]='',
[16060]='',
[16061]='',
[16062]='',
[16063]='',
[16064]='',
[16065]='',
[16066]='',
[16067]='',
[16068]='',
[16069]='',
[16070]='',
[16071]='',
[16072]='',
[16073]='',
[16074]='',
[16075]='',
[16076]='',
[16077]='',
[16078]='',
[16079]='',
[16080]='',
[16081]='',
[16082]='',
[16083]='',
[16084]='',
[16085]='',
[16086]='',
[16087]='',
[16088]='',
[16089]='',
[16090]='',
[16091]='',
[16092]='',
[16093]='',
[16094]='',
[16095]='',
[16096]='',
[16301]='',
[16302]='',
[16303]='',
[16304]='',
[16305]='',
[16306]='',
[16307]='',
[16308]='',
[16309]='',
[16310]='',
[16311]='',
[16312]='',
[16313]='',
[16314]='',
[16315]='',
[16316]='',
[16317]='',
[16318]='',
[16319]='',
[16320]='',
[16350]='',
[16351]='',
[16352]='',
[16353]='',
[16354]='',
[16355]='',
[16356]='',
[16357]='',
[16358]='',
[16359]='',
[16360]='',
[16361]='',
[16362]='',
[16363]='',
[16364]='',
[16365]='',
[16366]='',
[16367]='',
[16368]='',
[16369]='',
[16370]='',
[16371]='',
[16372]='',
[16373]='',
[16374]='',
[16375]='',
[16376]='',
[16377]='',
[16378]='',
[16379]='',
[16380]='',
[16381]='',
[16382]='',
[16383]='',
[16384]='',
[16385]='',
[16386]='',
[16387]='',
[16388]='',
[16401]='',
[16402]='',
[16403]='',
[16404]='',
[16405]='',
[16406]='',
[16407]='',
[16408]='',
[16409]='',
[16410]='',
[16411]='',
[16412]='',
[16413]='',
[16414]='',
[16415]='',
[16416]='',
[16417]='',
[16418]='',
[16419]='',
[16420]='',
[16421]='',
[16422]='',
[16423]='',
[16424]='',
[16425]='',
[16426]='',
[16427]='',
[16428]='',
[16429]='',
[16430]='',
[16431]='',
[16432]='',
[16433]='',
[16434]='',
[16435]='',
[16436]='',
[16437]='',
[16438]='',
[16439]='',
[16440]='',
[16441]='',
[16442]='',
[16443]='',
[16444]='',
[16445]='',
[16446]='',
[16447]='',
[16448]='',
[16449]='',
[16450]='',
[16451]='',
[16452]='',
[16453]='',
[16454]='',
[16455]='',
[16456]='',
[16457]='',
[16458]='',
[16459]='',
[16460]='',
[16461]='',
[16462]='',
[16463]='',
[16464]='',
[16465]='',
[16466]='',
[16467]='',
[16468]='',
[16469]='',
[16470]='',
[16471]='',
[16472]='',
[16473]='',
[16474]='',
[16475]='',
[16476]='',
[16477]='',
[16478]='',
[16479]='',
[16480]='',
[16481]='',
[16482]='',
[16483]='',
[16484]='',
[16485]='',
[16486]='',
[16487]='',
[16488]='',
[16489]='',
[16490]='',
[16491]='',
[16492]='',
[16493]='',
[16494]='',
[16495]='',
[16496]='',
[16497]='',
[16498]='',
[16499]='',
[16700]='',
[16701]='',
[16702]='',
[16703]='',
[16704]='',
[16705]='',
[16706]='',
[16707]='',
[16708]='',
[16709]='',
[16710]='',
[16711]='',
[16712]='',
[16713]='',
[16714]='',
[16715]='',
[16716]='',
[16717]='',
[16718]='',
[16719]='',
[16720]='',
[16721]='',
[16750]='',
[16751]='',
[16752]='',
[16753]='',
[16754]='',
[16755]='',
[16756]='',
[16757]='',
[16758]='',
[16759]='',
[16760]='',
[16761]='',
[16762]='',
[16763]='',
[16764]='',
[16765]='',
[16766]='',
[16767]='',
[16768]='',
[16769]='',
[16770]='',
[16771]='',
[16772]='',
[16773]='',
[16774]='',
[16788]='',
[16789]='',
[16790]='',
[16791]='',
[16792]='',
[16793]='',
[16794]='',
[16795]='',
[17500]='',
[17501]='',
[17502]='',
[17503]='',
[17504]='',
[17505]='',
[17506]='',
[17507]='',
[17508]='',
[17509]='',
[17510]='',
[17511]='',
[17512]='',
[17513]='',
[17514]='',
[17515]='',
[17516]='',
[17517]='',
[17526]='',
[17527]='',
[17599]='',
[18201]='',
[18202]='',
[18203]='',
[18204]='',
[18205]='',
[18206]='',
[18207]='',
[18208]='',
[18209]='',
[18210]='',
[18211]='',
[18212]='',
[18213]='',
[18214]='',
[18215]='',
[18216]='',
[18217]='',
[18218]='',
[18219]='',
[18220]='',
[18221]='',
[18222]='',
[18223]='',
[18224]='',
[18225]='',
[18226]='',
[18240]='',
[18241]='',
[18242]='',
[18243]='',
[18244]='',
[18245]='',
[18246]='',
[18247]='',
[18248]='',
[18249]='',
[18250]='',
[18251]='',
[18252]='',
[18253]='',
[18254]='',
[18255]='',
[18256]='',
[18257]='',
[18258]='',
[18285]='',
[18286]='',
[18287]='',
[18288]='',
[18289]='',
[18290]='',
[18301]='',
[18302]='',
[18303]='',
[18304]='',
[18305]='',
[18306]='',
[18307]='',
[18308]='',
[18321]='',
[18322]='',
[18323]='',
[18500]='',
[18501]='',
[18502]='',
[18503]='',
[18510]='',
[18511]='',
[18512]='',
[18513]='',
[18514]='',
[18520]='',
[18530]='',
[18531]='',
[18532]='',
[18533]='',
[18801]='',
[18802]='',
[18803]='',
[18804]='',
[18805]='',
[18806]='',
[18807]='',
[18808]='',
[18850]='',
[18852]='',
[19201]='',
[19202]='',
[19220]='',
[19400]='',
[19401]='',
[19402]='',
[19403]='',
[19404]='',
[19405]='',
[19406]='',
[19407]='',
[19408]='',
[19409]='',
[19410]='',
[19411]='',
[19412]='',
[19437]='',
[19438]='',
[19439]='',
[19440]='',
[19441]='',
[19442]='',
[19443]='',
[19444]='',
[19445]='',
[19446]='',
[19447]='',
[19448]='',
[19449]='',
[19450]='',
[19451]='',
[19452]='',
[19453]='',
[19454]='',
[19455]='',
[19456]='',
[19457]='',
[19458]='',
[19459]='',
[20001]='',
[20002]='',
[20003]='',
[20004]='',
[20005]='',
[20006]='',
[20007]='',
[20008]='',
[20009]='',
[20010]='',
[20011]='',
[20012]='',
[20013]='',
[20014]='',
[20015]='',
[20016]='',
[20017]='',
[20018]='',
[20019]='',
[20020]='',
[20021]='',
[20022]='',
[20023]='',
[20024]='',
[20025]='',
[20026]='',
[20027]='',
[20028]='',
[20029]='',
[20030]='',
[20031]='',
[20032]='',
[20033]='',
[20034]='',
[20035]='',
[20036]='',
[20037]='',
[20038]='',
[20039]='',
[20040]='',
[20041]='',
[20042]='',
[20043]='',
[20044]='',
[20045]='',
[20046]='',
[20047]='',
[20048]='',
[20049]='',
[20050]='',
[20051]='',
[20052]='',
[20053]='',
[20054]='',
[20055]='',
[20056]='',
[20057]='',
[20058]='',
[20059]='',
[20060]='',
[20061]='',
[20062]='',
[20063]='',
[20064]='',
[20065]='',
[20066]='',
[20070]='',
[20071]='',
[20072]='',
[20073]='',
[20074]='',
[20075]='',
[20076]='',
[20077]='',
[20078]='',
[20079]='',
[20081]='',
[20082]='',
[20083]='',
[20084]='',
[20090]='',
[20091]='',
[20092]='',
[20093]='',
[20094]='',
[20095]='',
[20096]='',
[20100]='',
[20101]='',
[20102]='',
[20103]='',
[20105]='',
[20106]='',
[20107]='',
[20108]='',
[20109]='',
[20111]='',
[20112]='',
[20113]='',
[20114]='',
[20115]='',
[20116]='',
[20117]='',
[20118]='',
[20119]='',
[20120]='',
[20121]='',
[20122]='',
[20123]='',
[20124]='',
[20125]='',
[20126]='',
[20127]='',
[20128]='',
[20129]='',
[20130]='',
[20131]='',
[20132]='',
[20133]='',
[20134]='',
[20135]='',
[20136]='',
[20137]='',
[20250]='',
[20251]='',
[20252]='',
[20253]='',
[20254]='',
[20255]='',
[20256]='',
[20260]='',
[20261]='',
[20262]='',
[20263]='',
[20264]='',
[20280]='',
[20281]='',
[20282]='',
[20283]='',
[20284]='',
[20285]='',
[20286]='',
[20287]='',
[20288]='',
[20289]='',
[20290]='',
[20291]='',
[20292]='',
[20340]='',
[20341]='',
[20342]='',
[20343]='',
[20344]='',
[20345]='',
[20346]='',
[20350]='',
[20351]='',
[20352]='',
[20353]='',
[20354]='',
[20355]='',
[20356]='',
[20360]='',
[20361]='',
[20362]='',
[20363]='',
[20364]='',
[20365]='',
[20366]='',
[20367]='',
[20368]='',
[20369]='',
[20370]='',
[20371]='',
[20380]='',
[20381]='',
[20382]='',
[20383]='',
[20390]='',
[20391]='',
[20392]='',
[20393]='',
[20394]='',
[20395]='',
[20396]='',
[20397]='',
[20398]='',
[20500]='',
[20501]='',
[20502]='',
[20503]='',
[20504]='',
[20505]='',
[20506]='',
[20507]='',
[20508]='',
[20509]='',
[20654]='',
[20655]='',
[20656]='',
[20657]='',
[20701]='',
[20702]='',
[20703]='',
[20711]='',
[20712]='',
[20713]='',
[20714]='',
[20715]='',
[20716]='',
[20717]='',
[20718]='',
[20719]='',
[20720]='',
[20721]='',
[20722]='',
[20723]='',
[20724]='',
[20725]='',
[20726]='',
[20727]='',
[20728]='',
[20729]='',
[20730]='',
[21001]='',
[21002]='',
[21003]='',
[21004]='',
[21005]='',
[21006]='',
[21007]='',
[21008]='',
[21009]='',
[21010]='',
[21011]='',
[21012]='',
[21013]='',
[21014]='',
[21015]='',
[21016]='',
[21017]='',
[21018]='',
[21019]='',
[21020]='',
[21021]='',
[21022]='',
[21023]='',
[21024]='',
[21025]='',
[21026]='',
[21027]='',
[21028]='',
[21029]='',
[21030]='',
[21031]='',
[21032]='',
[21033]='',
[21034]='',
[21035]='',
[21036]='',
[21037]='',
[21038]='',
[21039]='',
[21040]='',
[21041]='',
[21042]='',
[21043]='',
[21044]='',
[21045]='',
[21046]='',
[21047]='',
[21101]='',
[21102]='',
[21103]='',
[21104]='',
[21105]='',
[21106]='',
[21107]='',
[21108]='',
[21109]='',
[21110]='',
[21111]='',
[21112]='',
[21113]='',
[21114]='',
[21115]='',
[21116]='',
[21117]='',
[21118]='',
[21119]='',
[21120]='',
[21121]='',
[21122]='',
[21123]='',
[21124]='',
[21125]='',
[21126]='',
[21127]='',
[21128]='',
[21129]='',
[21130]='',
[21131]='',
[21132]='',
[21133]='',
[21134]='',
[21135]='',
[21136]='',
[21137]='',
[21138]='',
[21139]='',
[21140]='',
[21141]='',
[30701]='',
[30702]='',
[30703]='',
[30704]='',
[30705]='',
[30706]='',
[30707]='',
[30708]='',
[30709]='',
[30710]='',
[30711]='',
[30712]='',
[30713]='',
[30714]='',
[30715]='',
[30716]='',
[30717]='',
[30718]='',
[30801]='',
[30802]='',
[30803]='',
[30804]='',
[30805]='',
[30806]='',
[30807]='',
[30808]='',
[30809]='',
[30810]='',
[30811]='',
[30812]='',
[30813]='',
[30814]='',
[30815]='',
[30816]='',
[30817]='',
[30818]='',
[30820]='',
[30821]='',
[30822]='',
[30823]='',
[30824]='',
[30825]='',
[30826]='',
[30827]='',
[30828]='',
[30829]='',
[30830]='',
[30831]='',
[30832]='',
[30833]='',
[30834]='',
[30835]='',
[30836]='',
[30850]='',
[30851]='',
[30857]='',
[30858]='',
[30859]='',
[30860]='',
[30861]='',
[30862]='',
[30863]='',
[30864]='',
[30865]='',
[30866]='',
[30867]='',
[30868]='',
[30869]='',
[30870]='',
[30871]='',
[30872]='',
[30873]='',
[30874]='',
[30875]='',
[30876]='',
[30877]='',
[30878]='',
[30879]='',
[30880]='',
[30881]='',
[30882]='',
[30883]='',
[30884]='',
[30885]='',
[30950]=[[1. س: كيف يمكنني مواصلة اللعب بعد تغيير جهازي؟
ج: فور تحميل اللعبة والدخول إليها، يمكنك تسجيل الدخول باستخدام حسابك وكلمة مرورك في واجهة تسجيل الدخول لبدء اللعبة.]],
[30951]=[[2. س: ماذا أفعل إن سُرق حسابي، أو تم تغيير كلمة مروري؟
ج: إن تم تغيير كلمة مرورك، فتواصل مع خدمة العملاء بأسرع ما يمكن.]],
[30952]=[[3. س: ماذا أفعل إن حدث خطأ في مهلة الاتصال بالشبكة، ولم أتمكن من تسجيل الدخول؟
ج: إن لم تتمكن من تسجيل دخولك إلى اللعبة، فجرب إعادة الاتصال بالشبكة، أو إعادة تشغيل اللعبة.]],
[30953]=[[4. س: ماذا أفعل إن كانت اللعبة قيد التحديث بشكل متواصل، ولا يمكنني الدخول إليها؟
 ج: إن واجهت موقفًا علقت فيه اللعبة في وضع التحديث، فجرب إنهاء تشغيل اللعبة، أو التبديل إلى شبكة مختلفة، ثم أعد الدخول إلى اللعبة. إن لم تُحل المشكلة، فتواصل مع خدمة العملاء.]],
[30954]=[[5. س: ماذا أفعل إن فقدت بيانات لعبتي، أو حسابي؟
ج: إن فقدت بياناتك، فتأكد من أنك سجلت دخولك إلى السيرفر الصحيح؛ لأن البيانات لا تتم مشاركتها عبر السيرفرات المختلفة. إن كان السيرفر يعمل ولكنك ما زلت تواجه مشاكل، فتواصل معنا فورًا. حين تتواصل معنا، بلغنا بأكبر قدر من المعلومات؛ مثل معرِّفك في اللعبة واسم حسابك، وسنفعل ما بوسعنا لمساعدتك.]],
[30955]=[[6. س: ماذا أفعل إن استمر انقطاع الاتصال خلال اللعب؟
ج: إن واجهت انقطاعًا متكررًا في الاتصال خلال اللعب، فتفقد حالة اتصال جهازك بالشبكة، وجرب مجددًا بعد تعديل اتصالك بالشبكة. إن استمرت المشكلة، فتواصل مع موظفي خدمة العملاء.]],
[30956]=[[7. س: ماذا أفعل إن استمرت اللعبة في التعطل؟
 ج: إن تعطلت اللعبة، فجرب إنهاء تشغيل اللعبة على جهازك، وإعادة الدخول إلى اللعبة. إن استمرت المشكلة، فجرب إعادة تشغيل جهازك قبل الدخول إلى اللعبة مجددًا، أو التواصل مع خدمة العملاء للإبلاغ عن المشكلة.]],
[30957]=[[8. س: ماذا أفعل إن وجدت غشاشًا؟
ج: نحن ملتزمون بإنشاء بيئة لعب عادلة لكل اللاعبين. الإخلال بتوازن بيئة اللعب من خلال استغلال الثغرات ممنوع منعًا باتًّا. إن واجهت لاعبًا غشاشًا، فأبلغنا به في أسرع وقت؛ لكي نتمكن من اتخاذ الإجراءات اللازمة.]],
[30959]='',
[30961]='',
[30962]='',
[30963]='',
[30964]='',
[30965]='',
[30966]='',
[30967]='',
[30968]='',
[30969]=[[4. س: لماذا يختلف المبلغ المخصوم بعد عملية إعادة شحن ناجحة عن الثمن الموضح داخل اللعبة؟
ج: يتم تحويل المبلغ المخصوم بعد الشراء، وتحصيله بناءً على سعر صرف العملة في دولتك الحالية، والذي قد يختلف.
]],
[30970]='',
[30971]='',
[30972]='',
[30973]='',
[30974]='',
[30975]='',
[30976]='',
[30977]='',
[30978]='',
[30979]='',
[30980]='',
[30981]='',
[30982]='',
[30983]='',
[30984]='',
[30985]='',
[30986]='',
[30987]='',
[30988]='',
[30989]='',
[30990]='',
[30991]='',
[32001]='',
[32002]='',
[32003]='',
[32004]='',
[32005]='',
[32006]='',
[32007]='',
[32008]='',
[32009]='',
[32010]='',
[32011]='',
[32021]='',
[32022]='',
[32023]='',
[32024]='',
[32025]='',
[32026]='',
[32027]='',
[32028]='',
[32029]='',
[32030]='',
[32031]='',
[32032]='',
[32033]='',
[32034]='',
[32035]='',
[32036]='',
[32037]='',
[32038]='',
[32039]='',
[32040]='',
[32041]='',
[32042]='',
[32043]='',
[32044]='',
[32045]='',
[32046]='',
[32047]='',
[32048]='',
[32049]='',
[32050]='',
[32051]='',
[32052]='',
[32053]='',
[32054]='',
[32055]=[[تم تحسين قوة سييرا]],
[34000]='',
[34001]='',
[34002]='',
[34003]='',
[34004]='',
[34005]='',
[34006]='',
[34007]='',
[34008]='',
[34009]='',
[34010]='',
[34011]='',
[34012]='',
[34013]='',
[34014]='',
[34015]='',
[34016]='',
[34017]='',
[34018]='',
[34019]='',
[34020]='',
[34021]='',
[34022]='',
[34023]='',
[34024]='',
[34025]='',
[34026]='',
[34027]='',
[34028]='',
[34029]='',
[34030]='',
[34031]='',
[34032]='',
[34033]='',
[34034]='',
[34035]='',
[34036]='',
[34037]='',
[34038]='',
[34039]='',
[34040]='',
[34041]='',
[34042]='',
[34043]='',
[34044]='',
[34045]='',
[34046]='',
[34047]='',
[34049]='',
[34050]='',
[34051]='',
[34052]='',
[34053]='',
[34054]='',
[34056]='',
[34057]='',
[34058]='',
[34059]='',
[34060]='',
[34061]='',
[34062]='',
[34063]='',
[34064]='',
[34065]='',
[34066]='',
[34067]='',
[34068]='',
[34070]='',
[34071]='',
[34072]='',
[34073]='',
[34074]='',
[34076]='',
[34079]='',
[34081]='',
[34082]='',
[34085]='',
[34088]='',
[34091]='',
[34100]='',
[34101]='',
[35001]='',
[35002]='',
[35003]='',
[35004]='',
[35005]='',
[35006]='',
[35007]='',
[35008]='',
[35009]='',
[35010]='',
[35011]='',
[35012]='',
[35013]='',
[35014]='',
[35015]='',
[35016]='',
[35017]='',
[35018]='',
[35019]='',
[35020]='',
[35021]='',
[35022]='',
[35023]='',
[35024]='',
[35025]='',
[35026]='',
[35027]='',
[35028]='',
[35029]='',
[35030]='',
[35031]='',
[36001]='',
[36002]='',
[36003]='',
[36004]='',
[36005]='',
[36006]='',
[36007]='',
[36008]='',
[36009]='',
[36010]='',
[36011]='',
[36012]='',
[36013]='',
[36014]='',
[36015]='',
[36016]='',
[36017]='',
[36018]='',
[36019]='',
[36020]='',
[36021]='',
[36022]='',
[36023]='',
[36024]='',
[36025]='',
[36032]='',
[36033]='',
[36034]='',
[36035]='',
[36036]='',
[36037]='',
[36038]='',
[36039]='',
[36040]='',
[36041]='',
[36042]='',
[36043]='',
[36050]='',
[36051]='',
[36052]='',
[36053]='',
[36054]='',
[36055]='',
[36056]='',
[36057]='',
[36058]='',
[36059]='',
[36060]='',
[36061]='',
[36062]='',
[36063]='',
[36064]='',
[36065]='',
[36066]='',
[36067]='',
[36068]='',
[36069]='',
[36070]='',
[36071]='',
[36072]='',
[36073]='',
[36074]='',
[36075]='',
[36076]='',
[36077]='',
[36078]='',
[36081]='',
[36082]='',
[36083]='',
[36084]='',
[36085]=[[جميع المكافآت التي تم الحصول عليها]],
[40064]='',
[40065]='',
[40071]='',
[40072]='',
[40073]='',
[40074]='',
[40075]='',
[40076]='',
[40077]='',
[40078]='',
[40079]='',
[40080]='',
[40081]='',
[40082]='',
[40083]='',
[40084]='',
[40085]='',
[40086]='',
[40087]='',
[40088]='',
[40089]='',
[40090]='',
[40091]='',
[40092]='',
[40093]='',
[40094]='',
[40095]='',
[40096]='',
[40097]='',
[40098]='',
[40099]='',
[40101]='',
[40102]='',
[40103]='',
[40104]='',
[40105]='',
[40106]='',
[40107]='',
[40108]='',
[40109]='',
[40110]='',
[40111]='',
[40112]='',
[40113]='',
[40114]='',
[40115]='',
[40116]='',
[40117]='',
[40118]='',
[40119]='',
[40120]='',
[40121]='',
[40122]='',
[40123]='',
[40124]='',
[40125]='',
[40126]='',
[40127]='',
[40128]='',
[40129]='',
[40130]='',
[40131]='',
[40132]='',
[40133]='',
[40134]='',
[40135]='',
[40136]='',
[40137]='',
[40138]='',
[40139]='',
[40140]='',
[40141]='',
[40142]='',
[40143]='',
[40144]='',
[40145]='',
[40146]='',
[40147]='',
[40148]='',
[40149]='',
[40150]='',
[40151]='',
[40152]='',
[40153]='',
[40154]='',
[40155]='',
[40156]='',
[40157]='',
[40158]='',
[40159]='',
[40160]='',
[40161]='',
[40162]='',
[40163]='',
[40164]='',
[40165]='',
[40166]='',
[40167]='',
[40168]='',
[40169]='',
[40170]='',
[40171]='',
[40172]='',
[40173]='',
[40174]='',
[40175]='',
[40176]='',
[40177]='',
[40178]='',
[40179]='',
[40180]='',
[40181]='',
[40182]='',
[40183]='',
[40184]='',
[40185]='',
[40186]='',
[40187]='',
[40188]='',
[40189]='',
[40190]='',
[40191]='',
[40192]='',
[40193]='',
[40194]='',
[40195]='',
[40196]='',
[40197]='',
[40198]='',
[40199]='',
[40200]='',
[40201]='',
[40202]='',
[40203]='',
[40204]='',
[40205]='',
[40206]='',
[40207]='',
[40208]='',
[40209]='',
[40210]='',
[40211]='',
[40212]='',
[40213]='',
[40214]='',
[40215]='',
[40216]='',
[40217]='',
[40218]='',
[40219]='',
[40220]='',
[40221]='',
[40222]='',
[40223]='',
[40224]='',
[40225]='',
[40226]='',
[40227]='',
[40228]='',
[40229]='',
[40230]='',
[40231]='',
[40232]='',
[40233]='',
[40234]='',
[40235]='',
[40236]='',
[40237]='',
[40238]='',
[40239]='',
[40240]='',
[40241]='',
[40242]='',
[40243]='',
[40244]='',
[40245]='',
[40246]='',
[40247]='',
[40248]='',
[40249]='',
[40250]='',
[40251]='',
[40252]='',
[40253]='',
[40254]='',
[40255]='',
[40256]='',
[40257]='',
[40258]='',
[40259]='',
[40260]='',
[40261]='',
[40262]='',
[40263]='',
[40264]='',
[40265]='',
[40266]='',
[40267]='',
[40268]='',
[40269]='',
[40270]='',
[40271]='',
[40272]='',
[40273]='',
[40274]='',
[40275]='',
[40276]='',
[40277]='',
[40278]='',
[40279]='',
[40280]='',
[40281]='',
[40282]='',
[40283]='',
[40284]='',
[40285]='',
[40286]='',
[40287]='',
[40288]='',
[40289]='',
[40290]='',
[40291]='',
[40292]='',
[40293]='',
[40294]='',
[40295]='',
[40296]='',
[40297]='',
[40298]='',
[40299]='',
[40300]='',
[40301]='',
[40302]='',
[40303]='',
[40304]='',
[40305]='',
[40306]='',
[40307]='',
[40308]='',
[40309]='',
[40310]='',
[40311]='',
[40312]='',
[40313]='',
[40314]='',
[40315]='',
[40316]='',
[40317]='',
[40318]='',
[40319]='',
[40320]='',
[40321]='',
[40322]='',
[40323]='',
[40324]='',
[40325]='',
[40326]='',
[40327]='',
[40328]='',
[40329]='',
[40330]='',
[40331]='',
[40332]='',
[40333]='',
[40334]='',
[40335]='',
[40336]='',
[40337]='',
[40338]='',
[40339]='',
[40340]='',
[40341]='',
[40342]='',
[40343]='',
[40344]='',
[40345]='',
[40346]='',
[40347]='',
[40348]='',
[40349]='',
[40350]='',
[40351]='',
[40352]='',
[40353]='',
[40354]='',
[40355]='',
[40356]='',
[40357]='',
[40358]='',
[40359]='',
[40360]='',
[40361]='',
[40362]='',
[40363]='',
[40364]='',
[40365]='',
[40366]='',
[40367]='',
[40368]='',
[40369]='',
[40370]='',
[40371]='',
[40372]='',
[40373]='',
[40374]='',
[40375]='',
[40376]='',
[40377]='',
[40378]='',
[40379]='',
[40380]='',
[40381]='',
[40382]='',
[40383]='',
[40384]='',
[40385]='',
[40386]='',
[40387]='',
[40388]='',
[40389]='',
[40390]='',
[40391]='',
[40392]='',
[40393]='',
[40394]='',
[40395]='',
[40396]='',
[40397]='',
[40398]='',
[40399]='',
[40400]='',
[40401]='',
[40402]='',
[40403]='',
[40404]='',
[40405]='',
[40406]='',
[40407]='',
[40408]='',
[40409]='',
[40410]='',
[40411]='',
[40412]='',
[40413]='',
[40414]='',
[40415]='',
[40416]='',
[40417]='',
[40418]='',
[40419]='',
[40420]='',
[40421]='',
[40422]='',
[40423]='',
[40424]='',
[40425]='',
[40426]='',
[40427]='',
[40428]='',
[40429]='',
[40430]='',
[40431]='',
[40432]='',
[40433]='',
[40434]='',
[40435]='',
[40436]='',
[40437]='',
[40438]='',
[40439]='',
[40440]='',
[40441]='',
[40442]='',
[40443]='',
[40444]='',
[40445]='',
[40446]='',
[40447]='',
[40448]='',
[40449]='',
[40450]='',
[40451]='',
[40452]='',
[40453]='',
[40454]='',
[40471]='',
[40472]='',
[40473]='',
[40474]='',
[40475]='',
[40476]='',
[40477]='',
[40478]='',
[40479]='',
[40480]='',
[40481]='',
[40482]='',
[40483]='',
[40484]='',
[40485]='',
[40486]='',
[40487]='',
[40488]='',
[41100]='',
[41101]=[[هل تود تأكيد تحديد هذا الفصيل؟]],
[42001]='',
[42002]='',
[42003]='',
[42004]='',
[42005]='',
[42006]='',
[42007]='',
[42008]='',
[42009]='',
[42010]='',
[42011]='',
[42012]='',
[42013]='',
[42014]='',
[42015]='',
[42016]='',
[42017]='',
[42018]='',
[42019]='',
[42020]='',
[42021]='',
[42022]='',
[42023]='',
[42024]='',
[42025]='',
[42026]='',
[42027]='',
[42028]='',
[42029]='',
[42030]='',
[42031]='',
[42032]='',
[42033]='',
[42034]='',
[42035]='',
[42036]='',
[42037]='',
[42038]='',
[42039]='',
[42040]='',
[42041]='',
[42042]='',
[42043]='',
[42044]='',
[42045]='',
[42046]='',
[42047]='',
[42048]='',
[42049]='',
[42050]='',
[42051]='',
[42052]='',
[42053]='',
[42054]='',
[42055]='',
[42056]='',
[42057]='',
[42058]='',
[42061]='',
[42062]='',
[42063]='',
[42064]='',
[42065]='',
[42066]='',
[42067]='',
[42068]='',
[42069]='',
[42070]='',
[42071]='',
[42101]='',
[42102]='',
[42103]='',
[42104]='',
[42105]='',
[42106]='',
[42107]='',
[42108]='',
[42109]='',
[42110]='',
[42111]='',
[42112]='',
[42113]='',
[42114]='',
[42115]='',
[42116]='',
[42117]='',
[42118]='',
[42119]='',
[42120]='',
[42121]='',
[42122]='',
[42123]='',
[42124]='',
[42125]='',
[42126]='',
[42127]='',
[42128]='',
[42129]='',
[42130]='',
[42131]='',
[42132]='',
[42133]='',
[42134]='',
[42135]='',
[42136]='',
[42137]='',
[42138]='',
[42139]='',
[42140]='',
[42141]='',
[42142]='',
[42143]='',
[42144]='',
[42145]='',
[42146]='',
[42147]='',
[42148]='',
[42149]='',
[42150]='',
[42151]='',
[42152]='',
[42153]='',
[42154]='',
[42155]='',
[42156]='',
[42157]='',
[42158]='',
[42159]='',
[42160]='',
[42161]='',
[42162]='',
[42163]='',
[42164]='',
[42165]='',
[42166]='',
[42167]='',
[42168]='',
[42169]='',
[42171]='',
[42172]='',
[42173]='',
[42174]='',
[42175]='',
[42176]='',
[42177]='',
[42178]='',
[42179]='',
[42180]='',
[42181]='',
[42182]='',
[42183]='',
[42201]='',
[42202]='',
[42203]='',
[42204]='',
[42207]='',
[42208]='',
[42209]='',
[42211]='',
[42212]='',
[50001]='',
[50002]='',
[50003]='',
[50004]='',
[50005]='',
[50006]='',
[50007]='',
[50008]='',
[50009]='',
[50010]='',
[50011]='',
[50012]='',
[50013]='',
[50014]='',
[50015]='',
[50016]='',
[50017]='',
[50018]='',
[50019]='',
[50020]='',
[50021]='',
[50022]='',
[50023]='',
[50024]='',
[50025]='',
[50026]='',
[50027]='',
[50028]='',
[50029]='',
[50030]='',
[50031]='',
[50032]='',
[50033]='',
[50034]='',
[50035]='',
[50036]='',
[50037]='',
[50038]='',
[50039]='',
[50040]='',
[50051]='',
[50052]='',
[50053]='',
[50054]='',
[50055]='',
[50056]='',
[50057]='',
[50058]='',
[50059]='',
[50060]='',
[50061]='',
[50062]='',
[50063]='',
[50064]='',
[50065]='',
[50066]='',
[50067]='',
[50068]='',
[50101]='',
[50102]='',
[50103]='',
[50104]='',
[50105]='',
[50106]='',
[50107]='',
[50108]='',
[50109]='',
[50110]='',
[50111]='',
[50112]='',
[50113]='',
[50114]='',
[50115]='',
[50116]='',
[50117]='',
[50118]='',
[50119]='',
[50120]='',
[50121]='',
[50122]='',
[50123]='',
[50124]='',
[50125]='',
[50126]='',
[50127]='',
[50128]='',
[50129]='',
[50130]='',
[50131]='',
[50132]='',
[50133]='',
[50134]='',
[50135]='',
[50136]='',
[50137]='',
[50138]='',
[50139]='',
[50140]='',
[50141]='',
[50142]='',
[50143]='',
[50144]='',
[50145]='',
[50146]='',
[50147]='',
[50148]='',
[50149]='',
[50150]='',
[50151]='',
[50152]='',
[50153]='',
[50154]='',
[50155]='',
[50156]='',
[50157]='',
[50158]='',
[50159]='',
[50160]='',
[50161]='',
[50162]='',
[50163]='',
[50164]='',
[50165]='',
[50166]='',
[50167]='',
[50168]='',
[50169]='',
[50170]='',
[50171]='',
[50172]='',
[50173]='',
[50174]='',
[50175]='',
[50176]='',
[50177]='',
[50178]='',
[50179]='',
[50180]='',
[50181]='',
[50182]='',
[50183]='',
[50184]='',
[50185]='',
[50186]='',
[50187]='',
[50188]='',
[50189]='',
[50190]='',
[50191]='',
[50192]='',
[50193]='',
[50194]='',
[50195]='',
[50196]='',
[50197]='',
[50198]='',
[50199]='',
[50200]='',
[50201]='',
[50202]='',
[50203]='',
[50204]='',
[50205]='',
[50206]='',
[50207]='',
[50208]='',
[50209]='',
[50210]='',
[50211]='',
[50212]='',
[50213]='',
[50214]='',
[50215]='',
[50216]='',
[50217]='',
[50218]='',
[50219]='',
[50220]='',
[50221]='',
[50222]='',
[50223]='',
[50224]='',
[50225]='',
[50226]='',
[50227]='',
[50228]='',
[50229]='',
[50230]='',
[50231]='',
[50232]='',
[50233]='',
[50234]='',
[50235]='',
[50236]='',
[50237]='',
[50238]='',
[50239]='',
[50240]='',
[50241]='',
[50242]='',
[50243]='',
[50244]='',
[50245]='',
[50246]='',
[50247]='',
[50248]='',
[50249]='',
[50250]='',
[50251]='',
[50252]='',
[50253]='',
[50254]='',
[50255]='',
[50256]='',
[50257]='',
[50258]='',
[50259]='',
[50260]='',
[50261]='',
[50262]='',
[50263]='',
[50264]='',
[50265]='',
[50266]='',
[50267]='',
[50268]='',
[50269]='',
[50270]='',
[50271]='',
[50272]='',
[50273]='',
[50274]='',
[50275]='',
[50276]='',
[50277]='',
[50278]='',
[50279]='',
[50280]='',
[50281]='',
[50282]='',
[50283]='',
[50284]='',
[50285]='',
[50286]='',
[50287]='',
[50288]='',
[50289]='',
[50290]='',
[50291]='',
[50292]='',
[50293]='',
[50294]='',
[50295]='',
[50296]='',
[50297]='',
[50298]='',
[50299]='',
[50300]='',
[50301]='',
[50302]='',
[50303]='',
[50304]='',
[50305]='',
[50306]='',
[50307]='',
[50308]='',
[50309]='',
[50310]='',
[50311]='',
[50312]='',
[50313]='',
[50314]='',
[50315]='',
[50316]='',
[50317]='',
[50318]='',
[50319]='',
[50320]='',
[50321]='',
[50322]='',
[50323]='',
[50324]='',
[50325]='',
[50326]='',
[50327]='',
[50328]='',
[50329]='',
[50330]='',
[50331]='',
[50332]='',
[50333]='',
[50334]='',
[50335]='',
[50336]='',
[50337]='',
[50338]='',
[50339]='',
[50340]='',
[50341]='',
[50342]='',
[50343]='',
[50344]='',
[50345]='',
[50346]='',
[50347]='',
[50348]='',
[50349]='',
[50350]='',
[50351]='',
[50352]='',
[50353]='',
[50354]='',
[50355]='',
[50356]='',
[50357]='',
[50358]='',
[50359]='',
[50360]='',
[50361]='',
[50362]='',
[50363]='',
[50364]='',
[50365]='',
[50366]='',
[50367]='',
[50368]='',
[50369]='',
[50370]='',
[50371]='',
[50372]='',
[50373]='',
[50374]='',
[50375]='',
[50376]='',
[50377]='',
[50378]='',
[50379]='',
[50380]='',
[50381]='',
[50382]='',
[50383]='',
[50384]='',
[50385]='',
[50386]='',
[50387]='',
[50388]='',
[50389]='',
[50390]='',
[50391]='',
[50392]='',
[50393]='',
[50394]='',
[50395]='',
[50396]='',
[50397]='',
[50398]='',
[50399]='',
[50496]='',
[50497]='',
[50498]='',
[50499]='',
[50500]='',
[50501]='',
[50502]='',
[50503]='',
[50504]='',
[50505]='',
[50506]='',
[50507]='',
[50508]='',
[50509]='',
[50510]='',
[50511]='',
[50512]='',
[50513]='',
[50514]='',
[50515]='',
[50516]='',
[50517]='',
[50518]='',
[50519]='',
[50551]='',
[50552]='',
[50553]='',
[50554]='',
[50555]='',
[50556]='',
[50557]='',
[50558]='',
[50559]='',
[50560]='',
[50561]='',
[50562]='',
[50563]='',
[50564]='',
[50565]='',
[50566]='',
[50567]='',
[50568]='',
[50569]='',
[50570]='',
[50571]='',
[50572]='',
[50573]='',
[50574]='',
[50575]='',
[50576]='',
[50577]='',
[50578]='',
[50579]='',
[50580]='',
[50581]='',
[50582]='',
[50583]='',
[50584]='',
[50585]='',
[50586]='',
[50587]='',
[50588]='',
[50589]='',
[50590]='',
[50591]='',
[50592]='',
[50593]='',
[50594]='',
[50595]='',
[50596]='',
[50597]='',
[50598]='',
[50599]='',
[50600]='',
[50601]='',
[50602]='',
[50603]='',
[50604]='',
[50605]='',
[50606]='',
[50607]='',
[50608]='',
[50609]='',
[50610]='',
[50611]='',
[50612]='',
[50613]='',
[50614]='',
[50615]='',
[50616]='',
[50617]='',
[50618]='',
[50619]='',
[50620]='',
[50621]='',
[50622]='',
[50623]='',
[50624]='',
[50625]='',
[50626]='',
[50627]='',
[50628]='',
[50629]='',
[50630]='',
[50631]='',
[50632]='',
[50633]='',
[50634]='',
[50635]='',
[50636]='',
[50637]='',
[50638]='',
[50639]='',
[50640]='',
[50641]='',
[50642]='',
[50643]='',
[50644]='',
[50645]='',
[50646]='',
[50647]='',
[50648]='',
[50649]='',
[50650]='',
[50651]='',
[50652]='',
[50653]='',
[50654]='',
[50655]='',
[50656]='',
[50657]='',
[50658]='',
[50659]='',
[50660]='',
[50661]='',
[50662]='',
[50663]='',
[50664]='',
[50665]='',
[50666]='',
[50667]='',
[50668]='',
[50669]='',
[50670]='',
[50671]='',
[50672]='',
[50673]='',
[50674]='',
[50675]='',
[50676]='',
[50677]='',
[50678]='',
[50679]='',
[50680]='',
[50681]='',
[50682]='',
[50683]='',
[50684]='',
[50685]='',
[50686]='',
[50687]='',
[50688]='',
[50689]='',
[50690]='',
[50691]='',
[50692]='',
[50693]='',
[50694]='',
[50695]='',
[50696]='',
[50697]='',
[50698]='',
[50699]='',
[50700]='',
[50701]='',
[50702]='',
[50703]='',
[50704]='',
[50705]='',
[50706]='',
[50707]='',
[50708]='',
[50709]='',
[50710]='',
[50711]='',
[50712]='',
[50713]='',
[50714]='',
[50715]='',
[50716]='',
[50717]='',
[50718]='',
[50719]='',
[50720]='',
[50721]='',
[50722]='',
[50723]='',
[50724]='',
[50725]='',
[50726]='',
[50727]='',
[50728]='',
[50729]='',
[50730]='',
[50731]='',
[50732]='',
[50733]='',
[50734]='',
[50735]='',
[50736]='',
[50737]='',
[50738]='',
[50739]='',
[50740]='',
[50741]='',
[50742]='',
[50743]='',
[50744]='',
[50745]='',
[50746]='',
[50747]='',
[50748]='',
[50749]='',
[50750]='',
[50751]='',
[50752]='',
[50753]='',
[50754]='',
[50755]='',
[50756]='',
[50757]='',
[50758]='',
[50759]='',
[50760]='',
[50761]='',
[50762]='',
[50763]='',
[50764]='',
[50765]='',
[50766]='',
[50767]='',
[50768]='',
[50769]='',
[50770]='',
[50771]='',
[50772]='',
[50773]='',
[50774]='',
[50775]='',
[50776]='',
[50777]='',
[50778]='',
[50779]='',
[50780]='',
[50781]='',
[50782]='',
[50783]='',
[50784]='',
[50785]='',
[50786]='',
[50787]='',
[50788]='',
[50789]='',
[50790]='',
[50791]='',
[50792]='',
[50793]='',
[50794]='',
[50795]='',
[50796]='',
[50797]='',
[50798]='',
[50799]='',
[50800]='',
[50801]='',
[50802]='',
[50803]='',
[50804]='',
[50805]='',
[50806]='',
[50807]='',
[50808]='',
[50809]='',
[50810]='',
[50811]='',
[50812]='',
[50813]='',
[50814]='',
[50815]='',
[50816]='',
[50817]='',
[50818]='',
[50819]='',
[50820]='',
[50821]='',
[50822]='',
[50823]='',
[50824]='',
[50825]='',
[50826]='',
[50827]='',
[50828]='',
[50829]='',
[50830]='',
[50831]='',
[50832]='',
[50833]='',
[50834]='',
[50835]='',
[50836]='',
[50837]='',
[50838]='',
[50839]='',
[51001]='',
[51002]='',
[51003]='',
[51004]='',
[51005]='',
[51006]='',
[51007]='',
[51008]='',
[51009]='',
[51010]='',
[51011]='',
[51012]='',
[51013]='',
[51014]='',
[51015]='',
[51016]='',
[51017]='',
[51018]='',
[51019]='',
[51020]='',
[51021]='',
[51022]='',
[51023]='',
[51024]='',
[51025]='',
[51026]='',
[51027]='',
[51028]='',
[51029]='',
[51031]='',
[51032]='',
[51033]='',
[51038]='',
[51101]='',
[51102]='',
[51103]='',
[51104]='',
[51105]='',
[51106]='',
[51107]='',
[51108]='',
[51109]='',
[51110]='',
[51111]='',
[51112]='',
[51113]='',
[51114]='',
[51115]='',
[51116]='',
[51117]='',
[51118]='',
[51119]='',
[51120]='',
[51121]='',
[51122]='',
[51123]='',
[51124]='',
[51125]='',
[51126]='',
[51127]='',
[51128]='',
[51129]='',
[51130]='',
[51131]='',
[51132]='',
[51133]='',
[51134]='',
[51135]='',
[51136]='',
[51137]='',
[51138]='',
[51139]='',
[51140]='',
[51141]='',
[51142]='',
[51143]='',
[51144]='',
[51145]='',
[51146]='',
[51147]='',
[51148]='',
[51151]='',
[51152]='',
[51153]='',
[51155]='',
[51157]='',
[51159]='',
[51160]='',
[51161]='',
[51162]='',
[51163]='',
[51164]='',
[51165]='',
[51166]='',
[51167]='',
[51168]='',
[51173]='',
[51174]='',
[51175]='',
[51176]='',
[51177]='',
[51178]='',
[51179]='',
[51180]='',
[51181]='',
[51182]='',
[51185]='',
[51186]='',
[51187]='',
[51188]='',
[51195]='',
[51196]='',
[51199]='',
[51200]='',
[51201]='',
[51202]='',
[51203]='',
[51204]='',
[51205]='',
[51206]='',
[51207]='',
[51208]='',
[51209]='',
[51210]='',
[51211]='',
[51212]='',
[51214]='',
[51215]='',
[51216]='',
[51213]='',
[51217]='',
[51218]='',
[51219]='',
[51220]='',
[51221]='',
[51222]='',
[51223]='',
[51224]='',
[51225]='',
[51226]='',
[51227]='',
[51228]='',
[51229]='',
[51231]='',
[51232]='',
[51233]='',
[51238]='',
[51301]='',
[51302]='',
[51303]='',
[51304]='',
[51305]='',
[51306]='',
[51307]='',
[51308]='',
[51309]='',
[51310]='',
[51311]='',
[51312]='',
[51313]='',
[51314]='',
[51315]='',
[51316]='',
[51317]='',
[51318]='',
[51319]='',
[51320]='',
[51321]='',
[51322]='',
[51323]='',
[51324]='',
[51325]='',
[51326]='',
[51327]='',
[51328]='',
[51329]='',
[51330]='',
[51331]='',
[51332]='',
[51333]='',
[51334]='',
[51335]='',
[51336]='',
[51337]='',
[51338]='',
[51369]='',
[51370]='',
[51371]='',
[51372]='',
[51373]='',
[51374]='',
[51375]='',
[51376]='',
[51377]='',
[51378]='',
[53301]='',
[53302]='',
[53303]='',
[53304]='',
[53305]='',
[53306]='',
[53307]='',
[53308]='',
[53309]='',
[53310]='',
[53311]='',
[53312]='',
[53313]='',
[53314]='',
[53315]='',
[53316]='',
[53317]='',
[53318]='',
[53319]='',
[53320]='',
[53321]='',
[53322]='',
[53323]='',
[53324]='',
[53325]='',
[53326]='',
[53327]='',
[53328]='',
[53329]='',
[53330]='',
[53331]='',
[53332]='',
[53333]='',
[53334]='',
[53335]='',
[53336]='',
[53337]='',
[53338]='',
[53339]='',
[53340]='',
[53341]='',
[53342]='',
[53345]='',
[53346]='',
[53347]='',
[53348]='',
[53349]='',
[53350]='',
[53351]='',
[53352]='',
[53353]='',
[53357]='',
[53358]='',
[53362]='',
[53364]='',
[53371]='',
[53372]='',
[53373]='',
[53374]='',
[53375]='',
[53376]='',
[53377]='',
[53378]='',
[53379]='',
[53380]='',
[53401]='',
[53402]='',
[53403]='',
[53404]='',
[53405]='',
[53406]='',
[53407]='',
[53408]='',
[53409]='',
[53410]='',
[53411]='',
[53412]='',
[53413]='',
[53414]='',
[53415]='',
[53416]='',
[53417]='',
[53418]='',
[53419]='',
[53420]='',
[53421]='',
[53422]='',
[53423]='',
[53424]='',
[53425]='',
[53426]='',
[53427]='',
[53428]='',
[53429]='',
[53430]='',
[53901]='',
[53902]='',
[53903]='',
[53904]='',
[53905]='',
[53906]='',
[55500]='',
[55501]='',
[55502]='',
[55503]='',
[55504]='',
[55505]='',
[55506]='',
[55507]='',
[55508]='',
[55509]='',
[55510]='',
[55511]='',
[55512]='',
[55513]='',
[55514]='',
[55515]='',
[55516]='',
[55517]='',
[55518]='',
[55519]='',
[55520]='',
[55521]='',
[55522]='',
[55523]='',
[55524]='',
[55525]='',
[55526]='',
[55527]='',
[55528]='',
[55529]='',
[55530]='',
[55531]='',
[55532]='',
[55533]='',
[55534]='',
[55535]='',
[55536]='',
[55537]='',
[55538]='',
[55539]='',
[55540]='',
[55541]='',
[55542]='',
[55543]='',
[55544]='',
[55545]='',
[55546]='',
[55547]='',
[55548]='',
[55699]='',
[58001]='',
[58002]='',
[58003]='',
[58004]='',
[58005]='',
[58006]='',
[58007]='',
[58008]='',
[58009]='',
[58010]='',
[58011]='',
[58012]='',
[58013]='',
[58014]='',
[58015]='',
[58016]='',
[58017]='',
[58018]='',
[58019]='',
[58020]='',
[58021]='',
[58022]='',
[58023]='',
[58024]='',
[58025]='',
[58026]='',
[58027]='',
[58028]='',
[58029]='',
[58030]='',
[58031]='',
[58032]='',
[58033]='',
[58034]='',
[58035]='',
[58036]='',
[58037]='',
[58038]='',
[58039]='',
[58040]='',
[58041]='',
[58042]='',
[58043]='',
[58044]='',
[58045]='',
[58046]='',
[58047]='',
[58048]='',
[58049]='',
[58050]='',
[58051]='',
[58052]='',
[58053]='',
[58054]='',
[58055]='',
[58056]='',
[58057]='',
[58058]='',
[58059]='',
[58060]='',
[58061]='',
[58062]='',
[58063]='',
[58064]='',
[58065]='',
[58066]='',
[58067]='',
[58068]='',
[58069]='',
[58070]='',
[58071]='',
[58072]='',
[58073]='',
[58074]='',
[58075]='',
[58076]='',
[58077]='',
[58078]='',
[58079]='',
[58080]='',
[58081]='',
[58082]='',
[58083]='',
[58084]='',
[58085]='',
[58086]='',
[58087]='',
[58088]='',
[58089]='',
[58090]='',
[58091]='',
[58092]='',
[58093]='',
[58094]='',
[58095]='',
[58096]='',
[58097]='',
[58098]='',
[58099]='',
[58100]='',
[58101]='',
[58102]='',
[58103]='',
[58104]='',
[58105]='',
[58106]='',
[58107]='',
[58108]='',
[58109]='',
[58110]='',
[58111]='',
[58112]='',
[58113]='',
[58114]='',
[58115]='',
[58116]='',
[58117]='',
[58118]='',
[58119]='',
[58120]='',
[58121]='',
[58122]='',
[58123]='',
[58124]='',
[58125]='',
[58126]='',
[58127]='',
[58128]='',
[58129]='',
[58130]='',
[58131]='',
[58132]='',
[58133]='',
[58134]='',
[58135]='',
[58136]='',
[58137]='',
[58138]='',
[58139]='',
[58140]='',
[58141]='',
[58142]='',
[58143]='',
[58144]='',
[58145]='',
[58146]='',
[58147]='',
[58148]='',
[58149]='',
[58150]='',
[58151]='',
[58152]='',
[58153]='',
[58154]='',
[58155]='',
[58156]='',
[58157]='',
[58158]='',
[58159]='',
[58160]='',
[58161]='',
[58162]='',
[58163]='',
[58164]='',
[58165]='',
[58166]='',
[58167]='',
[58168]='',
[58169]='',
[58170]='',
[58171]='',
[58172]='',
[58173]='',
[58174]='',
[58175]='',
[58176]='',
[58177]='',
[58178]='',
[58179]='',
[58180]='',
[58181]='',
[58182]='',
[58183]='',
[58184]='',
[58185]='',
[58186]='',
[58187]='',
[58188]='',
[58189]='',
[58190]='',
[58191]='',
[58192]='',
[58193]='',
[58194]='',
[58195]='',
[58196]='',
[58197]='',
[58198]='',
[58199]='',
[58200]='',
[58201]='',
[58202]='',
[58203]='',
[58204]='',
[58205]='',
[58206]='',
[58207]='',
[58208]='',
[58209]='',
[58210]='',
[58211]='',
[58212]='',
[58213]='',
[58214]='',
[58215]='',
[58216]='',
[58217]='',
[58301]='',
[58302]='',
[58303]='',
[58304]='لافتة',
[58401]='',
[58402]='',
[60001]='',
[60002]='',
[60003]='',
[60004]='',
[60005]='',
[60006]='',
[60007]='',
[60008]='',
[60009]='',
[60010]='',
[60011]='',
[60012]='',
[60013]='',
[60014]='',
[60015]='',
[60016]='',
[60017]='',
[60018]='',
[60019]='',
[60020]='',
[60021]='',
[60022]='',
[60023]='',
[60024]='',
[60025]='',
[60026]='',
[60027]='',
[60028]='',
[60029]='',
[60030]='',
[60031]='',
[60032]='',
[60033]='',
[60034]='',
[60035]='',
[60036]='',
[60037]='',
[60038]='',
[60039]='',
[60040]='',
[60041]='',
[60042]='',
[60043]='',
[60044]='',
[60045]='',
[60046]='',
[60047]='',
[60048]='',
[60049]='',
[60050]='',
[60051]='',
[60052]='',
[60053]='',
[60054]='',
[60055]='',
[60056]='',
[60057]='',
[60058]='',
[60059]='',
[60100]='',
[60101]='',
[60102]='',
[60103]='',
[60104]='',
[60105]='',
[60106]='',
[60107]='',
[60108]='',
[60109]='',
[60110]='',
[60111]='',
[60112]='',
[70002]='',
[70003]='',
[70004]='',
[73361]=[[يرجى تشكيل تشكيلة الفريق الأول]],
[73362]=[[يرجى تشكيل تشكيلة الفريق الثاني]],
[73363]=[[يرجى تشكيل تشكيلة الفريق الثالث]],
[73364]=[[يرجى إكمال تشكيل الفريق أولاً]],
[73370]=[[هذا البطل موجود بالفعل في فريق آخر]],
[80001]='',
[80002]='',
[80005]='',
[80006]='',
[80007]='',
[80008]='',
[80009]='',
[80010]='',
[80011]='',
[80012]='',
[80013]='',
[80014]='',
[80015]='',
[80016]='',
[80017]='',
[80018]='',
[80019]='',
[80020]='',
[80021]='',
[80022]='',
[80023]='',
[80024]='',
[80025]='',
[80026]='',
[80027]='',
[80029]='',
[80030]='',
[80032]='',
[80033]='',
[80034]='',
[80035]='',
[80036]='',
[80037]='',
[80038]='',
[80039]='',
[80040]='',
[80041]='',
[80042]='',
[80043]='',
[80044]='',
[80045]='',
[80046]='',
[80047]='',
[80048]='',
[80049]='',
[80050]='',
[80051]='',
[80052]='',
[80053]='',
[80054]='',
[80055]='',
[80056]='',
[80057]='',
[80058]='',
[80059]='',
[80060]='',
[80061]='',
[80062]='',
[80063]='',
[80064]='',
[80065]='',
[80066]='',
[80067]='',
[80068]='',
[80069]='',
[80070]='',
[80071]='',
[80072]='',
[80085]='',
[80086]='',
[80087]='',
[80088]='',
[80089]='',
[80090]='',
[80091]='',
[80092]='',
[80093]='',
[80094]='',
[80095]='',
[80096]='',
[80097]='',
[80098]='',
[80099]='',
[80100]='',
[80101]='',
[80102]='',
[80103]='',
[80104]='',
[80105]='',
[80106]='',
[80107]='',
[80108]='',
[80109]='',
[80110]='',
[80111]='',
[80112]='',
[80113]='',
[80114]='',
[80115]='',
[80116]='',
[80117]='',
[80118]='',
[80119]='',
[80120]='',
[80121]='',
[80122]='',
[80123]='',
[80124]='',
[80125]='',
[80126]='',
[80127]='',
[80128]='',
[80129]='',
[80130]='',
[80131]='',
[80132]='',
[80133]='',
[80134]='',
[80135]='',
[80136]='',
[80137]='',
[80138]='',
[80139]='',
[80140]='',
[80141]='',
[80142]='',
[80143]='',
[80144]='',
[80145]='',
[80146]='',
[80147]='',
[80148]='',
[80149]='',
[80150]='',
[80151]='',
[80152]='',
[80153]='',
[80154]='',
[80155]='',
[80156]='',
[80157]='',
[80158]='',
[80159]='',
[80160]='',
[80161]='',
[80162]='',
[80163]='',
[80164]='',
[80165]='',
[80166]='',
[80167]='',
[80168]='',
[80169]='',
[80170]='',
[80171]='',
[80172]='',
[80173]='',
[80174]='',
[80177]='',
[80178]='',
[80179]='',
[80180]='',
[80181]='',
[80182]='',
[80183]='',
[80184]='',
[80185]='',
[80186]='',
[80187]='',
[80188]='',
[80189]='',
[80190]='',
[80191]='',
[80192]='',
[80193]='',
[80194]='',
[80195]='',
[80300]='',
[80301]='',
[80302]='',
[80303]='',
[80304]='',
[80305]='',
[80306]='',
[80310]='',
[80311]='',
[80312]='',
[80313]='',
[80314]='',
[80315]='',
[80316]='',
[80317]='',
[80318]='',
[80319]='',
[80320]='',
[80321]='',
[80322]='',
[80323]='',
[80324]='',
[80325]='',
[80326]='',
[80327]='',
[80500]='',
[80501]='',
[80502]='',
[80503]='',
[80504]='',
[80505]='',
[80506]='',
[80507]='',
[80508]='',
[80509]='',
[80510]='',
[80511]='',
[80512]='',
[80513]='',
[80514]='',
[80515]='',
[80516]='',
[80517]='',
[80518]='',
[80519]='',
[80520]='',
[80521]='',
[80522]='',
[80523]='',
[80524]='',
[80525]='',
[80526]='',
[80527]='',
[80528]='',
[80529]='',
[80530]='',
[80531]='',
[80532]='',
[80533]='',
[80534]='',
[80535]='',
[80536]='',
[80537]='',
[80538]='',
[80539]='',
[80540]='',
[80541]='',
[80542]='',
[80543]='',
[80544]='',
[80545]='',
[80546]='',
[80547]='',
[80548]='',
[80549]='',
[80550]='',
[80632]='',
[80633]='',
[80634]='',
[80635]='',
[80636]='',
[80637]='',
[80638]='',
[80639]='',
[80640]='',
[80641]='',
[80642]='',
[80643]='',
[80644]='',
[80645]='',
[80646]='',
[80647]='',
[80648]='',
[80649]='',
[80650]='',
[80651]='',
[80652]='',
[80653]='',
[80654]='',
[80655]='',
[80656]='',
[80657]='',
[80658]='',
[80659]='',
[80660]='',
[80661]='',
[80662]='',
[80663]='',
[80664]='',
[80665]='',
[80805]='',
[80806]='',
[80807]='',
[80808]='',
[80900]='',
[80901]='',
[80902]='',
[80903]='',
[80904]='',
[80905]='',
[80906]='',
[80907]='',
[80908]='',
[80909]='',
[80910]='',
[80911]='',
[80912]='',
[80913]='',
[80914]='',
[80915]='',
[80916]='',
[80917]='',
[80918]='',
[80930]='',
[80931]='',
[80932]='',
[80933]='',
[80934]='',
[80935]='',
[80936]='',
[80937]='',
[80938]='',
[80939]=[[هل تحب "X-Clash"؟]],
[80940]=[[اضغط على نجمة لتقييمها في متجر التطبيقات.]],
[80941]=[[امنحنا تقييمًا!]],
[80942]=[[‎‫ليس الآن‎‬]],
[80943]=[[مراجعتك عامة وتتضمن اسم ملفك الشخصي على Google وصورتك]],
[80944]='X-Clash',
[80950]='',
[80951]='',
[80952]='',
[80953]='',
[80954]='',
[80955]='',
[80956]='',
[80957]='',
[80958]='',
[80959]='',
[80960]='',
[80961]='',
[80962]='',
[80963]='',
[80964]='',
[80965]='',
[80966]='',
[80967]='',
[80968]='',
[80969]='',
[80970]='',
[80971]='',
[80972]='',
[80973]='',
[80974]='',
[80975]='',
[80976]='',
[80977]='',
[80978]='',
[80979]='',
[80980]='',
[80981]='',
[80982]='',
[80983]='',
[80984]='',
[80985]='',
[80986]='',
[80987]='',
[80988]='',
[80989]='',
[80990]='',
[80991]='',
[80992]='',
[80993]='',
[80994]='',
[80995]='',
[80996]='',
[80997]='',
[80998]='',
[80999]='',
[81000]='',
[81001]='',
[81002]='',
[81003]='',
[81004]='',
[81005]='',
[81006]='',
[81007]='',
[81008]='',
[81009]='',
[81010]='',
[81011]='',
[81012]='',
[81013]='',
[81014]='',
[81015]='',
[81016]='',
[81017]='',
[81018]='',
[81019]='',
[81020]='',
[81021]='',
[81022]='',
[81023]='',
[81024]='',
[81025]='',
[81026]='',
[81027]='',
[81028]='',
[81029]='',
[81030]='',
[81031]='',
[81032]='',
[81033]='',
[81034]='',
[81035]='',
[81036]='',
[81037]='',
[81038]='',
[81039]='',
[81040]='',
[81041]='',
[81042]='',
[81043]='',
[81044]='',
[81045]='',
[81046]='',
[81047]='',
[81048]='',
[81049]='',
[81050]='',
[81051]='',
[81052]='',
[81053]='',
[81054]='',
[81055]='',
[81056]='',
[81057]='',
[81058]='',
[81059]='',
[81060]='',
[81061]='',
[81062]='',
[81063]='',
[81064]='',
[81065]='',
[81066]='',
[81067]='',
[81068]='',
[81069]='',
[81070]='',
[81071]='',
[81072]='',
[81073]='',
[81074]='',
[81075]='',
[81076]='',
[81077]='',
[81078]='',
[81079]='',
[81080]='',
[81081]='',
[81082]='',
[81083]='',
[81084]='',
[81085]='',
[81086]='',
[81087]='',
[81088]='',
[81089]='',
[81090]='',
[81091]='',
[81092]='',
[81093]='',
[81094]='',
[81095]='',
[81096]='',
[81097]='',
[81098]='',
[81099]='',
[81100]='',
[81101]='',
[81102]='',
[81103]='',
[81104]='',
[81105]='',
[81106]='',
[81107]='',
[81108]='',
[81109]='',
[81110]='',
[81111]='',
[81112]='',
[81113]='',
[81114]='',
[81115]='',
[81116]='',
[81117]='',
[81118]='',
[81119]='',
[81120]='',
[81121]='',
[81122]='',
[81123]='',
[81124]='',
[81125]='',
[81126]='',
[81127]='',
[81128]='',
[81129]='',
[81130]='',
[81131]='',
[81132]='',
[81133]='',
[81134]='',
[81135]='',
[81136]='',
[81137]='',
[81138]='',
[81139]='',
[81140]='',
[81141]='',
[81142]='',
[81143]='',
[81144]='',
[81145]='',
[81146]='',
[81147]='',
[81148]='',
[81149]='',
[81150]='',
[81151]='',
[81152]='',
[81153]='',
[81154]='',
[81155]='',
[81156]='',
[81157]='',
[81158]='',
[81159]='',
[81160]='',
[81161]='',
[81162]='',
[81163]='',
[81164]='',
[81165]='',
[81166]='',
[81167]='',
[81168]='',
[81169]='',
[81170]='',
[81171]='',
[81172]='',
[81173]='',
[81174]='',
[81175]='',
[81176]='',
[81177]='',
[81178]='',
[81179]='',
[81180]='',
[81181]='',
[81182]='',
[81183]='',
[81184]='',
[81185]='',
[81186]='',
[81187]='',
[81188]='',
[81189]='',
[81190]='',
[81191]='',
[81193]='',
[81194]='',
[81195]='',
[81197]='',
[81198]='',
[81199]='',
[81200]='',
[81201]='',
[81202]='',
[81203]='',
[81204]='',
[81205]='',
[81206]='',
[81207]='',
[81208]='',
[81209]='',
[81210]='',
[81211]='',
[81212]='',
[81213]='',
[81214]='',
[81215]='',
[81216]='',
[81217]='',
[81218]='',
[81219]='',
[81220]='',
[81221]='',
[81222]='',
[81223]='',
[81224]='',
[81225]='',
[81226]='',
[81227]='',
[81228]='',
[81229]='',
[81230]='',
[81231]='',
[81232]='',
[81233]='',
[81234]='',
[81235]='',
[81236]='',
[81237]='',
[81238]='',
[81239]='',
[81240]='',
[81241]='',
[81242]='',
[81243]='',
[81244]='',
[81245]='',
[81246]='',
[81247]='',
[81248]='',
[81249]='',
[81250]='',
[81251]='',
[81252]='',
[81253]='',
[81254]='',
[81255]='',
[81256]='',
[81257]='',
[81258]='',
[81259]='',
[81260]='',
[81261]='',
[81262]='',
[81263]='',
[81264]='',
[81265]='',
[81266]='',
[81267]='',
[81268]='',
[81269]='',
[81270]='',
[81271]='',
[81272]='',
[81273]='',
[81274]='',
[81275]='',
[81276]='',
[81277]='',
[81278]='',
[81279]='',
[81280]='',
[81281]='',
[81282]='',
[81283]='',
[81284]='',
[81285]='',
[81286]='',
[81287]='',
[81288]='',
[81289]='',
[81290]='',
[81291]='',
[81292]='',
[81293]='',
[81294]='',
[81295]='',
[81296]='',
[81297]='',
[81298]='',
[81299]='',
[81300]='',
[81301]='',
[81302]='',
[81303]='',
[81304]='',
[81305]='',
[81306]='',
[81307]='',
[81308]='',
[81309]='',
[81310]='',
[81311]='',
[81312]='',
[81313]='',
[81314]='',
[81315]='',
[81316]='',
[81317]='',
[81318]='',
[81319]='',
[81320]='',
[81321]='',
[81322]='',
[81323]='',
[81324]='',
[81325]='',
[81326]='',
[81327]='',
[81328]='',
[81329]='',
[81330]='',
[81331]='',
[81332]='',
[81333]='',
[81334]='',
[81335]='',
[81336]='',
[81337]='',
[81338]='',
[81339]='',
[81340]='',
[81341]='',
[81342]='',
[81343]='',
[81344]='',
[81345]='',
[81346]='',
[81347]='',
[81348]='',
[81349]='',
[81350]='',
[81351]='',
[81352]='',
[81353]='',
[81354]='',
[81355]='',
[81356]='',
[81358]='',
[81359]='',
[81360]='',
[81361]='',
[81362]='',
[81363]='',
[81364]='',
[81365]='',
[81366]='',
[81367]='',
[81368]='',
[81369]='',
[81370]='',
[81371]='',
[81372]='',
[81373]='',
[81374]='',
[81375]='',
[81376]='',
[81377]='',
[81378]='',
[81379]='',
[81380]='',
[81381]='',
[81382]='',
[81383]='',
[81384]='',
[81385]='',
[81386]='',
[81387]='',
[81388]='',
[81389]='',
[81390]='',
[81391]='',
[81392]='',
[81393]='',
[81394]='',
[81395]='',
[81396]='',
[81397]='',
[81398]='',
[81399]='',
[81400]='',
[81401]='',
[81402]='',
[81403]='',
[81404]='',
[81405]='',
[81406]='',
[81408]='',
[81409]='',
[81410]='',
[81411]='',
[81412]='',
[81413]='',
[81414]='',
[81415]='',
[81416]='',
[81417]='',
[81418]='',
[81419]='',
[81420]='',
[81421]='',
[81422]='',
[81423]='',
[81424]='',
[81425]='',
[81426]='',
[81427]='',
[81428]='',
[81429]='',
[81430]='',
[81431]='',
[81432]='',
[81433]='',
[81434]='',
[81435]='',
[81436]='',
[81437]='',
[81438]='',
[81439]='',
[81440]='',
[81441]='',
[81442]='',
[81443]='',
[81444]='',
[81445]='',
[81446]='',
[81447]='',
[81448]='',
[81449]='',
[81450]='',
[81451]='',
[81452]='',
[81453]='',
[81454]='',
[81455]='',
[81456]='',
[81457]='',
[81458]='',
[81459]='',
[81460]='',
[81461]='',
[81462]='',
[81463]='',
[81464]='',
[81465]='',
[81466]='',
[81467]='',
[81468]='',
[81469]='',
[81470]='',
[81471]='',
[81472]='',
[81473]='',
[81474]='',
[81475]='',
[81476]='',
[81477]='',
[81478]='',
[81479]='',
[81480]='',
[81481]='',
[81482]='',
[81483]='',
[81484]='',
[81485]='',
[81486]='',
[81487]='',
[81488]='',
[81489]='',
[81490]='',
[81491]='',
[81492]='',
[81493]='',
[81494]='',
[81495]='',
[81496]='',
[81497]='',
[81498]='',
[81499]='',
[81500]='',
[81501]='',
[81502]='',
[81503]='',
[81504]='',
[81505]='',
[81506]='',
[81507]='',
[81508]='',
[81509]='',
[81510]='',
[81511]='',
[81512]='',
[81513]='',
[81515]='',
[81516]='',
[81517]='',
[81518]='',
[81519]='',
[81520]='',
[81521]='',
[81522]='',
[81523]='',
[81524]='',
[81525]='',
[81526]='',
[81527]='',
[81528]='',
[81529]='',
[81530]='',
[81531]='',
[81532]='',
[81533]='',
[81534]='',
[81535]='',
[81536]='',
[81537]='',
[81538]='',
[81539]='',
[81540]='',
[81541]='',
[81542]='',
[81543]='',
[81545]='',
[81546]='',
[81547]='',
[81548]='',
[81549]='',
[81550]='',
[81551]='',
[81552]='',
[81553]='',
[81554]='',
[81555]='',
[81556]='',
[81557]='',
[81562]='',
[81563]='',
[81564]='',
[81565]='',
[81566]='',
[81567]='',
[81568]='',
[81569]='',
[81570]='',
[81571]='',
[81572]='',
[81573]='',
[81574]='',
[81575]='',
[81576]='',
[81577]='',
[81578]='',
[81579]='',
[81580]='',
[81581]='',
[81582]='',
[81583]='',
[81584]='',
[81585]='',
[81586]='',
[81587]='',
[81588]='',
[81589]='',
[81590]='',
[81593]='',
[81594]='',
[81595]='',
[81596]='',
[81597]='',
[81598]='',
[81599]='',
[81600]='',
[81601]='',
[81602]='',
[81603]='',
[81604]='',
[81605]='',
[81606]='',
[81607]='',
[81608]='',
[81609]='',
[81610]='',
[81611]='',
[81612]='',
[81613]='',
[81614]='',
[81615]='',
[81616]='',
[81617]='',
[81618]='',
[81619]='',
[81673]='',
[82000]='',
[82001]='',
[82002]='',
[82003]='',
[82004]='',
[82005]='',
[82006]='',
[82007]='',
[82008]='',
[82009]='',
[82010]='',
[82011]='',
[82012]='',
[82013]='',
[82014]='',
[82015]='',
[82017]='',
[83001]='',
[83002]='',
[83003]='',
[83004]='',
[83005]='',
[83006]='',
[83007]='',
[83008]='',
[83009]='',
[83010]='',
[83011]='',
[83012]='',
[83013]='',
[83014]='',
[83015]='',
[83016]='',
[83017]='',
[83018]='',
[83019]='',
[83020]='',
[83021]='',
[83022]='',
[83023]='',
[83024]='',
[83025]='',
[83026]='',
[83027]='',
[83028]='',
[83029]='',
[83030]='',
[83031]='',
[83032]='',
[83033]='',
[83034]='',
[83035]='',
[83036]='',
[83037]='',
[83038]='',
[83039]='',
[83040]='+',
[83041]='',
[83043]='',
[83044]='',
[83045]='',
[83046]='',
[83047]='',
[84000]='',
[84001]='',
[84002]='',
[84003]='',
[84004]='',
[84005]='',
[84006]='',
[84007]='',
[84008]='',
[84009]='',
[84010]='',
[84011]='',
[84012]='',
[84013]='',
[84014]='',
[84015]='',
[84016]='',
[84017]='',
[84018]='',
[84019]='',
[84020]=[[يمكن الحصول على حساب ملزم لأول مرة]],
[84021]=[[بعد ربط حسابك، يمكنك تسجيل الدخول على أجهزة أخرى في أي وقت وفي أي مكان لتجنب فقدان حسابك]],
[84022]=[[(مرتبط بالفعل)]],
[84023]=[[إنشاء حسابك]],
[84024]=[[الرجاء إدخال عنوان بريد إلكتروني صالح لإنشاء حساب بريدك الإلكتروني]],
[84025]=[[الرجاء إدخال عنوان بريدك الإلكتروني هنا...]],
[84026]='@gmail.com',
[84027]='يُقدِّم',
[84028]=[[التحقق من صندوق البريد]],
[84029]=[[لقد تم إرسال رمز التحقق إلى صندوق بريدك: %s]],
[84030]=[[الرجاء إدخال رمز التحقق أدناه لإكمال عملية التحقق]],
[84031]=[[تبديل الحساب]],
[84032]=[[تسجيل الدخول]],
[84033]=[[الخطوة الاخيرة!]],
[84034]=[[بريد إلكتروني]],
[84035]=[[الرجاء إدخال حساب بريد إلكتروني صالح!]],
[84036]=[[تم ربط البريد الإلكتروني بنجاح]],
[84037]=[[خطأ في رمز التحقق]],
[84038]=[[تغيير عنوان البريد الإلكتروني المرتبط]],
[84039]=[[عنوان البريد الإلكتروني الحالي:]],
[84040]=[[هل تريد تغيير عنوان البريد الإلكتروني الحالي؟]],
[84041]=[[يرجى الملاحظة: في كل مرة تُغيّر فيها بريدك الإلكتروني، ستكون هناك فترة سماح مدتها 90 يومًا. لا يُمكنك إجراء العملية التالية إلا بعد انتهاء هذه الفترة.]],
[84300]='',
[84301]='',
[84302]='',
[84303]='',
[84304]='',
[84305]='',
[84306]='',
[84307]='',
[84308]='',
[84309]='',
[84310]='',
[84311]='',
[84312]='',
[84313]='',
[84314]='',
[84315]='',
[84316]='',
[84317]='',
[84318]='',
[84319]='',
[84320]='',
[84321]='',
[84322]='',
[84600]='',
[84601]='',
[84602]='',
[84610]='',
[84611]='',
[84612]='',
[84613]='',
[84614]='',
[84615]='',
[84616]='',
[84617]='',
[84700]='',
[84701]='',
[84702]='',
[84703]='',
[84704]='',
[84705]='',
[84706]='',
[84707]='',
[84708]='',
[84709]='',
[84710]='',
[84711]='',
[84712]='',
[84713]='',
[84714]='',
[84715]='',
[84801]='',
[84802]='',
[84803]='',
[84804]='',
[84805]='',
[84806]='',
[84807]='',
[84808]='',
[84809]='',
[84810]='',
[84811]='',
[84812]='',
[84813]='',
[84900]='',
[84901]='',
[84902]='',
[84903]='',
[84904]='',
[84905]='',
[84906]='',
[84907]='',
[84908]='',
[84909]='',
[84910]='',
[84911]='',
[84912]='',
[84920]='',
[84921]='',
[85001]='',
[85002]='',
[85003]='',
[86001]='',
[86002]='',
[86003]='',
[86004]='',
[86005]='',
[86006]='',
[86007]='',
[86008]='',
[86009]='',
[86010]='',
[86011]='',
[86012]='',
[86013]='',
[86014]='',
[86015]='',
[86016]='',
[86017]='',
[86018]='',
[86019]='',
[86020]='',
[86021]='',
[86022]='',
[86023]='',
[86024]='',
[86025]='',
[86026]='',
[86027]='',
[86028]='',
[86029]='',
[86030]='',
[86031]='',
[86032]='',
[86033]='',
[86034]='',
[86035]='',
[86036]='',
[86037]='',
[86038]='',
[86039]='',
[86040]='',
[86041]='',
[86042]='',
[86043]='',
[86044]='',
[86045]='',
[86046]='',
[86047]='',
[86048]='',
[86049]='',
[86050]='',
[86051]='',
[86052]='',
[86053]='',
[86054]='',
[86055]='',
[86056]='',
[86057]='',
[86058]='',
[86059]='',
[86060]='',
[86061]='',
[86062]='',
[86063]='',
[86064]='',
[86065]='',
[86066]='',
[86067]='',
[86068]='',
[86069]='',
[86070]='',
[86071]='',
[86072]='',
[86073]='',
[86074]='',
[86075]='',
[86076]='',
[86077]='',
[86078]='',
[86079]='',
[86080]='',
[86081]='',
[86082]='',
[86083]='',
[86084]='',
[86085]='',
[86086]='',
[86087]='',
[86088]='',
[86089]='',
[86090]='',
[86091]='',
[86092]='',
[86093]='',
[86094]='',
[86095]='',
[86096]='',
[86097]='',
[86098]='',
[86099]='',
[86100]='',
[86101]='',
[86102]='',
[86103]='',
[86104]='',
[86105]='',
[86106]='',
[86107]='',
[86108]='',
[86109]='',
[86110]='',
[86111]='',
[86112]='',
[86113]='',
[86114]='',
[86115]='',
[86116]='',
[86117]='',
[86118]='',
[86119]='',
[86120]='',
[86121]='',
[86122]='',
[86123]='',
[86124]='',
[86125]='',
[86126]='',
[86127]='',
[86128]='',
[86129]='',
[86130]='',
[86131]='',
[86132]='',
[86133]='',
[86134]='',
[86135]='',
[86136]='',
[86137]='',
[86138]='',
[86139]='',
[86140]='',
[86141]='',
[86142]='',
[86143]='',
[86144]='',
[86145]='',
[86146]='',
[86147]='',
[86148]='',
[86149]='',
[86150]='',
[86151]='',
[86152]='',
[86153]='',
[86154]='',
[86155]='',
[86156]='',
[86157]='',
[86158]='',
[86159]='',
[86160]='',
[86161]='',
[86162]='',
[86163]='',
[86164]='',
[86165]='',
[86166]='',
[86167]='',
[86168]='',
[86169]='',
[86170]='',
[86171]='',
[86172]='',
[86173]='',
[86174]='',
[86176]='',
[86177]='',
[86178]='',
[86179]='',
[86180]='',
[86181]='',
[86182]='',
[86183]='',
[86184]='',
[86185]='',
[86186]='',
[86187]='',
[86188]='',
[86189]='',
[86190]='',
[86191]='',
[86192]='',
[86193]='',
[86194]='',
[86195]='',
[86196]='',
[86197]='',
[86198]='',
[86199]='',
[86200]='',
[86201]='',
[86202]='',
[86203]='',
[86204]='',
[86205]='',
[86206]='',
[86207]='',
[86208]='',
[86209]='',
[86210]='',
[86211]='',
[86212]='',
[86213]='',
[86214]='',
[86215]='',
[86216]='',
[86217]='',
[86218]='',
[86219]='',
[86220]='',
[86221]='',
[86222]='',
[86223]='',
[86224]='',
[86225]='',
[86226]='',
[86227]='',
[86228]='',
[86229]='',
[86230]='',
[86231]='',
[86232]='',
[86233]='',
[86234]='',
[86235]='',
[86236]='',
[86237]='',
[86238]='',
[86239]='',
[86240]='',
[86241]='',
[86242]='',
[86243]='',
[86244]='',
[86245]='',
[86246]='',
[86247]='',
[86248]='',
[86249]='',
[86250]='',
[86251]='',
[86252]='',
[86253]='',
[86254]='',
[86255]='',
[86256]='',
[86257]='',
[86258]='',
[86260]='',
[86261]='',
[86262]='',
[86271]='',
[86272]='',
[86273]='',
[86274]='',
[86275]='',
[86276]='',
[86277]='',
[86281]='',
[86285]='',
[87001]='',
[87002]='',
[87003]='',
[87004]='',
[87005]='',
[87006]='',
[87007]='',
[87008]='',
[87009]='',
[87010]='',
[87011]='',
[87012]='',
[87013]='',
[87014]='',
[87015]='',
[87016]='',
[87017]='',
[87018]='',
[87019]='',
[87020]='',
[87021]='',
[87022]='',
[87023]='',
[87024]='',
[87025]='',
[87026]='',
[87027]='',
[87028]='',
[87029]='',
[87030]='',
[87031]='',
[87032]='',
[87033]='',
[87034]='',
[87035]='',
[87036]='',
[87037]='',
[87038]='',
[87039]='',
[87040]='',
[87041]='',
[87042]='',
[87043]='',
[87044]='',
[87045]='',
[87046]='',
[87047]='',
[87048]='',
[87049]='',
[87050]='',
[87051]='',
[87052]='',
[87053]='',
[87054]='',
[87055]='',
[87056]='',
[87057]='',
[87058]='',
[87059]='',
[87060]='',
[87061]='',
[87062]='',
[87063]='',
[87064]='',
[87065]='',
[87066]='',
[87067]='',
[87068]='',
[87069]='',
[87070]='',
[87071]='',
[87072]='',
[87073]='',
[87074]='',
[87075]='',
[87076]='',
[87077]='',
[87078]='',
[87079]='',
[87080]='',
[87081]='',
[87082]='',
[87083]='',
[87084]='',
[87085]='',
[87086]='',
[87087]='',
[87088]='',
[87089]='',
[87090]='',
[87091]='',
[87092]='',
[87093]='',
[87094]='',
[87095]='',
[87096]='',
[87097]='',
[87098]='',
[87099]='',
[87100]='',
[87101]='',
[87102]='',
[87103]='',
[87104]='',
[87105]='',
[87106]='',
[87107]='',
[87108]='',
[87109]='',
[87110]='',
[87111]='',
[87112]='',
[87113]='',
[87114]='',
[87115]='',
[87116]='',
[87117]='',
[87118]='',
[87119]='',
[87120]='',
[87121]='',
[87122]='',
[87123]='',
[87124]='',
[87125]='',
[87126]='',
[87127]='',
[87128]='',
[87129]='',
[87130]='',
[87131]='',
[87132]='',
[87133]='',
[87134]='',
[87135]='',
[87136]='',
[87137]='',
[87138]='',
[87139]='',
[87140]='',
[87141]='',
[87142]='',
[87143]='',
[87144]='',
[87145]='',
[87146]='',
[87147]='',
[87148]='',
[87149]='',
[87150]='',
[87151]='',
[87152]='',
[87153]='',
[87154]='',
[87155]='',
[87156]='',
[87157]='',
[87158]='',
[87159]='',
[87160]='',
[87161]='',
[87162]='',
[87163]='',
[87164]='',
[87165]='',
[87166]='',
[87167]='',
[87168]='',
[87169]='',
[87170]='',
[87171]='',
[87172]='',
[87173]='',
[87174]='',
[87175]='',
[87176]='',
[87177]='',
[87178]='',
[87179]='',
[87180]='',
[87181]='',
[87182]='',
[87183]='',
[87184]='',
[87185]='',
[87186]='',
[87187]='',
[87188]='',
[87189]='',
[87190]='',
[87191]='',
[87192]='',
[87193]='',
[87194]='',
[87195]='',
[87196]='',
[87197]='',
[87198]='',
[87199]='',
[87200]='',
[87201]='',
[87202]='',
[87203]='',
[87204]='',
[87205]='',
[87206]='',
[87207]='',
[87208]='',
[87209]='',
[87210]='',
[87211]='',
[87212]='',
[87213]='',
[87214]='',
[87215]='',
[87216]='',
[87217]='',
[87218]='',
[87219]='',
[87220]='',
[87221]='',
[87222]='',
[87223]='',
[87224]='',
[87225]='',
[87226]='',
[87227]='',
[87228]='',
[87229]='',
[87230]='',
[87231]='',
[87232]='',
[87233]='',
[87234]='',
[87235]='',
[87236]='',
[87237]='',
[87238]='',
[87239]='',
[87240]='',
[87241]='',
[87242]='',
[87243]='',
[87244]='',
[87245]='',
[87246]='',
[87247]='',
[87248]='',
[87249]='',
[87250]='',
[87251]='',
[87252]='',
[87253]='',
[87254]='',
[87255]='',
[87256]='',
[87257]='',
[87258]='',
[87259]='',
[87300]='',
[87301]='',
[87302]='',
[87303]='',
[87304]='',
[87305]='',
[87306]='',
[87307]='',
[87308]='',
[87309]='',
[87310]='',
[87311]='',
[87312]='',
[87313]='',
[87314]='',
[87315]='',
[87316]='',
[87317]='',
[87318]='',
[87319]='',
[87320]='',
[87321]='',
[87322]='',
[87327]='',
[87328]='',
[87329]='',
[87330]='',
[87331]='',
[87332]='',
[87333]='',
[87334]='',
[87335]='',
[87336]='',
[87337]='',
[87338]='',
[87339]='',
[87340]='',
[87341]='',
[87342]='',
[87343]='',
[87344]='',
[87345]='',
[87346]='',
[87347]='',
[87348]='',
[87349]='',
[87350]='',
[87351]='',
[87352]='',
[87353]='',
[87354]='',
[87355]='',
[87356]='',
[87357]='',
[87358]='',
[87359]='',
[87360]='',
[87361]='',
[87362]='',
[87363]='',
[87364]='',
[87365]='',
[87366]='',
[87367]='',
[87368]='',
[87369]='',
[87370]='',
[87371]='',
[87372]='',
[87373]='',
[87374]='',
[87375]='',
[87376]='',
[87377]='',
[87378]='',
[87379]='',
[87380]='',
[87381]='',
[87382]='',
[87383]='',
[87384]='',
[87385]='',
[87386]='',
[87387]='',
[87388]='',
[87389]='',
[87390]='',
[87391]='',
[87392]='',
[87393]='',
[87394]='',
[87395]='',
[87396]='',
[87397]='',
[87398]='',
[87399]='',
[87400]='',
[87401]='',
[87402]='',
[87403]='',
[87404]='',
[87406]='',
[87408]='',
[87409]='',
[87411]='',
[87412]='',
[87413]='',
[87416]='',
[87417]='',
[87426]='',
[87427]='',
[87428]='',
[87429]='',
[87430]='',
[87431]='',
[87435]='',
[87440]='',
[87450]='',
[87451]='',
[87452]='',
[88000]='',
[88001]='',
[88002]='',
[88003]='',
[88004]='',
[88005]='',
[88006]='',
[88007]='',
[88008]='',
[88009]='',
[88010]='',
[88011]='',
[88012]='',
[88013]='',
[88014]='',
[88015]='',
[88016]='',
[88017]='',
[88018]='',
[88019]='',
[88020]='',
[88021]='',
[88022]='',
[88023]='',
[88024]='',
[88025]='',
[88026]='',
[88027]='',
[88028]='',
[88029]='',
[88030]='',
[88031]='',
[88032]='',
[88033]='',
[88034]='',
[88035]='',
[88036]='',
[88037]='',
[88038]='',
[88039]='',
[88040]='',
[88041]='',
[88042]='',
[88043]='',
[88044]='',
[88045]='',
[88046]='',
[88047]='',
[88049]='',
[88050]='',
[88052]='',
[88053]='',
[88054]='',
[88055]='',
[88056]='',
[88057]='',
[88058]='',
[88059]='',
[88060]='',
[88062]='',
[88063]='',
[88065]='',
[88066]='',
[88068]='',
[88069]='',
[88070]='',
[88071]='',
[88072]='',
[88073]='',
[88074]='',
[88076]='',
[88078]='',
[88079]='',
[88080]='',
[88081]='',
[88082]='',
[88083]='',
[88084]='',
[88085]='',
[88086]='',
[88087]='',
[88088]='',
[88089]='',
[88090]='',
[88091]='',
[88092]='',
[88093]='',
[88094]='',
[88095]='',
[88096]='',
[88097]='',
[88098]='',
[88099]='',
[88100]='',
[88101]='',
[88301]='',
[88302]='',
[88303]='',
[88304]='',
[88305]='',
[88306]='',
[88307]='',
[88308]='',
[88309]='',
[88310]='',
[88311]='',
[88312]='',
[88313]='',
[88314]='',
[88315]='',
[88316]='',
[88317]='',
[88318]='',
[88319]='',
[88320]='',
[88321]='',
[88322]='',
[88323]='',
[88324]='',
[88325]='',
[88326]='',
[88327]='',
[88328]='',
[88329]='',
[88330]='',
[88331]='',
[88332]='',
[88333]='',
[88334]='',
[88335]='',
[88336]='',
[88337]='',
[88338]='',
[88339]='',
[88340]='',
[88341]='',
[88342]='',
[88343]='',
[88344]='',
[88345]='',
[88346]='',
[88347]='',
[88349]='',
[88350]='',
[88352]='',
[88353]='',
[88354]='',
[88355]='',
[88356]='',
[88357]='',
[88358]='',
[88359]='',
[88360]='',
[88362]='',
[88363]='',
[88365]='',
[88366]='',
[88368]='',
[88369]='',
[88370]='',
[88371]='',
[88372]='',
[88373]='',
[88374]='',
[88376]='',
[88378]='',
[88379]='',
[88380]='',
[88381]='',
[88382]='',
[88383]='',
[88384]='',
[88385]='',
[88386]='',
[88387]='',
[88388]='',
[88389]='',
[88390]='',
[88391]='',
[88392]='',
[88393]='',
[88394]='',
[88395]='',
[88396]='',
[88397]='',
[88398]='',
[88399]='',
[88400]='',
[88401]='',
[88402]='',
[88403]='',
[88404]='',
[88405]='',
[88406]='',
[88407]='',
[88502]='',
[88503]='',
[88504]='',
[88505]='',
[88506]='',
[88507]='',
[88508]='',
[88509]='',
[88510]='',
[88511]='',
[88512]='',
[88513]='',
[88514]='',
[88515]='',
[88516]='',
[88517]='',
[88518]='',
[88519]='',
[88520]='',
[88521]='',
[88522]='',
[88523]='',
[88524]='',
[88525]='',
[88526]='',
[88527]='',
[88528]='',
[88529]='',
[88530]='',
[88531]='',
[88532]='',
[88533]='',
[88534]='',
[88535]='',
[88536]='',
[88537]='',
[88538]='',
[88539]='',
[88540]='',
[88541]='',
[88542]='',
[88543]='',
[88544]='',
[88545]='',
[88546]='',
[88547]='',
[88549]='',
[88550]='',
[88552]='',
[88553]='',
[88554]='',
[88555]='',
[88556]='',
[88558]='',
[88559]='',
[88560]='',
[88562]='',
[88563]='',
[88565]='',
[88566]='',
[88568]='',
[88569]='',
[88570]='',
[88571]='',
[88572]='',
[88573]='',
[88574]='',
[88576]='',
[88578]='',
[88579]='',
[88580]='',
[88581]='',
[88582]='',
[88583]='',
[88584]='',
[88585]='',
[88586]='',
[88587]='',
[88588]='',
[88589]='',
[88592]='',
[88593]='',
[88594]='',
[88595]='',
[88596]='',
[88597]='',
[88598]='',
[88599]='',
[88600]='',
[88601]='',
[88701]='',
[88702]='',
[88703]='',
[88704]='',
[88705]='',
[88706]='',
[88707]='',
[88708]='',
[88709]='',
[88710]='',
[88711]='',
[88712]='',
[88713]='',
[88714]='',
[88715]='',
[88716]='',
[88717]='',
[88718]='',
[88719]='',
[88720]='',
[88722]='',
[88723]='',
[88724]='',
[88725]='',
[88726]='',
[88727]='',
[88728]='',
[88729]='',
[88730]='',
[88731]='',
[88732]='',
[88733]='',
[88734]='',
[88735]='',
[88736]='',
[88737]='',
[88738]='',
[88739]='',
[88740]='',
[88741]='',
[88742]='',
[88743]='',
[88744]='',
[88745]='',
[88746]='',
[88747]='',
[88749]='',
[88750]='',
[88752]='',
[88753]='',
[88754]='',
[88755]='',
[88756]='',
[88757]='',
[88758]='',
[88759]='',
[88760]='',
[88762]='',
[88763]='',
[88765]='',
[88766]='',
[88768]='',
[88769]='',
[88770]='',
[88771]='',
[88772]='',
[88773]='',
[88774]='',
[88776]='',
[88778]='',
[88779]='',
[88780]='',
[88781]='',
[88782]='',
[88783]='',
[88784]='',
[88785]='',
[88786]='',
[88787]='',
[88788]='',
[88789]='',
[88790]='',
[88791]='',
[88792]='',
[88793]='',
[88794]='',
[88795]='',
[88796]='',
[88797]='',
[88798]='',
[88799]='',
[88800]='',
[88801]='',
[88901]='',
[88902]='',
[88903]='',
[88904]='',
[88905]='',
[88906]='',
[88907]='',
[88908]='',
[88909]='',
[88910]='',
[88911]='',
[88912]='',
[88913]='',
[88914]='',
[88915]='',
[88916]='',
[88917]='',
[88918]='',
[88919]='',
[88920]='',
[88921]='',
[88922]='',
[88923]='',
[88924]='',
[88925]='',
[88926]='',
[88927]='',
[88928]='',
[88929]='',
[88930]='',
[88931]='',
[88932]='',
[88933]='',
[88934]='',
[88935]='',
[88936]='',
[88937]='',
[88938]='',
[88939]='',
[88940]='',
[88941]='',
[88942]='',
[88943]='',
[88944]='',
[88945]='',
[88946]='',
[88947]='',
[88948]='',
[88949]='',
[88950]='',
[88951]='',
[88952]='',
[88953]='',
[88954]='',
[88955]='',
[88956]='',
[88957]='',
[88958]='',
[88959]='',
[88960]='',
[88961]='',
[88962]='',
[88963]='',
[88964]='',
[88965]='',
[88966]='',
[88967]='',
[88968]='',
[88969]='',
[88970]='',
[88971]='',
[88972]='',
[88973]='',
[88974]='',
[88975]='',
[88976]='',
[88977]='',
[88978]='',
[88979]='',
[88980]='',
[88981]='',
[89101]='',
[89102]='',
[89103]='',
[89104]='',
[89105]='',
[89106]='',
[89107]='',
[89108]='',
[89109]='',
[89110]='',
[89111]='',
[89112]='',
[89113]='',
[89114]='',
[89115]='',
[89116]='',
[89117]='',
[89118]='',
[89119]='',
[89120]='',
[89121]='',
[89122]='',
[89123]='',
[89124]='',
[89125]='',
[89126]='',
[89127]='',
[89128]='',
[89129]='',
[89130]='',
[89131]='',
[89132]='',
[89133]='',
[89134]='',
[89135]='',
[89136]='',
[89137]='',
[89138]='',
[89139]='',
[89140]='',
[89141]='',
[89142]='',
[89143]='',
[89144]='',
[89145]='',
[89146]='',
[89147]='',
[89148]='',
[89149]='',
[89150]='',
[89151]='',
[89152]='',
[89153]='',
[89154]='',
[89155]='',
[89156]='',
[89157]='',
[89158]='',
[89159]='',
[89160]='',
[89161]='',
[89162]='',
[89163]='',
[89164]='',
[89165]='',
[89166]='',
[89167]='',
[89168]='',
[89169]='',
[89170]='',
[89171]='',
[89172]='',
[89173]='',
[89174]='',
[89175]='',
[89176]='',
[89177]='',
[89178]='',
[89179]='',
[89180]='',
[89181]='',
[89182]='',
[89183]='',
[89184]='',
[89185]='',
[89186]='',
[89187]='',
[89188]='',
[89189]='',
[89190]='',
[89191]='',
[89192]='',
[89193]='',
[89194]='',
[89195]='',
[89196]='',
[89201]='',
[89202]='',
[89203]='',
[89204]='',
[89205]='',
[89206]='',
[89207]='',
[89208]='',
[89209]='',
[89210]='',
[89211]='',
[89212]='',
[89213]='',
[91001]='',
[91002]='',
[91003]='',
[91004]='',
[91005]='',
[91006]='',
[91007]='',
[91008]='',
[91009]='',
[91010]='',
[91011]='',
[91012]='',
[91013]='',
[91014]='',
[91015]='',
[91016]='',
[91017]='',
[91018]='',
[91019]='',
[91020]='',
[91021]='',
[91022]='',
[91023]='',
[91024]='',
[91025]='',
[91026]='',
[91027]='',
[91028]='',
[91029]='',
[91030]='',
[91031]='',
[91033]='',
[91034]='',
[91035]='',
[91036]='',
[91042]='',
[91043]='',
[91044]='',
[91368]='',
[91369]='',
[91370]='',
[91371]='',
[91372]='',
[91373]='',
[91374]='',
[91375]='',
[91376]='',
[91377]='',
[91378]='',
[91379]='',
[92000]='',
[92001]='',
[92002]='',
[92003]='',
[92004]='',
[92005]='',
[95000]='',
[95001]='',
[95002]='',
[95003]='',
[95004]='',
[95005]='',
[95006]='',
[95007]='',
[95008]='',
[95009]='',
[95010]='',
[95011]='',
[95012]='',
[95013]='',
[95014]='',
[95015]='',
[95016]='',
[95017]='',
[95018]='',
[95019]='',
[95020]='',
[95021]='',
[95022]='',
[95023]='',
[95024]='',
[95025]='',
[95026]='',
[95027]='',
[95028]='',
[95029]='',
[95030]='',
[95031]='',
[95032]='',
[95033]='',
[95034]='',
[95035]='',
[95036]='',
[95037]='',
[95038]='',
[95039]='',
[95040]='',
[95041]='',
[95042]='',
[95043]='',
[95044]='',
[95045]='',
[95046]='',
[95047]='',
[95048]='',
[95049]='',
[95050]='',
[95996]='',
[95997]='',
[95998]='',
[95999]='',
[100000]='',
[100001]='',
[100002]='',
[100003]='',
[100004]='',
[100005]='',
[100006]='',
[100007]='',
[100008]='',
[100009]='',
[100010]='',
[100012]='',
[100013]='',
[100014]='',
[100015]='',
[100016]='',
[100017]='',
[100018]='',
[100019]='',
[100020]='',
[100021]='',
[100022]='',
[100023]='',
[100026]='',
[100029]='',
[100030]='',
[100031]='',
[100032]='',
[100033]='',
[100034]='',
[100035]='',
[100036]='',
[100037]='',
[100038]='',
[100039]='',
[100070]='',
[100071]='',
[100072]='',
[100073]='',
[100141]='',
[100142]='',
[100146]='',
[100200]='',
[100201]='',
[100202]='',
[100203]='',
[100204]='',
[100205]='',
[100206]='',
[100207]='',
[100208]='',
[100209]='',
[100214]='',
[100215]='',
[100216]='',
[100217]='',
[100219]='',
[100220]='',
[100230]='',
[100231]='',
[100232]='',
[100233]='',
[100234]='',
[100235]='',
[100236]='',
[100237]='',
[100240]='',
[100241]='',
[100242]='',
[100245]='',
[100246]='',
[100250]='',
[100300]='',
[100301]='',
[100302]='',
[100303]='',
[100304]='',
[100305]='',
[100306]='',
[100307]='',
[100402]='',
[100403]='',
[100404]='',
[100405]='',
[100406]='',
[100407]='',
[100408]='',
[100409]='',
[100410]='',
[100411]='',
[100450]='',
[100451]='',
[100452]='',
[100453]='',
[100454]='',
[100455]='',
[100456]='',
[100457]='',
[100458]='',
[100459]='',
[100460]='',
[100461]='',
[100462]='',
[100480]='',
[100481]='',
[100482]='',
[100483]='',
[100484]='',
[100485]='',
[100486]='',
[100487]='',
[100490]='',
[100491]='',
[100492]='',
[100493]='',
[100500]='',
[100501]='',
[100502]='',
[100503]='',
[100504]='',
[100505]='',
[100506]='',
[100507]='',
[100508]='',
[100550]='',
[100551]='',
[100552]='',
[100553]='',
[100554]='',
[100555]='',
[100556]='',
[100557]='',
[100558]='',
[100559]='',
[100560]='',
[100561]='',
[100562]='',
[100563]='',
[100564]='',
[100565]='',
[100566]='',
[100567]='',
[100568]='',
[100569]='',
[100570]='',
[100571]='',
[100578]=[[تم الوصول إلى الحد الأقصى لعدد التذاكر التي تم شراؤها في ذلك اليوم]],
[100600]='',
[100601]='',
[100602]='',
[100603]='',
[100604]='',
[100605]='',
[100606]='',
[100607]='',
[100608]='',
[100609]='',
[100610]='',
[100611]='',
[100612]='',
[100613]='',
[100614]='',
[100615]='',
[100616]='',
[100617]='',
[100630]='',
[100631]='',
[100632]='',
[100633]='',
[100634]='',
[100635]='',
[100640]='',
[100641]='',
[100642]='',
[100643]='',
[100644]='',
[100700]='',
[100701]='',
[100702]='',
[100703]='',
[100704]='',
[100705]='',
[100706]='',
[100707]='',
[100708]='',
[100709]='',
[100801]='',
[100802]='',
[100803]='',
[100804]='',
[100805]='',
[100806]='',
[100807]='',
[100808]='',
[100809]='',
[100820]='',
[100821]='',
[100822]='',
[100823]='',
[100826]='',
[100829]='',
[100830]='',
[100831]='',
[100832]='',
[100833]='',
[100834]='',
[100835]='',
[100901]='',
[100902]='',
[100903]='',
[100904]='',
[100905]='',
[100906]='',
[100907]='',
[100908]='',
[100910]='',
[100911]='',
[100912]='',
[100913]='',
[100914]='',
[100915]='',
[100916]='',
[100917]='',
[100970]='',
[100971]='',
[100972]='',
[100973]='',
[100974]='',
[100975]='',
[100976]='',
[100977]='',
[100978]='',
[100979]='',
[100980]='',
[100981]='',
[100982]='',
[100987]='',
[100988]='',
[100989]='',
[100990]='',
[100991]='',
[100992]='',
[100993]='',
[101100]='',
[101101]='',
[101102]='',
[101103]='',
[101104]='',
[101105]='',
[101106]='',
[101107]='',
[101108]='',
[101109]='',
[101110]='',
[101112]='',
[101113]='',
[101114]='',
[101115]='',
[101116]='',
[101117]='',
[101118]='',
[101119]='',
[101120]='',
[101121]='',
[101122]='',
[101123]='',
[101124]='',
[101125]='',
[101126]='',
[101127]='',
[101128]='',
[101129]='',
[101130]='',
[101131]='',
[101132]='',
[101133]='',
[101134]='',
[101135]='',
[101136]='',
[101137]='',
[101138]='',
[101139]='',
[101140]='',
[101141]='',
[101142]='',
[101143]='',
[101144]='',
[101145]='',
[101146]='',
[101147]='',
[101148]='',
[101149]='',
[101150]='',
[101151]='',
[101152]='',
[101153]='',
[101154]='',
[101155]='',
[101156]='',
[101157]='',
[101158]='',
[101159]='',
[101160]='',
[101161]='',
[101162]='',
[101164]='',
[101165]='',
[101167]='',
[101168]='',
[101200]='',
[101201]='',
[101202]='',
[101203]='',
[101230]='',
[101231]='',
[101232]='',
[101233]='',
[101234]='',
[101303]='',
[101304]='',
[101305]='',
[101401]='',
[101402]='',
[101403]='',
[101404]='',
[101405]='',
[101406]='',
[101452]='',
[101500]='',
[101501]='',
[101502]='',
[101503]='',
[101504]='',
[101505]='',
[101506]='',
[101507]='',
[101508]='',
[101509]='',
[101510]='',
[101511]='',
[101512]='',
[101513]='',
[101514]='',
[101515]='',
[101516]='',
[101517]='',
[101550]='',
[101600]='',
[101601]='',
[101602]='',
[101603]='',
[101604]='',
[101605]='',
[101606]='',
[101607]='',
[101608]='',
[101609]='',
[101610]='',
[101611]='',
[101612]='',
[101613]='',
[101614]='',
[101615]='',
[101616]='',
[101617]='',
[101618]='',
[101619]='',
[101620]='',
[101621]='',
[101624]='',
[101700]='',
[101701]='',
[101702]='',
[101750]='',
[101751]='',
[101752]='',
[101753]='',
[101754]='',
[101755]='',
[101756]='',
[101757]='',
[101758]='',
[101759]='',
[101760]='',
[101761]='',
[101762]='',
[101763]='',
[101764]='',
[101765]='',
[101766]='',
[101768]='',
[101780]='',
[101781]='',
[101782]='',
[101783]='',
[101784]='',
[101801]='',
[101802]='',
[101821]='',
[101822]='',
[101823]='',
[101824]='',
[101825]='',
[101841]='',
[101842]='',
[101843]='',
[101844]='',
[101845]='',
[101846]='',
[101861]=[[أنت غير مؤهل حاليًا للتفعيل]],
[101862]=[[رقم البطاقة غير صالح أو تم حظره.]],
[101863]=[[مرحباً! رقم البطاقة هذا غير صالح أو تم استخدامه.]],
[101864]=[[مرحباً! رمز التفعيل هذا غير صالح أو منتهي الصلاحية.]],
[101865]=[[مرحباً، منطقة لعبتك لا تسمح بتفعيل هذه الحزمة]],
[101866]=[[مرحبًا، لم يتم تفعيل رمز التفعيل بعد.]],
[101867]=[[مرحبًا، لقد انتهت صلاحية رمز التفعيل.]],
[101868]=[[لم يبدأ وقت جمع رمز التفعيل]],
[101869]=[[لا يمكن لحسابك تفعيل رمز تفعيل القناة المحددة!]],
[101870]=[[وصل عدد المجموعات اليوم إلى الحد الأقصى. يُرجى المحاولة مجددًا غدًا.]],
[101871]=[[تم التنشيط بنجاح بالفعل، لا حاجة لإعادة التنشيط]],
[101881]='',
[101882]='',
[101883]='',
[101901]='',
[101902]='',
[101903]='',
[101904]='',
[101905]='',
[101906]='',
[101911]='',
[101912]='',
[101913]='',
[101914]='',
[101931]='',
[101932]='',
[101933]='',
[101934]='',
[101935]='',
[101936]='',
[101937]='',
[101938]='',
[101939]='',
[101940]='',
[101941]='',
[101942]='',
[101943]='',
[101944]='',
[101945]='',
[101946]='',
[101947]='',
[101948]='',
[101949]='',
[101950]='',
[101951]='',
[101952]='',
[101953]='',
[101954]='',
[101955]='',
[101956]='',
[101957]='',
[101958]='',
[102050]='',
[102051]='',
[102052]='',
[102053]='',
[102054]='',
[102055]='',
[102071]='',
[102072]='',
[102073]='',
[102074]='',
[102075]='',
[102076]='',
[102077]='',
[102078]='',
[102079]='',
[102080]='',
[102081]='',
[102082]='',
[102083]='',
[102084]='',
[102085]='',
[102086]='',
[102087]='',
[102088]='',
[102100]='',
[102101]='',
[102102]='',
[102103]='',
[102104]='',
[102105]='',
[102106]='',
[102107]='',
[102108]='',
[102109]='',
[102110]='',
[102111]='',
[102131]='',
[102132]='',
[102133]='',
[102134]='',
[102135]='',
[102136]='',
[102137]='',
[102138]='',
[102139]='',
[102140]='',
[102141]='',
[102142]='',
[102143]='',
[102144]='',
[102145]='',
[102146]='',
[102147]='',
[102171]='',
[102172]='',
[102173]='',
[102174]='',
[102175]='',
[102176]='',
[102177]='',
[102178]='',
[102179]='',
[102180]='',
[102181]='',
[102182]='',
[102183]='',
[102184]='',
[102185]='',
[102186]='',
[102187]='',
[102188]='',
[102189]='',
[102190]='',
[102191]='',
[102192]='',
[102193]='',
[102194]='',
[102195]='',
[102204]='',
[102206]='',
[102220]='',
[102270]='',
[102261]='',
[102262]='',
[102263]='',
[102264]='',
[102265]='',
[102266]='',
[102267]='',
[102268]='',
[102281]='',
[102282]='',
[102283]='',
[102284]='',
[102301]='',
[102302]='',
[102303]='',
[102304]='',
[102305]='',
[102306]='',
[102307]='',
[102308]='',
[102401]='',
[102402]='',
[102403]='',
[102404]='',
[102405]='',
[102521]='',
[102522]='',
[102523]='',
[102524]='',
[102525]='',
[102526]='',
[102527]='',
[102528]='',
[102529]='',
[102530]='',
[102531]='',
[102532]='',
[102533]='',
[102534]='',
[102535]='',
[102601]='',
[102602]='',
[102603]='',
[102604]='',
[102605]='',
[102606]='',
[102607]='',
[102608]='',
[102609]='',
[102610]='',
[102611]='',
[102612]='',
[102613]='',
[102614]='',
[102615]='',
[102616]='',
[102617]='',
[102618]='',
[102619]='',
[102620]='',
[102621]='',
[102622]='',
[102623]='',
[102624]='',
[102625]='',
[102626]='',
[102627]='',
[102628]='',
[102629]='',
[102630]='',
[102631]='',
[102632]='',
[102633]='',
[102634]='',
[102635]='',
[102636]='',
[102637]='',
[102638]='',
[102639]='',
[102700]='',
[102701]='',
[102702]='',
[102703]='',
[102704]='',
[102705]='',
[102751]='',
[102752]='',
[102753]='',
[102771]='',
[102772]='',
[102791]='',
[102792]='',
[102793]='',
[102794]='',
[102795]='',
[102796]='',
[102797]='',
[102798]='',
[102799]='',
[102800]='',
[102801]='',
[102802]='',
[102803]='',
[102804]='',
[102805]='',
[102821]='',
[102822]='',
[102823]='',
[102824]='',
[102825]='',
[102826]='',
[102827]='',
[102828]='',
[102829]='',
[102830]='',
[102831]='',
[102832]='',
[102833]='',
[102834]='',
[102835]='',
[102836]='',
[102837]='',
[102838]='',
[102861]='',
[102862]='',
[102863]='',
[102900]='',
[102901]='',
[102902]='',
[102903]='',
[102904]='',
[102905]='',
[102906]='',
[103001]='',
[103002]='',
[103003]='',
[103004]='',
[103005]='',
[103006]='',
[103007]='',
[103008]='',
[103009]='',
[103010]='',
[103011]='',
[103012]='',
[103013]='',
[103014]='',
[103015]='',
[103016]='',
[103017]='',
[103018]='',
[103151]='',
[103201]='',
[103202]='',
[103203]='',
[103204]='',
[103205]='',
[103210]='',
[103211]='',
[103221]='',
[103222]='',
[103223]='',
[103224]='',
[103225]='',
[103226]='',
[103227]='',
[103228]='',
[103229]='',
[103231]='',
[103232]='',
[103250]='',
[103251]='',
[103252]='',
[103301]='',
[103302]='',
[103303]='',
[103304]='',
[103311]='',
[103351]='',
[103352]='',
[103361]='',
[103362]='',
[103363]='',
[103364]='',
[103451]='',
[103452]='',
[103453]='',
[103454]='',
[103455]='',
[103456]='',
[103457]='',
[103461]='',
[103462]='',
[103463]='',
[103464]='',
[103465]='',
[103466]='',
[103467]='',
[103500]='',
[103501]='',
[103502]='',
[103503]='',
[103504]='',
[103505]='',
[103506]='',
[103507]='',
[103508]='',
[103509]='',
[103510]='',
[103511]='',
[103512]='',
[103513]='',
[103514]='',
[103515]='',
[103516]='',
[103517]='',
[103518]='',
[103519]='',
[103520]='',
[103521]='',
[103522]='',
[103523]='',
[103524]='',
[103551]='',
[103552]='',
[103553]='',
[103554]='',
[103555]='',
[103561]='',
[103562]='',
[103571]='',
[103572]='',
[103573]='',
[103574]='',
[103651]='',
[103652]='',
[103653]='',
[103654]='',
[103701]='',
[103702]='',
[103703]='',
[103704]='',
[103721]='',
[103722]='',
[103726]='',
[103727]='',
[103736]='',
[103737]='',
[103738]='',
[103739]='',
[103741]='',
[103742]='',
[103743]='',
[103744]='',
[103581]='',
[103582]='',
[103583]='',
[103584]='',
[103585]='',
[103586]='',
[103587]='',
[103588]='',
[103750]='',
[103751]='',
[103761]='',
[103762]='',
[103763]='',
[103764]='',
[103765]='',
[103766]='',
[103767]='',
[103768]='',
[103769]='',
[103781]='',
[103782]='',
[103901]='',
[103902]='',
[103903]='',
[103904]='',
[103905]='',
[103906]='',
[103921]='',
[103922]='',
[103923]='',
[103924]='',
[103925]='',
[103926]='',
[103927]='',
[103928]='',
[103929]='',
[103930]='',
[103931]='',
[103932]='',
[103933]='',
[103934]='',
[103935]='',
[103936]='',
[103937]='',
[103938]='',
[103939]='',
[103940]='',
[103941]='',
[103942]='',
[103943]='',
[103944]='',
[103945]='',
[103946]='',
[103947]='',
[103948]='',
[103949]='',
[103950]='',
[103951]='',
[103952]='',
[103953]='',
[103954]='',
[103980]='',
[104001]='',
[104002]='',
[104003]='',
[104004]='',
[104005]='',
[104006]='',
[104007]='',
[104008]='',
[104009]='',
[104010]='',
[104011]='',
[104013]='',
[104014]='',
[104015]='',
[104016]='',
[104017]='',
[104018]='',
[104019]='',
[104020]='',
[104021]='',
[104023]='',
[104024]='',
[104025]='',
[104026]='',
[104051]='',
[104052]='',
[104053]='',
[104054]='',
[104055]='',
[104056]='',
[104057]='',
[104058]='',
[104059]='',
[104060]='',
[104061]='',
[104062]='',
[104063]='',
[104064]='',
[104065]='',
[104066]='',
[104067]='',
[104068]='',
[104069]='',
[104070]='',
[104071]='',
[104072]='',
[104073]='',
[104074]='',
[104077]='',
[104081]='',
[104101]='',
[104102]='',
[104103]='',
[104104]='',
[104105]='',
[104110]='',
[104111]='',
[104112]='',
[104121]='',
[104122]='',
[104140]='',
[104141]='',
[104142]='',
[104143]='',
[104144]='',
[104150]='',
[104151]='',
[104152]='',
[104153]='',
[104154]='',
[104155]='',
[104156]='',
[104157]='',
[104205]='',
[104220]='',
[104221]='',
[104222]='',
[104223]='',
[104224]='',
[104323]='',
[104324]='',
[104325]='',
[104326]='',
[104327]='',
[104328]='',
[104329]='',
[104331]='',
[104336]='',
[104337]='',
[104338]='',
[104339]='',
[104340]='',
[104341]='',
[104342]='',
[104343]='',
[104344]='',
[104345]='',
[104347]='',
[104348]='',
[104349]='',
[104350]='',
[104351]='',
[104352]='',
[104353]='',
[104354]='',
[104355]='',
[104357]='',
[104358]='',
[104360]='',
[104361]='',
[104362]='',
[104363]='',
[104364]='',
[104365]='',
[104366]='',
[104367]='',
[104368]='',
[104369]='',
[104370]='',
[104371]='',
[104372]='',
[104373]='',
[104374]='',
[104375]='',
[104376]='',
[104377]='',
[104378]='',
[104385]='',
[104386]='',
[104387]='',
[104388]='',
[104389]='',
[104390]='',
[104391]='',
[104392]='',
[104393]='',
[104395]='',
[104396]='',
[104402]='',
[104403]='',
[104404]='',
[104405]='',
[104406]='',
[104407]='',
[104408]='',
[104409]='',
[104410]='',
[104411]='',
[104412]='',
[104413]='',
[104513]='',
[104514]='',
[104515]='',
[104516]='',
[104517]='',
[104518]='',
[104519]='',
[104523]='',
[104524]='',
[104525]='',
[104526]='',
[104527]='',
[104528]='',
[104529]='',
[104530]='',
[104531]='',
[104532]='',
[104533]='',
[104534]='',
[104535]='',
[104536]='',
[104537]='',
[104538]='',
[104539]='',
[104540]='',
[104541]='',
[104542]='',
[104543]='',
[104544]='',
[104545]='',
[104546]='',
[104547]='',
[104548]='',
[104549]='',
[104550]='',
[104551]='',
[104552]='',
[104553]='',
[104554]='',
[104555]='',
[104556]='',
[104557]='',
[104558]='',
[104760]='',
[104761]='',
[104762]='',
[104763]='',
[104764]='',
[104765]='',
[104766]='',
[104767]='',
[104768]='',
[104769]='',
[104770]='',
[104771]='',
[104772]='',
[104773]='',
[104774]='',
[104775]='',
[104776]='',
[104777]='',
[104778]='',
[104779]='',
[104780]='',
[104781]='',
[104782]='',
[104783]='',
[104784]='',
[104785]='',
[104786]='',
[104787]='',
[104788]='',
[104789]='',
[104790]='',
[104791]='',
[104792]='',
[104793]='',
[104794]='',
[104795]='',
[104796]='',
[104797]='',
[104798]='',
[104799]='',
[104800]='',
[104801]='',
[104802]='',
[104803]='',
[104804]='',
[104805]='',
[104806]='',
[104807]='',
[104808]='',
[104809]='',
[104810]='',
[104811]='',
[104812]='',
[104813]='',
[104814]='',
[104815]='',
[104816]='',
[104817]='',
[104818]='',
[104819]='',
[104820]='',
[104821]='',
[104822]='',
[104823]='',
[104851]='',
[104852]='',
[104853]='',
[104854]='',
[104855]='',
[104856]='',
[104857]='',
[104858]='',
[104859]='',
[104860]='',
[104861]='',
[104862]='',
[104863]='',
[104864]='',
[104865]='',
[104866]='',
[104867]='',
[104868]='',
[104869]='',
[104870]='',
[104871]='',
[104917]='',
[104918]='',
[104919]='',
[104920]='',
[104921]='',
[104922]='',
[104923]='',
[104924]='',
[104925]='',
[104926]='',
[104927]='',
[104928]='',
[104929]='',
[104930]='',
[104931]='',
[104960]='',
[104961]='',
[104962]='',
[104963]='',
[104964]='',
[104965]='',
[104966]='',
[104967]='',
[104968]='',
[104969]='',
[104970]='',
[104971]='',
[104972]='',
[104973]='',
[104985]='',
[104986]='',
[104987]='',
[104988]='',
[104989]='',
[104990]='',
[104991]='',
[104992]='',
[104993]='',
[104994]='',
[104995]='',
[104996]='',
[104997]='',
[104998]='',
[104999]='',
[105000]='',
[105001]='',
[105002]='',
[105003]='',
[105004]='',
[105005]='',
[105006]='',
[105007]='',
[105008]='',
[105009]='',
[105010]='',
[105011]='',
[105012]='',
[105013]='',
[105014]='',
[105015]='',
[105016]='',
[105017]='',
[105018]='',
[105019]='',
[105020]='',
[105021]='',
[105022]='',
[105029]='',
[105049]='',
[105050]='',
[105051]='',
[105052]='',
[105053]='',
[105054]='',
[105055]='',
[105056]='',
[105057]='',
[105058]='',
[105059]='',
[105060]='',
[105101]='',
[105102]='',
[105103]='',
[105104]='',
[105105]='',
[105106]='',
[105107]='',
[105108]='',
[105109]='',
[105110]='',
[105111]='',
[105112]='',
[105113]='',
[105114]='',
[105115]='',
[105116]='',
[105117]='',
[105129]='',
[105130]='',
[105131]='',
[105132]='',
[105133]='',
[105134]='',
[105135]='',
[105136]='',
[105137]='',
[105170]='',
[105171]='',
[105172]='',
[105173]='',
[105174]='',
[105175]='',
[105151]='',
[105152]='',
[105153]='',
[105154]='',
[105155]='',
[105156]='',
[105157]='',
[105158]='',
[105159]='',
[105160]='',
[105161]='',
[105162]='',
[105455]='',
[110011]='',
[110012]='',
[110013]='',
[110014]='',
[121201]='',
[121202]='',
[121203]='',
[121204]='',
[121211]='',
[121212]='',
[121213]='',
[121214]='',
[121215]='',
[121216]='',
[121217]='',
[121218]='',
[121219]='',
[121221]='',
[121222]='',
[121223]='',
[121224]='',
[121231]='',
[121232]='',
[121233]='',
[121234]='',
[121235]='',
[121236]='',
[121237]='',
[121238]='',
[121239]='',
[121241]='',
[121242]='',
[121243]='',
[121244]='',
[121245]='',
[121246]='',
[121251]='',
[121252]='',
[121253]='',
[121254]='',
[121255]='',
[121256]='',
[121257]='',
[121261]='',
[121262]='',
[121271]='',
[121272]='',
[121281]='',
[121282]='',
[121291]='',
[121292]='',
[121301]='',
[121302]='',
[121303]='',
[121304]='',
[121305]='',
[121311]='',
[121312]='',
[121313]='',
[121314]='',
[121315]='',
[121316]='',
[121317]='',
[121321]='',
[121322]='',
[121323]='',
[121324]='',
[121331]='',
[121332]='',
[121333]='',
[121334]='',
[121341]='',
[121342]='',
[121343]='',
[121344]='',
[121345]='',
[121346]='',
[121347]='',
[121348]='',
[121351]='',
[121352]='',
[121361]='',
[121362]='',
[121363]='',
[121364]='',
[121365]='',
[121371]='',
[121372]='',
[121373]='',
[121374]='',
[121375]='',
[121376]='',
[121381]='',
[121382]='',
[121383]='',
[121391]='',
[121392]='',
[121393]='',
[121394]='',
[121395]='',
[121401]='',
[121402]='',
[121403]='',
[121404]='',
[121405]='',
[121411]='',
[121412]='',
[121413]='',
[121414]='',
[121415]='',
[121421]='',
[121422]='',
[121431]='',
[121432]='',
[121433]='',
[121434]='',
[121435]='',
[121436]='',
[121441]='',
[121442]='',
[121443]='',
[121444]='',
[121445]='',
[121451]='',
[121452]='',
[121453]='',
[121454]='',
[121455]='',
[121456]='',
[121461]='',
[121462]='',
[121471]='',
[121472]='',
[121473]='',
[121474]='',
[121475]='',
[121476]='',
[121481]='',
[121482]='',
[121483]='',
[121484]='',
[121491]='',
[121492]='',
[121493]='',
[121494]='',
[121496]='',
[121497]='',
[121498]='',
[121499]='',
[121501]='',
[121502]='',
[121511]='',
[121512]='',
[121513]='',
[121514]='',
[121515]='',
[121516]='',
[121521]='',
[121522]='',
[121523]='',
[121524]='',
[121525]='',
[121526]='',
[121531]='',
[121532]='',
[121533]='',
[121534]='',
[121535]='',
[121541]='',
[121542]='',
[121543]='',
[121544]='',
[121545]='',
[121546]='',
[121547]='',
[121551]='',
[121552]='',
[121553]='',
[121556]='',
[121557]='',
[121561]='',
[121562]='',
[121563]='',
[121566]='',
[121571]='',
[121572]='',
[121576]='',
[121577]='',
[121581]='',
[121586]='',
[121587]='',
[130013]='',
[130014]='',
[130015]='',
[130019]='',
[130033]='',
[130034]='',
[130035]='',
[130037]='',
[130038]='',
[130042]='',
[130043]='',
[130044]='',
[130055]='',
[130056]='',
[130100]='',
[130101]='',
[130102]='',
[130103]='',
[130104]='',
[130105]='',
[130106]='',
[130107]='',
[130108]='',
[130109]='',
[130110]='',
[130111]='',
[130112]='',
[130113]='',
[130114]='',
[130115]='',
[130116]='',
[130117]='',
[130118]='',
[130119]='',
[130120]='',
[130121]='',
[130122]='',
[130123]='',
[130124]='',
[130125]='',
[130126]='',
[130127]='',
[130128]='',
[130129]='',
[130130]='',
[130131]='',
[130132]=[[العنوان يحتوي على كلمات حساسة]],
[130133]=[[لا يمكن الانضمام إلى تحالف دون بناء مركز تحالف]],
[130134]=[[لا يوجد تحالف في الوقت الحالي، يرجى الانضمام إلى التحالف أولاً]],
[130135]=[[يتم تبريد المشاركة، يرجى المحاولة مرة أخرى لاحقًا]],
[130136]=[[ليس لديك الأذونات الكافية للمشاركة]],
[130200]='',
[130201]='',
[130202]='',
[130203]='',
[130204]='',
[130205]='',
[130206]='',
[130207]=[[التكنولوجيا الحالية قيد البحث بالفعل]],
[130208]=[[لا يوجد مباني فارغة]],
[130216]='',
[130217]='',
[130218]='',
[130219]='',
[130236]=[[فشل في استخدام صندوق موارد المستوى]],
[130237]=[[صناديق الموارد ذات المستوى غير كافية]],
[130238]=[[مستوى المدينة خاطئ]],
[130239]=[[فشل في استخدام صندوق الكنز لاختيار الموارد]],
[130240]=[[صندوق الكنز بسبب عدم اختيار الموارد الكافية]],
[130241]=[[فشل في استخدام صناديق الموارد الأخرى]],
[130242]=[[عدم كفاية صناديق الموارد الأخرى]],
[130243]=[[فشل في فتح صندوق الكنز]],
[130244]=[[صناديق الكنز غير كافية]],
[130245]=[[فشل تجديد الموارد بنقرة واحدة]],
[130246]=[[عدم كفاية صناديق الموارد الأخرى]],
[130247]=[[فشل في فتح صندوق الكنز]],
[130248]=[[فشل في فتح صندوق الكنز]],
[130249]=[[فشل في فتح صندوق الكنز]],
[130250]=[[فشل تجديد الموارد بنقرة واحدة]],
[130251]=[[فشل تجديد الموارد بنقرة واحدة]],
[130300]='',
[130301]='',
[130302]='',
[130303]='',
[130304]='',
[130305]='',
[130306]='',
[130307]='',
[130308]='',
[130309]='',
[130310]='',
[130311]='',
[130312]='',
[130313]='',
[130314]='',
[130315]='',
[130316]='',
[130317]='',
[130318]='',
[130319]='',
[130320]='',
[130321]='',
[130322]='',
[130323]='',
[130324]='',
[130325]='',
[130326]='',
[130327]='',
[130328]='',
[130329]=[[قيمة الدفاع عن المدينة ممتلئة]],
[130330]=[[شراء قيمة دفاع المدينة في حالة تباطؤ]],
[130360]='',
[130361]='',
[130362]='',
[130363]='',
[130364]='',
[130365]='',
[130366]='',
[130367]='',
[130410]='',
[130411]='',
[130421]=[[شظايا ناجية اصطناعية غير كافية]],
[130430]='',
[130431]='',
[130432]='',
[130433]='',
[130434]='',
[130435]='',
[130436]='',
[130437]=[[فشلت المهمة، غير قادر على المساعدة]],
[130438]=[[لا يمكنك المطالبة بالكنز أو استخراجه خلال 24 ساعة بعد الانضمام إلى أي تحالف]],
[130439]='',
[130440]='',
[130441]='',
[130442]=[[لا يمكن تنفيذ المهام الاستخباراتية في المناطق الملوثة]],
[130443]=[[لا يوجد حاليًا أي مواقع متاحة حول القلعة، يرجى المحاولة مرة أخرى لاحقًا.]],
[130444]=[[غير موجود في صندوق الرمل الخاص به، يرجى العودة إلى صندوق الرمل الخاص به]],
[130501]='',
[130502]='',
[130509]='',
[130510]='',
[130511]='',
[130512]='',
[130513]='',
[130514]='',
[130515]='',
[130516]='',
[130517]='',
[130518]='',
[130519]=[[لا يوجد كشافة متاحة]],
[130520]='',
[130521]='',
[130522]='',
[130523]='',
[130524]='',
[130525]='',
[130526]='',
[130527]='',
[130528]='',
[130529]='',
[130530]='',
[130531]='',
[130532]='',
[130533]='',
[130534]='',
[130535]='',
[130536]='',
[130537]='',
[130538]='',
[130539]='',
[130540]='',
[130541]='',
[130542]='',
[130543]='',
[130544]='',
[130545]='',
[130546]='',
[130547]='',
[130548]='',
[130549]='',
[130550]='',
[130551]='',
[130552]='',
[130553]='',
[130554]='',
[130555]='',
[130556]='',
[130557]='',
[130558]='',
[130559]=[[خطأ نوع الخادم المتقاطع]],
[130560]='',
[130561]='',
[130562]='',
[130563]='',
[130564]='',
[130565]=[[طائرة الاستطلاع في طريق العودة]],
[130566]=[[ولم تغادر طائرة الاستطلاع]],
[130567]=[[حاليا في عالم آخر]],
[130568]=[[فشل في دخول عوالم أخرى]],
[130569]=[[دخول عوالم أخرى]],
[130570]=[[لقد اختفى الهدف]],
[130571]=[[الهدف ليس في الموقع الحالي]],
[130572]=[[مؤثرات خاصة للمدينة غير الطبيعية]],
[130573]=[[الهدف في منطقة آمنة]],
[130574]=[[لا يمكن للهجمات عبر الخوادم استهداف]],
[130575]=[[مستوى مدينتك الرئيسي منخفض للغاية بحيث لا يمكنك الانتقال إلى المنطقة الملوثة]],
[130576]=[[الهدف موجود في القرص المضغوط، غير قادر على اكتشافه]],
[130578]=[[لا يوجد حاليًا موقع نقل التحالف، يرجى المحاولة مرة أخرى لاحقًا]],
[130600]='',
[130601]='',
[130602]='',
[130603]='',
[130604]='',
[130605]='',
[130606]='',
[130607]='',
[130608]='',
[130609]='',
[130610]='',
[130611]='',
[130612]='',
[130707]=[[فشل تحديث تأثيرات القلعة]],
[130708]='',
[130709]='',
[130710]=[[فشل تحديث ديكور القلعة]],
[130711]=[[مثل الفشل]],
[130712]=[[لا يوجد ما يكفي من الإعجابات]],
[130713]=[[فشل في الحصول على سجل الإعجابات]],
[130714]=[[أنا الأفضل! (لا أستطيع أن أمنح نفسي إبهامًا للأعلى)]],
[130715]=[[لقد اعجبك هذا البريد الإلكتروني]],
[131713]='',
[131714]='',
[131715]='',
[131716]='',
[131717]='',
[131718]='',
[131719]='',
[131750]='',
[131751]='',
[131752]='',
[131753]='',
[131754]='',
[131780]='',
[131781]='',
[131800]='',
[131801]='',
[131802]='',
[131803]='',
[131804]='',
[131805]='',
[131806]='',
[131807]='',
[131808]='',
[131809]='',
[131810]='',
[131811]='',
[131812]='',
[131813]='',
[131814]='',
[131850]=[[إنجازات التحالف غير مفعلة]],
[131851]=[[لم يتم فتح الإنجاز]],
[131852]=[[انتهى الإنجاز]],
[131853]=[[غير قادر على المطالبة بالمكافآت]],
[131854]=[[لقد حصلت بالفعل على المكافآت]],
[131855]='',
[131856]=[[انتهت صلاحية معلومات الدعوة]],
[131857]=[[لا يوجد لدى اللاعب أي تحالف ولا يمكن دعوته]],
[131858]=[[اللاعب لديه تحالف بالفعل]],
[131859]=[[غير قادر على نقل المدن مجانًا]],
[131861]=[[تم الوصول إلى حد إعادة الشحن]],
[131862]=[[لم يتم بدء النشاط]],
[131863]='',
[131901]='',
[131902]='',
[131903]='',
[131904]='',
[131905]='',
[131911]='',
[131912]='',
[131913]='',
[131914]='',
[131915]='',
[131921]='',
[131922]='',
[131923]='',
[131931]='',
[131932]='',
[131933]='',
[131934]='',
[131935]='',
[131941]='',
[131942]='',
[131943]='',
[131944]='',
[131945]=[[أكملت جميع المستويات]],
[132001]='',
[132002]='',
[132003]='',
[132004]='',
[132005]='',
[132006]='',
[132007]='',
[132008]='',
[132009]='',
[132010]='',
[132011]='',
[132012]='',
[132013]='',
[132014]='',
[132015]='',
[132016]='',
[132017]='',
[132018]='',
[132019]='',
[132020]='',
[132021]='',
[132022]='',
[132023]='',
[132024]='',
[132025]='',
[132026]='',
[132027]='',
[132028]='',
[132029]='',
[132030]='',
[132031]='',
[132032]='',
[132033]='',
[132034]='',
[132035]='',
[132036]='',
[132037]='',
[132038]='',
[133900]='',
[133901]='',
[133902]='',
[134000]='',
[134001]='',
[134002]='',
[134003]='',
[134004]='',
[134005]='',
[134006]='',
[134007]='',
[134008]='',
[134009]='',
[134010]='',
[134011]='',
[134012]='',
[134013]='',
[134014]='',
[134015]='',
[134016]='',
[134017]='',
[134018]='',
[134020]=[[وصلت باقة الهدايا إلى الحد الأقصى للشراء لهذا اليوم]],
[134021]=[[العنصر التبادلي غير موجود]],
[134022]=[[عدد غير كاف من عناصر الاسترداد]],
[134023]=[[لقد وصل عنصر الاسترداد إلى الحد الأقصى]],
[134025]=[[أوقات السحب غير كافية]],
[134100]=[[لم يشارك في مبارزة التحالف]],
[134101]=[[عرض أخطاء لوحة المتصدرين]],
[134102]=[[عرض أخطاء لوحة المتصدرين]],
[134103]=[[عدم المشاركة في دوري المبارزة]],
[134104]=[[غير قادر على تنفيذ عمليات زيادة الحصة أو تقليلها]],
[134149]=[[عنصر نائب]],
[134150]='',
[134151]='',
[134152]='',
[134153]='',
[134154]='',
[134155]='',
[134156]='',
[134157]='',
[134158]='',
[134159]=[[لم يتم استيفاء شروط الإرسال. يرجى إرسال الأبطال الذين يستوفون الشروط إلى المعركة.]],
[134160]='',
[134161]='',
[134162]='',
[134163]='',
[134164]='',
[134165]='',
[134166]='',
[134167]='',
[134168]='',
[134169]='',
[134170]='',
[134171]='',
[134172]='',
[134173]='',
[134174]=[[لا يوجد موقع مناسب لإنشاء المهمة، يرجى المحاولة مرة أخرى لاحقًا.]],
[134175]='',
[134200]='',
[134201]='',
[134202]='',
[134203]='',
[134204]='',
[134205]='',
[134211]='',
[134212]='',
[134213]='',
[134214]='',
[134215]='',
[134221]='',
[134222]='',
[134223]='',
[134224]='',
[134231]='',
[134232]='',
[134233]='',
[134234]='',
[134241]='',
[134242]='',
[134243]='',
[134244]='',
[134245]='',
[134246]='',
[134247]='',
[134248]='',
[134251]='',
[134252]='',
[134253]='',
[134254]='',
[134255]='',
[134261]='',
[134262]='',
[134271]='',
[134272]='',
[134273]='',
[134274]='',
[134275]='',
[134276]='',
[134277]='',
[134278]='',
[134281]='',
[134282]='',
[134283]='',
[134284]='',
[134285]='',
[134291]='',
[134300]=[[لا تتوفر في ساحات العرض مساحة كافية لتخزين الجنود.]],
[134301]='',
[134302]='',
[134305]='الإشغال',
[134306]='',
[134307]='',
[134310]='الإشغال',
[134400]='',
[134500]='',
[134501]='',
[134502]='',
[134503]='',
[134504]='',
[134505]='',
[134506]='',
[134507]='',
[134508]='',
[134509]='',
[134510]='',
[134600]='',
[134601]='',
[134602]='',
[134603]='',
[134604]='',
[134605]='',
[134700]=[[جدار الشرف ليس مفتوحا]],
[134701]=[[الأبطال ليس لديهم تكوين جدار الشرف]],
[134702]=[[البطل لا يفي بمتطلبات جدار الشهرة]],
[134703]=[[لقد وصل البطل إلى أعلى مستوى في جدار الشهرة]],
[134704]=[[شظايا غير كافية لترقية جدار شرف البطل]],
[134900]=[[ليس مستوى لعبة صغيرة]],
[135401]='',
[135402]='',
[135403]='',
[135404]='',
[135405]='',
[135406]='',
[135407]='',
[135408]='',
[135409]='',
[135410]='',
[135411]='',
[135412]='',
[135413]=[[لا يوجد كشافة متاحة]],
[135414]='',
[135415]='',
[135416]='',
[135417]='',
[135418]='',
[135419]='',
[135420]='',
[135421]='',
[135422]='',
[135423]='',
[135424]='',
[135425]='',
[135426]='',
[135427]='',
[135428]='',
[135429]='',
[135430]='',
[135431]='',
[135432]='',
[135433]='',
[135434]='',
[135435]='',
[135436]='',
[135437]='',
[135438]='',
[135439]='',
[135440]='',
[135441]='',
[135442]='',
[135443]='',
[135444]='',
[135445]='',
[135446]='',
[135447]='',
[135448]='',
[135449]='',
[135450]='',
[135451]=[[التقدم القتالي غير قابل للترقية حاليًا]],
[135452]=[[لقد وصل مستوى تقدم المعركة إلى الحد الأقصى]],
[135453]=[[التشكيل غير مفتوح]],
[135454]=[[لم يتم فتح حل قلب البلورة]],
[135455]=[[لا يوجد مثل هذا النواة]],
[135456]=[[غير قادر على تشغيل النواة المجهزة]],
[135457]=[[عناصر جوهر الكريستال الوحشي غير الإلهي]],
[135458]=[[لا يمكن ترقية قلب الكريستال]],
[135459]=[[إن النواة البلورية الحالية غير مجهزة ولا يمكن ترقيتها.]],
[135460]=[[فشل في إزالة قلب البلورة]],
[135461]=[[فشل في تجهيز قلب البلورة]],
[135462]=[[مواد ترقية قلب الكريستال غير كافية]],
[135463]=[[فشل في خصم العناصر عند ترقية قلب الكريستال]],
[135464]=[[لم يتم ترقية البلورة ولا يمكن إعادة تعيينها]],
[135465]=[[رقم التشكيل خاطئ]],
[135466]=[[الفريق المجهز بحل النواة البلورية الحالي ليس خاملاً]],
[135467]=[[وقد وضع هذا التشكيل خطة التبلور]],
[135468]=[[هذه الفتحة مجهزة بالفعل بنواة بلورية]],
[135469]=[[فشل في إزالة النواة البلورية للجهاز]],
[135470]=[[فشل استبدال النواة]],
[135471]=[[لا يمكن تجهيز هذا النوع من النواة في هذه الفتحة]],
[135472]=[[تكوين نوع وظيفة قلب البلورة غير طبيعي]],
[135473]=[[فشل إعداد الخطة]],
[135500]=[[لم يتم تسجيل أي بيانات]],
[135501]=[[لا مزيد من البيانات]],
[135550]='',
[135551]='',
[135560]=[[عدم كفاية الدعائم لهجرة التحالف]],
[135600]=[[نقاط غير كافية]],
[135601]=[[لم تنتهي بعد]],
[135602]=[[لقد حصلت بالفعل على المكافآت]],
[135603]=[[ليس الأول في النقاط]],
[135604]=[[لم يتم بدء النشاط]],
[135605]=[[ليس الفائز]],
[135606]=[[نقاط غير كافية]],
[135607]=[[لا وداعا]],
[135650]=[[أيام فتح الخادم الحالية غير كافية]],
[135651]=[[العدد الحالي للأيام لإنشاء دور جديد غير كافٍ]],
[135652]=[[شروط مستوى التخليص الحالية غير كافية]],
[135653]=[[إن ظروف مستوى البناء الحالية غير كافية]],
[135654]=[[شروط حدث الفتح الحالية غير كافية]],
[135655]=[[إن مستوى البحث العلمي الحالي غير كاف]],
[135656]=[[شروط الفتح الخاصة الحالية غير كافية]],
[135657]=[[لم يتم استيفاء شرط أيام فتح الخادم الحالي]],
[135658]=[[لم يتم استيفاء شروط البناء الحالية]],
[135700]='',
[135701]=[[البيانات غير موجودة]],
[135800]=[[الحد الأقصى لقيمة رمز الخطأ الشائع]],
[135801]=[[استثناء الحصول على معلومات التحالف]],
[135802]=[[حالة مركبة التحالف الفضائية غير طبيعية]],
[135803]=[[ليس حاليًا قبطان سفينة فضاء]],
[135804]=[[فشل في الحصول على موقع خريطة العالم]],
[135805]=[[استثناء اكتساب الفريق]],
[135806]=[[استثناء في الحصول على تشكيلة الفريق]],
[135807]=[[تعيين استثناء الفريق]],
[135808]=[[فشل في إعداد فريق سفينة الفضاء]],
[135809]=[[فشل في تعيين تشكيلة فريق سفينة الفضاء]],
[135810]=[[تشكيلة سفينة الفضاء التابعة للتحالف فارغة]],
[135811]=[[الهجوم بشكل متكرر للغاية]],
[135812]=[[المركبة الفضائية في فترة الحماية ولا يمكنها الهجوم.]],
[135813]=[[حدث خطأ أثناء الحصول على معلومات الكابتن]],
[135814]=[[فشل في بدء النهب]],
[135815]=[[حدث خطأ أثناء الحصول على مكافأة سفينة الفضاء]],
[135816]=[[عدد خاطئ من عمليات الغنائم في اليوم الواحد]],
[135817]=[[سفينة الفضاء لم تغادر]],
[135818]=[[فشل في الحصول على سجل المعركة]],
[135819]=[[حدث خطأ أثناء تبديل التشكيلة]],
[135820]=[[لم يتم العثور على هدف الهجوم]],
[135821]=[[فشل في الحصول على موقع عبر الخادم]],
[135822]=[[فشل في الحصول على موقف المركبة الفضائية]],
[135823]=[[المركبة الفضائية قيد الإعداد حاليًا ولا يمكن تشغيلها.]],
[135824]=[[إذا انضممت إلى التحالف منذ أقل من 24 ساعة، فلن تتمكن من الصعود إلى مركبة التحالف الفضائية.]],
[136001]=[[فشل في الحصول على نقاط التحالف]],
[136002]=[[فشل في الحصول على تصنيف التحالف]],
[136003]=[[فشل في الحصول على نقاط شخصية التحالف]],
[136050]=[[تبريد نقل المدينة]],
[136051]=[[لا يمكن نقل المنطقة الآمنة للعدو]],
[136052]=[[لا يمكن مهاجمة القواعد الموجودة في المناطق الآمنة أو اكتشافها]],
[136100]=[[نشاط الخادم غير مفتوح]],
[136101]=[[لا يمكن للاعبين الذين يكون مستوى قلعتهم أقل من 15 المشاركة في المسابقة]],
[136102]=[[لم ينضم إلى التحالف]],
[136103]=[[نوع الفريق خاطئ]],
[136104]=[[لا يمكن طرد اللاعبين أثناء معركة الآثار]],
[136117]=[[لا يمكن تعديل وقت الدخول خلال فترة عدم التسجيل]],
[136118]=[[لا يمكن تعديل الأعضاء المشاركين خلال فترة عدم التسجيل]],
[136119]=[[لا يمكن تعديل طلب المعركة خلال فترة عدم التسجيل]],
[136120]=[[التحالف لم يسجل في ساحة المعركة]],
[136121]=[[لم يتم تحميل بيانات التسجيل]],
[136122]=[[عدم وجود وقت كاف لإنشاء تحالف]],
[136123]='',
[136124]=[[مستوى القاعدة غير كافٍ]],
[136125]=[[طبقة حليفة غير كافية]],
[136126]=[[لا يوجد بيانات تصنيف حاليًا، حاول مرة أخرى لاحقًا]],
[136127]=[[ليس عضوا في هذا التحالف]],
[136129]=[[تكرار التسجيل]],
[136130]=[[غير مسجل]],
[136131]=[[ضبط وقت المعركة بشكل غير قانوني]],
[136132]=[[وقت القتال - الاستعداد - القيمة المكررة]],
[136133]=[[تعيين الأعضاء المشاركين - لا تغيير]],
[136134]=[[تعيين الأعضاء المشاركين - الفريق الرئيسي ممتلئ]],
[136135]=[[إعداد الأعضاء المشاركين - البدلاء ممتلئون]],
[136136]=[[ضبط الأعضاء المشاركين - استثناء غير معروف]],
[136137]=[[لا يمكنك إرسال وقت المعركة بشكل متكرر.]],
[136140]=[[مرحلة غير قتالية]],
[136141]=[[لم يقم الخادم بتحميل بيانات اللعبة]],
[136142]=[[الأعضاء غير المشاركين]],
[136143]=[[شذوذ في تكوين وقت المعركة]],
[136144]=[[الفريق المضيف - لم يحن وقت الدخول بعد]],
[136145]=[[بديل - ليس الوقت المناسب للدخول بعد]],
[136146]=[[لقد انتهى وقت المعركة]],
[136147]=[[لا يمكن الدخول إلى صندوق رمل اللعبة الأخرى]],
[136148]=[[خرجت من اللعبة]],
[136149]=[[ساحة المعركة مليئة]],
[136150]=[[القاعدة الأصلية في المنطقة السوداء]],
[136151]=[[لم يتم إنشاء Sandbox]],
[136152]=[[لم يتم الدخول إلى صندوق الرمل]],
[136153]=[[إنها ليست صندوق الرمل الخاص بك]],
[136154]=[[فشل إنشاء قاعدة في المنطقة الآمنة]],
[136155]=[[الحلفاء يقومون بالتعدين]],
[136156]=[[بئر النفط ليس لديه أي موارد متبقية]],
[136157]=[[الرجاء الانتظار حتى تبدأ المعركة]],
[136158]=[[المبنى تحت الصيانة]],
[136300]=[[لم يتم العثور على أي كائنات حية ميتة ثمينة قريبة. يُرجى البحث في مناطق أخرى.]],
[136301]=[[لقد اختفى الهدف، يرجى البحث عن هدف جديد]],
[136350]=[[لم يبدأ الحدث ولا يمكن بدء التجمع.]],
[136351]=[[مستوى القلعة غير كاف ولا يمكن بدء التجمع.]],
[136352]=[[مستوى القلعة غير كافي ولا يمكن البحث عن وحوش من هذا المستوى.]],
[136370]=[[تبادل العناصر غير الطبيعية]],
[136371]=[[عدد غير كاف من عناصر التبادل]],
[136372]=[[فشلت المعاملة وغادر اللاعب التحالف]],
[136373]=[[خطأ في إنشاء الطلب]],
[136374]=[[تم تداول الطلب]],
[136375]=[[معاملة الطلب قيد التقدم]],
[136376]=[[هناك أمر ينتظر التداول]],
[136390]=[[مفاتيح خاصة غير كافية]],
[136391]=[[برجاء تحديد جائزة بيت الكنز في هذا الطابق أولاً]],
[136392]='',
[136401]=[[لا توجد بيانات بطاقة أسبوعية للاعب المحدد]],
[136402]=[[تم شراء تذكرة أسبوعية تم اختيارها ذاتيًا]],
[136403]=[[لا يوجد تكوين بطاقة أسبوعية اختياري]],
[136404]=[[كمية خاطئة من العناصر المحددة]],
[136405]=[[تم اختيار العنصر الخطأ]],
[136406]=[[تم شراء تذكرة أسبوعية تم اختيارها ذاتيًا]],
[136407]=[[حصلت على مكافأة البطاقة الأسبوعية المختارة]],
[136451]=[[لن تتمكن من استلام الجائزة إذا لم يتم الوصول إلى وقت الاستلام]],
[136452]=[[لا توجد جائزة متكررة]],
[136411]=[[طلب بيانات صندوق الهدايا غير طبيعي]],
[136412]=[[اللاعب لا يملك صندوق الهدايا هذا]],
[136413]=[[أنت تعتقد بالفعل أنه رائع!]],
[136414]=[[انتهت صلاحية صندوق الهدايا]],
[136415]=[[لقد تم الحصول على صندوق الهدايا هذا]],
[136416]=[[لقد تم بيع صندوق الهدايا]],
[136417]=[[لقد وصل عدد صناديق الهدايا التي يمكنك الحصول عليها اليوم إلى الحد الأقصى]],
[136418]=[[لقد تم بيع صناديق الهدايا]],
[136419]=[[فشل في إرسال صندوق الهدايا في الدردشة]],
[136420]=[[غير قادر على استخدام صندوق هدايا Kingdom Duel]],
[136501]=[[غير قادر على إطفاء الحرائق لغير الحلفاء]],
[136502]=[[مدينة اللاعب ليست مشتعلة]],
[136521]=[[الوظيفة الحالية غير مفعلة]],
[136522]=[[لقد حصلت بالفعل على هذه المكافأة ولا يمكنك الحصول عليها مرة أخرى]],
[136523]=[[لا يمكن المطالبة بالمكافآت بسبب تغييرات التحالف/الموقع]],
[136524]=[[صفك غير كاف]],
[136525]='',
[136601]=[[لقد وصل عدد صناديق الكنز الحالية التي تلقيتها إلى الحد اليومي.]],
[140000]='',
[140001]='',
[140002]='',
[140003]='',
[159216]='',
[159217]='',
[159218]='',
[159219]='',
[159220]='',
[159221]='',
[159222]='',
[159223]='',
[159224]='',
[159225]='',
[159226]='',
[159227]='',
[159228]='',
[159229]='',
[159230]='',
[159231]='',
[159232]='',
[159233]='',
[159234]='',
[159235]='',
[159236]='',
[159237]='',
[159238]='',
[159239]='',
[159240]='',
[159241]='',
[159242]='',
[159243]='',
[159244]='',
[159245]='',
[159246]='',
[159247]='',
[159248]='',
[159249]='',
[159250]='',
[159251]='',
[159252]='',
[159253]='',
[159254]='',
[159255]='',
[159256]='',
[159257]='',
[159258]='',
[159259]='',
[159260]='',
[159261]='',
[159262]='',
[159263]='',
[159264]='',
[159265]='',
[159266]='',
[159267]='',
[159268]='',
[159269]='',
[159270]='',
[159271]='',
[159272]='',
[159273]='',
[159274]='',
[159275]='',
[159288]='',
[159289]='',
[159290]='',
[159291]='',
[159292]='',
[159293]='',
[159294]='',
[159295]='',
[159296]='',
[159297]='',
[159298]='',
[159299]='',
[160001]='',
[160002]='',
[160003]='',
[160005]='',
[160006]='',
[160007]='',
[160009]='',
[160010]='',
[160011]='',
[160013]='',
[160014]='',
[160015]='',
[160016]='',
[160018]='',
[160019]='',
[160020]='',
[160022]='',
[160023]='',
[160024]='',
[160025]='',
[160026]='',
[160027]='',
[160028]='',
[160029]='',
[160030]='',
[160031]='',
[160032]='',
[160033]='',
[160034]='',
[160035]='',
[160036]='',
[160037]='',
[160038]='',
[160039]='',
[160040]='',
[160041]='',
[160042]='',
[160043]='',
[160044]='',
[160045]='',
[160046]='',
[160048]='',
[160049]='',
[160051]='',
[160052]='',
[160054]='',
[160055]='',
[160056]='',
[160057]='',
[160058]='',
[160059]='',
[160061]='',
[160062]='',
[160064]='',
[160065]='',
[160067]='',
[160068]='',
[160069]='',
[160070]='',
[160071]='',
[160072]='',
[160073]='',
[160075]='',
[160077]='',
[160078]='',
[160079]='',
[160080]='',
[160081]='',
[160082]='',
[160083]='',
[160084]='',
[160085]='',
[160086]='',
[160087]='',
[160088]='',
[160089]='',
[160090]='',
[160091]='',
[160092]='',
[160093]='',
[160094]='',
[160095]='',
[160096]='',
[160097]='',
[160098]='',
[160099]='',
[160100]='',
[160101]='',
[160102]='',
[160103]='',
[160104]='',
[160105]='',
[160106]='',
[160107]='',
[160108]='',
[160109]='',
[160110]='',
[160111]='',
[160112]='',
[160113]='',
[160114]='',
[160115]='',
[160116]='',
[160120]='',
[160121]='',
[160125]='',
[160127]='',
[162501]='',
[162502]='',
[162503]='',
[162504]='',
[162505]='',
[162506]='',
[162507]='',
[162508]='',
[162509]='',
[162510]='',
[162511]='',
[162512]='',
[162513]='',
[162514]='',
[162515]='',
[162516]='',
[162517]='',
[162518]='',
[162519]='',
[162520]='',
[162521]='',
[162522]='',
[162523]='',
[162524]='',
[162525]='',
[170001]='',
[170002]='',
[170003]='',
[170004]='',
[170005]='',
[170006]='',
[170007]='',
[170008]='',
[170009]='',
[170010]='',
[170011]='',
[170012]='',
[170013]='',
[170014]='',
[170015]='',
[170016]='',
[170017]='',
[170018]='',
[170019]='',
[170020]='',
[170021]='',
[170022]='',
[170023]='',
[170024]='',
[170025]='',
[170026]='',
[170027]='',
[170028]='',
[170029]='',
[170030]='',
[170031]='',
[170032]='',
[170033]='',
[170034]='',
[170035]='',
[170036]='',
[170037]='',
[170038]='',
[170039]='',
[170040]='',
[170041]='',
[170042]='',
[170043]='',
[170044]='',
[170045]='',
[170046]='',
[170047]='',
[170048]='',
[170049]='',
[170050]='',
[170051]='',
[170052]='',
[170053]='',
[170054]='',
[170055]='',
[170056]='',
[170057]='',
[170058]='',
[170059]='',
[170060]='',
[170061]='',
[170062]='',
[170063]='',
[170064]='',
[170065]='',
[170066]='',
[170067]='',
[170068]='',
[170069]='',
[170070]='',
[170071]='',
[170072]='',
[170073]='',
[170074]='',
[170075]='',
[170076]='',
[170077]='',
[170078]='',
[170079]='',
[170080]='',
[170081]='',
[170082]='',
[171001]='',
[171002]='',
[171003]='',
[171004]='',
[171005]='',
[171006]='',
[171007]='',
[171008]='',
[171009]='',
[171010]='',
[171011]='',
[171012]='',
[171013]='',
[171014]='',
[171015]='',
[171016]='',
[171017]='',
[171018]='',
[171019]='',
[171020]='',
[171021]='',
[171022]='',
[171023]='',
[171024]='',
[171025]='',
[171026]='',
[171027]='',
[171028]='',
[171029]='',
[171030]='',
[171031]='',
[171032]='',
[171033]='',
[171034]='',
[171035]='',
[171036]='',
[171037]='',
[171038]='',
[171039]='',
[171040]='',
[171041]='',
[171042]='',
[171043]='',
[171044]='',
[171045]='',
[171046]='',
[171047]='',
[171048]='',
[171049]='',
[171050]='',
[171051]='',
[171052]='',
[171053]='',
[171054]='',
[171055]='',
[171056]='',
[171057]='',
[171058]='',
[171059]='',
[171060]='',
[171061]='',
[171062]='',
[171063]='',
[171064]='',
[171065]='',
[171066]='',
[171067]='',
[171068]='',
[171069]='',
[171070]='',
[171071]='',
[171072]='',
[171073]='',
[171074]='',
[171075]='',
[171076]='',
[171077]='',
[171078]='',
[171079]='',
[171080]='',
[171081]='',
[171082]='',
[171083]='',
[171084]='',
[171085]='',
[171086]='',
[171087]='',
[171088]='',
[171089]='',
[171090]='',
[171091]='',
[171092]='',
[171093]='',
[171094]='',
[171095]='',
[171096]='',
[171097]='',
[171098]='',
[171099]='',
[171100]='',
[171101]='',
[171102]='',
[171103]='',
[171104]='',
[171105]='',
[171106]='',
[171107]='',
[171108]='',
[171109]='',
[171110]='',
[171111]='',
[171112]='',
[171113]='',
[171114]='',
[171115]='',
[171116]='',
[171117]='',
[171118]='',
[171119]='',
[171120]='',
[172001]='',
[172002]='',
[172003]='',
[172004]='',
[172005]='',
[172006]='',
[172007]='',
[172008]='',
[172009]='',
[172010]='',
[172011]='',
[172012]='',
[172013]='',
[172014]='',
[172015]='',
[172016]='',
[172017]='',
[172018]='',
[172019]='',
[172020]='',
[172021]='',
[172022]='',
[172023]='',
[172024]='',
[172025]='',
[172026]='',
[172027]='',
[172028]='',
[172029]='',
[172030]=[[عندما تبدأ المعركة، تكتسب أفيليا <color=#df2d52>30</color> نقطة طاقة.]],
[172031]='',
[172032]='',
[172033]='',
[172034]='',
[172035]='',
[172036]='',
[172037]='',
[172038]='',
[172039]='',
[172040]='',
[172041]='',
[172042]='',
[172043]='',
[172044]='',
[172045]='',
[172046]='',
[172047]='',
[172048]='',
[172049]='',
[172050]='',
[172051]='',
[172052]='',
[172053]='',
[172054]='',
[172055]='',
[172056]='',
[172057]='',
[172058]='',
[172059]='',
[172060]='',
[172061]='',
[172062]='',
[172063]='',
[172064]='',
[172065]='',
[172066]='',
[172067]='',
[172068]='',
[172069]='',
[172070]='',
[172071]='',
[172072]='',
[172073]='',
[172074]='',
[172075]='',
[172076]='',
[172077]='',
[172078]='',
[172079]='',
[172080]='',
[172081]='',
[172082]='',
[172083]='',
[172084]='',
[172085]='',
[172086]='',
[172087]='',
[172088]='',
[172089]='',
[172090]='',
[172091]='',
[172092]='',
[173103]='',
[173104]='',
[173653]='',
[173654]='',
[173693]='',
[173243]='',
[173703]='',
[173713]='',
[173714]='',
[173733]='',
[173734]='',
[173743]='',
[173744]='',
[173763]='',
[173783]='',
[173784]='',
[173803]='',
[173804]='',
[174101]='',
[174102]='',
[174103]='',
[174104]='',
[174105]='',
[174106]='',
[174107]='',
[174108]='',
[174651]='',
[174652]='',
[174653]='',
[174654]='',
[174655]='',
[174656]='',
[174691]='',
[174692]='',
[174693]='',
[174701]='',
[174702]='',
[174703]='',
[174761]='',
[174762]='',
[174763]='',
[174241]='',
[174242]='',
[174243]='',
[174711]='',
[174712]='',
[174713]='',
[174714]='',
[174715]='',
[174716]='',
[174731]='',
[174732]='',
[174733]='',
[174734]='',
[174735]='',
[174736]='',
[174741]='',
[174742]='',
[174743]='',
[174744]='',
[174745]='',
[174746]='',
[174781]='',
[174782]='',
[174783]='',
[174784]='',
[174785]='',
[174786]='',
[174787]='',
[174788]='',
[174801]='',
[174802]='',
[174803]='',
[174804]='',
[174805]='',
[174806]='',
[175001]='',
[175002]='',
[175003]='',
[175004]='',
[175005]='',
[175006]='',
[175007]='',
[175008]='',
[175009]='',
[175010]='',
[175011]='',
[175012]='',
[175013]='',
[175014]='',
[175015]='',
[175016]='',
[175017]='',
[175018]='',
[175019]='',
[175020]='',
[175021]='',
[175022]='',
[175023]='',
[176001]='',
[176002]='',
[176003]='',
[176004]='',
[176005]='',
[176006]='',
[176007]='',
[176008]='',
[176009]='',
[176010]='',
[176011]='',
[176012]='',
[176013]='',
[176014]='',
[176015]='',
[176016]='',
[176017]='',
[176018]='',
[176019]='',
[176020]='',
[176021]='',
[176022]='',
[176023]='',
[176024]='',
[176025]='',
[176026]='',
[176027]='',
[176028]='',
[176029]='',
[176030]='',
[176031]='',
[176032]='',
[176033]='',
[176034]='',
[176035]='',
[176036]='',
[176037]='',
[176038]='',
[176039]='',
[176040]='',
[176041]='',
[176042]='',
[176043]='',
[176044]='',
[176045]='',
[176046]='',
[176047]='',
[176048]='',
[176049]='',
[176050]='',
[176051]='',
[176052]='',
[176053]='',
[176054]='',
[176055]='',
[176056]='',
[176057]='',
[176058]='',
[176059]='',
[176060]='',
[176061]='',
[176062]='',
[176063]='',
[176064]='',
[176065]='',
[176066]='',
[176067]='',
[176068]='',
[176069]='',
[176070]='',
[176071]='',
[176072]='',
[176073]='',
[176074]='',
[176075]='',
[176076]='',
[176077]='',
[176078]='',
[176079]='',
[176080]='',
[176081]='',
[176082]='',
[176083]='',
[176084]='',
[176085]='',
[176086]='',
[176087]='',
[176088]='',
[176089]='',
[176090]='',
[176091]='',
[176092]='',
[176093]='',
[176094]='',
[176095]='',
[176096]='',
[176097]='',
[176098]='',
[176099]='',
[176100]='',
[176101]='',
[176102]='',
[176103]='',
[176104]='',
[176105]='',
[176106]='',
[176107]='',
[176108]='',
[176109]='',
[176110]='',
[176111]='',
[176112]='',
[176113]='',
[176114]='',
[176115]='',
[176116]='',
[176117]='',
[176118]='',
[176119]='',
[176120]='',
[176121]='',
[176122]='',
[176123]='',
[176124]='',
[176125]='',
[176126]='',
[176127]='',
[176128]='',
[176129]='',
[176130]='',
[176131]='',
[176132]='',
[176133]='',
[177001]='',
[177002]='',
[177003]='',
[177004]='',
[177005]='',
[177006]='',
[177007]='',
[177008]='',
[177009]='',
[177010]='',
[177011]='',
[177012]='',
[177013]='',
[177014]='',
[177015]='',
[177016]='',
[177017]='',
[177018]='',
[177019]='',
[177020]='',
[177021]='',
[177022]='',
[177023]='',
[177024]='',
[177025]='',
[177026]='',
[177027]='',
[177028]='',
[177029]='',
[177030]='',
[177031]='',
[177032]='',
[177033]='',
[177034]='',
[177035]='',
[177036]='',
[177037]='',
[177038]='',
[177039]='',
[177040]='',
[177041]='',
[177042]='',
[177043]='',
[177044]='',
[177045]='',
[177046]='',
[177047]='',
[177048]='',
[177049]='',
[177050]='',
[177051]='',
[177052]='',
[177053]='',
[177054]='',
[177055]='',
[177056]='',
[177057]='',
[177058]='',
[177059]='',
[177060]='',
[177061]='',
[177062]='',
[177063]='',
[177064]='',
[177065]='',
[177066]='',
[177067]='',
[177068]='',
[177069]='',
[177070]='',
[177071]='',
[177072]='',
[177073]='',
[177074]='',
[177075]='',
[177076]='',
[177077]='',
[177078]='',
[177079]='',
[177080]='',
[177081]='',
[177082]='',
[177083]='',
[177084]='',
[177085]='',
[177086]='',
[177087]='',
[177088]='',
[177089]='',
[177090]='',
[177091]='',
[177092]='',
[177093]='',
[177094]='',
[177095]='',
[177096]='',
[177097]='',
[177098]='',
[177099]='',
[177100]='',
[177101]='',
[177102]='',
[177103]='',
[177104]='',
[177105]='',
[177106]='',
[177107]='',
[177108]='',
[177109]='',
[177110]='',
[177111]='',
[177112]='',
[177113]='',
[177114]='',
[177115]='',
[177116]='',
[177117]='',
[177118]='',
[177119]='',
[177120]='',
[177121]='',
[177122]='',
[177123]='',
[177124]='',
[177125]='',
[177126]='',
[177127]='',
[177128]='',
[177129]='',
[177130]='',
[177131]='',
[177132]='',
[177133]='',
[180001]='',
[180002]='',
[180003]='',
[180004]='',
[180005]='',
[180006]='',
[180007]='',
[180008]='',
[180009]='',
[180011]='',
[180012]='',
[180100]='',
[180101]='',
[180102]='',
[180103]='',
[180104]='',
[180105]='',
[180106]='',
[180107]='',
[180108]='',
[180109]='',
[180110]='',
[180111]='',
[180112]='',
[180113]='',
[180114]='',
[180115]='',
[180116]='',
[180117]='',
[180118]='',
[180119]='',
[180120]='',
[180121]='',
[180122]='',
[180123]='',
[180124]='',
[180125]='',
[180126]='',
[180127]='',
[180128]='',
[180129]='',
[180130]='',
[180131]='',
[180132]='',
[180133]='',
[180134]='',
[180135]='',
[180136]='',
[180137]='',
[180138]='',
[180139]='',
[180150]='',
[180151]='',
[180152]='',
[180153]='',
[180154]='',
[180155]='',
[180156]='',
[180157]='',
[180158]='',
[180159]='',
[180160]='',
[180161]='',
[180162]='',
[180163]='',
[180164]='',
[180165]='',
[180166]='',
[180167]='',
[180168]='',
[180169]='',
[180170]='',
[180171]='',
[180172]='',
[180173]='',
[180174]='',
[180175]='',
[180176]='',
[180177]='',
[180178]='',
[180179]='',
[180180]='',
[180181]='',
[180182]='',
[180183]='',
[180184]='',
[180185]='',
[180186]='',
[180187]='',
[180188]='',
[180189]='',
[180200]='',
[180201]='',
[180202]='',
[180203]='',
[180204]='',
[180205]='',
[180206]='',
[180207]='',
[180208]='',
[180209]='',
[180210]='',
[180211]='',
[180212]='',
[180213]='',
[180214]='',
[180215]='',
[180216]='',
[180217]='',
[180218]='',
[180219]='',
[180220]='',
[181000]='',
[181001]='',
[181002]='',
[181003]='',
[181004]='',
[181005]='',
[181006]='',
[181007]='',
[181008]='',
[181009]='',
[181010]='',
[181011]='',
[181012]='',
[181013]='',
[181014]='',
[181015]='',
[181016]='',
[181017]='',
[181018]='',
[181019]='',
[181020]='',
[181021]='',
[181022]='',
[181023]='',
[181024]='',
[181025]='',
[181026]='',
[181027]='',
[181028]='',
[181029]='',
[181030]='',
[181031]='',
[181032]='',
[181033]='',
[181034]='',
[181035]='',
[181036]='',
[181037]='',
[181038]='',
[181039]='',
[181040]='',
[181041]='',
[181042]='',
[181043]='',
[181044]='',
[181045]='',
[181046]='',
[181047]='',
[181048]='',
[181049]='',
[181050]='',
[181051]='',
[181052]='',
[181053]='',
[181054]='',
[181055]='',
[181056]='',
[181057]='',
[181058]='',
[181059]='',
[181060]='',
[181061]='',
[181062]='',
[181063]='',
[181064]='',
[181065]='',
[181066]='',
[181067]='',
[181068]='',
[181069]='',
[181070]='',
[181071]='',
[181072]='',
[181073]='',
[181074]='',
[181075]='',
[181076]='',
[181077]='',
[181078]='',
[181079]='',
[181080]='',
[181081]='',
[181082]='',
[181083]='',
[181084]='',
[181085]='',
[181086]='',
[181087]='',
[181088]='',
[181089]='',
[181090]='',
[181091]='',
[181092]='',
[181093]='',
[181094]='',
[181095]='',
[181096]='',
[181097]='',
[181098]='',
[181099]='',
[181100]='',
[181101]='',
[181102]='',
[181103]='',
[181104]='',
[181105]='',
[181106]='',
[181107]='',
[181108]='',
[181109]='',
[181152]='',
[181201]='',
[181202]='',
[181203]='',
[181204]='',
[181205]='',
[181206]='',
[181207]='',
[181208]='',
[181209]='',
[181210]='',
[181211]='',
[181212]='',
[181213]='',
[181214]='',
[181215]='',
[181216]='',
[181217]='',
[181218]='',
[181219]='',
[181220]='',
[181221]='',
[181222]='',
[181223]='',
[181224]='',
[181225]='',
[181226]='',
[181227]='',
[181228]='',
[181229]='',
[181230]='',
[181231]='',
[181232]='',
[181233]='',
[181234]='',
[181235]='',
[181236]='',
[181237]='',
[181238]='',
[181239]='',
[181240]='',
[181401]='',
[181402]='',
[181403]='',
[181404]='',
[181405]='',
[181406]='',
[181407]='',
[181408]='',
[181409]='',
[181410]='',
[181411]='',
[181412]='',
[181413]='',
[181414]='',
[181415]='',
[181416]='',
[181417]='',
[181418]='',
[181419]='',
[181420]='',
[181421]='',
[181422]='',
[181423]='',
[181424]='',
[181425]='',
[181426]='',
[181427]='',
[181428]='',
[181429]='',
[181430]='',
[181431]='',
[181432]='',
[181433]='',
[181434]='',
[181435]='',
[181436]='',
[181437]='',
[181438]='',
[181439]='',
[181440]='',
[181601]='',
[181602]='',
[181603]='',
[181604]='',
[181605]='',
[181606]='',
[181607]='',
[181608]='',
[181609]='',
[181610]='',
[181611]='',
[181612]='',
[181613]='',
[181614]='',
[181615]='',
[181616]='',
[181617]='',
[181618]='',
[181619]='',
[181620]='',
[181621]='',
[181622]='',
[181623]='',
[181624]='',
[181625]='',
[181626]='',
[181627]='',
[181628]='',
[181629]='',
[181630]='',
[181631]='',
[181632]='',
[181633]='',
[181634]='',
[181635]='',
[181636]='',
[181637]='',
[181638]='',
[181639]='',
[181640]='',
[181801]='',
[181802]='',
[181803]='',
[181804]='',
[181805]='',
[181806]='',
[181807]='',
[181808]='',
[181809]='',
[181810]='',
[181811]='',
[181851]='',
[181852]='',
[181853]='',
[181854]='',
[181855]='',
[181856]='',
[181857]='',
[181858]='',
[181859]='',
[181860]='',
[181861]='',
[181901]='',
[181902]='',
[181903]='',
[181904]='',
[181905]='',
[181906]='',
[181907]='',
[181908]='',
[181909]='',
[181910]='',
[181911]='',
[182001]='',
[182002]='',
[182003]='',
[182004]='',
[182005]='',
[182006]='',
[182007]='',
[182008]='',
[182009]='',
[182010]='',
[182011]='',
[182012]='',
[182013]='',
[182014]='',
[182015]='',
[182016]='',
[182017]='',
[182018]='',
[182019]='',
[182020]='',
[182021]='',
[182022]='',
[182023]='',
[182024]='',
[182025]='',
[182026]='',
[182027]='',
[182028]='',
[182029]='',
[182030]='',
[182031]='',
[182032]='',
[182033]='',
[182034]='',
[182035]='',
[182036]='',
[182037]='',
[182038]='',
[182039]='',
[182040]='',
[182041]='',
[182042]='',
[182043]='',
[182044]='',
[182045]='',
[182046]='',
[182047]='',
[182048]='',
[182049]='',
[182050]='',
[182051]='',
[182052]='',
[182053]='',
[182054]='',
[182055]='',
[182056]='',
[182057]='',
[182058]='',
[182059]='',
[182060]='',
[182061]='',
[182062]='',
[182063]='',
[182064]='',
[182065]='',
[182066]='',
[182067]='',
[182068]='',
[182069]='',
[182070]='',
[182071]='',
[182072]='',
[182073]='',
[182074]='',
[182075]='',
[182076]='',
[182077]='',
[182078]='',
[182079]='',
[182080]='',
[182081]='',
[182082]='',
[182084]='',
[182085]='',
[182086]='',
[182087]='',
[182088]='',
[182089]='',
[182090]='',
[182091]='',
[182092]='',
[182093]='',
[182094]='',
[182095]='',
[182096]='',
[182097]='',
[182098]='',
[182099]='',
[182100]='',
[182101]='',
[182102]='',
[182103]='',
[182104]='',
[182105]='',
[182106]='',
[182107]='',
[182108]='',
[182109]='',
[182110]='',
[182111]='',
[182112]='',
[182113]='',
[182114]='',
[182115]='',
[182116]='',
[182117]='',
[182118]='',
[182119]='',
[182120]='',
[182121]='',
[182122]='',
[182123]='',
[182124]='',
[182125]='',
[182126]='',
[182127]='',
[182128]='',
[182129]='',
[182130]='',
[182131]='',
[182132]='',
[182133]='',
[182134]='',
[182135]='',
[182136]='',
[182137]='',
[182138]='',
[182139]='',
[182140]='',
[182141]='',
[182142]='',
[182143]='',
[182144]='',
[182145]='',
[182146]='',
[182147]='',
[182148]='',
[182149]='',
[182150]='',
[182151]='',
[182152]='',
[182153]='',
[182154]='',
[182155]='',
[184001]='',
[184002]='',
[184003]='',
[184004]='',
[184005]='',
[184006]='',
[184007]='',
[184008]='',
[184009]='',
[184010]='',
[184011]='',
[184012]='',
[184013]='',
[184014]='',
[184015]='',
[184016]='',
[185001]='',
[185002]='',
[185003]='',
[185004]='',
[185005]='',
[185006]='',
[185007]='',
[185008]='',
[185009]='',
[185010]='',
[186000]='',
[186001]='',
[186002]='',
[186003]='',
[186004]='',
[186005]='',
[186006]='',
[186007]='',
[186008]='',
[186009]='',
[186010]='',
[186011]='',
[186012]='',
[186013]='',
[186014]='',
[186015]='',
[186016]='',
[186017]='',
[186018]='',
[186019]='',
[186020]='',
[186021]='',
[186022]='',
[186023]='',
[186024]='',
[186025]='',
[186026]='',
[186027]='',
[186028]='',
[186029]='',
[186030]='',
[186031]='',
[186032]='',
[186033]='',
[186034]='',
[186035]='',
[186036]='',
[186037]='',
[186038]='',
[186039]='',
[186040]='',
[186041]='',
[186042]='',
[186043]='',
[186044]='',
[186045]='',
[186046]='',
[186047]='',
[186048]='',
[186049]='',
[186050]='',
[186051]='',
[186052]='',
[186053]='',
[186054]='',
[186055]='',
[186056]='',
[186057]='',
[186058]='',
[186059]='',
[186060]='',
[186061]='',
[186062]='',
[186063]='',
[186064]='',
[186065]='',
[186066]='',
[186067]='',
[190000]='',
[190001]='',
[190002]='',
[190003]='',
[190004]='',
[190005]='',
[190006]='',
[190007]='',
[193115]='',
[193116]='',
[193117]='',
[193118]='',
[193119]='',
[193120]='',
[195001]='',
[195002]='',
[195003]='',
[195004]='',
[195006]='',
[195007]='',
[195010]='',
[195012]='',
[195013]='',
[195014]='',
[195019]='',
[195020]='',
[195023]='',
[195027]='',
[195030]='',
[195033]='',
[195034]='',
[195101]='',
[195102]='',
[195103]='',
[195104]='',
[195105]='',
[195106]='',
[195107]='',
[195108]='',
[195109]='',
[195110]='',
[195111]='',
[200000]='',
[200001]='',
[200002]='',
[200003]='',
[200004]='',
[200005]='',
[200200]='',
[200201]='',
[200202]='',
[200203]='',
[200204]='',
[200205]='',
[200206]='',
[200207]='',
[200208]='',
[200209]='',
[200210]='',
[200211]='',
[200212]='',
[200213]='',
[200214]='',
[200215]='',
[200216]='',
[200217]='',
[200218]='',
[200219]='',
[200220]='',
[200221]='',
[200300]='',
[200301]='',
[200302]='',
[200303]='',
[200304]='',
[202000]='',
[202001]='',
[202002]='',
[202003]='',
[202004]='',
[204000]='',
[204001]='',
[204002]='',
[204003]='',
[204004]='',
[204005]='',
[204006]='',
[204007]='',
[204008]='',
[205000]='',
[205001]='',
[205002]='',
[205003]='',
[240000]='',
[240001]='',
[240002]='',
[240003]='',
[240004]='',
[240005]='',
[240006]='',
[240007]='',
[240008]='',
[240009]='',
[240010]='',
[240011]='',
[240012]='',
[240013]='',
[240014]='',
[240015]='',
[240016]='',
[240017]='',
[240018]='',
[240019]='',
[240020]='',
[240021]='',
[240022]='',
[240023]='',
[240024]='',
[240025]='',
[240026]='',
[240027]='',
[240028]='',
[240100]='',
[240101]='',
[240102]='',
[240103]='',
[240104]='',
[240105]='',
[240106]='',
[240107]='',
[240108]='',
[240109]='',
[240110]='',
[240111]='',
[240112]='',
[241000]='',
[241001]='',
[241002]='',
[241003]='',
[241004]='',
[241005]='',
[241006]='',
[241007]='',
[241008]='',
[241009]='',
[241011]='',
[241012]='',
[241015]='',
[241016]='',
[241023]='',
[241024]='',
[241025]='',
[241026]='',
[241027]='',
[241028]='',
[241029]='',
[241030]='',
[241031]='',
[241032]='',
[241033]='',
[241034]='',
[241035]='',
[241036]='',
[241037]='',
[241038]='',
[241039]='',
[241040]='',
[241041]='',
[241042]='',
[241043]='',
[241044]='',
[241045]='',
[241046]='',
[241047]='',
[241048]='',
[241049]='',
[241050]='',
[241051]='',
[241052]='',
[241053]='',
[241054]='',
[241055]='',
[241056]='',
[241057]='',
[241058]='',
[241059]='',
[241060]='',
[241061]='',
[241062]='',
[241063]='',
[241064]='',
[241065]='',
[241066]='',
[241067]='',
[241068]='',
[241069]='',
[241070]='',
[241071]='',
[241072]='',
[241073]='',
[241074]='',
[241075]='',
[241076]='',
[241077]='',
[241078]='',
[241079]='',
[241080]='',
[241081]='',
[241082]='',
[241083]='',
[241084]='',
[241085]='',
[241086]='',
[241087]='',
[241088]='',
[241089]='',
[241090]='',
[241091]='',
[241092]='',
[241093]='',
[241094]='',
[241095]='',
[241096]='',
[241097]='',
[241098]='',
[241099]='',
[241100]='',
[242000]='',
[242001]='',
[242002]='',
[242004]='',
[242005]='',
[242006]='',
[242007]='',
[242008]='',
[242009]='',
[242010]='',
[242011]='',
[242012]='',
[242013]='',
[242014]='',
[242015]='',
[242016]='',
[242017]='',
[242018]='',
[242019]='',
[242020]='',
[242021]='',
[242022]='',
[242023]='',
[242024]='',
[242025]='',
[242026]='',
[242028]='',
[242029]='',
[242030]='',
[242031]='',
[242032]='',
[242033]='',
[242034]='',
[242035]='',
[242036]='',
[242037]='',
[242038]='',
[242039]='',
[242040]='',
[242041]='',
[242043]='',
[242044]='',
[242045]='',
[242046]='',
[242047]='',
[242048]='',
[242049]='',
[242050]='',
[242051]='',
[242052]='',
[242053]='',
[242054]='',
[242055]='',
[242056]='',
[242057]='',
[242058]='',
[242059]='',
[242060]='',
[242061]='',
[242062]='',
[242063]='',
[242064]='',
[242065]='',
[242066]='',
[242067]='',
[242068]='',
[242069]='',
[242070]='',
[242071]='',
[242072]='',
[242073]='',
[242074]='',
[242075]='',
[242076]='',
[242077]='',
[242078]='',
[242079]='',
[242080]='',
[242081]='',
[242082]='',
[242084]='',
[242085]='',
[242086]='',
[242087]='',
[242088]='',
[242089]='',
[242090]='',
[242091]='',
[242092]='',
[242093]='',
[242094]='',
[242095]='',
[242096]='',
[242097]='',
[242098]='',
[242099]='',
[242100]='',
[242101]='',
[242102]='',
[242103]='',
[242104]='',
[242105]='',
[242106]='',
[242108]='',
[242109]='',
[242110]='',
[242111]='',
[242112]='',
[242113]='',
[242114]='',
[242115]='',
[242118]='',
[242119]='',
[242120]='',
[242121]='',
[242122]='',
[242123]='',
[242125]='',
[242126]='',
[242127]='',
[242128]='',
[242129]='',
[242130]='',
[242132]='',
[242133]='',
[242134]='',
[242135]='',
[242136]='',
[242137]='',
[242139]='',
[242140]='',
[242141]='',
[242142]='',
[242143]='',
[242144]='',
[242147]='',
[242148]='',
[242149]='',
[242150]='',
[242151]='',
[242152]='',
[242153]='',
[242154]='',
[242155]='',
[242156]='',
[242157]='',
[242158]='',
[242161]='',
[242162]='',
[242163]='',
[242165]='',
[242166]='',
[242167]='',
[242168]='',
[242169]='',
[242170]='',
[242171]='',
[242172]='',
[242173]='',
[242174]='',
[242175]='',
[242176]='',
[242177]='',
[242178]='',
[242179]='',
[242180]='',
[242181]='',
[242182]='',
[242183]='',
[242184]='',
[242185]='',
[242186]='',
[242187]='',
[242188]='',
[242189]='',
[242190]='',
[242191]='',
[242192]='',
[242193]='',
[242195]='',
[242196]='',
[242197]='',
[242199]='',
[242200]='',
[242201]='',
[242202]='',
[242204]='',
[242205]='',
[242206]='',
[242207]='',
[242208]='',
[242209]='',
[242210]='',
[242211]='',
[242219]='',
[242501]='',
[242502]='',
[242503]='',
[242504]='',
[242505]='',
[242506]='',
[242507]='',
[242508]='',
[242509]='',
[242510]='',
[242511]='',
[242514]='',
[242515]='',
[242516]='',
[242517]='',
[242518]='',
[242519]='',
[242520]='',
[242521]='',
[242522]='',
[242523]='',
[242524]='',
[242525]='',
[242526]='',
[242527]='',
[242528]='',
[242529]='',
[242530]='',
[242531]='',
[242532]='',
[242533]='',
[242534]='',
[242535]='',
[242536]='',
[242537]='',
[242538]='',
[242539]='',
[250001]='',
[250002]='',
[250003]='',
[250004]='',
[250005]='',
[250006]='',
[250010]='',
[250011]='',
[250012]='',
[250013]='',
[250014]='',
[250015]='',
[250016]='',
[250017]='',
[250018]='',
[250019]='',
[253000]='',
[253001]='',
[253002]='',
[253003]='',
[253004]='',
[253005]='',
[253006]='',
[253007]='',
[253008]='',
[253009]='',
[253010]='',
[253011]='',
[253012]='',
[253013]='',
[253014]='',
[253015]='',
[253016]='',
[253017]='',
[253018]='',
[253043]='',
[253044]='',
[253045]='',
[253046]='',
[253047]='',
[253048]='',
[254000]='',
[254001]='',
[254002]='',
[254003]='',
[254004]='',
[254005]='',
[254006]='',
[254007]='',
[254008]='',
[254009]='',
[254020]='',
[254021]='',
[254022]='',
[254023]='',
[254024]='',
[254025]='',
[254026]='',
[254027]='',
[254028]='',
[254029]='',
[254040]='',
[254041]='',
[254042]='',
[254043]='',
[254044]='',
[254060]='',
[254061]='',
[254062]='',
[254063]='',
[254064]='',
[254065]='',
[254066]='',
[254067]='',
[254080]='',
[254081]='',
[254082]='',
[254083]='',
[254084]='',
[254085]='',
[254086]='',
[254087]='',
[254100]='',
[254101]='',
[254102]='',
[254103]='',
[254104]='',
[254105]='',
[254106]='',
[254107]='',
[254108]='',
[254109]='',
[254110]='',
[254111]='',
[254112]='',
[254113]='',
[254114]='',
[254115]='',
[254116]='',
[254117]='',
[254118]='',
[254119]='',
[254120]='',
[254121]='',
[254122]='',
[254123]='',
[254124]='',
[254125]='',
[254126]='',
[254127]='',
[254128]='',
[254129]='',
[254130]='',
[254131]='',
[254132]='',
[254133]='',
[254134]='',
[254135]='',
[254136]='',
[254137]='',
[254138]='',
[254139]='',
[254140]='',
[254141]='',
[254142]='',
[254143]='',
[254144]='',
[254145]='',
[254146]='',
[254147]='',
[254148]='',
[254149]='',
[254200]='',
[254201]='',
[254202]='',
[254203]='',
[254204]='',
[254205]='',
[254206]='',
[254207]='',
[254208]='',
[254209]='',
[254210]='',
[254211]='',
[254212]='',
[254213]='',
[254214]='',
[254215]='',
[254216]='',
[254217]='',
[254218]='',
[254219]='',
[254220]='',
[254221]='',
[254222]='',
[254223]='',
[254224]='',
[254225]='',
[254226]='',
[254227]='',
[254228]='',
[254229]='',
[254230]='',
[254231]='',
[254232]='',
[254233]='',
[254234]='',
[254235]='',
[254236]='',
[254237]='',
[254238]='',
[254239]='',
[254240]='',
[254241]='',
[254242]='',
[254243]='',
[254244]='',
[254245]='',
[254246]='',
[254247]='',
[254248]='',
[254300]='',
[254301]='',
[254302]='',
[254303]='',
[254304]='',
[254305]='',
[254306]='',
[254307]='',
[254308]='',
[254309]='',
[254310]='',
[254311]='',
[254312]='',
[254313]='',
[254314]='',
[254315]='',
[254316]='',
[254317]='',
[254318]='',
[254319]='',
[254320]='',
[254321]='',
[254322]='',
[254323]='',
[254324]='',
[254325]='',
[254326]='',
[254327]='',
[254328]='',
[254329]='',
[254330]='',
[254331]='',
[254332]='',
[254333]='',
[254334]='',
[254335]='',
[254336]='',
[254337]='',
[254338]='',
[254339]='',
[254340]='',
[254341]='',
[254342]='',
[254343]='',
[254344]='',
[254345]='',
[254346]='',
[254347]='',
[254348]='',
[255700]='',
[255701]='',
[255704]='',
[255705]='',
[255706]='',
[255707]='',
[255708]='',
[255709]='',
[255710]='',
[255711]='',
[255712]='',
[255713]='',
[255714]='',
[255715]='',
[255716]='',
[255717]='',
[255718]='',
[255731]='',
[255732]='',
[259000]='',
[259001]='',
[259002]='',
[259003]='',
[259004]='',
[259005]='',
[259006]='',
[259007]='',
[259008]='',
[259009]='',
[259010]='',
[259011]='',
[259012]='',
[259013]='',
[259014]='',
[259015]='',
[259016]='',
[259017]='',
[259018]='',
[259019]='',
[259020]='',
[259021]='',
[259022]='',
[259023]='',
[259024]='',
[259025]='',
[259026]='',
[259027]='',
[259028]='',
[259029]='',
[259030]='',
[259031]='',
[259032]='',
[259033]='',
[259034]='',
[259035]='',
[259036]='',
[259037]='',
[259038]='',
[259039]='',
[259040]='',
[259041]='',
[259042]='',
[259043]='',
[259044]='',
[259045]='',
[259046]='',
[259047]='',
[259048]='',
[259049]='',
[259101]='',
[259201]='',
[259202]='',
[259203]='',
[259204]='',
[259205]='',
[259206]='',
[255000]='',
[255001]='',
[255002]='',
[255003]='',
[255004]='',
[255005]='',
[255006]='',
[255007]='',
[255008]='',
[255009]='',
[255020]='',
[255021]='',
[255022]='',
[255023]='',
[255030]='',
[255032]='',
[255033]='',
[255034]='',
[255035]='',
[255036]='',
[255037]='',
[255038]='',
[255039]='',
[255040]='',
[255041]='',
[255042]='',
[255043]='',
[255044]='',
[255045]='',
[255046]='',
[255047]='',
[255048]='',
[255049]='',
[255050]='',
[255051]='',
[255052]='',
[255053]='',
[255054]='',
[255055]='',
[255056]='',
[255057]='',
[255058]='',
[255059]='',
[255060]='',
[255061]='',
[255062]='',
[255063]='',
[255064]='',
[255065]='',
[255066]='',
[255067]='',
[255068]='',
[255069]='',
[255070]='',
[255071]='',
[255072]='',
[255073]='',
[255074]='',
[255075]='',
[255076]='',
[255077]='',
[255078]='',
[255079]='',
[255080]='',
[255081]='',
[255082]='',
[255083]='',
[255084]='',
[255085]='',
[255086]='',
[255087]='',
[255088]='',
[255089]='',
[255090]='',
[255091]='',
[255092]='',
[255093]='',
[255094]='',
[255095]='',
[255096]='',
[255097]='',
[255098]='',
[255099]='',
[255100]='',
[255101]='',
[255102]='',
[255103]='',
[255104]='',
[255105]='',
[255106]='',
[255107]='',
[255108]='',
[255109]='',
[255110]='',
[255111]='',
[255112]='',
[255113]='',
[255114]='',
[255115]='',
[255116]='',
[255117]='',
[255118]='',
[255119]='',
[255120]='',
[255121]='',
[255122]='',
[255123]='',
[255124]='',
[255125]='',
[255126]='',
[255127]='',
[255128]='',
[255129]='',
[255130]='',
[255131]='',
[255132]='',
[255133]='',
[255134]='',
[255135]='',
[255136]='',
[255137]='',
[255138]='',
[255139]='',
[255140]='',
[255141]='',
[255142]='',
[255143]='',
[255144]='',
[255145]='',
[255146]='',
[255147]='',
[255148]='',
[255149]='',
[255150]='',
[255151]='',
[255152]='',
[255153]='',
[255154]='',
[255155]='',
[255156]='',
[255157]='',
[255158]='',
[255159]='',
[255160]='',
[255161]='',
[255162]='',
[255163]='',
[255164]='',
[255165]='',
[255166]='',
[255167]='',
[255168]='',
[255169]='',
[255170]='',
[255171]='',
[255172]='',
[255173]='',
[255174]='',
[255175]='',
[255176]='',
[255177]='',
[255178]='',
[255179]='',
[255180]='',
[255181]='',
[255182]='',
[255183]='',
[255184]='',
[255185]='',
[255186]='',
[255187]='',
[255188]='',
[255189]='',
[255190]='',
[255191]='',
[255192]='',
[255193]='',
[255194]='',
[255195]='',
[255196]='',
[255197]='',
[255198]='',
[255199]='',
[255200]='',
[255201]='',
[255202]='',
[255203]='',
[255204]='',
[255205]='',
[255206]='',
[255207]='',
[255208]='',
[255209]='',
[255210]='',
[255211]='',
[255212]='',
[255213]='',
[255214]='',
[255215]='',
[255216]='',
[255217]='',
[255218]='',
[255219]='',
[255220]='',
[255221]='',
[255222]='',
[255223]='',
[255224]='',
[255225]='',
[255226]='',
[255227]='',
[255228]='',
[255229]='',
[255230]='',
[255231]='',
[255232]='',
[255233]='',
[255234]='',
[255235]='',
[255236]='',
[255237]='',
[255238]='',
[255239]='',
[255240]='',
[255241]='',
[255242]='',
[255243]='',
[255244]='',
[255245]='',
[255246]='',
[255247]='',
[255248]='',
[255249]='',
[255250]='',
[255251]='',
[255252]='',
[255253]='',
[255254]='',
[255255]='',
[255256]='',
[255257]='',
[255258]='',
[255259]='',
[255260]='',
[255261]='',
[255262]='',
[255263]='',
[255264]='',
[255265]='',
[255266]='',
[255267]='',
[255268]='',
[255269]='',
[255270]='',
[255271]='',
[255272]='',
[255273]='',
[255274]='',
[255275]='',
[255276]='',
[255277]='',
[255278]='',
[255279]='',
[255280]='',
[255281]='',
[255282]='',
[255283]='',
[255284]='',
[255285]='',
[255286]='',
[255287]='',
[255288]='',
[255289]='',
[255290]='',
[255291]='',
[255292]='',
[255293]='',
[255294]='',
[255295]='',
[255296]='',
[255297]='',
[255298]='',
[255299]='',
[255300]='',
[255301]='',
[255302]='',
[255303]='',
[255304]='',
[255305]='',
[255306]='',
[255307]='',
[255308]='',
[255309]='',
[255310]='',
[255311]='',
[255312]='',
[255313]='',
[255314]='',
[255315]='',
[255316]='',
[255317]='',
[255318]='',
[255319]='',
[255320]='',
[255321]='',
[255325]='',
[255328]='',
[255370]='',
[255373]='',
[255719]='',
[255720]='',
[255721]='',
[255722]='',
[255723]='',
[255724]='',
[255725]='',
[255726]='',
[255727]='',
[255728]='',
[256100]='',
[256101]='',
[256102]='',
[256103]='',
[256104]='',
[256105]='',
[256106]='',
[256107]='',
[256108]='',
[256109]='',
[256110]='',
[256111]='',
[256112]='',
[257500]='',
[257501]='',
[257502]='',
[258380]='',
[259050]='',
[259051]='',
[259052]='',
[259053]='',
[259054]='',
[259055]='',
[259056]='',
[259057]='',
[259058]='',
[259059]='',
[259060]='',
[259061]='',
[259062]='',
[259063]='',
[259064]='',
[259065]='',
[259066]='',
[259067]='',
[259068]='',
[259073]='',
[261000]='',
[261002]='',
[261003]='',
[261004]='',
[261005]='',
[261006]='',
[261007]='',
[261008]='',
[261009]='',
[261010]='',
[261011]='',
[261013]='',
[261014]='',
[261015]='',
[261016]='',
[261017]='',
[261018]='',
[261019]='',
[261020]='',
[261021]='',
[261022]='',
[261023]='',
[261024]='',
[261025]='',
[261026]='',
[261027]='',
[261028]='',
[261030]='',
[261032]='',
[261033]='',
[261034]='',
[261035]='',
[261036]='',
[261037]='',
[261038]='',
[261039]='',
[261040]='',
[261041]='',
[261042]='',
[261043]='',
[261044]='',
[261049]='',
[261050]='',
[261051]='',
[261052]='',
[261053]='',
[261054]='',
[261055]='',
[261056]='',
[261057]='',
[261058]='',
[261059]='',
[261060]='',
[261061]='',
[261062]='',
[261063]='',
[261064]='',
[261065]='',
[261067]='',
[261068]='',
[261069]='',
[261070]='',
[261071]='',
[261072]='',
[261073]='',
[261074]='',
[261075]='',
[261076]='',
[261077]='',
[261078]='',
[261079]='',
[261080]='',
[261081]='',
[261082]='',
[261083]='',
[261084]='',
[261085]='',
[261086]='',
[261087]='',
[261088]='',
[261089]='',
[261090]='',
[261091]='',
[261092]='',
[261093]='',
[261094]='',
[263113]='',
[264210]='',
[264211]='',
[264212]='',
[264213]='',
[264214]='',
[264215]='',
[264216]='',
[264217]='',
[264218]='',
[264219]='',
[264220]='',
[264221]='',
[264222]='',
[264223]='',
[264224]='',
[264225]='',
[264226]='',
[264227]='',
[264228]='',
[264229]='',
[264230]='',
[271001]='',
[271002]='',
[271003]='',
[271004]='',
[271005]='',
[271006]='',
[271007]='',
[271008]='',
[271009]='',
[271010]='',
[271011]='',
[271012]='',
[271013]='',
[271014]='',
[271015]='',
[271016]='',
[271017]='',
[271018]='',
[271019]='',
[271020]='',
[271021]='',
[271022]='',
[271023]='',
[271024]='',
[271025]='',
[271026]='',
[271027]='',
[271028]='',
[271029]='',
[271030]='',
[271031]='',
[271032]='',
[271033]='',
[271034]='',
[271035]='',
[271036]='',
[271037]='',
[271038]='',
[271039]='',
[271040]='',
[271041]='',
[271042]='',
[271043]='',
[271044]='',
[271045]='',
[271046]='',
[271047]='',
[271048]='',
[271049]='',
[271050]='',
[271051]='',
[271052]='',
[271053]='',
[271054]='',
[271055]='',
[271056]='',
[271057]='',
[271058]='',
[271059]='',
[271060]='',
[271061]='',
[271062]='',
[271063]='',
[271064]='',
[271065]='',
[271066]='',
[271067]='',
[271068]='',
[271069]='',
[271070]='',
[271071]='',
[271072]='',
[271073]='',
[271074]='',
[271075]='',
[271076]='',
[271077]='',
[271078]='',
[271079]='',
[271080]='',
[271081]='',
[271082]='',
[271083]='',
[271084]='',
[271085]='',
[271086]='',
[271087]='',
[271088]='',
[271089]='',
[271090]='',
[271091]='',
[271092]='',
[271093]='',
[271094]='',
[271095]='',
[271096]='',
[271097]='',
[271098]='',
[271099]='',
[271100]='',
[271101]='',
[271102]='',
[271103]='',
[271104]='',
[271105]='',
[271106]='',
[271107]='',
[271108]='',
[271109]='',
[271110]='',
[271111]='',
[271112]='',
[271113]='',
[271114]='',
[271115]='',
[271116]='',
[271117]='',
[271118]='',
[271119]='',
[271120]='',
[271121]='',
[271122]='',
[271123]='',
[271124]='',
[271125]='',
[271126]='',
[271127]='',
[271128]='',
[271129]='',
[271132]='',
[271130]='',
[271135]='',
[271138]='',
[271140]='',
[271141]='',
[271142]='',
[271143]='',
[271144]='',
[271145]='',
[271146]='',
[271147]='',
[271148]='',
[271149]='',
[271150]='',
[271151]='',
[271152]='',
[271153]='',
[271154]='',
[271155]='',
[271156]='',
[275001]='',
[275002]='',
[275003]='',
[275004]='',
[275005]='',
[275006]='',
[275007]='',
[275008]='',
[275009]='',
[275010]='',
[275011]='',
[275012]='',
[275013]='',
[275014]='',
[275015]='',
[275016]='',
[275017]='',
[275018]='',
[275019]='',
[275020]='',
[275021]='',
[275022]='',
[275023]='',
[275024]='',
[275025]='',
[275026]='',
[275027]='',
[275028]='',
[275029]='',
[275030]='',
[275031]='',
[275032]='',
[275033]='',
[275034]='',
[275035]='',
[275036]='',
[275037]='',
[275038]='',
[275039]='',
[275040]='',
[275041]='',
[275042]='',
[275043]='',
[275044]='',
[275045]='',
[275046]='',
[275047]='',
[275048]='',
[275049]='',
[275050]='',
[275051]='',
[275052]='',
[275053]='',
[275054]='',
[275055]='',
[275056]='',
[275057]='',
[275058]='',
[275059]='',
[275060]='',
[275061]='',
[275062]='',
[275063]='',
[275064]='',
[275065]='',
[275066]='',
[275067]='',
[275068]='',
[275069]='',
[275070]='',
[275071]='',
[275072]='',
[275101]='',
[275102]='',
[275103]='',
[275104]='',
[275105]='',
[275106]='',
[275107]='',
[275108]='',
[275109]='',
[275110]='',
[275111]='',
[275112]='',
[275113]='',
[275114]='',
[275115]='',
[275116]='',
[275117]='',
[275118]='',
[275119]='',
[275120]='',
[275121]='',
[275122]='',
[275123]='',
[275124]='',
[275125]='',
[275126]='',
[275127]='',
[275128]='',
[275129]='',
[275130]='',
[275131]='',
[275132]='',
[275133]='',
[275134]='',
[275135]='',
[275136]='',
[275137]='',
[275138]='',
[275139]='',
[275140]='',
[275141]='',
[275142]='',
[275143]='',
[275144]='',
[275145]='',
[275146]='',
[275147]='',
[275148]='',
[275149]='',
[275150]='',
[275151]='',
[275152]='',
[275153]='',
[275154]='',
[275155]='',
[275156]='',
[275157]='',
[275158]='',
[275159]='',
[275160]='',
[275161]='',
[275162]='',
[275163]='',
[275164]='',
[275165]='',
[275166]='',
[275167]='',
[275168]='',
[275169]='',
[275170]='',
[275171]='',
[275172]='',
[275173]='',
[275174]='',
[275175]='',
[275176]='',
[275177]='',
[275178]='',
[275201]='',
[275202]='',
[275203]='',
[275204]='',
[275205]='',
[275206]='',
[275207]='',
[275208]='',
[275209]='',
[275210]='',
[275211]='',
[275212]='',
[275213]='',
[275214]='',
[275215]='',
[275216]='',
[275217]='',
[275218]='',
[275219]='',
[275220]='',
[275221]='',
[275222]='',
[275223]='',
[275224]='',
[275225]='',
[275226]='',
[275227]='',
[275228]='',
[275229]='',
[275230]='',
[275231]='',
[275232]='',
[275233]='',
[275234]='',
[275235]='',
[275236]='',
[275237]='',
[275238]='',
[275239]='',
[275240]='',
[275241]='',
[275242]='',
[275243]='',
[275244]='',
[275245]='',
[275246]='',
[275247]='',
[275248]='',
[275249]='',
[275250]='',
[275251]='',
[275252]='',
[275253]='',
[275254]='',
[275255]='',
[275256]='',
[275257]='',
[275258]='',
[275259]='',
[275260]='',
[275261]='',
[275262]='',
[275263]='',
[275264]='',
[275265]='',
[275266]='',
[275267]='',
[275268]='',
[275269]='',
[275270]='',
[275271]='',
[275272]='',
[275273]='',
[275274]='',
[275275]='',
[275276]='',
[275277]='',
[275278]='',
[275279]='',
[275280]='',
[275281]='',
[275282]='',
[275283]='',
[275284]='',
[275285]='',
[275286]='',
[275287]='',
[275288]='',
[275289]='',
[275290]='',
[275291]='',
[275292]='',
[275293]='',
[275294]='',
[275295]='',
[275296]='',
[275297]='',
[275298]='',
[275299]='',
[275300]='',
[275301]='',
[275302]='',
[275303]='',
[275304]='',
[275305]='',
[275306]='',
[275307]='',
[275308]='',
[275309]='',
[275310]='',
[275311]='',
[275312]='',
[275313]='',
[275314]='',
[275315]='',
[275316]='',
[275317]='',
[275318]='',
[275319]='',
[275320]='',
[275321]='',
[275322]='',
[275323]='',
[275324]='',
[275325]='',
[275326]='',
[275327]='',
[275328]='',
[275329]='',
[275330]='',
[275331]='',
[275332]='',
[275401]='',
[275402]='',
[275403]='',
[275404]='',
[275405]='',
[275406]='',
[275407]='',
[275408]='',
[275409]='',
[275410]='',
[275411]='',
[275412]='',
[275413]='',
[275414]='',
[275415]='',
[275416]='',
[275417]='',
[275418]='',
[275419]='',
[275420]='',
[275421]='',
[275422]='',
[275423]='',
[275424]='',
[275425]='',
[275426]='',
[275427]='',
[275428]='',
[275429]='',
[275430]='',
[275431]='',
[275432]='',
[275433]='',
[275434]='',
[275435]='',
[275436]='',
[275437]='',
[275438]='',
[275439]='',
[275440]='',
[275441]='',
[275442]='',
[275443]='',
[275444]='',
[275445]='',
[275446]='',
[275447]='',
[275448]='',
[275449]='',
[275450]='',
[275451]='',
[275452]='',
[275453]='',
[275454]='',
[275455]='',
[275456]='',
[275457]='',
[275458]='',
[275459]='',
[275460]='',
[275461]='',
[275462]='',
[275463]='',
[275464]='',
[275465]='',
[275466]='',
[275467]='',
[275468]='',
[275469]='',
[275470]='',
[275471]='',
[275472]='',
[275473]='',
[275474]='',
[275475]='',
[275476]='',
[275477]='',
[275478]='',
[275479]='',
[275480]='',
[275481]='',
[275482]='',
[275483]='',
[275484]='',
[275485]='',
[275486]='',
[275487]='',
[275488]='',
[275489]='',
[275490]='',
[275491]='',
[275492]='',
[275493]='',
[275494]='',
[275495]='',
[275496]='',
[275497]='',
[275498]='',
[275499]='',
[275500]='',
[275501]='',
[275502]='',
[275503]='',
[275504]='',
[275505]='',
[275506]='',
[275507]='',
[275508]='',
[275509]='',
[275510]='',
[275511]='',
[275512]='',
[275513]='',
[275514]='',
[275515]='',
[275516]='',
[275517]='',
[275518]='',
[275519]='',
[275520]='',
[275521]='',
[275522]='',
[275523]='',
[275524]='',
[275525]='',
[275526]='',
[275527]='',
[275528]='',
[275529]='',
[275530]='',
[275531]='',
[275532]='',
[275533]='',
[275534]='',
[275535]='',
[275536]='',
[275537]='',
[275538]='',
[275539]='',
[275540]='',
[275541]='',
[275542]='',
[275543]='',
[275544]='',
[275545]='',
[275546]='',
[275547]='',
[275548]='',
[275549]='',
[275550]='',
[275551]='',
[275552]='',
[275553]='',
[275554]='',
[275555]='',
[275556]='',
[275557]='',
[275558]='',
[275559]='',
[275560]='',
[275561]='',
[275562]='',
[275563]='',
[275564]='',
[275565]='',
[275566]='',
[275567]='',
[275568]='',
[275569]='',
[275570]='',
[275571]='',
[275572]='',
[275573]='',
[275574]='',
[275575]='',
[275576]='',
[275577]='',
[275578]='',
[275579]='',
[275580]='',
[275581]='',
[275582]='',
[275583]='',
[275584]='',
[275585]='',
[275586]='',
[275587]='',
[275588]='',
[275589]='',
[275590]='',
[275591]='',
[275592]='',
[275593]='',
[275594]='',
[275595]='',
[275596]='',
[275597]='',
[275598]='',
[275901]='',
[275902]='',
[275903]='',
[275904]='',
[276000]='',
[276001]='',
[276002]='',
[276003]='',
[276004]='',
[276005]='',
[276006]='',
[276007]='',
[276008]='',
[276009]='',
[276010]='',
[276011]='',
[276012]='',
[276013]='',
[276014]='',
[276015]='',
[276016]='',
[276017]='',
[276100]='',
[276101]='',
[277000]='',
[277001]='',
[277002]='',
[277003]='',
[277004]='',
[277005]='',
[277006]='',
[278001]='',
[278002]='',
[278003]='',
[278004]='',
[278005]='',
[278006]='',
[278007]='',
[278008]='',
[278009]='',
[278010]='',
[278011]='',
[278012]='',
[278013]='',
[278014]='',
[278015]='',
[278016]='',
[278017]='',
[278018]='',
[278019]='',
[278020]='',
[278021]='',
[278022]='',
[278023]='',
[278024]='',
[278025]='',
[278026]='',
[278027]='',
[278028]='',
[278029]='',
[278030]='',
[278031]='',
[278032]='',
[278033]='',
[278034]='',
[278035]='',
[278036]='',
[278037]='',
[278038]='',
[278039]='',
[278040]='',
[278041]='',
[278042]='',
[278043]='',
[278044]='',
[278045]='',
[278046]='',
[278047]='',
[278048]='',
[278049]='',
[278050]='',
[278051]='',
[278052]='',
[278053]='',
[278054]='',
[278055]='',
[278056]='',
[278057]='',
[278058]='',
[278059]='',
[278060]='',
[278061]='',
[278062]='',
[278063]='',
[278064]='',
[278065]='',
[278066]='',
[278067]='',
[278068]='',
[278069]='',
[278070]='',
[278071]='',
[278072]='',
[278101]='',
[278102]='',
[278103]='',
[278104]='',
[278105]='',
[278106]='',
[278107]='',
[278108]='',
[278109]='',
[278110]='',
[278111]='',
[278112]='',
[278113]='',
[278114]='',
[278115]='',
[278116]='',
[278117]='',
[278118]='',
[278119]='',
[278120]='',
[278121]='',
[278122]='',
[278123]='',
[278124]='',
[278125]='',
[278126]='',
[278127]='',
[278128]='',
[278129]='',
[278130]='',
[278131]='',
[278132]='',
[278133]='',
[278134]='',
[278135]='',
[278136]='',
[278137]='',
[278138]='',
[278139]='',
[278140]='',
[278141]='',
[278142]='',
[278143]='',
[278144]='',
[278145]='',
[278146]='',
[278147]='',
[278148]='',
[278149]='',
[278150]='',
[278151]='',
[278152]='',
[278153]='',
[278154]='',
[278155]='',
[278156]='',
[278157]='',
[278158]='',
[278159]='',
[278160]='',
[278161]='',
[278162]='',
[278163]='',
[278164]='',
[278165]='',
[278166]='',
[278167]='',
[278168]='',
[278169]='',
[278170]='',
[278171]='',
[278172]='',
[278173]='',
[278174]='',
[278175]='',
[278176]='',
[278177]='',
[278178]='',
[278201]='',
[278202]='',
[278203]='',
[278204]='',
[278205]='',
[278206]='',
[278207]='',
[278208]='',
[278209]='',
[278210]='',
[278211]='',
[278212]='',
[278213]='',
[278214]='',
[278215]='',
[278216]='',
[278217]='',
[278218]='',
[278219]='',
[278220]='',
[278221]='',
[278222]='',
[278223]='',
[278224]='',
[278225]='',
[278226]='',
[278227]='',
[278228]='',
[278229]='',
[278230]='',
[278231]='',
[278232]='',
[278233]='',
[278234]='',
[278235]='',
[278236]='',
[278237]='',
[278238]='',
[278239]='',
[278240]='',
[278241]='',
[278242]='',
[278243]='',
[278244]='',
[278245]='',
[278246]='',
[278247]='',
[278248]='',
[278249]='',
[278250]='',
[278251]='',
[278252]='',
[278253]='',
[278254]='',
[278255]='',
[278256]='',
[278257]='',
[278258]='',
[278259]='',
[278260]='',
[278261]='',
[278262]='',
[278263]='',
[278264]='',
[278265]='',
[278266]='',
[278267]='',
[278268]='',
[278269]='',
[278270]='',
[278271]='',
[278272]='',
[278273]='',
[278274]='',
[278275]='',
[278276]='',
[278277]='',
[278278]='',
[278279]='',
[278280]='',
[278281]='',
[278282]='',
[278283]='',
[278284]='',
[278285]='',
[278286]='',
[278287]='',
[278288]='',
[278289]='',
[278290]='',
[278291]='',
[278292]='',
[278293]='',
[278294]='',
[278295]='',
[278296]='',
[278297]='',
[278298]='',
[278299]='',
[278300]='',
[278301]='',
[278302]='',
[278303]='',
[278304]='',
[278305]='',
[278306]='',
[278307]='',
[278308]='',
[278309]='',
[278310]='',
[278311]='',
[278312]='',
[278313]='',
[278314]='',
[278315]='',
[278316]='',
[278317]='',
[278318]='',
[278319]='',
[278320]='',
[278321]='',
[278322]='',
[278323]='',
[278324]='',
[278325]='',
[278326]='',
[278327]='',
[278328]='',
[278329]='',
[278330]='',
[278331]='',
[278332]='',
[278401]='',
[278402]='',
[278403]='',
[278404]='',
[278405]='',
[278406]='',
[278407]='',
[278408]='',
[278409]='',
[278410]='',
[278411]='',
[278412]='',
[278413]='',
[278414]='',
[278415]='',
[278416]='',
[278417]='',
[278418]='',
[278419]='',
[278420]='',
[278421]='',
[278422]='',
[278423]='',
[278424]='',
[278425]='',
[278426]='',
[278427]='',
[278428]='',
[278429]='',
[278430]='',
[278431]='',
[278432]='',
[278433]='',
[278434]='',
[278435]='',
[278436]='',
[278437]='',
[278438]='',
[278439]='',
[278440]='',
[278441]='',
[278442]='',
[278443]='',
[278444]='',
[278445]='',
[278446]='',
[278447]='',
[278448]='',
[278449]='',
[278450]='',
[278451]='',
[278452]='',
[278453]='',
[278454]='',
[278455]='',
[278456]='',
[278457]='',
[278458]='',
[278459]='',
[278460]='',
[278461]='',
[278462]='',
[278463]='',
[278464]='',
[278465]='',
[278466]='',
[278467]='',
[278468]='',
[278469]='',
[278470]='',
[278471]='',
[278472]='',
[278473]='',
[278474]='',
[278475]='',
[278476]='',
[278477]='',
[278478]='',
[278479]='',
[278480]='',
[278481]='',
[278482]='',
[278483]='',
[278484]='',
[278485]='',
[278486]='',
[278487]='',
[278488]='',
[278489]='',
[278490]='',
[278491]='',
[278492]='',
[278493]='',
[278494]='',
[278495]='',
[278496]='',
[278497]='',
[278498]='',
[278499]='',
[278500]='',
[278501]='',
[278502]='',
[278503]='',
[278504]='',
[278505]='',
[278506]='',
[278507]='',
[278508]='',
[278509]='',
[278510]='',
[278511]='',
[278512]='',
[278513]='',
[278514]='',
[278515]='',
[278516]='',
[278517]='',
[278518]='',
[278519]='',
[278520]='',
[278521]='',
[278522]='',
[278523]='',
[278524]='',
[278525]='',
[278526]='',
[278527]='',
[278528]='',
[278529]='',
[278530]='',
[278531]='',
[278532]='',
[278533]='',
[278534]='',
[278535]='',
[278536]='',
[278537]='',
[278538]='',
[278539]='',
[278540]='',
[278541]='',
[278542]='',
[278543]='',
[278544]='',
[278545]='',
[278546]='',
[278547]='',
[278548]='',
[278549]='',
[278550]='',
[278551]='',
[278552]='',
[278553]='',
[278554]='',
[278555]='',
[278556]='',
[278557]='',
[278558]='',
[278559]='',
[278560]='',
[278561]='',
[278562]='',
[278563]='',
[278564]='',
[278565]='',
[278566]='',
[278567]='',
[278568]='',
[278569]='',
[278570]='',
[278571]='',
[278572]='',
[278573]='',
[278574]='',
[278575]='',
[278576]='',
[278577]='',
[278578]='',
[278579]='',
[278580]='',
[278581]='',
[278582]='',
[278583]='',
[278584]='',
[278585]='',
[278586]='',
[278587]='',
[278588]='',
[278589]='',
[278590]='',
[278591]='',
[278592]='',
[278593]='',
[278594]='',
[278595]='',
[278596]='',
[278597]='',
[278598]='',
[278901]='',
[278902]='',
[278903]='',
[278904]='',
[279101]='',
[279102]='',
[279103]='',
[279104]='',
[279105]='',
[279106]='',
[279107]='',
[279108]='',
[279109]='',
[279110]='',
[279111]='',
[279112]='',
[279113]='',
[279114]='',
[279115]='',
[279116]='',
[279117]='',
[279118]='',
[279119]='',
[279120]='',
[279121]='',
[279122]='',
[279123]='',
[279124]='',
[279125]='',
[279126]='',
[279127]='',
[279128]='',
[279129]='',
[279130]='',
[279131]='',
[279201]='',
[279202]='',
[279300]='',
[282104]='',
[282105]='',
[282106]='',
[282107]='',
[282108]='',
[282109]='',
[282125]='',
[282202]='',
[282203]='',
[282204]='',
[282205]='',
[282206]='',
[282207]='',
[282208]='',
[282209]='',
[282210]='',
[282211]='',
[282212]='',
[282213]='',
[282218]='',
[282226]='',
[282227]='',
[282247]='',
[282250]='',
[282251]='',
[282252]='',
[282253]='',
[279001]='',
[279002]='',
[280001]='',
[280002]='',
[280003]='',
[283590]='',
[280702]='',
[294001]='',
[294002]='',
[294003]='',
[294004]='',
[294201]='',
[294202]='',
[296000]='',
[296001]='',
[300000]='',
[300001]='',
[300002]='',
[300003]='',
[300004]='',
[300005]='',
[300006]='',
[300007]='',
[300008]='',
[300009]='',
[300010]='',
[300011]='',
[300012]='',
[300013]='',
[300014]='',
[300015]='',
[300016]='',
[300017]='',
[300018]='',
[300019]='',
[300020]='',
[300021]='',
[300022]='',
[300023]='',
[300024]='',
[300025]='',
[300026]='',
[300027]='',
[300028]='',
[300029]='',
[300030]='',
[300031]='',
[300032]='',
[300033]='',
[300034]='',
[300035]='',
[300036]='',
[300037]='',
[300038]='',
[300039]='',
[300040]='',
[300200]='',
[300201]='',
[300202]='',
[300203]='',
[300204]='',
[300205]='',
[300206]='',
[300207]='',
[300208]='',
[300209]='',
[300210]='',
[300211]='',
[300212]='',
[300213]='',
[300214]='',
[300215]='',
[300216]='',
[300217]='',
[300218]='',
[300219]='',
[300220]='',
[300221]='',
[300222]='',
[300223]='',
[300224]='',
[300225]='',
[300226]='',
[300227]='',
[300228]='',
[300229]='',
[300230]='',
[300231]='',
[300232]='',
[300233]='',
[300234]='',
[300235]=[[وفقًا للقوانين واللوائح والأحكام التنظيمية ذات الصلة في جمهورية الصين الشعبية، نضمن لك ممارسة الحقوق التالية فيما يتعلق بمعلوماتك الشخصية:

6.1 الاستعلام والتعديل والتصحيح

يمكنك الاستعلام عن اسم حسابك وكلمة مروره ورقم هاتفك الجوال المرتبط بتسجيل الدخول إلى مركز إدارة الحسابات على موقعنا الرسمي (رابط: http://passport.q1.com/) أو من خلال قسم المعلومات الشخصية في اللعبة - دليل خدمة العملاء - دليل خدمة العملاء - الاتصال بخدمة العملاء. يمكنك النقر على الصورة الرمزية - تبديل الحساب - سياسة الخصوصية لعرض سياسة الخصوصية؛ كما يمكنك تعديل وتصحيح رقم هاتفك الجوال المرتبط وعنوان بريدك الإلكتروني المرتبط وكلمة مرور حسابك، باستثناء ما تنص عليه القوانين واللوائح. يمكنك أيضًا التواصل معنا عبر قنوات التعليقات المدرجة في قسم "كيفية التواصل معنا" من هذه السياسة، وسنرد عليك في أقرب وقت ممكن.

٦.٢ الحذف

في الحالات التالية، يمكنك طلب حذف معلوماتك الشخصية، أو التواصل معنا عبر معلومات الاتصال المذكورة في هذه السياسة، أو حذفها بنفسك من اللعبة عبر قسم المعلومات الشخصية - خدمة العملاء اليدوية - خدمة العملاء اليدوية - التواصل مع خدمة العملاء:

(١) إذا كان جمعنا واستخدامنا للمعلومات الشخصية يُخالف أحكام القوانين واللوائح أو يُخالف الاتفاقية المبرمة معك؛

(٢) إذا كانت مشاركتنا للمعلومات الشخصية ونقلها إلى طرف ثالث تُخالف أحكام القوانين واللوائح أو تُخالف الاتفاقية المبرمة معك؛

(٣) إذا كان إفصاحنا العلني عن المعلومات الشخصية يُخالف أحكام القوانين واللوائح أو يُخالف الاتفاقية المبرمة معك.

بعد حذف المعلومات من خدماتنا، قد لا نحذف المعلومات المقابلة من نظام النسخ الاحتياطي فورًا، ولكننا سنحذفها عند تحديث النسخة الاحتياطية.

٦.٣ سحب الموافقة

عندما ترغب في سحب تفويضك لنا بجمع معلوماتك الشخصية، يمكنك فتح صفحة هذه السياسة والتمرير لأسفلها. يمكنك سحب موافقتك على هذه السياسة بالنقر على زر "سحب الموافقة". بعد سحب موافقتك، لن نجمع معلوماتك الشخصية بعد الآن، وستغادر لعبتنا. مع ذلك، لن يؤثر قرارك بسحب موافقتك على جمع معلوماتك الشخصية بناءً على تفويضك. بعد سحب موافقتك، سيحتفظ النظام بمعلوماتك الشخصية. إذا كنت ترغب في حذف معلوماتك الشخصية، يُرجى النقر على "تسجيل الخروج" في نافذة التأكيد المنبثقة الثانية للدخول إلى عملية إلغاء الحساب، وتقديم طلب لإلغاء حسابك، وحذف معلوماتك الشخصية.]],
[300236]='',
[300237]=[[٦.٧ إلغاء الحساب

إذا كنت تستوفي شروط الخدمة والقوانين واللوائح الوطنية ذات الصلة، يمكنك إلغاء حسابك في اللعبة بالانتقال إلى "المعلومات الشخصية" - "خدمة العملاء اليدوية" - "خدمة العملاء اليدوية" - "التواصل مع خدمة العملاء" - "إلغاء الحساب". يمكنك أيضًا تسجيل الدخول إلى مركز إدارة الحساب على موقعنا الرسمي (الرابط: http://passport.q1.com/)، واختيار "إلغاء الحساب"، والنقر على "اتفاقية إلغاء الحساب". بعد التحقق من رقم هاتفك الجوال ورقم الهوية، يمكنك التحقق من الرصيد أو الدور في حسابك. إذا كنت لا تزال ترغب في إلغاء حساب اللعبة، يمكنك تقديم طلب إلغاء الحساب. سيتواصل معك فريق خدمة العملاء خلال ٢-٧ أيام عمل ويساعدك في إلغاء حسابك بعد التحقق. مع ذلك، تجدر الإشارة إلى أن الحساب الذي تستخدمه لتسجيل الدخول إلى لعبتنا يمكنه تسجيل الدخول إلى جميع ألعابنا وشركاتنا التابعة في نفس الوقت. إذا كنت بحاجة إلى إلغاء حساب اللعبة، فسيتم إلغاء جميع ألعاب حسابك معًا. إذا كان لا يزال هناك رصيد أو شخصية لعبة غير محذوفة في حسابك، فبعد موافقتك على إلغاء حسابك، يُعتبر رصيد اللعبة والشخصية مهجورين تمامًا، وسيتم مسح جميع سجلات بيانات اللعبة الخاصة بك في الحساب ولن تتمكن من استعادتها. بعد إلغاء الحساب بنجاح، لن تتمكن من استخدامه لتسجيل الدخول إلى أي منتج تابع لنا أو لشركاتنا التابعة.

يمكنك أيضًا اختيار التواصل معنا عبر قنوات التعليقات المدرجة في قسم "كيفية التواصل معنا" من هذه السياسة، وسنرد عليك في أقرب وقت ممكن. بعد إلغاء حسابك، سنتوقف عن تقديم جميع خدماتنا أو جزء منها لك، وستظل معلوماتك الشخصية غير قابلة للاسترداد أو الوصول إليها أو مجهولة المصدر، ما لم تنص القوانين أو اللوائح أو اللوائح الإدارية أو الوثائق التنظيمية الحكومية على خلاف ذلك.

نذكرك هنا بضرورة اختيار إلغاء حسابك بعناية، لأن إلغاء حسابك سيؤثر على استخدامك المعتاد لخدماتنا أو سيسبب لك العديد من الإزعاج في استخدامك اللاحق لها. بمجرد إلغاء حسابك، لن يتم استعادته. يرجى الاحتفاظ بنسخة احتياطية من جميع المعلومات والبيانات المتعلقة بحسابك قبل العملية. لا نتحمل أي مسؤولية عن أي آثار سلبية قد تلحق بك نتيجة طلب إلغاء حسابك.

في حال تسجيل الدخول بسرعة عبر حساب جهة خارجية، ستحتاج إلى التواصل مع الجهة الخارجية لمعالجة عملية إلغاء حسابك.

يرجى العلم أننا لن نلغي معلوماتك الشخصية عند حذف تطبيق لعبتنا على جهازك المحمول فقط، ولن تُحذف معلوماتك الشخصية. لا يزال يتعين عليك إلغاء حسابك لتحقيق الغرض المذكور أعلاه. عند إلغاء حسابك، يُعتبر ذلك بمثابة سحب لموافقتك على هذه السياسة، ولن نجمع المعلومات الشخصية المتعلقة بها بعد الآن. مع ذلك، لن يؤثر قرارك بسحب الموافقة على جمع المعلومات الشخصية الذي تم إجراؤه سابقًا بناءً على تفويضك.]],
[300238]='',
[300239]='',
[300240]='',
[300241]='',
[300242]='',
[300243]='',
[300244]='',
[300245]='',
[300246]='',
[300247]='',
[300248]='',
[300249]='',
[300250]='',
[300251]='',
[300252]='',
[300253]='',
[300270]='',
[300271]='',
[300272]='',
[300273]='',
[300274]='',
[300275]='',
[301041]='',
[301042]='',
[310000]='',
[310005]='',
[310006]='',
[310011]='',
[310012]='',
[310013]='',
[310066]='',
[310067]='',
[310068]='',
[310069]='',
[310070]='',
[310071]='',
[310072]='',
[310073]='',
[310074]='',
[310075]='',
[310076]='',
[310077]='',
[310078]='',
[310079]='',
[310080]='',
[310081]='',
[310082]='',
[310083]='',
[310084]='',
[310085]='',
[310086]='',
[310087]='',
[310088]='',
[310089]='',
[310090]='',
[310091]='',
[310092]='',
[310093]='',
[310095]='',
[310096]='',
[310097]='',
[310098]='',
[310099]='',
[310100]='',
[310101]='',
[310102]='',
[310103]='',
[310104]='',
[310105]='',
[310106]='',
[310109]='',
[310008]='',
[310009]='',
[310010]='',
[310062]='',
[310063]='',
[310064]='',
[310065]='',
[310146]='',
[310148]='',
[310149]='',
[310150]='',
[310151]='',
[310152]='',
[310153]='',
[310154]='',
[310160]='',
[310187]='',
[310188]='',
[310189]='',
[310190]='',
[310191]='',
[310192]='',
[310193]='',
[310215]='',
[310216]='',
[310217]='',
[310218]='',
[310221]='',
[310222]='',
[310299]='',
[310300]='',
[310301]='',
[310302]='',
[310316]='',
[310455]='',
[310469]='',
[310470]='',
[310471]='',
[310472]='',
[310500]='',
[310501]='',
[310502]='',
[310503]='',
[310504]='',
[310505]='',
[310495]='',
[310496]='',
[310497]='',
[310498]='',
[310510]='',
[310511]='',
[310512]='',
[310513]='',
[310540]='',
[310541]='',
[310584]='',
[310586]='',
[310590]='',
[310591]='',
[310592]='',
[310593]='',
[310597]='',
[310598]='',
[310599]='',
[310600]='',
[310601]='',
[310602]='',
[310603]='',
[310604]='',
[310605]='',
[310606]='',
[310607]='',
[310608]='',
[310610]='',
[310611]='',
[310612]='',
[310613]='',
[310614]='',
[310615]='',
[310616]='',
[310617]='',
[310618]='',
[310619]='',
[310620]='',
[310630]='',
[310631]='',
[310632]='',
[310633]='',
[310634]='',
[310635]='',
[310636]='',
[310637]='',
[310638]='',
[310639]='',
[310640]='',
[310641]='',
[310642]='',
[310643]='',
[310644]='',
[310645]='',
[310646]='',
[310647]='',
[310648]='',
[310649]='',
[310650]='',
[310651]='',
[310652]='',
[310653]='',
[310654]='',
[310655]='',
[310656]='',
[310657]='',
[310658]='',
[310659]='',
[310660]='',
[310661]='',
[310662]='',
[310663]='',
[310664]='',
[310665]='',
[310666]='',
[310667]='',
[310668]='',
[310669]='',
[310670]='',
[310671]='',
[310672]='',
[310673]='',
[310674]='',
[310675]='',
[310676]='',
[310677]='',
[310681]='',
[310682]='',
[310686]='',
[310687]='',
[310688]='',
[310689]='',
[310690]='',
[310691]='',
[310700]='',
[310701]='',
[310702]='',
[310703]='',
[310704]='',
[310710]='',
[310711]='',
[310714]='',
[310715]='',
[310717]='',
[310757]='',
[310821]='',
[330000]='',
[330001]='',
[330002]='',
[330003]='',
[330004]='',
[330005]='',
[330006]='',
[330007]='',
[330008]='',
[330009]='',
[330010]='',
[330011]='',
[330012]='',
[330013]='',
[330014]='',
[330015]='',
[330016]='',
[330017]='',
[330018]='',
[330019]='',
[330020]='',
[330057]='',
[330058]='',
[330059]='',
[330060]=[[في وضعية "الهاوية"، بعد استخدام "نظرة الهاوية"، تدخل إيزولد في حالة "الكسوف".

"الكسوف": عندما يوشك بطل العدو على استهلاك طاقة لاستخدام مهارة نشطة، تزيل إيزولد 50 نقطة طاقة من بطل العدو، وتُزال حالة الكسوف بعد تفعيل تأثير إزالة الطاقة (فعال فقط في سيناريوهات لاعب ضد لاعب).]],
[330061]='',
[330062]='',
[330063]='',
[330064]='',
[330065]='',
[330066]='',
[330067]='',
[330068]='',
[330069]='',
[330070]='',
[330071]='',
[330072]='',
[330073]='',
[330074]='',
[330075]='',
[330095]='',
[330096]='',
[330097]='',
[330098]='',
[330099]='',
[330100]='',
[330101]='',
[330102]='',
[330103]='',
[330104]='',
[330105]='',
[330106]='',
[330107]='',
[330108]='',
[330109]='',
[330110]='',
[330111]='',
[330112]='',
[330113]='',
[350000]='',
[350001]='',
[350002]='',
[350003]='',
[350004]='',
[350005]='',
[350006]='',
[350007]='',
[350008]='',
[350009]='',
[372000]='',
[372001]='',
[372003]='',
[372004]='',
[372005]='',
[372006]='',
[372011]='',
[372012]='',
[373000]='',
[373001]='',
[373002]='',
[373003]='',
[373004]='',
[373005]='',
[373006]='',
[373007]='',
[373008]='',
[373009]='',
[373010]='',
[373011]='',
[373021]='',
[373022]='',
[373023]='',
[373024]='',
[373025]='',
[373026]='',
[373027]='',
[373028]='',
[373029]='',
[373030]='',
[373031]='',
[375000]='',
[375001]='',
[375002]='',
[375003]='',
[375004]='',
[375005]='',
[380000]='',
[380001]='',
[283701]='',
[283702]='',
[283703]='',
[283704]='',
[283705]='',
[290091]='',
[290092]='',
[290093]='',
[290094]='',
[290095]='',
[290096]='',
[381531]='',
[381532]='',
[381680]='',
[381931]='',
[381932]='',
[384255]='',
[384400]='',
[385000]='',
[385010]='',
[385011]='',
[385012]='',
[385005]='',
[385020]='',
[385021]='',
[385022]='',
[385023]='',
[385024]='',
[385025]='',
[385026]='',
[385027]='',
[385028]='',
[385029]='',
[385030]='',
[385031]='',
[385032]='',
[385033]='',
[385034]='',
[385035]='',
[385036]='',
[385037]='',
[385038]='',
[385039]='',
[385040]='',
[385041]='',
[385042]='',
[385043]='',
[385044]='',
[385045]='',
[385046]='',
[385047]='',
[385048]='',
[385049]='',
[385050]='',
[385051]='',
[385052]='',
[385053]='',
[385054]='',
[385055]='',
[385056]='',
[385057]='',
[385058]='',
[385059]='',
[385060]='',
[385061]='',
[385062]='',
[385063]='',
[385064]='',
[385065]='',
[385066]='',
[385067]='',
[385068]='',
[385069]='',
[385070]='',
[385071]='',
[385072]='',
[385073]='',
[385074]='',
[385075]='',
[385076]='',
[385077]='',
[385078]='',
[385079]='',
[385800]='',
[385801]='',
[385802]='',
[385803]='',
[385804]='',
[385805]='',
[385806]='',
[385807]='',
[385808]='',
[385809]='',
[385810]='',
[385811]='',
[385812]='',
[385813]='',
[385814]='',
[385815]='',
[385816]='',
[385817]='',
[385818]='',
[385819]='',
[385820]='',
[385821]='',
[385822]='',
[385823]='',
[385824]='',
[393000]='',
[393001]='',
[393002]='',
[393003]='',
[393004]='',
[393005]='',
[393006]='',
[393007]='',
[393008]='',
[393009]='',
[393010]='',
[393011]='',
[393012]='',
[393013]='',
[393014]='',
[393015]='',
[393016]='',
[393017]='',
[393018]='',
[393019]='',
[393020]='',
[393021]='',
[393022]='',
[393023]='',
[385999]='',
[383000]='',
[390100]='',
[390101]='',
[390102]='',
[390103]='',
[390104]='',
[390105]='',
[390106]='',
[390107]='',
[390108]='',
[390109]='',
[390110]='',
[390111]='',
[390112]='',
[390113]='',
[390114]='',
[390115]='',
[392000]='',
[392001]='',
[392002]='',
[392003]='',
[392004]='',
[392005]='',
[392006]='',
[392007]='',
[392008]='',
[392009]='',
[392010]='',
[392011]='',
[392012]='',
[392013]='',
[392014]='',
[392016]='',
[392017]='',
[392018]='',
[392019]='',
[392020]='',
[392021]='',
[392022]='',
[392023]='',
[392024]='',
[392025]='',
[392026]='',
[392027]='',
[392030]='',
[392031]='',
[392032]='',
[392033]='',
[392034]='',
[392035]='',
[392036]='',
[392037]='',
[392038]='',
[392039]='',
[392040]='',
[392051]='',
[392053]='',
[392054]='',
[392055]='',
[392056]='',
[392057]='',
[392060]='',
[392061]='',
[392062]='',
[392063]='',
[392064]='',
[394001]='',
[394002]='',
[394003]='',
[394004]='',
[394005]='',
[394006]='',
[402001]='',
[402150]='',
[402151]='',
[402152]='',
[402153]='',
[402154]='',
[402155]='',
[402156]='',
[402157]='',
[402158]='',
[402159]='',
[402160]='',
[402161]='',
[402162]='',
[402163]='',
[402164]='',
[402165]='',
[402166]='',
[402167]='',
[402168]='',
[402169]='',
[402170]='',
[402171]='',
[402172]='',
[402173]='',
[402174]='',
[402175]='',
[402176]='',
[402177]='',
[402178]='',
[402179]='',
[402180]='',
[402181]='',
[402182]='',
[402183]='',
[402184]='',
[402185]='',
[402186]='',
[402187]='',
[402188]='',
[402189]='',
[402190]='',
[402191]='',
[402192]='',
[402193]='',
[402194]='',
[402195]='',
[402196]='',
[402197]='',
[402198]='',
[402199]='',
[402200]='',
[402201]='',
[402202]='',
[402203]='',
[402204]='',
[402205]='',
[402206]='',
[402207]='',
[402208]='',
[402209]='',
[402210]='',
[402211]='',
[402212]='',
[402213]='',
[402214]='',
[402215]='',
[402216]='',
[402217]='',
[402218]='',
[402219]='',
[402220]='',
[402221]='',
[402222]='',
[402223]='',
[402224]='',
[402225]='',
[402226]='',
[402227]='',
[402228]='',
[402229]='',
[402230]='',
[402231]='',
[402232]='',
[402233]='',
[402234]='',
[402235]='',
[402236]='',
[402237]='',
[402238]='',
[402239]='',
[402240]='',
[402241]='',
[402242]='',
[402243]='',
[402244]='',
[402245]='',
[402246]='',
[402247]='',
[402248]='',
[402249]='',
[402250]='',
[402251]='',
[402252]='',
[402253]='',
[402254]='',
[402255]='',
[402256]='',
[402257]='',
[402258]='',
[402259]='',
[402260]='',
[402261]='',
[402262]='',
[402263]='',
[402264]='',
[402265]='',
[402266]='',
[402267]='',
[402268]='',
[402269]='',
[402270]='',
[402271]='',
[402272]='',
[402273]='',
[402274]='',
[402275]='',
[402276]='',
[402277]='',
[402278]='',
[402279]='',
[402280]='',
[402281]='',
[402282]='',
[402283]='',
[402284]='',
[402285]='',
[402286]='',
[402287]='',
[402288]='',
[402289]='',
[402290]='',
[402400]='',
[402500]='',
[402501]='',
[402502]='',
[402503]='',
[402600]='',
[404350]='',
[404351]='',
[404352]='',
[404353]='',
[404354]='',
[404355]='',
[404356]='',
[404357]='',
[404358]='',
[404359]='',
[404360]='',
[404361]='',
[404362]='',
[404363]='',
[404364]='',
[404365]='',
[404367]='',
[404369]='',
[470001]='',
[470002]='',
[470003]='',
[470004]='',
[470005]='',
[470006]='',
[470007]='',
[470008]='',
[470009]='',
[470010]='',
[470011]='',
[470012]='',
[470013]='',
[470014]='',
[470015]='',
[470016]='',
[470017]='',
[470018]='',
[470019]='',
[470020]='',
[470021]='',
[470022]='',
[470023]='',
[470024]='',
[470025]='',
[470026]='',
[470027]='',
[470028]='',
[470029]='',
[470030]='',
[470031]='',
[470032]='',
[470033]='',
[560001]='',
[560002]='',
[560003]='',
[560004]='',
[560005]='',
[560006]='',
[560007]='',
[560008]='',
[560009]='',
[560010]='',
[560011]='',
[560012]='',
[560013]='',
[560014]='',
[560015]='',
[560016]='',
[560017]='',
[560018]='',
[560019]='',
[560020]='',
[560021]='',
[560022]='',
[560023]='',
[560024]='',
[560025]='',
[560026]='',
[560027]='',
[560028]='',
[560029]='',
[560030]='',
[560031]='',
[560032]='',
[560033]='',
[560034]='',
[560035]='',
[560036]='',
[560037]='',
[560038]='',
[560039]='',
[560040]='',
[560041]='',
[560042]='',
[560043]='',
[560044]='',
[560045]='',
[560046]='',
[560047]='',
[560048]='',
[560049]='',
[560050]='',
[560051]='',
[560052]='',
[560053]='',
[560054]='',
[560055]='',
[560056]='',
[560057]='',
[560058]='',
[560059]='',
[560060]='',
[560061]='',
[560062]='',
[560063]='',
[560064]='',
[560065]='',
[560066]='',
[560067]='',
[560068]='',
[560069]='',
[560070]='',
[560071]='',
[560072]='',
[560073]='',
[560074]='',
[560075]='',
[560076]='',
[560077]='',
[560078]='',
[560079]='',
[560080]='',
[560081]='',
[560082]='',
[560083]='',
[560084]='',
[560085]='',
[560086]='',
[560087]='',
[560088]='',
[560089]='',
[560090]='',
[560091]='',
[560092]='',
[560093]='',
[560094]='',
[560095]='',
[560096]='',
[560097]='',
[560098]='',
[560099]='',
[560100]='',
[560101]='',
[560102]='',
[560103]='',
[560104]='',
[560105]='',
[560106]='',
[560107]='',
[560108]='',
[560109]='',
[560110]='',
[560111]='',
[560112]='',
[560113]='',
[560114]='',
[560115]='',
[560116]='',
[560117]='',
[560118]='',
[560119]='',
[560120]='',
[560121]='',
[560122]='',
[560123]='',
[560124]='',
[560125]='',
[560126]='',
[560127]='',
[560128]='',
[560129]='',
[560130]='',
[560131]='',
[560132]='',
[560133]='',
[560134]='',
[560135]='',
[560136]='',
[560137]='',
[560138]='',
[560139]='',
[560140]='',
[560141]='',
[560142]='',
[560143]='',
[560144]='',
[560145]='',
[560146]='',
[560147]='',
[560148]='',
[560149]='',
[560150]='',
[560151]='',
[560152]='',
[560153]='',
[560154]='',
[560155]='',
[560156]='',
[560157]='',
[560158]='',
[560159]='',
[560160]='',
[560161]='',
[560162]='',
[560163]='',
[560164]='',
[560165]='',
[560166]='',
[560167]='',
[560168]='',
[560169]='',
[560170]='',
[560171]='',
[560172]='',
[560173]='',
[560174]='',
[560175]='',
[560176]='',
[560177]='',
[560178]='',
[560179]='',
[560180]='',
[560181]='',
[560182]='',
[560183]='',
[560184]='',
[560185]='',
[560186]='',
[560187]='',
[560188]='',
[560189]='',
[560190]='',
[560191]='',
[560192]='',
[560193]='',
[560194]='',
[560195]='',
[560196]='',
[560197]='',
[560198]='',
[560199]='',
[560200]='',
[560201]='',
[560202]='',
[560203]='',
[560204]='',
[560205]='',
[560206]='',
[560207]='',
[560208]='',
[560209]='',
[560210]='',
[560211]='',
[560212]='',
[560213]='',
[560214]='',
[560215]='',
[560216]='',
[560217]='',
[560218]='',
[560219]='',
[560220]='',
[560221]='',
[560222]='',
[560223]='',
[560224]='',
[560225]='',
[560226]='',
[560227]='',
[560228]='',
[560229]='',
[560230]='',
[560231]='',
[560232]='',
[560233]='',
[560234]='',
[560235]='',
[560236]='',
[560237]='',
[560238]='',
[560239]='',
[560240]='',
[560241]='',
[560242]='',
[560243]='',
[560244]='',
[560245]='',
[560246]='',
[560247]='',
[560248]='',
[560249]='',
[560250]='',
[560251]='',
[560252]='',
[560253]='',
[560254]='',
[560255]='',
[560256]='',
[560257]='',
[560258]='',
[560259]='',
[560260]='',
[560261]='',
[560262]='',
[560263]='',
[560264]='',
[560265]='',
[560266]='',
[560267]='',
[560268]='',
[560269]='',
[560270]='',
[560271]='',
[560272]='',
[560273]='',
[560274]='',
[560275]='',
[560276]='',
[560277]='',
[560278]='',
[560279]='',
[560280]='',
[560281]='',
[560282]='',
[560283]='',
[560284]='',
[560285]='',
[560286]='',
[560287]='',
[560288]='',
[560289]='',
[560290]='',
[560291]='',
[560292]='',
[560293]='',
[560294]='',
[560295]='',
[560296]='',
[560297]='',
[560298]='',
[560299]='',
[560300]='',
[560301]='',
[560302]='',
[560303]='',
[560304]='',
[560305]='',
[560306]='',
[560307]='',
[560308]='',
[560309]='',
[560310]='',
[560311]='',
[560312]='',
[560313]='',
[560314]='',
[560315]='',
[560316]='',
[560317]='',
[560318]='',
[560319]='',
[560320]='',
[560321]='',
[560322]='',
[560323]='',
[560324]='',
[560325]='',
[560326]='',
[560327]='',
[560328]='',
[560329]='',
[560330]='',
[560331]='',
[560332]='',
[560333]='',
[560334]='',
[560335]='',
[560336]='',
[560337]='',
[560338]='',
[560339]='',
[560340]=[[ابدأ التحدي]],
[560341]='',
[560342]='',
[560343]='',
[560344]='',
[560345]='',
[560346]='',
[560347]='',
[560348]='',
[560349]='',
[560350]='',
[560351]='',
[560352]='',
[560353]='',
[560354]='',
[560355]='',
[560356]='',
[560357]='',
[560358]='',
[560359]='',
[560360]=[[يا رب، لقد حصلت على {%s1} من القوة البدنية]],
[560361]=[[لقد انطلق فريق التجميع الخاص بـ {%s1}.]],
[560362]=[[لقد ذهب الفريق للمشاركة في تجميع {%s1}.]],
[560363]=[[لقد اختفى {%s1}.]],
[560364]=[[{%s1}: دعوة التجمع]],
[560365]=[[لقد انضممت بالفعل إلى المجموعة ولا يمكنك الانضمام مرة أخرى]],
[560366]=[[لقد خرجت من المجموعة]],
[560367]=[[لقد انتهى وقت التجمع الحالي ولا يمكنك الانضمام إلى التجمع بعد الآن]],
[560368]=[[لم يتم العثور على الهدف بعد، لذلك لا يمكن القفز.]],
[560369]='',
[600001]='',
[600002]='',
[600003]='',
[600004]='',
[600005]='',
[600006]='',
[600007]='',
[600008]='',
[600009]='',
[600010]='',
[600011]='',
[600012]='',
[600013]='',
[600014]='',
[600015]='',
[600016]='',
[600017]='',
[600018]='',
[600019]='',
[600020]='',
[600021]='',
[600022]='',
[600023]='',
[600024]='',
[600025]='',
[600026]='',
[600027]='',
[600028]='',
[600029]='',
[600030]='',
[600031]='',
[600032]='',
[600033]='',
[600034]='',
[600035]='',
[600036]='',
[600037]='',
[600038]='',
[600039]='',
[600040]='',
[600041]='',
[600042]='',
[600043]='',
[600044]='',
[600045]='',
[600046]='',
[600047]='',
[600048]='',
[600049]='',
[600050]='',
[600051]='',
[600052]='',
[600053]='',
[600054]='',
[600055]='',
[600056]='',
[600057]='',
[600058]='',
[600059]='',
[600060]='',
[600061]='',
[600062]='',
[600063]='',
[600064]='',
[600065]='',
[600066]='',
[600067]='',
[600068]='',
[600069]='',
[600070]='',
[600071]='',
[600072]='',
[600073]='',
[600074]='',
[600075]='',
[600076]='',
[600077]='',
[600078]='',
[600079]='',
[600080]='',
[600081]='',
[600082]='',
[600083]='',
[600084]='',
[600085]='',
[600086]='',
[600087]='',
[600088]='',
[600089]='',
[600090]='',
[600091]='',
[600092]='',
[600093]='',
[600094]='',
[600095]='',
[600096]='',
[600097]='',
[600098]='',
[600099]='',
[600100]='',
[600101]='',
[600102]='',
[600103]='',
[600104]='',
[600105]='',
[600106]='',
[600107]='',
[600108]='',
[600109]='',
[600110]='',
[600111]='',
[600112]='',
[600113]='',
[600114]='',
[600115]='',
[600116]='',
[600117]='',
[600118]='',
[600119]='',
[600120]='حَشد',
[600121]='',
[600122]='',
[600123]='',
[600124]='',
[600125]='',
[600126]='',
[600127]='',
[600128]='',
[600129]='',
[600130]='',
[600131]='',
[600132]='',
[600133]='',
[600134]='',
[600135]='',
[600136]='',
[600137]='',
[600138]='',
[600139]='',
[600140]='',
[600141]='',
[600142]='',
[600143]='',
[600144]='',
[600145]='',
[600146]='',
[600147]='',
[600148]='',
[600149]='',
[600150]='',
[600151]='',
[600152]='',
[600153]='',
[600154]='',
[600155]='',
[600156]='',
[600157]='',
[600158]='',
[600159]='',
[600160]='',
[600161]='',
[600162]='',
[600163]='',
[600164]='',
[600165]='',
[600166]='',
[600167]='',
[600168]='',
[600169]=[[ساعدني في بناء المستوى {%s1} {%s2}]],
[600170]='',
[600171]='',
[600172]='',
[600173]='',
[600174]='',
[600175]='',
[600176]='',
[600177]='',
[600178]='',
[600179]='',
[600180]='',
[600181]='',
[600182]='',
[600183]='',
[600184]='',
[600185]='',
[600186]='',
[600187]='',
[600188]='',
[600189]='',
[600190]='',
[600191]='',
[600192]='',
[600193]='',
[600194]='',
[600195]='',
[600196]='',
[600197]='',
[600198]='',
[600199]='',
[600200]='',
[600201]='',
[600202]='',
[600203]='',
[600204]='',
[600205]='',
[600206]='',
[600207]='',
[600208]='',
[600209]='',
[600210]='',
[600211]='',
[600212]='',
[600213]='',
[600214]='',
[600215]='',
[600216]='',
[600217]='',
[600218]='',
[600219]='',
[600220]='',
[600221]='',
[600222]='',
[600223]='',
[600224]='',
[600225]='',
[600226]='',
[600227]='',
[600228]='',
[600229]='',
[600230]='',
[600231]='',
[600232]='',
[600233]='',
[600234]='',
[600235]='',
[600236]='',
[600237]='',
[600238]='',
[600239]='',
[600240]='',
[600241]='',
[600242]='',
[600243]='',
[600244]='',
[600245]='',
[600246]='',
[600247]='',
[600248]='',
[600249]='',
[600250]='',
[600251]='',
[600252]='',
[600253]='',
[600254]='',
[600255]='',
[600256]='',
[600257]='',
[600258]='',
[600259]='',
[600260]='',
[600261]='',
[600262]='',
[600263]='',
[600264]='',
[600265]='',
[600266]='',
[600267]='',
[600268]='',
[600269]='',
[600270]='',
[600271]='',
[600272]='',
[600273]='',
[600274]='',
[600275]='',
[600276]='',
[600277]='',
[600278]='',
[600279]='',
[600280]='',
[600281]='',
[600282]='',
[600283]='',
[600284]='',
[600285]='',
[600286]='',
[600287]='',
[600288]='',
[600289]='',
[600290]='',
[600291]='',
[600292]='',
[600293]='',
[600294]='',
[600295]='',
[600296]='',
[600297]='',
[600298]='',
[600299]='',
[600300]='',
[600301]='',
[600302]='',
[600303]='',
[600304]='',
[600305]='',
[600306]='',
[600307]='',
[600308]='',
[600309]='',
[600310]='',
[600311]='',
[600312]='',
[600313]='',
[600314]='',
[600315]='',
[600316]='',
[600317]='',
[600318]='',
[600319]='',
[600320]='',
[600321]='',
[600322]='',
[600323]='',
[600324]='',
[600325]='',
[600326]='',
[600327]='',
[600328]='',
[600329]='',
[600330]='',
[600331]='',
[600332]='',
[600333]='',
[600334]='',
[600335]='',
[600336]='',
[600337]='',
[600338]='',
[600339]='',
[600340]='',
[600341]='',
[600342]='',
[600343]='',
[600344]='',
[600345]='',
[600346]='',
[600347]='',
[600348]='',
[600349]='',
[600350]='',
[600351]='',
[600352]='',
[600353]='',
[600354]='',
[600355]='',
[600356]='',
[600357]='',
[600358]='',
[600359]='',
[600360]='',
[600361]='',
[600362]='',
[600363]='',
[600364]='',
[600365]='',
[600366]='',
[600367]='',
[600368]='',
[600369]='',
[600370]='',
[600371]='',
[600372]='',
[600373]='',
[600374]='',
[600375]='',
[600376]='',
[600377]='',
[600378]='',
[600379]='',
[600380]='',
[600381]='',
[600382]='',
[600383]='',
[600384]='',
[600385]='',
[600386]='',
[600387]='',
[600388]='',
[600389]='',
[600390]='',
[600391]=[[أيها الحلفاء، شكرا جزيلا لكم على مساعدتكم.]],
[600392]='',
[600393]='',
[600394]='',
[600395]='',
[600396]='',
[600397]='',
[600398]='',
[600399]='',
[600400]='',
[600401]='',
[600402]='',
[600403]='',
[600404]='',
[600405]='',
[600406]='',
[600407]='',
[600408]='',
[600409]='',
[600410]='',
[600411]='',
[600412]='',
[600413]='',
[600414]='',
[600415]='',
[600416]='',
[600417]='',
[600418]='',
[600419]='',
[600420]='',
[600421]='',
[600422]='',
[600423]='',
[600424]='',
[600425]='',
[600426]='',
[600427]='',
[600428]='',
[600429]='',
[600430]='',
[600431]='',
[600432]='',
[600433]='',
[600434]='',
[600435]='',
[600436]='',
[600437]='',
[600438]='',
[600439]='',
[600440]='',
[600441]='',
[600442]='',
[600443]='',
[600444]='',
[600445]='',
[600446]='',
[600447]='',
[600448]='',
[600449]='',
[600450]='',
[600451]='',
[600452]='',
[600453]='',
[600454]='',
[600455]='',
[600456]='',
[600457]='',
[600458]='',
[600459]='',
[600460]='',
[600461]='',
[600462]='',
[600463]='English',
[600464]='Français',
[600465]='Deutsch',
[600466]='Русский',
[600467]='한국어',
[600468]='ไทย',
[600469]='日本語',
[600470]='Português',
[600471]='Español',
[600472]='Türkçe',
[600473]='繁體中文',
[600474]='Italiano',
[600475]='العربية',
[600476]=[[Tiếng Việt]],
[600477]='简体中文',
[600478]=[[Bahasa Indonesia]],
[600479]='',
[600480]='',
[600481]='',
[600482]='',
[600483]='',
[600484]='',
[600485]='',
[600486]='',
[600487]='',
[600488]='',
[600489]='',
[600490]='',
[600491]='',
[600492]='',
[600493]='',
[600494]='',
[600495]='',
[600496]='',
[600497]='',
[600498]='',
[600499]='',
[600500]='',
[600501]='',
[600502]='',
[600503]='',
[600504]='',
[600505]='',
[600506]='',
[600507]='',
[600508]='',
[600509]='',
[600510]='',
[600511]='',
[600512]='',
[600513]='',
[600514]='',
[600515]='',
[600516]='',
[600517]='',
[600518]='',
[600519]='',
[600520]='',
[600521]='',
[600522]='',
[600523]='',
[600524]='',
[600525]='',
[600526]='',
[600527]='',
[600528]='',
[600529]='',
[600530]='',
[600531]='',
[600532]='',
[600533]='',
[600534]='',
[600535]='',
[600536]='',
[600537]='',
[600538]='',
[600539]='',
[600540]='',
[600541]='',
[600542]='',
[600543]='',
[600544]='',
[600545]='',
[600546]='',
[600547]='',
[600548]='',
[600549]='',
[600550]='',
[600551]='',
[600552]='',
[600553]='',
[600554]='',
[600555]='',
[600556]='',
[600557]='',
[600558]='',
[600559]='',
[600560]='',
[600561]='',
[600562]='',
[600563]='',
[600564]='',
[600565]='',
[600566]='',
[600567]='',
[600568]='',
[600569]='',
[600570]='',
[600571]='',
[600572]='',
[600573]='',
[600574]='',
[600575]='',
[600576]='',
[600577]='',
[600578]='',
[600579]='',
[600580]='',
[600581]='',
[600582]='',
[600583]='',
[600584]=[[قلعتك بعيدة جدًا. يدعوك القائد للذهاب إلى نقطة التجمع <color=#319f38>{%s1}</color>. هل تريد الرد؟]],
[600585]=[[وقد طلبت المساعدة من جميع الحلفاء]],
[600586]=[[نصائح لتغيير التحالفات]],
[600587]=[[عزيزي الرب، لأن تحالفك ليس نشطًا بما فيه الكفاية، فقد قام النظام بتغييرك إلى تحالف أكثر نشاطًا. \nيمكنك الاستمتاع بالمزايا التالية في تحالف نشط:\n\n<color=#319f38>تسريع البناء/البحث العلمي:</color> بعد الانضمام إلى التحالف، يمكنك أن تطلب المساعدة من الحلفاء <color=#317BC7></color>، ويمكن للحلفاء مساعدتك <color=#317BC7> في تقصير البناء والبحث العلمي. الوقت المطلوب</color>، كلما كان تحالفك أكثر نشاطًا، كان تأثير التسارع أفضل.\n\n<color=#319f38>هدية التحالف</color>: حليفك <color=#317BC7><يهزم الزعيم></color> أو <color=#317BC7><شراء حزمة الهدايا></co lor>، سيجلب لك هدية تحالف ويزودك بالكثير من الموارد\n\n<color=#319f38>مكافأة التكنولوجيا</color>: كلما كان التحالف أكثر نشاطًا، ارتفع المستوى التكنولوجي للتحالف، وسوف تستمتع بـ <color=#317BC7>تسريع الأبحاث</color>، <color=#317BC 7>تسريع مارس</color> وغيرها من مكافآت التأثير التكنولوجي القوية\n\n<color=#319f38>Alliance Store</color>: ستحصل التحالفات النشطة على المزيد من مساهمة التحالف نقاط. يمكنك استخدام نقاط المساهمة لشراء <color=#317BC7>عناصر مختلفة فعالة من حيث التكلفة</color> في متجر التحالف.]],
[600588]=[[لا يمكن تشغيل حالة الخادم المشترك. يرجى العودة إلى المنطقة الأصلية والمحاولة مرة أخرى.]],
[600589]=[[مجموعة الدعوات]],
[600590]=[[حاليًا، <color=#58f5ff>{%s1}</color> من الأشخاص بعيدون جدًا]],
[600591]=[[نقل سريع للمدينة]],
[600592]=[[المسافة بينك وبين نقطة التجمع <color=#c032df>{%s1}</color> <color=#23932a>مناسبة</color>، ومناسبة للمشاركة في الحدث، ولا حاجة للانتقال]],
[600593]=[[المسافة بينك وبين نقطة التجمع <color=#c032df>{%s1} <color=#23932a>بعيدة جدًا</color>، مما يجعل مشاركتك في الفعالية غير مناسبة. يُرجى استخدام هجرة مدينة التحالف للانتقال إلى موقع مناسب!]],
[600594]=[[هل أنت متأكد من استخدام حليف واحد للانتقال إلى موقع تجميع الحليف؟ (لديك حاليًا حليف واحد فقط)]],
[600595]=[[هل أنت متأكد من استخدام حليف واحد للانتقال إلى موقع تجمع الحلفاء؟ (متاح هذه المرة)]],
[600596]='{%s1}كم',
[600597]=[[لا توجد هدايا متاحة حتى الآن]],
[600598]='Hindi',
[600600]=[[يرجى الخروج من التحالف الحالي قبل المتابعة]],
[600601]='دعوة',
[600602]=[[عزيزي {%s1}]],
[600603]=[[تم رصد أن تحالفك الحالي ليس نشطًا بما يكفي، مما قد يؤثر على فوائد الموارد. إليك بعض التحالفات النشطة الجديدة الموصى بها. انضم إلينا!]],
[600604]=[[انضم بنجاح للحصول على هذه المكافأة ({%s1})]],
[600605]=[[أعضاء التحالف]],
[600606]='',
[600607]=[[التقدم بطلب الانضمام]],
[600608]=[[عدد الأشخاص ممتلئ ولا يمكنك الانضمام]],
[600609]=[[التحالف الحالي لم يعد موجودا]],
[600610]=[[هذا التحالف كامل]],
[600611]='',
[600612]=[[دعوة التحالف]],
[600613]=[[دعوة الأعضاء]],
[600614]='يدعو',
[600615]=[[انقر على الزر لمشاركة معلومات التحالف في قناة الدردشة]],
[600616]=[[مشاركة التهدئة]],
[600617]=[[انضم إلينا]],
[600618]=[[مشاركة التحالف]],
[600619]=[[مشاركة دعوة التحالف]],
[600620]=[[لقد تم تقديم الطلب، يرجى الانتظار بصبر]],
[600901]='الصينية',
[600902]='إنجليزي',
[600903]='الألمانية',
[600904]='فرنسي',
[600905]='الاندونيسية',
[600906]=[[لغة الملايو]],
[600907]='التايلاندية',
[600908]='الروسية',
[600909]='الأسبانية',
[600910]='لا',
[600911]='كوري',
[600912]=[[الصينية التقليدية]],
[600913]='اليابانية',
[600914]='الفيتنامية',
[600915]='البرتغالية',
[600916]='عربي',
[600917]='ايطالي',
[600918]='تركي',
[600919]='بولندي',
[600920]='فلبينية',
[600921]='هولندي',
[601000]='',
[601001]='',
[601002]='',
[601003]='',
[601004]='',
[601005]='',
[601006]='',
[601007]='',
[601008]='',
[601009]='',
[601010]='',
[601011]='',
[601012]='',
[601013]='',
[601014]='',
[601015]='',
[601200]='',
[601201]='',
[601202]='',
[601203]='',
[601204]='',
[601205]='',
[601206]='',
[601207]='',
[601208]='',
[601209]='',
[601210]='',
[601211]='',
[601212]='',
[601213]='',
[601214]='',
[601215]='',
[601216]='',
[601217]='',
[601218]='',
[601219]='',
[601220]='',
[601221]='',
[601222]='',
[601223]='',
[601224]='',
[601225]='',
[601226]='',
[601227]='',
[601228]='',
[601229]='',
[601230]='',
[601231]='',
[601232]='',
[601233]='',
[601234]='',
[601235]='',
[601236]='',
[601237]='',
[601238]='',
[601239]='',
[601240]='',
[601241]='',
[601242]='',
[601243]='',
[601244]='',
[601245]='',
[601246]='',
[601247]='',
[601248]='',
[601249]='',
[601250]='',
[601251]='',
[601252]='',
[601253]='',
[601254]='',
[601255]='',
[601256]='',
[601257]='',
[601258]='',
[601259]='',
[601260]='',
[601261]='',
[601262]='',
[601263]='',
[601264]='',
[601265]='',
[601266]='',
[601267]='',
[601268]='',
[601269]='',
[601270]='',
[601271]='',
[601272]='',
[601273]='',
[601274]='',
[601275]='',
[601276]='',
[601277]='',
[601278]='',
[601279]='',
[601280]='',
[601281]='',
[601282]='',
[601283]='',
[601284]='',
[601285]='',
[601286]='',
[601287]='',
[601288]='',
[601289]='',
[601290]='',
[601291]='',
[601292]='',
[601293]='',
[601294]='',
[601295]='',
[601296]='',
[601297]='',
[601298]='',
[601299]='',
[601300]='',
[601301]='',
[601302]='',
[601303]='',
[601304]='',
[601305]='',
[601306]='',
[601307]='',
[601308]='',
[601309]='',
[601310]='',
[601311]='',
[601312]='',
[601313]='',
[601314]='',
[601315]='',
[601316]='',
[601317]='',
[601318]='',
[601319]='',
[601320]='',
[601321]='',
[601322]='',
[601323]='',
[601324]='',
[601325]='',
[601326]='',
[601327]='',
[601328]='',
[601329]='',
[601330]='',
[601331]='',
[601332]='',
[601333]='',
[601335]='',
[601336]='',
[601337]='',
[601338]='',
[601339]='',
[601340]='',
[601341]='',
[601342]='',
[601343]='',
[601344]='',
[601345]='',
[601346]='',
[601347]='',
[601348]='',
[601349]='',
[601350]='',
[601351]='',
[601352]='',
[601353]='',
[601354]='',
[601355]='',
[601356]='',
[601357]='',
[601358]='',
[601359]='',
[601360]='',
[601361]='',
[601362]='',
[601363]='',
[601364]='',
[601365]='',
[601366]='',
[601367]='',
[601368]='',
[601369]='',
[601370]='',
[601371]='',
[601372]='',
[601373]='',
[601374]='',
[601375]='',
[601376]='',
[601377]='',
[601378]='',
[601379]='',
[601380]='',
[601381]='',
[601382]='',
[601383]='',
[601384]='',
[601385]='',
[601386]='',
[601387]='',
[601388]='',
[601389]='',
[601390]='',
[601391]='',
[601392]='',
[601393]='',
[601394]='',
[601395]='',
[601396]='',
[601397]='',
[601398]='',
[601399]='',
[601400]='',
[601401]='',
[601402]='',
[601403]='',
[601404]='',
[601405]='',
[601406]='',
[601407]='',
[601408]='',
[601409]='',
[601410]='',
[601411]='',
[601412]='',
[601413]='',
[601414]='',
[601415]='',
[601446]='',
[601447]='',
[601448]='',
[601449]='',
[601450]='',
[601451]='',
[601452]='',
[601453]=[[عربة الصياد، يمكن أن تساعدنا في جمع الغنائم]],
[601454]='',
[601455]='',
[601456]='',
[601457]='',
[601458]='',
[601459]='',
[601460]='',
[601461]='',
[601462]='',
[601463]='',
[601464]='',
[601465]='',
[601466]='',
[601467]='',
[601468]='',
[601469]='',
[601470]='',
[601471]='',
[601472]='',
[601473]='',
[601474]='',
[601475]='',
[601476]='',
[601477]='',
[601478]='',
[601479]='',
[601480]='',
[601481]='',
[601482]='',
[601483]='',
[601484]='',
[601485]='',
[601486]='',
[601487]='',
[601488]='',
[601489]='',
[601490]='',
[601491]='',
[601492]='',
[601493]='',
[601494]='',
[601495]='',
[601496]='',
[601497]='',
[601498]='',
[601499]='',
[601500]='',
[601501]='',
[601502]='',
[601503]='',
[601504]='',
[601505]='',
[601506]='',
[601507]='',
[601508]='',
[601509]='',
[601510]='',
[601511]='',
[601512]='',
[601513]='',
[601514]='',
[601515]='',
[601516]='',
[601517]='',
[601518]='',
[601519]='',
[601520]='',
[601521]='',
[601522]='',
[601523]='',
[601524]='',
[601525]='',
[601526]='',
[601527]='',
[601528]='',
[601529]=[[نصائح ملحق الموارد]],
[601530]=[[أنت على وشك استخدام الكثير من الماس لشراء الموارد، وقيمة cp منخفضة. نوصي باستخدام طرق أخرى أكثر فعالية من حيث التكلفة لتجديد الموارد. هل تريد الذهاب؟ (لا مزيد من المطالبات أثناء الاتصال بالإنترنت)]],
[601531]=[[الاستمرار في الشراء]],
[601532]=[[ملحق الموارد]],
[601533]=[[أنفق <color=#2EC564>250 ماسة</color> لاستعادة قيمة دفاع المدينة البالغة 2500]],
[601534]=[[المساعدة من الحلفاء]],
[601535]=[[تلقيت المساعدة من <color=#FDE01B><size=40>{%s1}</size></color> حلفاء متحمسين]],
[601536]=[[أي مركز أبحاث]],
[601537]=[[أي مذبح]],
[602001]='',
[602002]='',
[602003]='',
[602004]='',
[602005]='',
[602006]='',
[602007]='',
[602008]='',
[602009]='',
[602010]='',
[602011]='',
[602012]='',
[602013]='',
[602014]='',
[602015]='+',
[602016]='',
[602017]='',
[602018]='',
[602019]='',
[602020]='',
[602021]='',
[602022]='',
[602023]='',
[602024]='',
[602025]='',
[602026]='',
[602027]='',
[602028]='',
[602029]='',
[602030]='',
[602031]='',
[602032]='',
[602033]='',
[602034]='',
[602035]='',
[602036]='',
[602037]='',
[602038]='',
[602039]='',
[602040]='',
[602041]='',
[602042]='',
[602043]='',
[602044]='',
[602045]='',
[602046]='',
[602047]='',
[602048]='',
[602049]='',
[602050]='',
[602051]='',
[602052]='',
[602053]='',
[602054]='',
[602055]='',
[602056]='',
[602057]='',
[602058]='',
[602059]='',
[602060]='',
[602061]='',
[602062]='',
[602063]='',
[602064]='',
[602065]='',
[602066]='',
[602067]='',
[602068]='',
[602069]='',
[602070]='',
[602071]='',
[602072]='',
[602073]='',
[602074]='',
[602075]='',
[602076]='',
[602077]='',
[602078]='',
[602079]='',
[602080]='',
[602081]='',
[602082]='',
[602083]='',
[602084]='',
[602085]='',
[602086]='',
[602087]='',
[602088]='',
[602089]='',
[602090]='',
[602091]='',
[602092]='',
[602093]='',
[602094]='',
[602095]='',
[602096]='',
[602097]='',
[602098]='',
[602099]='',
[602100]='',
[602101]='',
[602102]='',
[602103]='',
[602104]='',
[602105]='',
[602106]='',
[602107]='',
[602108]='',
[602109]='',
[602110]='',
[602111]='',
[602112]='',
[602113]='',
[602114]='',
[602115]='',
[602116]='',
[602117]='',
[602118]='',
[602119]='',
[602120]='',
[602121]='',
[602122]='',
[602123]='',
[602124]='',
[602125]='',
[602126]='',
[602127]='',
[602128]='',
[602129]='',
[602130]='',
[602131]='',
[602132]='',
[602133]='',
[602134]='',
[602135]='',
[602136]='',
[602137]='',
[602138]='',
[602139]='',
[602140]='',
[602141]='',
[602142]='',
[602143]='',
[602144]='',
[602145]='',
[602146]='',
[602147]='',
[602148]='',
[602149]='',
[602150]='',
[602151]='',
[602152]='',
[602153]='',
[602154]='',
[602155]='',
[602156]='',
[602157]='',
[602158]='',
[602159]='',
[602160]='',
[602161]='',
[602162]='',
[602163]='',
[602164]='جائزة',
[602165]=[[وصف المكافأة]],
[602166]=[[المكافآت المتاحة]],
[602167]='',
[602168]='جديد',
[602501]='',
[602502]='',
[602503]='',
[602504]='',
[602505]='',
[602506]='',
[602507]='',
[602508]='',
[602509]='',
[602510]='',
[602511]='',
[602512]='',
[602513]='',
[602514]='',
[602515]='',
[602516]='',
[602517]='',
[602518]='',
[602519]='',
[602520]='',
[602521]='',
[602522]='',
[602523]='',
[602524]='',
[602525]='',
[602526]='',
[602527]='',
[602528]='',
[602529]='',
[602530]='',
[602531]='',
[602532]='',
[602533]='',
[602534]='',
[602535]='',
[602536]='',
[602537]='',
[602538]='',
[602539]='',
[602540]='',
[602541]='',
[602542]='',
[602543]='',
[602544]='',
[602545]='',
[602546]='',
[602547]='',
[602548]='',
[602549]='',
[602550]='',
[602551]='',
[602552]='',
[602553]='',
[602554]='',
[602555]='',
[602556]='',
[602557]='',
[602558]='',
[602559]='',
[602560]='',
[602561]='',
[602562]='',
[602563]='',
[602564]='',
[602565]='',
[602566]='',
[602567]='',
[602568]='',
[602569]='',
[603001]='',
[603002]='',
[603003]='',
[603004]='',
[603005]='',
[603006]='',
[603007]='',
[603008]='',
[603009]='',
[603010]='',
[603011]='',
[603012]='',
[603013]='',
[603014]='',
[603015]='',
[603016]='',
[603017]='',
[603018]='',
[603019]='',
[603020]='',
[603021]='',
[603022]='',
[603023]='',
[603024]='',
[603025]='',
[603026]='',
[603027]='',
[603028]='',
[603029]='',
[603030]='',
[603031]='',
[603032]='',
[603033]='',
[603034]='',
[603035]='',
[603036]='',
[603037]='',
[603038]='',
[603039]='',
[603040]='',
[603041]='',
[603042]='',
[603043]='',
[603044]='',
[603045]='',
[603046]='',
[603047]='',
[603048]='',
[603049]='',
[603050]='',
[603051]='',
[603052]='',
[603053]='',
[603054]='',
[603055]='',
[603056]='',
[603057]='',
[603058]='',
[603059]='',
[603060]='',
[603061]='',
[603062]='',
[603063]='',
[603064]='',
[603065]='',
[603066]='',
[603067]='',
[603068]='',
[603069]='',
[603070]='',
[603071]='',
[603072]='',
[603073]='',
[603074]='',
[603075]='',
[603076]='',
[603077]='',
[603078]='',
[603079]='',
[603080]='',
[603081]='',
[603082]='',
[603083]='',
[603084]='',
[603085]='',
[603086]='',
[603087]='',
[603088]='',
[603089]='',
[603090]='',
[603091]='',
[603092]='',
[603093]='',
[603094]='',
[603095]='',
[603096]='',
[603097]='',
[603098]='',
[603099]='',
[603100]='',
[603101]='',
[603102]='',
[603103]='',
[603104]='',
[603105]='',
[603106]='',
[603107]='',
[603108]='',
[603109]='',
[603110]='',
[603111]='',
[603112]='',
[603113]='',
[603114]='',
[603115]='',
[603116]='',
[603117]='',
[603118]='',
[603119]='',
[603120]='',
[603121]='',
[603122]='',
[603123]='',
[603124]='',
[603125]='',
[603126]='',
[603127]='',
[603128]='',
[603129]='',
[603130]='',
[603131]='',
[603132]='',
[603133]='',
[603134]='',
[603135]='',
[603136]='',
[603137]='',
[603138]='',
[603139]='',
[603140]='',
[603141]='',
[603142]='',
[603143]='',
[603144]='',
[603145]='',
[603146]='',
[603147]='',
[603148]='',
[603149]='',
[603150]='',
[603151]='',
[603152]='',
[603153]='',
[603154]='',
[603155]='',
[603156]='',
[603157]='',
[603158]='',
[603159]='',
[603160]='',
[603161]='',
[603162]='',
[603163]='',
[603164]='',
[603165]='',
[603166]='',
[603167]='',
[603168]='',
[603169]='',
[603170]='',
[603171]='',
[603172]='',
[603173]='',
[603174]='',
[603175]='',
[603176]='',
[603177]='',
[603178]='',
[603179]='',
[603180]='',
[603181]='',
[603182]='',
[603183]='',
[603184]='',
[603185]='',
[603186]='',
[603187]='',
[603188]='',
[603189]='',
[603190]='',
[603191]='',
[603192]='',
[603193]='',
[603194]='',
[603195]='',
[603196]='',
[603197]='',
[603198]='',
[603199]='',
[603200]='',
[603201]='',
[603202]='',
[603203]='',
[603204]='',
[603205]='',
[603206]='',
[603207]='',
[603208]='',
[603209]='',
[603210]='',
[603211]='',
[603212]='',
[603213]='',
[603214]='',
[603215]='',
[603216]='',
[603217]='',
[603218]='',
[603219]='',
[603220]='',
[603221]='',
[603222]='',
[603223]='',
[603224]='',
[603225]='',
[603226]='',
[603227]='',
[603228]='',
[603229]='',
[603230]='',
[603231]='',
[603232]='',
[603233]='',
[603234]='',
[603235]='',
[603236]='',
[603237]='',
[603238]='',
[603239]='',
[603240]='',
[603241]='',
[603242]='',
[603243]='',
[603244]='',
[603245]='',
[603246]='',
[603247]='',
[603248]='',
[603249]='',
[603250]='',
[603251]='',
[603252]='',
[603253]='',
[603254]='',
[603255]='',
[603256]='',
[603257]='',
[603258]='',
[603259]='',
[603260]='',
[603261]='',
[603262]='',
[603263]='',
[603264]='',
[603265]='',
[603266]='',
[603267]='',
[603268]='',
[603269]='',
[603270]='',
[603271]='',
[603272]='',
[603273]='',
[603274]='',
[603275]='',
[603276]='',
[603277]='',
[603278]='',
[603279]='',
[603280]='',
[603281]='',
[603282]='',
[603283]='',
[603284]='',
[603285]='',
[603286]='',
[603287]='',
[603288]='',
[603289]='',
[603290]='',
[603291]='',
[603292]='',
[603293]='',
[603294]='',
[603295]='',
[603296]='',
[603297]='',
[603298]='',
[603299]='',
[603300]='',
[603301]='',
[603302]='',
[603303]='',
[603304]='',
[603305]='',
[603306]='',
[603307]='',
[603308]='',
[603309]='',
[603310]='',
[603311]='',
[603312]='',
[603313]='',
[603314]='',
[603315]='',
[603316]='',
[603317]='',
[603318]='',
[603319]='',
[603320]=[[عند الهجوم، قم بزيادة حياة البطل وهجومه ودفاعه.]],
[603321]=[[عند الدفاع، قم بزيادة حياة البطل وهجومه ودفاعه.]],
[603322]='',
[603323]='',
[603324]='',
[603325]='',
[603326]='',
[603327]='',
[603328]='',
[603329]='',
[603330]='',
[603331]='',
[603332]='',
[603333]='',
[603334]='',
[603335]='',
[603336]='',
[603337]='',
[603338]='',
[603339]='',
[603340]='',
[603341]='',
[603342]='',
[603343]='',
[603344]='',
[603345]='',
[603346]='',
[603347]='',
[603348]='',
[603349]='',
[603350]='',
[603351]='',
[603352]='',
[603353]='',
[603354]='',
[603501]='',
[603502]='',
[603503]='',
[603504]='',
[603505]='',
[603506]='',
[603507]='',
[603508]='',
[603509]='',
[603510]='',
[603511]='',
[603512]='',
[603513]='',
[603514]='',
[603515]='',
[603516]='',
[603517]='',
[603551]='',
[603552]='',
[603561]='',
[603562]='',
[603563]='',
[603564]='',
[603565]='',
[603566]='',
[603567]='',
[603568]='',
[603569]='',
[603570]='',
[603571]='',
[603572]='',
[603573]='',
[603574]='',
[603575]='',
[603576]='',
[603577]='',
[603578]='',
[603579]='',
[603580]='',
[603581]='',
[603582]='',
[603583]='',
[603584]='',
[603585]='',
[603586]='',
[603587]='',
[603588]='',
[603589]='',
[603590]='',
[603591]='',
[603592]='',
[603593]='',
[603594]='',
[603595]='',
[603596]='',
[603597]='',
[603598]='',
[603599]='',
[603600]='',
[603601]='',
[603602]='',
[603603]='',
[603604]='',
[603605]='',
[603606]='',
[603607]='',
[603608]='',
[603609]='',
[603610]='',
[603611]='',
[603612]='',
[603613]='',
[603614]='',
[603615]='',
[603616]='',
[603617]='',
[603618]='',
[603619]='',
[603620]='',
[603621]='',
[603622]='',
[603623]='',
[603624]='',
[603625]='',
[603626]='',
[603627]='',
[603628]='',
[603629]='',
[603630]='',
[603631]='',
[603632]='',
[603633]='',
[603634]='',
[603635]='',
[603636]='',
[603637]='',
[603638]='',
[603639]='',
[603640]='',
[603641]='',
[603642]='',
[603643]='',
[603644]='',
[603645]='',
[603646]='',
[603647]='',
[603648]='',
[603649]='',
[603650]='',
[603651]='',
[603652]='',
[603653]='',
[603654]='',
[603655]='',
[603656]='',
[603657]='',
[603658]='',
[603659]='',
[603660]='',
[603661]='',
[603662]='',
[603663]='',
[603664]='',
[603665]='',
[603666]='',
[603667]='',
[603668]='',
[603669]='',
[603670]='',
[603671]='',
[603672]='',
[603673]='',
[603674]='',
[603675]='',
[603676]='',
[603677]='',
[603678]='',
[603679]='',
[603680]='',
[603681]='',
[603682]='',
[603683]='',
[603684]='',
[603685]='',
[603686]='',
[603687]='',
[603688]='',
[603689]='',
[603690]='',
[603691]='',
[603692]='',
[603693]='',
[603694]='',
[603695]='',
[603696]='',
[603697]='',
[603698]='',
[603699]='',
[603700]='',
[603701]='',
[603702]='',
[603703]='',
[603704]='',
[603705]='',
[603706]='',
[603707]='',
[603708]='',
[603709]='',
[603710]='',
[603711]='',
[603712]='',
[603713]='',
[603714]='',
[603715]='',
[603716]='',
[603717]='',
[603718]='',
[603719]='',
[603720]='',
[603721]='',
[603722]='',
[603723]='',
[603724]='',
[603725]='',
[603726]='',
[603727]='',
[603728]='',
[603729]='',
[603730]='',
[603731]='',
[603732]='',
[603733]='',
[603734]='',
[603735]='',
[603736]='',
[603737]='',
[603738]='',
[603739]='',
[603740]='',
[603741]='',
[603742]='',
[603743]='',
[603744]='',
[603745]='',
[603746]='',
[603747]='',
[603748]='',
[603749]='',
[603750]='',
[603751]='',
[603752]='',
[603753]='',
[603754]='',
[603755]='',
[603756]='',
[603757]='',
[603758]='',
[603759]='',
[603760]='',
[603761]='',
[603762]='',
[603763]='',
[603764]='',
[603765]='',
[603766]='',
[603767]='',
[603768]='',
[603769]='',
[603770]='',
[603771]='',
[603772]='',
[603773]='',
[603774]='',
[603775]='',
[603776]='',
[603777]='',
[603778]='',
[603779]='',
[603780]='',
[603781]='',
[603782]='',
[603783]='',
[603784]='',
[603785]='',
[603786]='',
[603787]='',
[603788]='',
[603789]='',
[603790]='',
[603791]='',
[603792]='',
[603793]='',
[603794]='',
[603795]='',
[603796]='',
[603797]='',
[603798]='',
[603799]='',
[603800]='',
[603801]='',
[603802]='',
[603803]='',
[603804]='',
[603805]='',
[603806]='',
[603807]='',
[603808]='',
[603809]='',
[603810]='',
[603811]='',
[603812]='',
[603813]='',
[603814]='',
[603815]='',
[603816]='',
[603817]='',
[603818]='',
[603819]='',
[603820]='',
[603821]='',
[603822]='',
[603823]='',
[603824]='',
[603825]='',
[603826]='',
[603827]='',
[603828]='',
[603829]='',
[603830]='',
[603831]='',
[603832]='',
[603833]='',
[603834]='',
[603835]='',
[603836]='',
[603837]='',
[603838]='',
[603839]='',
[603840]='',
[603841]='',
[603842]='',
[603843]='',
[603844]='',
[603845]='',
[603846]='',
[603847]='',
[603848]='',
[603849]='',
[603850]='',
[603851]='',
[603852]='',
[603853]='',
[603854]='',
[603855]='',
[603856]='',
[603857]='',
[603858]='',
[603859]='',
[603860]='',
[603861]='',
[603862]='',
[603863]='',
[603864]='',
[603865]='',
[603866]='',
[603867]='',
[603868]='',
[603869]='',
[603870]='',
[603871]='',
[603872]='',
[603873]='',
[603874]='',
[603875]='',
[603876]='',
[603877]='',
[603878]='',
[603879]='',
[603880]='',
[603881]='',
[603882]='',
[603883]='',
[603884]='',
[603885]='',
[603886]='',
[603887]='',
[603888]='',
[603889]='',
[603890]='',
[603891]='',
[603892]='',
[603893]='',
[603894]='',
[603895]='',
[603896]='',
[603897]='',
[603898]='',
[603899]='',
[603900]='',
[603901]='',
[603902]='',
[603903]='',
[603904]='',
[603905]='',
[603906]='',
[603907]='',
[603908]='',
[603909]='',
[603910]='',
[603911]='',
[603912]='',
[603913]='',
[603914]='',
[603915]='',
[603916]='',
[603917]='',
[603918]='',
[603919]='',
[603920]='',
[603921]='',
[603922]='',
[603923]='',
[603924]='',
[603925]='',
[603926]='',
[603927]='',
[603928]='',
[603929]='',
[603930]='',
[603931]='',
[603932]='',
[603933]='',
[603934]='',
[603935]='',
[603936]='',
[603937]='',
[603938]='',
[603939]='',
[603940]='',
[603941]='',
[604001]=[[الحد اليومي لاكتساب الصدر +5]],
[604002]=[[الحد اليومي لاكتساب الصدر +6]],
[604003]=[[الحد اليومي لاكتساب الصدر +7]],
[604004]=[[الحد اليومي لاكتساب الصدر +8]],
[604005]=[[الحد اليومي لاكتساب الصدر +9]],
[604006]=[[الحد اليومي لاكتساب الصدر +10]],
[604007]=[[الحد اليومي لاكتساب الصدر +11]],
[604008]=[[الحد اليومي لاكتساب الصدر +12]],
[604009]=[[الحد اليومي لاكتساب الصدر +13]],
[604010]=[[الحد اليومي لاكتساب الصدر +14]],
[604011]=[[الحد اليومي لاكتساب الصدر +15]],
[604012]=[[الحد اليومي لاكتساب الصدر +16]],
[604013]=[[الحد اليومي لاكتساب الصدر +17]],
[604014]=[[الحد اليومي لاكتساب الصدر +18]],
[604015]=[[الحد اليومي لاكتساب الصدر +19]],
[604016]=[[الحد اليومي لاكتساب الصدر +20]],
[604017]=[[الحد اليومي لاكتساب الصدر +21]],
[604018]=[[الحد اليومي لاكتساب الصدر +22]],
[604019]=[[الحد اليومي لاكتساب الصدر +23]],
[604020]=[[الحد اليومي لاكتساب الصدر +24]],
[604021]=[[الحد اليومي لاكتساب الصدر +25]],
[604022]=[[الحد اليومي لاكتساب الصدر +26]],
[604023]=[[الحد اليومي لاكتساب الصدر +27]],
[604024]=[[الحد اليومي لاكتساب الصدر +28]],
[604025]=[[الحد اليومي لاكتساب الصدر +29]],
[604026]=[[الحد اليومي لاكتساب الصدر +30]],
[604027]=[[مركز الاستخبارات: +1 صندوق كنز لكل مهمة]],
[604028]=[[مركز الاستخبارات: احصل على +2 صندوق كنز لكل مهمة]],
[604029]=[[مركز الاستخبارات: احصل على +3 صناديق كنز لكل مهمة]],
[604030]=[[مركز الاستخبارات: احصل على صناديق الكنز لكل مهمة +4]],
[604031]=[[مركز الاستخبارات: احصل على صناديق الكنز لكل مهمة +5]],
[604032]='',
[604033]='',
[604034]=[[المغامر لورا على دراية جيدة بالبحث عن الكنوز. وجودها يمكن أن يساعد اللورد في الحصول على كنوز إضافية عند إكمال المهام الاستخباراتية!]],
[604035]=[[تستطيع القائدة جيسيكا دائمًا قيادة القوات للفوز في كل معركة. معها، يمكنك زيادة عدد القوات التي يقودها جميع الأبطال وزيادة قوة هجومهم بشكل كبير!]],
[604501]='',
[604502]='',
[604503]='',
[604504]='',
[604505]='',
[604506]='',
[604507]='',
[604508]='',
[604509]='',
[604510]='',
[604511]='',
[604512]='',
[604513]='',
[604514]='',
[604515]='',
[604516]='',
[604517]='',
[604518]='',
[604519]='',
[604520]='',
[604521]='',
[604522]='',
[604523]='',
[604524]='',
[604525]='',
[604526]='',
[604527]='',
[604528]='',
[604529]='',
[604530]='',
[604531]='',
[604532]='',
[604533]='',
[604534]='',
[604535]='',
[604536]='',
[604537]='',
[604538]='',
[604539]='',
[604540]='',
[604541]='',
[604542]='',
[604543]='',
[604544]='',
[604545]='',
[604546]='',
[604547]='',
[604548]='',
[604549]='',
[604550]='',
[604551]='',
[604552]='',
[604553]='',
[604554]='',
[604555]='',
[604556]='',
[604557]='',
[604558]='',
[604559]='',
[604560]='',
[604561]='',
[604562]='',
[604563]='',
[604564]='',
[604565]='',
[604566]='',
[604567]='',
[604568]='',
[604569]='',
[604570]='',
[604571]='',
[604572]='',
[604573]='',
[604574]='',
[604575]='',
[604576]='',
[604577]='',
[604578]='',
[604579]='',
[604580]='',
[604581]='',
[604582]='',
[604583]='',
[604584]='',
[604585]='',
[604586]='',
[604587]='',
[604588]='',
[604589]='',
[604590]='',
[604591]='',
[604592]='',
[604593]='',
[604594]='',
[604595]='',
[604596]='',
[604597]='',
[604598]='',
[604599]='',
[604600]='',
[604601]='',
[604602]='',
[604603]='',
[604604]='',
[604605]=[[هاجم {%s1} مدينة {%s2} انتصار؛ اقتل الجنود: {%s3}]],
[604606]=[[فشل الهجوم على مدينة {%s1} {%s2}؛ قُتل جنود: {%s3}]],
[604607]='',
[604608]='',
[604609]='',
[604610]='',
[604611]='',
[604612]='',
[604613]='',
[604614]='',
[604615]='',
[604616]='',
[604617]='',
[604618]='',
[604619]='',
[604620]='',
[604621]='',
[604622]='',
[604623]='',
[604624]='',
[604625]='',
[604626]='',
[604627]='',
[604628]=[[تم اكتشاف الرسالة من {%s1}]],
[604629]=[[خاض معركة مع {%s1}، وخسر]],
[604630]=[[حارب مع {%s1} واربح]],
[604631]=[[شارك النجاح!]],
[604632]=[[خطأ في المشاركة، يرجى المحاولة مرة أخرى]],
[604633]=[[تم الكشف عن {%s1}]],
[604634]=[[فريق العدو]],
[605001]='',
[605002]='',
[605003]='',
[605004]='',
[605005]='',
[605006]='',
[605007]='',
[605008]='',
[605009]='',
[605010]='',
[605011]='',
[605012]='',
[605013]='',
[605014]='',
[605015]='',
[605016]='',
[605017]='',
[605018]='',
[606001]='',
[606002]='',
[606003]='',
[606004]='',
[606005]='',
[606006]='',
[606007]='',
[606008]='',
[606009]='',
[606010]='',
[606011]='',
[606012]='',
[606013]='',
[606014]='',
[606015]='',
[606016]='',
[606017]='',
[606018]='',
[606019]='',
[606020]='',
[606021]='',
[606022]='',
[606023]='',
[606024]='',
[606025]='',
[606026]='',
[606027]='',
[606028]='',
[606029]='',
[606030]='',
[606031]='',
[606032]='',
[606033]='',
[606034]='',
[606035]='',
[606036]='',
[606037]='',
[606038]='',
[606039]='',
[606040]='',
[606041]='',
[606042]='',
[606043]='',
[606044]='',
[606045]='',
[606046]='',
[606047]='',
[606048]='',
[606049]='',
[606050]='',
[606051]='',
[606052]='',
[606053]='',
[606054]='',
[606055]='',
[606056]='',
[606057]='',
[606058]='',
[606059]='',
[606060]='',
[606061]='',
[606062]='',
[606063]='',
[606064]='',
[606065]='',
[606066]='',
[606067]='',
[606068]='',
[606069]='',
[606070]='',
[606071]='',
[606072]='',
[606073]='',
[606074]='',
[606075]='',
[606076]='',
[606077]='',
[606078]='',
[606079]='',
[606080]='',
[606081]='',
[606082]='',
[606083]=[[【المعدات】إعادة إصدار العناصر الزائدة من حقيبة الظهر]],
[606084]=[[حقيبة معداتك ممتلئة. يُرجى تفكيكها وتنظيفها قبل استلامها.]],
[607001]='',
[607002]='',
[607003]='',
[607004]='',
[607005]='',
[607006]='',
[607011]='',
[607012]='',
[607013]='',
[607014]='',
[607015]='',
[607016]='',
[607017]='',
[607018]='',
[607019]='',
[607020]='',
[607021]='',
[607022]='',
[607023]='',
[607024]='',
[607025]='',
[607026]='',
[607027]='',
[607028]='',
[607029]='',
[607030]='',
[607031]='',
[607032]='',
[607033]='',
[607034]='',
[607035]='',
[607036]='',
[607037]='',
[607038]='',
[607039]='',
[607040]='',
[607041]='',
[607042]='',
[607046]=[[تضخيم المهارات]],
[607047]=[[مرحلة تعزيز القتال {%s1}]],
[607048]=[[(الاختراق المرحلي: المستوى {%s1})]],
[607049]=[[تم الانتهاء من المرحلة المتقدمة]],
[607050]=[[(المستوى المتقدم كامل)]],
[607051]=[[تعزيز القتال]],
[607052]=[[مهارة أساسية]],
[607053]='يرقي',
[607054]='صدر',
[607055]='يصف',
[607057]=[[استخدم بطاقات الخبرة المتقدمة ونوى الكريستال غير المستخدمة للترقية]],
[607058]=[[إضافة سريعة]],
[607060]=[[احصل على المزيد]],
[607061]=[[حسّن مستوى تقدمك القتالي باستمرار للوصول إلى مرحلة أعلى. ستزيد هذه المرحلة أيضًا من مهارة الكريستال، مما سيعزز مهاراتك في الكريستال عدديًا.]],
[607062]=[[فتح نظام مهارة جوهر الكريستال]],
[607063]=[[حل فتح كريستال كور 1]],
[607064]=[[فتح حل كريستال كور 2]],
[607065]=[[فتح حل كريستال كور 3]],
[607066]=[[فتح حل كريستال كور 4]],
[607067]=[[تمت زيادة مهارة الكريستال إلى +1]],
[607068]=[[تمت زيادة مهارة الكريستال إلى +2]],
[607069]=[[تمت زيادة مهارة الكريستال إلى +3]],
[607070]=[[تمت زيادة مهارة الكريستال إلى +4]],
[607071]=[[تمت زيادة مهارة الكريستال إلى +5]],
[607072]=[[تم تطوير هذه البلورة. هل ما زلت ترغب في استخدامها للتقدم في القتال؟]],
[607073]=[[إعادة ضبط الكريستال]],
[607074]=[[تغيير قائمة الانتظار]],
[607075]=[[مجموعة كريستال الغابة]],
[607076]=[[مجموعة التبلور البشري]],
[607077]=[[مجموعة كريستال الليل المظلم]],
[607078]=[[مجموعة بلورية مختلطة]],
[607079]=[[مهارات الافتتاح]],
[607080]=[[مهارات الهجوم]],
[607081]=[[المهارات الدفاعية]],
[607082]=[[مهارات التعطيل]],
[607083]=[[ترقية نجمة الكريستال]],
[607084]=[[تفاصيل التبلور]],
[607085]=[[تفاصيل ترقية النجمة]],
[607086]=[[تضخيم المهارات]],
[607087]=[[زيادة مهارة الكريستال الحالية]],
[607088]=[[استمر في تحسين مستوى التقدم في القتال، ويمكن أن يؤدي اختراق المرحلة إلى تعزيز زيادة مهارة الكريستال]],
[607089]=[[المرحلة التالية من الاختراق: المستوى {%s1}]],
[607090]=[[لا يمكن استخدام بلورات الجودة البرتقالية كمواد لترقية معركة الوحوش الأسطورية]],
[607091]=[[يتم استخدام هذه البلورة كمادة للتقدم في معركة الوحوش الأسطورية، ويمكنها زيادة {%s1} نقطة من الخبرة]],
[607092]=[[تم تطوير هذه البلورة، ويمكن استخدامها كمادة للمعركة المتقدمة ضد الوحش الأسطوري. يمكنها زيادة {%s1} نقطة خبرة.]],
[607093]=[[اختر المهارة الهجومية]],
[607094]=[[اختر مهارة الافتتاح الخاصة بك]],
[607095]=[[اختر المهارات الدفاعية]],
[607096]=[[اختر مهارة التعطيل]],
[607097]='حَشد',
[607098]=[[معهد أبحاث التبلور]],
[607099]=[[حدد البلورة التي تريد إنشاءها]],
[607100]=[[الرجاء تحديد عنصر الخبرة أولاً]],
[607101]='',
[607102]='',
[607103]='',
[607104]='',
[607105]='',
[607106]='',
[607107]='',
[607108]='',
[607120]=[[لم يتم الحصول على نواة بلورية حتى الآن]],
[607121]='يكتب',
[607123]='ندرة',
[607124]=[[تم فتحه بعد الوصول إلى مستوى {%s1} من معهد الأبحاث]],
[607125]=[[نجاح التصنيع]],
[607126]=[[<color=#F47AFF>فتح مرحلة المعركة المتقدمة {%s1}]],
[607127]=[[عندما تتقدم المعركة إلى <color=#FFC64C>المستوى.{%s1}، يمكنك الاختراق إلى <color=#F47AFF>مرحلة تقدم المعركة{%s2}.</color> والاستمرار في التحرك للأمام!]],
[607128]=[[لا توجد نوى قابلة للتجهيز]],
[607129]=[[تم تطبيق {%s1} على الحل {%s2}، هل تريد تثبيته على الحل الحالي؟]],
[607130]=[[استهلاك ترقية النجوم]],
[607131]=[[لم أحصل على نواة البلورة بعد. هل عليّ الذهاب إلى معهد الأبحاث لصنع واحدة؟]],
[607132]=[[يتم استخدام قلب الكريستال هذا كمواد للتقدم في قتال الوحش الإلهي، ويمكن أن يوفر {%s1} نقطة خبرة]],
[607133]=[[حدد جوهر المهارة الذي تريد إعادة تعيينه. \nبعد إعادة التعيين، سيصبح الجوهر غير مُحدَّث، وسيتم إرجاع جميع النوى التي تحمل نفس الاسم والتي تم استهلاكها أثناء الترقية. \nيمكن إعادة تعيين النوى غير المثبتة في الخطة فقط.]],
[607134]=[[الرجاء تحديد أي قلب بلوري أولاً]],
[607135]=[[لا توجد نوى قابلة لإعادة الضبط]],
[607136]=[[يتم حاليًا استخدام محلول قلب البلورة {%s1} في التكوين {%s2}. هل يحتاج إلى استبدال؟]],
[607137]=[[تشكيل التغيير]],
[607138]=[[قوة قتالية من كريستال كور]],
[607139]=[[خطة التعديل]],
[607140]=[[حدد خطة مهارة جوهر الكريستال]],
[607141]=[[مهارة أساسية]],
[607142]=[[مستوى التقدم القتالي]],
[607143]=[[نجمة النواة البلورية]],
[607144]=[[1. سوف يستمر الوحش الأسطوري في مهاجمة العدو أثناء المعركة، لكنه لن يتعرض للهجوم.]],
[607145]=[[٢. تكتسب الوحوش الإلهية قدرًا معينًا من تقدم الخبرة عند استهلاك عناصر الخبرة. بمجرد تحقيق هذا التقدم، ترتفع مستوياتها وتزداد سماتها. <color=#39A242>تُحوَّل سمات الوحوش الإلهية إلى سمات بطلنا بنسبة معينة. يُمكنك الاطلاع على نسبة التحويل والزيادة الفعلية لسمات البطل في الواجهة ذات الصلة.</color>\nهناك احتمال لإصابة حرجة عند اكتساب الخبرة: عندما يكون شريط التقدم أقل من ٨٠٪، تكون احتمالية الإصابة الحرجة ٥٠٪، مما يُضاعف تقدم الخبرة. عندما يكون شريط التقدم أكبر من أو يساوي ٨٠٪، لن تُفعَّل الضربات الحرجة. علاوة على ذلك، لن تحدث ضربات حرجة خلال مراحل الاختراق.]],
[607146]=[[3. عندما يصل مستوى الوحش الإلهي إلى <color=#39A242>30/50/70/90/110</color>، سيزداد مستوى مهارته.]],
[607147]=[[٤. عند وصول قلعتك إلى المستوى ١٥، تُصبح ميزة <color=#39A242>علامة الوحش الإلهي</color> متاحة. للوحوش الإلهية ست علامات، كل منها تُعزز إحصائيات البطل بشكل ملحوظ. يمكن دمج ثلاث علامات من نفس النوع والمستوى لإنشاء علامة أعلى مستوى من نفس النوع.]],
[607148]=[[٥. في اليوم الثاني والثمانين من إطلاق الخادم، سيتم فتح نظامي "تقدم المعركة" و"أساس المهارة". يتيح "تقدم المعركة" للاعبين تعزيز وحوشهم الإلهية وتحسين سماتها. يفتح الوصول إلى مستوى "تقدم المعركة" العاشر نظام "أساس المهارة". بمجرد فتح نظام "أساس المهارة"، يمكن لكل خطة أساسية تثبيت مهارة افتتاحية، ومهارة هجومية، ومهارة تعطيل، ومهارة دفاعية. قبل بدء معارك الفريق، يمكنك تحديد خطة لمنح الوحش الإلهي تأثيرات مهارة "أساس المهارة". يمكن ترقية "أساس المهارة" و"أساس المهارة" من خلال استهلاك "أساس المهارة" الذي يحمل الاسم نفسه، مما يعزز تأثيرات مهاراته بشكل أكبر. علاوة على ذلك، يمكن استخدام "أساس المهارة" غير المستخدمة للتقدم في المعركة. في كل مرة تجتاز فيها مرحلة تقدم في المعركة، يمكنك زيادة "تضخيم مهارة" "أساس المهارة"، مما يعزز قيم مهارات "أساس المهارة".]],
[607149]=[[لا يوجد تشكيل متاح حاليًا، يرجى الانتقال إلى بناء التشكيل أولاً]],
[607150]=[[معهد أبحاث كريستال كور]],
[607151]=[[القدرة على إنشاء وتفكيك نوى البلورات]],
[607152]=[[احتفظ بنواة الكريستال هذه واستخدمها لترقية نواة الكريستال التي تحمل الاسم نفسه. هل ما زلت ترغب في استخدامها لترقيات التقدم في القتال؟]],
[607153]=[[ربما ينبغي للرب أن يُجهّز هذا اللب البلوري. هل يُنصح باستخدامه للتقدم في القتال والترقية؟]],
[607201]='',
[607202]='',
[607203]='',
[607204]='',
[607205]='',
[607206]='',
[607207]='',
[607208]='',
[607209]='',
[607301]='',
[607302]='',
[607303]='',
[607304]='',
[607305]='',
[607306]='',
[607307]='',
[607308]='',
[607309]='',
[607310]='',
[607311]='',
[607312]='',
[607313]='',
[607314]='',
[607315]='',
[607316]='',
[607317]='',
[607318]=[[إصلاح بوابة المدينة وإنقاذ أصدقائك!]],
[607319]='',
[607320]='',
[607321]='',
[607322]='',
[607323]='',
[607324]='',
[607325]='',
[607326]='',
[607327]='',
[607328]='',
[607329]='',
[607330]='',
[607331]='',
[607332]='',
[607333]='',
[607334]='',
[607335]='',
[607336]='',
[607337]='',
[607338]='',
[607339]='',
[607340]='',
[607341]='',
[607342]='',
[607343]='',
[607344]='',
[607345]='',
[607346]='',
[607347]='',
[607348]='',
[607349]='',
[607350]='',
[607351]='',
[607352]='',
[607353]=[[فتح القفل بعد اجتياز المستوى الرئيسي [{%s1}] ]],
[607354]='',
[607355]='',
[607356]='',
[607357]='',
[607358]='',
[607359]='',
[607360]='',
[607361]='',
[607362]=[[واحدة من أقوى المخرجات]],
[607363]='',
[607364]='',
[607365]='',
[607366]='',
[607367]='',
[607368]='',
[607369]='',
[607370]='',
[607371]='',
[607372]=[[لقد كنت محاصرا هنا لفترة طويلة. حرر هذه المنطقة ودعني أقاتل من أجلك!]],
[607373]=[[يمكنني توفير مساحة كبيرة من الضرر أثناء القتال والقضاء على الفور على مجموعات كبيرة من الزومبي!]],
[607374]=[[يمكنني حماية فريقك وزيادة قوة جميع فرقك.]],
[607375]=[[احصل على مفتاح جديد]],
[607376]=[[أنقذ <color=#F071FF>فيرنا</color> وستنضم إلى فريقك]],
[607377]=[[مازلنا بحاجة إلى مفاتيح <color=#4BFF27>{%s1}</color>]],
[607378]=[[يا أميرة، وجدتُ المفتاح الأول! هذا القفل مفتوح!]],
[607379]=[[آه، أنت أيها الفارس الشجاع! شكرًا جزيلًا لك! أخيرًا، خفّضت السلسلة قليلًا.]],
[607380]=[[من فضلك، استمر في البحث عن المفاتيح المتبقية. كل آمالي معلقة عليك!]],
[607381]=[[صاحب السمو، انظر، لقد وجدت مفتاحًا آخر!]],
[607382]=[[يا له من روعة! في كل مرة تفتح فيها قفلًا، أشعر أنني أقترب خطوةً من الحرية.]],
[607383]=[[شكراً جزيلاً لك يا فارس. لا يزال هناك <color=#EE622A>ثلاث خصلات</color>، أرجوك لا تيأس!]],
[607384]=[[يا أميرة، انتظري! تم فتح <color=#EE622A>القفل الثالث</color> أيضًا!]],
[607385]=[[أنت ملاكي الحارس! أشعر أن القيود تخفّ كثيرًا!]],
[607386]=[[شكراً لكم! لم يتبقَّ سوى <color=#EE622A>الاثنان الأخيران</color>، والنصر قريب!]],
[607387]=[[كن مطمئنًا، سأقوم بإنقاذك بأمان بالتأكيد!]],
[607388]=[[يا أميرة، المفتاح الرابع هنا! النصر قريب!]],
[607389]=[[كان قلبي ينبض بسرعة! كنتُ حرًا تقريبًا، ولو قليلًا!]],
[607390]=[[لم يبقَ إلا <color=#EE622A>القيد الأخير!</color>شكرًا لك أيها المحارب الشجاع، لن أنسى لطفك أبدًا. أرجوك ابحث عن المفتاح الأخير!]],
[607391]=[[يا أميرة! هذا هو <color=#EE622A>المفتاح الأخير! </color> لقد نجحنا!]],
[607392]=[[أخيرًا... أخيرًا أنا حر! بسرعة، بسرعة، افتحه! دعني أرى ضوء النهار من جديد!]],
[607393]=[[أنا حر! أخيرًا حر! أيها المحارب، لا كلمات تعبر عن امتناني. أنت أروع بطل في حياتي!]],
[607394]=[[قم بمسح الشبكة للحصول على المفتاح]],
[607395]=[[تم فتحه بعد مستوى القلعة {%s1}]],
[607396]=[[قم بترقية القلعة إلى مستوى أعلى لفتح المزيد من المستويات، واستمر في ترقية المبنى!]],
[607397]=[[هجوم الجنود]],
[607398]=[[حروب المدافع]],
[607399]=[[أنقذوا الكلاب]],
[607400]=[[المهمة تم إنجازها]],
[607401]=[[فشلت المهمة]],
[607402]=[[أكثر من {%s1}% من اللاعبين]],
[607403]=[[فتح القفل بعد {%s1}h{%s2}m]],
[607404]=[[فتح القفل بعد {%s1}m{%s3}s]],
[607405]=[[استخدم بوابة الضرب للحصول على المزيد من الجنود]],
[607406]=[[بعد بناء مبنى "اللعبة الصغيرة"، يمكنك الحصول على مكافآت الجندي]],
[607407]=[[تم فتح مستويات جديدة]],
[607408]=[[أكثر من 200]],
[607409]=[[لا يمكن أن يكون هناك أي تراخي في خط الدفاع.]],
[607410]=[[هناك رائحة غير عادية في الريح.]],
[607411]=[[أتمنى أن يكون اليوم سلميًا أيضًا.]],
[607412]=[[نصلتي دائما جاهزة.]],
[607413]=[[يعد هذا الكأس من البيرة أفضل مكافأة ليوم عمل شاق.]],
[607414]=[[صمتًا، دعني أستمع إلى الأخبار التي جلبتها الريح.]],
[607415]=[[رسالة من المنزل تساوي ألف قطعة من الذهب.]],
[607416]=[[يرجى إعطاء التعليمات.]],
[607417]=[[سيفي في خدمتك.]],
[607418]=[[أي نتائج؟]],
[607419]=[[أنا مستعد.]],
[607420]=[[حصلت عليه، سأذهب إلى هناك على الفور.]],
[607421]=[[تم تأكيد الهدف.]],
[607422]=[[هذا الطريق آمن، اتبعني.]],
[607423]=[[اتركها لي.]],
[607424]='انطلقت.',
[607425]=[[الكلاب الضالة المشردة]],
[607426]=[[المسكين الصغير، من يستطيع إنقاذه؟]],
[607427]=[[بيت الكلب]],
[607428]=[[كابينة مريحة توفر مأوى للكلاب]],
[607429]=[[يعتبر المنزل الريفي الرائع والجميل منزلًا سعيدًا للكلاب]],
[607430]=[[الجرو خرج للبحث عن الكنز ...]],
[607431]=[[احصل على المكافآت]],
[607432]=[[تبني الكلاب الضالة]],
[607433]=[[سأبحث عن الكنز لسيدي 24 ساعة في اليوم!]],
[607434]=[[فتح ميزة "البحث عن كنز الحيوانات الأليفة" بشكل دائم]],
[607435]=[[هل يمكنك أن تأخذني إلى الداخل؟]],
[607436]=[[وصف المكافأة]],
[607437]=[[مكافأة التعدين الفردية]],
[607438]=[[البحث عن الكنز العادي]],
[607439]=[[البحث عن الكنز النادر]],
[607440]=[[البحث عن الكنز الأسطوري]],
[607441]=[[عندما يجد حيوان أليف صندوق الكنز، تكون لديه فرصة الحصول على المكافآت التالية:]],
[607442]=[[احتمالية التعدين: {%s1}%]],
[607443]=[[ترقية بيت الكلب!]],
[607444]=[[تم ترقية "البحث عن الكنز النادر" إلى "البحث عن الكنز الأسطوري"!]],
[607445]=[[الحد الأقصى اليومي للبحث عن الكنز هو 10 (<color=#319F38>المستوى التالي: 15</color>)]],
[607446]=[[يمكن للحيوانات الأليفة العثور على صناديق الكنز: صناديق الكنز النادرة (<color=#319F38>المستوى التالي: صناديق الكنز الأسطورية</color>)]],
[607447]=[[الحد الأقصى اليومي للبحث عن الكنز هو 15]],
[607448]=[[يمكن للحيوانات الأليفة العثور على صناديق الكنز: صناديق الكنز الأسطورية]],
[607449]=[[حد البحث عن الكنز اليومي]],
[607450]='10',
[607451]='15',
[607452]=[[يمكن للحيوانات الأليفة العثور على صناديق الكنز:]],
[607453]=[[صندوق الكنز النادر]],
[607454]=[[الصندوق الأسطوري]],
[608000]='',
[608001]='',
[608002]='',
[608003]='',
[608004]='',
[608005]='',
[608006]='',
[608007]='',
[608008]='',
[608009]='',
[608010]='',
[608011]='',
[608012]='',
[608013]='',
[608014]='',
[608015]='',
[608016]='',
[608017]='',
[608018]='',
[608019]='',
[608020]='',
[608021]='',
[608022]='',
[608023]='',
[608024]='',
[608025]='',
[608026]='',
[608027]='',
[608028]='',
[608029]='',
[608030]='',
[608031]='',
[608032]='',
[608033]='',
[608034]='',
[608035]='',
[608036]='',
[608037]='',
[608038]='',
[608039]='',
[608040]='',
[608041]='',
[608042]='',
[608043]='',
[608044]='',
[608045]='',
[608046]='',
[608047]='',
[608048]='',
[608049]='',
[630001]='',
[630002]='',
[630003]='',
[630004]='',
[630005]='',
[630006]='',
[630007]='',
[630008]='',
[630009]='',
[630010]='',
[630011]='',
[630012]='',
[630013]='',
[630014]='',
[630015]='',
[630016]='',
[630017]='',
[630018]='',
[630019]='',
[630020]='',
[630021]='',
[630022]='',
[630023]='',
[630024]='',
[630025]=[[<color=#FF3E3E>الأضرار الجسدية</color>]],
[630026]=[[هذه المهارة سوف تسبب ضررا جسديا]],
[630027]=[[<color=#BD3FFF>ضرر الطاقة</color>]],
[630028]=[[هذه المهارة سوف تسبب ضررا في الطاقة]],
[630029]=[[<color=#FF3E3E>الحماية المادية</color>]],
[630030]=[[ستوفر هذه المهارة الحماية الجسدية وتقلل من الضرر الجسدي الذي يتعرض له البطل]],
[630031]=[[<color=#BD3FFF>حماية الطاقة</color>]],
[630032]=[[ستوفر هذه المهارة حماية الطاقة وتقلل من الضرر الناتج عن الطاقة الذي يتعرض له البطل]],
[630033]=[[<color=#6AD440>تأثير الفائدة</color>]],
[630034]=[[ستوفر هذه المهارة تأثيرًا معززًا لحزبنا]],
[630035]=[[<color=#FF3E3E>تأثير إضعاف</color>]],
[630036]='',
[630037]='',
[630038]='',
[630039]='',
[630040]='',
[630041]='',
[630042]='',
[630043]='',
[630044]='',
[630045]='',
[630046]='',
[630047]='',
[630048]='',
[630049]='',
[630050]='',
[630051]='',
[630052]='',
[630053]='',
[630054]='',
[630055]='',
[630056]='',
[630057]='',
[630058]='',
[630059]='',
[630060]='',
[630061]='',
[630062]='',
[630063]='',
[630064]='',
[630065]='',
[630066]='',
[630067]='',
[630068]='',
[630069]='',
[630070]='',
[630071]='',
[630072]='',
[630073]='',
[630074]='',
[630075]='',
[630076]='',
[630077]='',
[630078]='',
[630079]='',
[630080]='',
[630081]='',
[630082]='',
[630083]='',
[630084]='',
[630085]='',
[630086]='',
[630087]='',
[630088]='',
[630089]='',
[630090]='',
[630091]='',
[630092]='',
[630093]='',
[630094]='',
[630095]='',
[630096]='',
[630097]='',
[630098]='',
[630099]='',
[630100]='',
[630101]='',
[630102]='',
[630103]='',
[630104]='',
[630105]='',
[630106]='',
[631001]='',
[631002]='',
[631003]='',
[631004]='',
[631005]='',
[631006]='',
[631007]='',
[631008]='',
[631009]='',
[631010]='',
[631011]='',
[631012]='',
[631013]='',
[631014]='',
[631015]='',
[631016]='',
[631017]='',
[631018]='',
[631019]='',
[631020]='',
[631021]=[[تمت إضافة البطل إلى جدار الشرف]],
[650001]='',
[650002]='',
[650003]='',
[650004]='',
[650005]='',
[650006]='',
[650007]='',
[650008]='',
[650009]='',
[650010]='',
[650011]='',
[650012]='',
[650013]='',
[650014]='',
[650015]='',
[650016]='',
[650017]='',
[650018]='',
[650019]='',
[650020]='',
[650021]='',
[650022]='',
[650023]='',
[650024]='',
[650025]='',
[650026]='',
[650027]='',
[650028]='',
[650029]='',
[650030]='',
[650031]='',
[650032]='',
[650033]='',
[650034]='',
[650035]='',
[650036]='',
[650037]='',
[650038]='',
[650039]='',
[650040]='',
[650041]='',
[650042]='',
[650043]='',
[650044]='',
[650045]='',
[650046]='',
[650047]='',
[650048]='',
[650049]='',
[650050]='',
[650051]='',
[650052]='',
[650053]='',
[650054]='',
[650055]='',
[650056]='',
[650057]='',
[650058]='',
[650059]='',
[650060]='',
[650061]=[[في Kingdom Showdown، إطار الصورة الرمزية الحصري للرئيس في منطقة البطل!]],
[650062]=[[حدث معركة المملكة]],
[650063]=[[في Kingdom Showdown، إطار الصورة الرمزية الحصري لمنطقة البطل!]],
[650064]='',
[650065]='',
[650066]=[[إطار صورة المارشال]],
[650067]=[[إطار الصورة العامة]],
[650068]=[[إطار الصورة الرمزية للكابتن]],
[650069]=[[إطار صورة القبطان]],
[650070]=[[إطار صورة سنتوريون]],
[650071]='',
[650072]='',
[650073]='',
[650074]='',
[650075]='',
[650076]='',
[650077]='',
[650078]='',
[650079]='',
[650080]='',
[650081]='',
[650082]='',
[650083]='',
[650084]='',
[650085]='',
[650086]='',
[650087]='',
[650088]='',
[650089]='',
[650090]='',
[650091]='',
[650092]='',
[650093]='',
[650094]='',
[650095]='',
[650096]=[[مثل النجاح]],
[650097]=[[قم بتفعيل Elite Pass للاستخدام]],
[650098]=[[تم الحصول عليها من خلال حدث [محاكمة سيرا] ]],
[650099]='',
[650100]=[[القائمة فارغة]],
[650101]=[[المملكة {%s1}]],
[650102]='',
[650103]=[[تم إلغاء حظر {%s1}]],
[650104]=[[يمكن الحصول على مظهر أجنحة السرعة من خلال المشاركة في [دوري المبارزة] ]],
[650501]='',
[650502]='',
[650503]='',
[650504]='',
[650505]='',
[650506]='',
[650507]='',
[650508]='',
[650509]='',
[650510]='',
[650511]='',
[650512]='',
[650513]='',
[650514]='',
[650515]='',
[650516]='',
[650517]='',
[650518]='',
[650519]='',
[650520]='',
[650521]='',
[650522]='',
[650523]='',
[650524]='',
[650525]='',
[650526]='',
[650527]='',
[650528]='',
[650529]='',
[650530]='',
[650531]='',
[650541]='',
[650601]='',
[650602]='',
[650603]='',
[650604]='',
[650605]='',
[650606]='',
[650607]='',
[650608]='',
[650609]='',
[650610]='',
[650611]='',
[650612]='',
[650701]='',
[650702]='',
[650703]='',
[650704]='',
[650705]='',
[650706]='',
[650707]='',
[650708]='',
[650709]='',
[650710]='',
[650711]='',
[650712]='',
[650713]='',
[650714]='',
[650715]='',
[650716]='',
[650717]='',
[650718]='',
[650719]='',
[650720]='',
[650721]='',
[650722]='',
[650723]='',
[650724]='',
[650725]='',
[650726]='',
[650801]='',
[650802]='',
[650803]='',
[650804]='',
[650805]='',
[650806]='',
[650807]='',
[650808]='',
[650809]='',
[650810]='',
[650811]='',
[650812]='',
[650813]='',
[650814]='',
[650815]='',
[650816]='',
[650901]='',
[650902]='',
[650903]='',
[650904]='',
[650905]='',
[650906]='',
[650907]='',
[650908]='',
[650909]='',
[650910]='',
[650911]='',
[650912]='',
[650913]='',
[650914]='',
[650915]='',
[650916]='',
[650917]='',
[650918]='',
[650919]='',
[651001]='',
[651002]='',
[651003]='',
[651004]='',
[651005]='',
[651006]='',
[651007]='',
[651008]='',
[651009]='',
[651010]='',
[651011]='',
[651012]='',
[651013]='',
[651014]='',
[651015]='',
[651016]='',
[651017]='',
[651018]='',
[651019]='',
[651020]='',
[651201]='',
[651202]='',
[651203]='',
[651204]='',
[651205]='',
[651206]='',
[651207]='',
[651208]='',
[651209]='',
[651210]='',
[651401]='',
[651402]='',
[651403]='',
[651404]='',
[651405]='',
[651406]='',
[651407]='',
[651408]='',
[651409]='',
[651410]='',
[651411]='',
[651412]='',
[651413]='',
[651414]='',
[651415]='',
[651416]='',
[651417]='',
[651418]=[[الحد اليومي لاكتساب الصدر]],
[651419]='',
[651420]='',
[651501]='',
[651502]='',
[651503]='',
[651504]='',
[651505]='',
[651506]='',
[651507]='',
[651508]='',
[651509]='',
[651510]='',
[651511]='',
[651512]='',
[651513]='',
[651514]='',
[651515]='',
[651516]='',
[651517]='',
[651518]='',
[651519]='',
[651520]='',
[651521]='',
[651522]='1k',
[651523]='10k',
[651524]='50k',
[651525]='1M',
[651526]='5M',
[651527]='15M',
[651528]='',
[651529]='',
[651530]='',
[651531]='',
[651532]='',
[651533]='',
[652001]='',
[652002]='',
[652003]='',
[652004]='',
[652005]='',
[652006]='',
[652007]='',
[652008]='',
[652009]='',
[652010]='',
[652011]='',
[652012]='',
[652013]='',
[652014]='',
[652015]='',
[652016]='',
[652017]='',
[652018]='',
[652019]='',
[652020]='',
[652021]='',
[652022]='',
[652023]='',
[652024]='',
[652025]='',
[652026]='',
[652027]='',
[652028]='',
[652029]='',
[652030]='',
[652031]='',
[652032]='',
[652033]='',
[652034]='',
[652035]='',
[652036]='',
[652037]='',
[652038]='',
[652039]='',
[652040]='',
[652041]='',
[652042]='',
[652043]='',
[652044]='',
[652045]='',
[652046]='',
[652047]='',
[652048]=[[يمكنك القتال ضد أمراء غير حلفاء في العالم، مثل نهب القلاع ومهاجمة نقاط الموارد وما إلى ذلك. القتال مع أمراء آخرين أمر خطير وقد يسبب خسائر لجنودك، لذا يرجى توخي الحذر!]],
[652049]='',
[652050]='',
[652051]='',
[652052]='',
[652053]='',
[652054]='',
[652055]='',
[652056]='',
[652057]='',
[652058]='',
[652059]='',
[652060]='',
[652061]='',
[652062]='',
[652063]='',
[652064]='',
[652065]='',
[652066]='',
[652067]='',
[652068]='',
[652069]='',
[652070]='',
[652071]='',
[652072]='',
[652073]='',
[652074]='',
[652075]='',
[652076]='',
[652077]='',
[652078]='',
[652079]='',
[652080]='',
[652081]='',
[652082]='',
[652083]='',
[652084]='',
[652085]='',
[652086]='',
[652087]='',
[652088]='',
[652089]='',
[652090]='',
[652091]='',
[652092]='',
[652093]='',
[652094]='',
[652095]='',
[652096]='',
[652097]='',
[652098]='',
[652099]='',
[652100]='',
[652101]='',
[652102]='',
[652103]='',
[652104]='',
[652105]='',
[652106]='',
[652107]='',
[652108]='',
[652109]='',
[652110]='',
[652111]='',
[652112]='',
[652113]='',
[652114]='',
[652115]='',
[652116]='',
[652117]='',
[652118]='',
[652119]='',
[652120]='',
[652121]='',
[652122]='',
[652123]='',
[652201]='',
[652202]='',
[652211]='',
[652212]='',
[652221]='',
[652222]='',
[652231]='',
[652232]='',
[652241]='',
[652242]='',
[652261]='',
[652262]='',
[652271]='',
[652272]='',
[652281]='',
[652282]='',
[652291]='',
[652292]='',
[652293]=[[<color=#6fe978>{%s1}</color> ساعدك في إكمال الحدث]],
[652294]=[[حلفاؤك المفيدون سيساعدونك في إكمال بعض مهام الاستخبارات الخاصة بك!]],
[652295]='شاكر',
[652296]=[[شكرا على النجاح!]],
[653001]='',
[653002]='',
[653003]='',
[653004]='',
[653005]='',
[653006]='',
[653007]='',
[653008]='',
[653009]='',
[653010]='',
[653011]='',
[653012]='',
[653013]='',
[653014]='',
[653015]='',
[653016]='',
[653017]='',
[653018]='',
[653019]='',
[653020]='',
[653021]='',
[653022]='',
[653023]='',
[653024]='',
[653025]='',
[653026]='',
[653027]='',
[653028]='',
[653029]='',
[653030]='',
[653031]='',
[653032]='',
[653033]='',
[653034]='',
[653035]='',
[653036]='',
[653037]='',
[653038]='',
[653039]='',
[653040]='',
[653041]=[[تفاصيل القوة القتالية]],
[653501]='',
[653502]='',
[653503]='',
[653504]='',
[653505]='',
[653506]='',
[653507]='',
[653508]='',
[653509]='',
[653510]='',
[653511]='',
[653512]='',
[653513]='',
[653514]='',
[653515]='',
[653516]='',
[653517]='',
[653518]='',
[653519]='',
[653520]='',
[653521]='',
[653522]='',
[653523]='',
[653524]='',
[653525]='',
[653526]='',
[653527]='',
[653528]='',
[653530]='',
[653531]='',
[653532]='',
[653533]='',
[653534]='',
[653535]='',
[653536]='',
[653537]='',
[653538]='',
[653539]='',
[653540]='',
[653541]='',
[653542]='',
[653543]='',
[653544]='',
[653545]='',
[653546]='',
[653547]='',
[653548]='',
[653549]='',
[653550]='',
[653551]='',
[653552]='',
[653553]='',
[653554]='',
[653555]='',
[653556]='',
[653557]='',
[653558]='',
[653559]='',
[653560]='',
[653561]='',
[653562]='',
[653563]='',
[653564]='',
[653565]='',
[653566]='',
[653567]='',
[653568]='',
[653569]='',
[653570]='',
[653571]='',
[653572]='',
[653573]='',
[653574]='',
[653575]='',
[653576]='',
[653577]='',
[653578]='',
[653579]='',
[653580]='',
[653581]='',
[653582]='',
[653583]='',
[653584]='',
[653585]='',
[653586]='',
[653587]='',
[653588]='',
[653589]='',
[653590]='',
[653591]='',
[653592]='',
[653593]='',
[653594]='',
[653595]='',
[653596]='',
[653597]='',
[653598]='',
[653599]='',
[653600]='',
[653601]='',
[653602]='',
[653603]='',
[653604]='',
[653605]='',
[653606]='',
[653607]='',
[653608]='',
[653609]='',
[653610]='',
[653611]='',
[653612]='',
[653613]='',
[653614]=[[لا يوجد أفضل لاعب حتى الآن]],
[653615]=[[المشاركة دون اتصال]],
[654001]='',
[654002]='',
[654003]='',
[654004]='',
[654005]='',
[654006]='',
[654007]='',
[654008]='',
[654009]='',
[654010]='',
[654011]='',
[654012]='',
[654013]='',
[654014]='',
[654015]='',
[654016]='',
[654017]='',
[654018]='',
[654019]='',
[654020]='',
[654021]='',
[654022]='',
[654023]='',
[654024]='',
[654025]='',
[654026]='',
[654027]='',
[654028]='',
[654029]='',
[654030]='',
[654031]='',
[654032]='',
[654033]='',
[654034]='',
[654035]='',
[654036]='',
[654037]='',
[654038]='',
[654039]='',
[654040]='',
[654041]='',
[654042]='',
[654043]='',
[654044]='',
[654045]='',
[654046]='',
[654047]='',
[654048]='',
[654049]='',
[654050]='',
[654051]='',
[654052]='',
[654053]='',
[654054]='',
[654055]='',
[654056]='',
[654057]='',
[654058]='',
[654059]='',
[654060]='',
[654061]='',
[654062]='',
[654063]='',
[654064]='',
[654065]='',
[654066]='',
[654067]='',
[654068]='',
[654069]='',
[654070]='',
[654071]='',
[654072]='',
[654073]='',
[654074]='',
[654075]='',
[654076]=[[١. كل اثنين وخميس وأحد، في الساعة ٠:٠٠، ٦:٠٠، ١٢:٠٠، و١٨:٠٠ على الخادم، ينزل العملاق ذو الرأسين إلى العالم، ويبقى على قيد الحياة لمدة ٣ ساعات في كل مرة. يمكن للوردات العثور عليه بسرعة من خلال واجهة الحدث.]],
[654077]='',
[654078]='',
[654079]='',
[654080]='',
[654081]='',
[654082]='',
[654083]='',
[654084]='',
[654085]='',
[654086]='',
[654087]='',
[654088]='',
[654089]='',
[654090]='',
[654091]='',
[654092]='',
[654093]='',
[654094]='',
[654095]='',
[654096]='',
[654097]='',
[654098]='',
[654099]='',
[654100]='',
[654101]='',
[654102]='',
[654103]='',
[654104]='',
[654105]='',
[654106]='',
[654107]='',
[654108]='',
[654109]='',
[654110]='',
[654111]=[[مهلة القتال]],
[654112]=[[الأضرار الناجمة عن هذا الهجوم]],
[654113]='',
[654114]=[[لديك المكافآت التالية لتلقيها]],
[654115]=[[أعلى ضرر يُلحقه اللوردات في مملكتين!
احسب إجمالي الضرر الذي يُسببه أفضل 200 لورد في كل منطقة معركة، والفائز هو صاحب أعلى إجمالي ضرر!]],
[655001]='',
[655002]='',
[655003]='',
[655004]='',
[655005]='',
[655006]='',
[655007]='',
[655008]='',
[655009]='',
[655010]='',
[655011]='',
[655012]='',
[655013]='',
[655014]='',
[655015]='',
[655016]='',
[655017]='',
[655018]='',
[655019]='',
[655020]='',
[655021]='',
[655022]='',
[655023]='',
[655024]='',
[655025]='',
[655026]='',
[655027]='',
[655028]='',
[655029]='',
[655030]='',
[655031]='',
[655032]='',
[655033]=[[أكمل مهمة هدف التنشيط]],
[655050]='',
[655051]='',
[655052]='',
[655053]='',
[655054]='',
[655055]='',
[655056]='',
[655057]='',
[655058]='',
[655059]='',
[655060]='',
[655061]='',
[655062]='',
[655063]='',
[655064]='',
[655065]='',
[655066]='',
[655067]='',
[655068]='',
[655069]='',
[655070]='',
[655071]='',
[655072]='',
[655073]='',
[655074]='',
[655075]='',
[655076]='أنا',
[655077]=[[أنت في منطقة حرب أخرى، ولا يمكنك الانضمام إلى مجموعة سباق التسلح. يُرجى العودة إلى منطقة الحرب الأصلية والمحاولة مرة أخرى.]],
[655078]=[[استثناء الحصول على بيانات النشاط، يرجى المحاولة مرة أخرى لاحقًا]],
[656001]='',
[656002]='',
[656003]='',
[656004]='',
[656005]='',
[656006]='',
[656007]='',
[656008]='',
[656009]='',
[656010]='',
[656011]='',
[656012]='',
[656013]='',
[656014]='',
[656015]='',
[656016]='',
[656017]='',
[656018]='',
[656019]='',
[656020]='',
[656021]='',
[656022]='',
[656023]='',
[656024]='',
[656025]='',
[656026]='',
[656027]='',
[656028]='',
[656029]='',
[656030]='',
[656031]='',
[656032]='',
[656033]='',
[656034]='',
[656035]='',
[656036]='',
[656037]='',
[656038]='',
[656039]='',
[656040]='',
[656041]='',
[656042]='',
[656043]='',
[656044]='',
[656045]='',
[656046]='',
[656047]='',
[656048]='',
[656049]='',
[656050]='',
[656051]='',
[656052]='',
[656053]='',
[656054]='',
[656055]='',
[656056]='',
[656057]=[[أنت حاليًا في مملكة أخرى ولا يمكنك البحث عن أعداء Dark Night Trial. يرجى العودة إلى المملكة الأصلية والمحاولة مرة أخرى.]],
[657001]='',
[657002]='',
[657003]='',
[657004]='',
[657005]='',
[657006]='',
[657007]='',
[657008]='',
[657009]='',
[657010]='',
[657011]='',
[657012]='',
[657013]='',
[657014]='',
[657015]='',
[657016]='',
[657017]='',
[657018]='',
[657019]='',
[657020]='',
[657021]='',
[657022]='',
[657023]='',
[657024]='',
[657025]='',
[657026]='',
[657027]='',
[657028]='',
[657029]='',
[657030]='',
[657031]='',
[657032]='',
[657033]='',
[657034]='',
[657035]='',
[657036]='',
[657037]='',
[657038]='',
[657039]='',
[657040]='',
[657041]='',
[657042]='',
[657043]=[[أكمل تجربة الصعوبة {%s1} {%s2}]],
[657044]='',
[657045]='',
[657046]=[[يجب عليك الوصول إلى VIP8 وتنشيطه قبل أن تتمكن من استخدامه]],
[657047]='',
[657048]='',
[657049]='',
[657050]='',
[657051]='',
[657052]='',
[657053]='',
[657054]='',
[657055]='',
[657056]='',
[657057]='',
[657058]='',
[657059]=[[تمت زيادة صحة <color=#319f38>{%s1}</color> بنسبة <color=#319f38>50%</color>]],
[657060]=[[تمت زيادة هجوم <color=#319f38>{%s1}</color> بنسبة <color=#319f38>25%</color>]],
[657061]=[[تمت زيادة صحة <color=#319f38>{%s1}</color> بنسبة <color=#319f38>50%</color>]],
[657062]=[[تمت زيادة هجوم <color=#319f38>{%s1}</color> بنسبة <color=#319f38>25%</color>]],
[657063]=[[تمت زيادة هجوم <color=#319f38>{%s1}</color> بنسبة <color=#319f38>25%</color>]],
[657064]=[[تمت زيادة هجوم <color=#319f38>{%s1}</color> بنسبة <color=#319f38>25%</color>]],
[657065]=[[تمت زيادة صحة <color=#319f38>{%s1}</color> بنسبة <color=#319f38>50%</color>]],
[657066]=[[تمت زيادة صحة <color=#319f38>{%s1}</color> بنسبة <color=#319f38>50%</color>]],
[657067]=[[تمت زيادة هجوم <color=#319f38>{%s1}</color> بنسبة <color=#319f38>25%</color>]],
[657068]=[[تمت زيادة صحة <color=#319f38>{%s1}</color> بنسبة <color=#319f38>50%</color>]],
[657069]=[[تمت زيادة هجوم <color=#319f38>{%s1}</color> بنسبة <color=#319f38>25%</color>]],
[657070]=[[تمت زيادة صحة <color=#319f38>{%s1}</color> بنسبة <color=#319f38>50%</color>]],
[657071]=[[تمت زيادة هجوم <color=#319f38>{%s1}</color> بنسبة <color=#319f38>25%</color>]],
[657072]=[[تمت زيادة صحة <color=#319f38>{%s1}</color> بنسبة <color=#319f38>50%</color>]],
[657073]=[[تمت زيادة هجوم <color=#319f38>{%s1}</color> بنسبة <color=#319f38>25%</color>]],
[657074]=[[تمت زيادة الضرر الذي يسببه أبطال الغابة بنسبة <color=#319f38>25%</color>]],
[657075]=[[تمت زيادة الضرر الذي يسببه الأبطال البشريون بنسبة <color=#319f38>25%</color>]],
[657076]=[[تمت زيادة الضرر الذي يسببه Night Hero بنسبة <color=#319f38>25%</color>]],
[657077]=[[أكمل تجربة الغابة على مستوى الصعوبة {%s2}]],
[657078]=[[أكمل تجربة الإنسان على مستوى الصعوبة {%s2}]],
[657079]=[[أكمل تجربة Dark Night على مستوى الصعوبة {%s2}]],
[658001]='',
[658002]='',
[658003]='',
[658004]='',
[658005]='',
[658006]='',
[658007]='',
[658008]='',
[658009]='',
[658010]='',
[658011]='',
[658012]='',
[658013]='',
[658014]='',
[658015]='',
[658016]='',
[658017]='',
[658018]='',
[658019]='',
[658020]='',
[658021]='',
[658022]='',
[658023]='',
[658024]='',
[658025]='',
[658026]='',
[658027]='',
[658028]='',
[658029]='',
[658030]='',
[658031]='',
[658032]='',
[658033]='',
[658034]='',
[658035]='',
[658036]='',
[658037]='',
[658038]='',
[658039]='',
[658040]='',
[658041]='',
[658042]='',
[658043]='',
[658044]='',
[658045]='',
[658046]=[[1. يمكن للقادة إجراء التجارة بين المدن حتى 4 مرات في اليوم. سوف تشغل كل تجارة شاحنة لفترة من الزمن. كلما تم فتح المزيد من الفرق، زاد عدد الشاحنات المتاحة.]],
[658047]=[[2. بعد انطلاق الشاحنة، ستتحرك في طريق عشوائي وقد تتعرض للنهب من قبل الآخرين أثناء العملية. لا يتم استهلاك أي جنود في معركة النهب، ويمكن نهب كل شاحنة حتى مرتين.]],
[658048]='',
[658049]='',
[658050]='',
[658051]='',
[658052]='',
[658053]='',
[658054]='',
[658055]='',
[658056]='',
[658057]='',
[658058]=[[1. عند تشغيل شاحنة غير حليفة على الخريطة، يمكن للقائد نهبها، حتى 4 مرات في اليوم.]],
[658059]=[[2. بعد نجاح الهجوم، سيتم نهب البضائع والموارد الموجودة على الشاحنة بشكل عشوائي، وستنخفض سلامة الشاحنة بنسبة معينة.]],
[658060]='',
[658061]='',
[658062]='',
[658063]='',
[658064]='',
[658065]=[[[1]:C[50] ب[30] أ[10] ق[5] ق+[5] ]],
[658066]=[[[2]:C[30] ب[30] أ[20] ق[10] ق+[10] ]],
[658067]=[[[3]:C[20] ب[20] أ[30] ق[15] ق+[15] ]],
[658068]=[[[4]:C[0] ب[20] أ[40] ق[20] ق+[20] ]],
[658069]=[[[5]:C[0] ب[0] أ[50] ق[25] ق+[25] ]],
[658070]=[[[>5]:C[0] B[0] A[0] S[0] S+[100] ]],
[658071]=[[2. بعد فتح شاحنة الكنز، ستتاح لها فرصة الظهور عند استخدام عقد تجاري لاستبدال الشاحنة. وزن شاحنة الكنز ثابت عند 10 ولا يتأثر بعدد التحديثات.]],
[658072]=[[3. حساب الاحتمال: وزن الشاحنة التي تم تحديثها في وقت معين هو S+[100]، وشاحنة الكنز [10]، واحتمال تحديث شاحنة S+ هو 90.9%، واحتمال تحديث شاحنة الكنز هو 9.1%\n]],
[658073]='',
[658074]='',
[658075]='',
[658076]='',
[658077]='',
[658078]=[[3. لا يجوز نهب سلامة النقل بنسبة تقل عن 50%]],
[658079]='',
[658080]='',
[658081]='',
[658082]='',
[658083]='',
[658084]='',
[658085]='',
[658086]='',
[658087]='',
[658088]='',
[658089]='',
[658090]='',
[658091]='',
[658092]='',
[658093]='',
[658094]='',
[658095]='',
[658096]='مهزوم',
[658097]='',
[658098]=[[أيها القائد، هل تريد إنفاق {%s1} من الماس لتحديث العربة؟]],
[658099]='',
[658100]='',
[658101]=[[فتح بعد %s]],
[659001]='',
[659002]='',
[659003]='',
[659004]='',
[659005]='',
[660001]='',
[660002]='',
[660003]='',
[660004]='',
[660005]='',
[660006]='',
[660007]='',
[660008]='',
[660009]='',
[660010]='',
[660011]='',
[661001]='',
[661002]='',
[661003]='',
[661004]='',
[661005]='',
[661006]='',
[661007]='',
[661008]='',
[661009]='',
[661010]='',
[661011]='',
[661012]='',
[661013]='',
[661014]='',
[661015]='',
[661016]='',
[661017]='',
[661018]='',
[661019]='',
[661020]='',
[661021]='',
[661022]='',
[661023]='',
[661024]='',
[661025]='',
[661026]='',
[661027]='',
[661028]='',
[661029]='',
[661030]='',
[661031]='',
[661032]='',
[661033]='',
[661034]='',
[661035]='',
[661036]='',
[661037]='',
[661038]='',
[661039]='المملكة:',
[662001]='',
[662002]='',
[662003]=[[تهانينا على التسبب في ضرر إجمالي قدره {%s1} خلال حدث Undead Treasure. حصل على التصنيف {%s2} وحصل على المكافآت التالية.]],
[662004]='',
[662005]='',
[662006]='',
[662007]='',
[662008]='',
[662009]='',
[662010]='',
[662011]='',
[662012]='',
[662013]='',
[662014]=[[البحث عن كنز الرجل الميت]],
[662015]='',
[662016]='',
[662017]='',
[662018]='',
[662019]='',
[662020]='',
[662021]=[[ستصل الموجة التالية من الكنز غير الميت بعد {%s1}]],
[662022]='',
[662023]='',
[662024]='',
[662025]='',
[662026]='',
[662027]='',
[662028]='',
[662029]='',
[662030]='',
[662031]='',
[662032]=[[1. بعد إطلاق حدث الكنز الأموات الأحياء، سيظهر عدد كبير من الكنوز الأموات الأحياء في العالم، وستكون المكافآت سخية للغاية!]],
[662033]=[[2. يمكنك البحث بسرعة عن الكنوز غير الحية القريبة في واجهة الحدث، أو يمكنك البحث يدويًا على الخريطة.]],
[662034]=[[٣. بعد تدمير كنز الموتى الأحياء، ستحصل على عملة نشاط. تذكّر أن تذهب إلى المتجر لاستبدالها بمكافآت إضافية.]],
[662035]=[[٤. في كل مرة تُدمّر فيها كنز الموتى الأحياء، هناك فرصة لاستدعاء قائد الموتى الأحياء. يتمتع قائد الموتى الأحياء بمكافآت عالية جدًا، وقوته مرعبة للغاية، ويتطلب تدميره تجمعًا.]],
[662036]='',
[662037]=[[لم يكتشف تحالفك زعيم الموتى الأحياء بعد. قد يكشف قتل كنز الموتى الأحياء عن آثارهم.]],
[662038]=[[لم تعثر على زعيم الموتى الأحياء بعد. قد يكشف قتل كنز الموتى الأحياء عن آثارهم.]],
[662039]='',
[662040]='',
[662041]='',
[662042]='',
[662043]=[[مكافآت المستدعي]],
[662044]='المستدعي',
[662045]=[[كنز الميت مقطورة]],
[662046]=[[أوندد على وشك الغزو مع الكثير من الكنوز!]],
[662047]=[[لا تزال بعيدة عن الغزو]],
[662048]=[[دليل المبتدئين]],
[662049]=[[<color=#a036d5><size=30>【كنز الموتى】</size></color>]],
[662050]=[[<color=#ed810f><b>·</b>وقت التحديث:</color>]],
[662051]=[[خلال الحدث، سيتم تحديثه كل ساعة من الساعة 0:00 إلى 23:00 كل يوم.]],
[662052]='<color=#ed810f><b>·</b>المكافآت:</color>',
[662053]=[[ستكسب من خلال هزيمتهم طعامًا وخامًا من الحديد وعملات ذهبية وخبرة بطل وعناصر حدث. [وسام الحارس] ]],
[662054]=[[<color=#ed810f><b>·</b>الاستراتيجية الموصى بها:</color>]],
[662055]=[[الوحوش سهلة نسبيًا والمكافآت سخية. يُنصح بشراء أو استخدام أدوات مادية لهزيمة المزيد من كنوز الموتى الأحياء للحصول على مكافآت سخية.]],
[662056]=[[<color=#a036d5><size=30>【زعيم الموتى الأحياء】</size></color>]],
[662057]=[[<color=#ed810f><b>·</b>تحديث القواعد:</color>]],
[662058]=[[إن هزيمة كنز الموتى الأحياء تمنحك فرصة لظهور زعيم الموتى الأحياء]],
[662059]='<color=#ed810f><b>·</b>المكافآت:</color>',
[662060]=[[مكافآت الاستدعاء: عند هزيمة زعيم الموتى الأحياء المستدعى، سيحصل المستدعي على طعام وخام حديد وعملات ذهبية وخبرة بطل وعناصر حدث. [وسام الحارس] ]],
[662061]=[[مكافآت التجمع: بعد التجمع وهزيمة زعيم الموتى الأحياء، سيحصل المبادر على كمية كبيرة من الطعام وخام الحديد والعملات الذهبية وخبرة البطل وعدد كبير من أدوات النشاط. [وسام الحارس] ]],
[662062]=[[<color=#ed810f><b>·</b>الاستراتيجية الموصى بها:</color>]],
[662063]=[[أعط الأولوية لجمع زعماء الموتى الأحياء من المستوى الأدنى، وقم بدعوة الحلفاء الأقوياء للانضمام إلى المعركة ضد زعماء الموتى الأحياء من المستوى الأعلى.]],
[662064]=[[<color=#a036d5><size=30>【مكافآت التصنيف】</size></color>]],
[662065]=[[أثناء الحدث، سيتم تجميع الضرر الذي يلحق بزعيم الموتى الأحياء وتوزيعه وفقًا للتصنيف في نهاية الحدث.]],
[663000]='',
[663001]='',
[663002]='',
[663003]='',
[663004]='',
[663005]='',
[663006]='',
[663007]='',
[663008]='',
[663009]='',
[663010]='',
[663011]='',
[663012]='',
[663013]='',
[663014]='',
[663015]='',
[663016]='',
[663017]='',
[663018]='',
[663019]='',
[663020]='',
[663021]=[[1. خلال الحدث، استهلك الطاقة لبدء تجمع لقتل "كلاس". بعد النصر، سيكون لديك فرصة للحصول على أجزاء من البطل فيرنا.]],
[663022]=[[2. حد المكافأة اليومية للمشاركة في التجمع لقتل "كلاس" هو 20 مرة، ولا يوجد حد لعدد المرات التي يمكنك فيها بدء التجمع.]],
[663023]='',
[663024]='نشاط',
[663025]=[[الحد الأقصى لمستوى البحث يساوي مستوى المقر الرئيسي]],
[663026]=[[لم يتم استيفاء متطلبات المهمة.]],
[663027]=[[بدأ مسيرة وهزم {%s1} من الوحوش الغازية!]],
[663028]=[[{%s1} تم إكمال مهمة عملية التجميع]],
[663029]=[[انتهى الحدث الحالي ولا يمكن الوصول إليه]],
[664001]='',
[664002]='',
[664003]='',
[664004]='',
[664005]='',
[664006]='',
[664007]='',
[664008]='',
[664009]='',
[664010]='',
[664011]='',
[664012]='',
[664013]='',
[664014]='',
[664015]='',
[664016]='',
[664017]='',
[664018]='',
[664019]='',
[664020]='',
[664021]='',
[664022]='',
[664023]='',
[664024]='',
[664025]='',
[664026]='',
[664027]='',
[664028]='',
[664029]='',
[664030]=[[يتم إرسال مهام الحانة تلقائيًا]],
[664031]='',
[664032]='',
[664033]='',
[664034]='',
[664035]='',
[664036]='',
[664037]='',
[664038]='',
[664039]='',
[664040]='',
[664041]='',
[664042]='',
[664043]='',
[664044]='',
[664045]='',
[664046]='',
[664047]='',
[664048]='',
[664049]='',
[664050]='',
[664051]='',
[664052]='',
[664053]='',
[664054]='',
[664055]='',
[664056]='',
[664057]='',
[664058]='',
[664059]='',
[664060]='',
[664061]='',
[664062]='',
[664063]='',
[664064]='',
[664065]='',
[664066]='',
[664067]='',
[664068]='',
[664069]='',
[664070]='',
[664071]='',
[664072]='',
[664073]='',
[664074]='',
[664075]=[[يفتح VIP{%s1} القدرة على عرض الصور الرمزية]],
[665001]='',
[665002]='',
[665003]='',
[665004]='',
[665005]='',
[665006]='',
[665007]='',
[665008]='',
[665009]='',
[665010]='',
[665011]='',
[665012]='',
[665013]='',
[665014]='',
[665015]='',
[665016]='',
[665017]='',
[665018]='',
[665019]='',
[665020]=[[يمكن للأبطال الذين تصل قوتهم القتالية الإجمالية إلى أعلى 200 في المنطقة والخادم المشاركة على الفور!]],
[665021]='',
[665022]='',
[665023]='',
[665024]=[[فقط أفضل 200 بطل من حيث إجمالي القوة القتالية في كل منطقة وخادم يمكنهم المشاركة في كل مسابقة!]],
[665025]='',
[665026]='',
[665027]='',
[665028]='',
[665029]='',
[665030]='',
[665031]='',
[665032]='',
[665033]='',
[665034]='',
[665035]='',
[665036]='',
[665037]='',
[665038]='',
[665039]='',
[665040]='',
[665041]='',
[665042]='',
[665043]='',
[665044]='',
[665045]='',
[665046]='',
[665047]='',
[665048]='',
[665049]='',
[665050]='',
[665051]='',
[665052]='',
[665053]='',
[665054]='',
[665055]='',
[665056]='',
[665057]='',
[665058]='',
[665059]='',
[665060]='',
[665061]='',
[665062]='',
[665063]='',
[665064]='',
[665065]='',
[665066]='',
[665067]='',
[665068]='',
[665069]='',
[665070]='',
[665071]='',
[665072]='',
[665073]='',
[665074]='',
[665075]='',
[665076]='',
[665077]='',
[665078]='',
[665079]='',
[665080]='',
[665081]='',
[665082]='',
[665083]='',
[665084]='',
[665085]='',
[665086]='',
[665087]='',
[665088]='',
[665089]='',
[665090]='',
[665091]='',
[665092]='',
[665093]='',
[665094]='',
[665095]='',
[665096]='',
[665097]='',
[665098]='',
[665099]='',
[665100]='',
[665101]='',
[665102]='',
[665103]='',
[665104]='',
[665105]='',
[665106]='',
[665107]='',
[665108]='',
[665109]='',
[665110]='',
[665111]='',
[665112]='',
[665113]='',
[665114]='',
[665115]='',
[665116]='',
[665117]=[[غير قادر على عرض التفاصيل الشخصية للمدرب المجند]],
[665118]='',
[665119]='',
[665120]='',
[665121]=[[إعادة إصدار مكافأة رتبة المبتدئ في الساحة]],
[665122]=[[إعادة إصدار مكافأة تصنيف المبتدئ في الساحة]],
[665123]=[[لديك مكافآت تصنيف غير مُطالب بها في ساحة المبتدئين. فيما يلي المكافآت التي لم تطالب بها.]],
[665124]=[[تهانينا على حصولك على المركز {%s1} في ساحة المبتدئين. وفيما يلي المكافآت الخاصة بك.]],
[665125]=[[إجمالي الإصابات]],
[665126]=[[تدريب المجندين الجدد]],
[665127]=[[أكمل التحدي]],
[665128]=[[إعادة إصدار مكافأة إنجاز Newbie Arena]],
[665129]=[[لديك مكافآت إنجاز لم يطالب بها أحد في ساحة المبتدئين. فيما يلي المكافآت التي لم تطالب بها.]],
[665130]=[[سباق في التصنيف العالمي والحصول على جميع المكافآت]],
[665131]=[[تمت زيادة الترتيب إلى {%s1}]],
[665132]=[[فتح الساحة]],
[665133]=[[ما إذا كان سيتم استخدام {%s1} من الماس لشراء تحدي واحد]],
[665134]=[[ما إذا كان سيتم استخدام %s%d من الماس لشراء تحدي واحد]],
[665135]=[[ما إذا كان سيتم استخدام %s%d من الماسات لتحديث قائمة المعارك]],
[665136]=[[افتح ساحة المبتدئين]],
[665137]=[[وصل ترتيب ساحة المبتدئين إلى {%s1}]],
[665138]=[[لقد وصل عدد التحديثات اليوم إلى الحد الأعلى]],
[665139]=[[نظرة عامة على المكافآت]],
[665140]=[[الجائزة الأساسية]],
[665141]='',
[665142]=[[لم يقم الخصم بإعداد تشكيلة دفاعية، وتم تخطي المعركة.]],
[665143]=[[جنود العدو أعلى مستوى من جنودك، وسوف يتم قمع أبطالك من قبلهم!]],
[665144]=[[مدرب مجند جديد]],
[666001]='',
[666002]='',
[666003]='',
[666004]='',
[666005]='',
[666006]='',
[667001]='',
[667002]='',
[667003]='',
[667004]='',
[667005]='',
[667006]='',
[667007]='',
[667008]='',
[667009]='',
[667010]='',
[667011]='',
[667012]='',
[667013]='',
[667014]='',
[667015]='',
[667016]='',
[667017]='',
[667018]='',
[667019]='',
[667020]='',
[667021]='',
[667022]='',
[667023]='',
[667024]='',
[667025]='',
[667026]='',
[667027]='',
[667028]='',
[667029]='',
[667030]='',
[667031]='',
[667032]='',
[667033]='',
[667034]='',
[667035]='',
[667036]='',
[667037]='',
[667038]='',
[667039]='',
[667040]='',
[667041]='',
[667042]='',
[667043]='',
[667044]='',
[667045]='',
[667046]='',
[667047]='',
[667048]='',
[667049]='',
[667050]='',
[667051]='',
[667052]='',
[667053]='',
[667054]='',
[667055]='',
[667056]='',
[667057]='',
[667058]='',
[667059]='',
[667060]='',
[667061]='',
[667062]='',
[667063]='',
[667064]='',
[667065]='',
[667066]='',
[667067]='',
[667068]='',
[667069]='',
[667070]='',
[667071]='',
[667072]='',
[667073]='',
[667074]='',
[667075]='',
[667076]='',
[667077]='',
[667078]='',
[667079]='',
[667080]='',
[667081]='',
[667082]='',
[667083]='',
[667084]='',
[667085]='',
[667086]='',
[667087]='',
[667088]='',
[667089]='',
[667090]='',
[667091]='',
[667092]='',
[667093]='',
[667094]='',
[667095]='',
[667096]='',
[667097]='',
[667098]='',
[667099]='',
[667100]='',
[667101]='',
[667102]='',
[667103]='',
[667104]='',
[667105]='',
[667106]='',
[667107]='',
[667108]='',
[667109]='',
[667110]='',
[667111]='',
[667112]='',
[667113]=[[{%s1} بدأت المسابقة في]],
[667114]='',
[667115]='',
[667116]='',
[667117]='',
[667118]='',
[667119]='',
[667120]='',
[667121]='',
[667122]='',
[667123]='',
[667124]='',
[667125]='',
[667126]='',
[667127]='',
[667128]='',
[667129]='',
[667130]='',
[667131]='',
[667132]='',
[667133]='',
[667134]='',
[667135]='',
[667136]='',
[667137]='',
[667138]='',
[667139]='',
[667140]='',
[667141]='',
[667142]='',
[667143]='',
[667144]='',
[667145]='',
[667146]='',
[667147]='',
[667148]='',
[667149]='',
[667150]='',
[667151]='',
[667152]='',
[667153]='',
[667154]='',
[667155]='',
[667156]='',
[667157]='',
[667158]='',
[667159]='',
[667160]='',
[667161]='',
[667162]='',
[667163]='',
[667164]='',
[667165]='',
[667166]='',
[667167]='',
[667168]='',
[667169]='',
[667170]='',
[667171]='',
[667172]='',
[667173]='',
[667174]='',
[667175]='',
[667176]='',
[667177]='',
[667178]='',
[667179]='',
[667180]='',
[667181]='',
[667182]='',
[667183]='',
[667184]='',
[667185]='',
[667186]='',
[667187]='',
[667188]='',
[667189]='',
[667190]='',
[667191]='',
[667192]='',
[667193]='',
[667194]='',
[667195]='',
[667196]=[[لقد أعجبتك بالفعل، لا داعي لأن تعجبك مرة أخرى]],
[667197]='',
[667198]=[[1. معركة المدينة الملكية مفتوحة مرة واحدة في الأسبوع. فقط التحالفات التي تشغل مدن المستوى 6 يمكنها التنافس على المدينة الملكية.]],
[667199]=[[2. معركة المدينة الملكية تستمر لمدة 8 ساعات. التحالف الأول الذي يحتل المدينة الملكية لمدة 4 ساعات، أو التحالف الذي يتمتع بأطول فترة احتلال تراكمي بنهاية الحرب، سيكون هو الفائز في معركة المدينة الملكية هذه.]],
[667200]=[[3. سيصبح زعيم التحالف الفائز هو الملك.]],
[667201]=[[4. المنطقة التي تقع فيها المدينة الملكية هي مكان ملوث. لن يتمكن اللاعبون الذين يدخلون من فتح الدرع الواقي، وفي الوقت نفسه، لن يتمكنوا من الحصول على المهام الاستخباراتية، ولن يتم تنفيذ مهمة Acorn Tavern.]],
[667202]='',
[667203]=[[<color=#a036d5><size=30>[مهارات الحصار]</size></color>]],
[667204]=[[<color=#ed810f><b>·</b>تعيين نقطة التجمع</color>: بعد إعداد نقطة تجمع التحالف، يمكن لأعضاء التحالف الانتقال إلى المدينة بسهولة من خلال [تحرك التحالف].]],
[667205]=[[<color=#ed810f><b>·</b>Invite Rally</color>: في Alliance-Invite Rally، يمكنك بدء دعوة للتجمع لأعضاء التحالف.]],
[667206]=[[<color=#ed810f><b>·</b>إخطار الحلفاء</color>: تتطلب غربلة المدينة تعاون عدد كبير من الحلفاء. إنه اختيار جيد لإخطار الحلفاء مسبقًا بمهاجمة المدينة في الوقت المحدد!]],
[667207]=[[<color=#ed810f><b>·</b>مساعدة متعددة الفرق</color>: في المدينة الملكية، كلما زاد عدد الفرق المشاركة في المساعدة، كلما كان تقدم الاحتلال أسرع! \n]],
[667208]=[[<color=#a036d5><size=30>[نصائح هامة]</size></color>]],
[667209]=[[<color=#ed810f><b>·</b>مهاجمة المنافسين</color>: عند توجيه المعركة، من الجيد إعطاء الأولوية لمهاجمة قلعة المنافس!]],
[667210]=[[<color=#ed810f><b>·</b>الدفاع المساعد المتعدد</color>: عندما يكون فريق الحليف بالخارج وغير قادر على العودة إلى الدفاع، فإن طلب المساعدة من الحلفاء القريبين يعد إستراتيجية اختيارية!]],
[667211]=[[<color=#ed810f><b>·</b>العلاج في الوقت المناسب</color>: إذا تعرضت لهجوم، من فضلك تذكر معالجة الجنود في الوقت المناسب لتجنب الوفاة المباشرة للجنود المصابين الذين يتجاوزون قدرة المستشفى.]],
[667212]=[[وصف النقاط التنافسية\n1. المعركة في المدينة الملكية مفتوحة مرة واحدة في الأسبوع. فقط اللوردات الذين يقاتلون على الأرض الملوثة يمكنهم الحصول على نقاط الشرف. \n2. عند القتال من أجل المدينة الملكية، يمكنك الحصول على نقاط عن طريق التسبب في إصابات خطيرة أو موت الجنود المحليين. كلما ارتفع مستوى الجندي، زادت النقاط التي تحصل عليها. \n3. إذا مات جنودك في المعركة، فسوف تحصل على نقاط. سيتم الإعلان عن مستوى الجنود الذين ماتوا في المعركة، وكلما زادت النقاط التي تحصل عليها. \n4. إذا وصلت إلى عدد معين من النقاط، سوف تتلقى المكافآت الفخرية المقابلة.]],
[667213]=[[لا يمكن المطالبة بصناديق الكنز من التحالفات الأخرى]],
[667214]=[[المدن ذات المستوى {%s1} على وشك أن يتم التنافس عليها]],
[667215]=[[الاستعدادات للحرب]],
[667216]=[[مسابقة المدينة على وشك أن تبدأ. علينا أن نكون مستعدين ونتعاون لاحتلال المدينة والحصول على موارد غنية!]],
[667217]=[[العد التنازلي للحصار]],
[667218]=[[التوقيت المحلي]],
[667219]=[[تم نقله إلى نقطة التجمع للتحالف]],
[667220]=[[اعمل بجد لترقية مستوى القلعة]],
[667221]=[[ما يحتاج التحالف منك أن تفعله]],
[668001]='',
[668002]='',
[668003]='',
[668004]='',
[668005]='',
[668006]='',
[668007]='',
[668008]='',
[668009]='',
[668010]='',
[668011]='',
[668012]='',
[668013]='',
[668014]='',
[668015]='',
[668016]='',
[668017]='',
[668018]='',
[668019]='',
[668020]='',
[668021]='',
[668022]='',
[668023]='',
[668024]='',
[668025]='',
[668026]='',
[668027]='',
[668028]='',
[668029]='',
[668030]='',
[668031]='',
[668032]='',
[668033]='',
[668034]='',
[668035]='',
[668036]='',
[668037]='',
[668038]='',
[668039]='',
[668040]='',
[668041]='',
[668042]='',
[668043]='',
[668044]='',
[668045]='',
[668046]='',
[668047]='',
[668048]='',
[668049]='',
[668050]='',
[668051]=[[المهمة الأرجوانية: 24%]],
[668052]='',
[668053]='',
[668054]='',
[668055]='',
[668056]='',
[668057]=[[<color=#FF0000>الوصول إلى VIP4 وتفعيله</color> لبدء معركة بنقرة واحدة]],
[668058]=[[<color=#FF0000>الوصول إلى VIP12 وتفعيل</color> أو <color=#FF0000>مستوى القلعة 27</color> للاستخدام]],
[668059]='',
[668060]='',
[668061]='',
[668062]='',
[668063]='',
[668064]='',
[668065]='',
[668066]='',
[668067]='',
[668068]='',
[668069]='',
[668070]=[[مهمة حانة {%s1}.]],
[668071]=[[لديك حاليًا مهام برتقالية لم يتم إرسالها. هل تريد تحديثها؟]],
[668072]=[[المهمة لم تكتمل، يرجى المحاولة مرة أخرى في وقت لاحق]],
[668073]=[[لقد سرقت هذه المهمة بالفعل، اترك بعض المكافآت للطرف الآخر]],
[668074]=[[هذه المهمة ليس لديها المزيد من الأوقات للسرقة. يرجى العثور على مهام أخرى للسرقة.]],
[668075]=[[الكنز الغامض]],
[668076]=[[يمكنك الحصول على الكثير من المكافآت من خلال التنقيب عن الكنوز، لا تفوتها]],
[668077]=[[إذا قمت بحفر <color=#319f38>{%s1}</color> مرات أكثر، فستجد بالتأكيد صندوق الكنز الأسطوري! بعد استخراج صندوق الكنز الأسطوري، سيتم إعادة ضبط العدد المضمون من المرات.]],
[668078]=[[حفر للكنز]],
[668079]=[[تخطي الرسوم المتحركة]],
[668080]=[[شظايا المبادلة]],
[668081]=[[في كل مرة تقوم فيها بالحفر، عليك أن تستهلك جزءًا واحدًا من خريطة الكنز من كل نوع.]],
[668082]=[[الكنز العادي]],
[668083]=[[كنز نادر]],
[668084]=[[الكنز الأسطوري]],
[668085]=[[سيؤدي فتح صندوق الكنز إلى مكافأتك بشكل عشوائي {%s1} مرة. احتمالية كل مكافأة هي كما يلي:]],
[668086]=[[احتمالية التعدين: <color=#319F38>{%s1}</color>]],
[668087]=[[سوف تحصل]],
[668088]=[[سوف تخسر]],
[668089]=[[الجزء رقم 1]],
[668090]=[[الجزء رقم 2]],
[668091]=[[الجزء رقم 3]],
[668092]=[[الجزء رقم 4]],
[668093]=[[الجزء رقم 5]],
[668094]=[[القطعة رقم 6]],
[668095]=[[القطعة رقم 7]],
[668096]=[[الشروع في التبادل]],
[668097]=[[التبادل الخاص بي]],
[668098]=[[تبادل حليف]],
[668099]=[[الرجاء تحديد العنصر الذي تريد الحصول عليه. بعد الاستبدال، سوف <color=#319438>تحصل</color> على العنصر.]],
[668100]=[[الرجاء تحديد العنصر الذي تريد استبداله. سوف <color=#ff5656>تفقد</color> العنصر بعد الاستبدال.]],
[668101]=[[يملكها: {%s1}]],
[668102]=[[ما إذا كان سيتم استخدام <color=#ed810f>{%s1}</color> وتبادل الحلفاء <color=#ed810f>{%s2}</color>]],
[668103]=[[ابدأ بالتبادل]],
[668104]=[[لقد قمت باستبدال <color=#ed810f>{%s1}</color> و{%s2} بـ <color=#ed810f>{%s3}</color>]],
[668105]=[[خريطة الكنز جزء رقم 1]],
[668106]=[[خريطة الكنز جزء رقم 2]],
[668107]=[[خريطة الكنز جزء رقم 3]],
[668108]=[[خريطة الكنز جزء رقم 4]],
[668109]=[[خريطة الكنز جزء رقم 5]],
[668110]=[[خريطة الكنز جزء رقم 6]],
[668111]=[[خريطة الكنز جزء رقم 7]],
[668112]=[[تبادل السجلات]],
[668113]=[[وقد بدأ التبادل، في انتظار الرد من الحلفاء]],
[668114]=[[إلغاء التبادل]],
[668115]=[[لا يمكن أن تكون أرقام الأجزاء المفقودة والمكتسبة متسقة.]],
[668116]=[[أنت لا تملك الجزء ولا يمكنك استبداله به]],
[668117]=[[أنت لم تنضم إلى التحالف بعد. يرجى الانضمام إلى التحالف للمشاركة في الأنشطة.]],
[668118]=[[ليس لديك تحالف ولا يمكنك مشاركة معلومات التبادل]],
[668119]=[[لقد انتهت صلاحية معلومات التبادل هذه، يرجى العثور على حلفاء آخرين لتبادلها]],
[668120]=[[هل هناك من يريد المبادلة؟]],
[668121]=[[أكملت التبادل معك]],
[668122]=[[بدء تبادل جديد]],
[668123]=[[تبادل ناجح! لقد حصلت على {%s1}]],
[668124]=[[الجزء {%s1}]],
[668125]=[[مكافأة التعدين واحدة]],
[668126]=[[لم يبدأ أي من الحلفاء التبادل بعد]],
[668127]=[[لم تكمل الاختيار ولا يمكنك الاستبدال]],
[668128]=[[1. سيؤدي إكمال مهام الحانة ذات الجودة الأرجوانية وما فوق إلى إنتاج أجزاء من خريطة الكنز بشكل عشوائي. إذا أكملت المهمة بشكل مثالي، فهناك فرصة للحصول على أجزاء إضافية من خريطة الكنز.]],
[668129]=[[2. هناك 7 أجزاء من خريطة الكنز، ويلزم الحصول على قطعة واحدة من كل نوع من الأجزاء لاستخراج الكنز.]],
[668130]=[[3. يمكنك تبادل الأجزاء مع حلفائك للحصول على الأجزاء التي تريدها. بعد بدء عملية التبادل، سيتم خصم الأجزاء التي تريد منحها للطرف الآخر مؤقتًا.]],
[668131]=[[4. عندما تحصل على أجزاء خريطة الكنز من خلال مهام الحانة، فإن احتمالية الحصول على كل جزء تكون متساوية.]],
[668132]=[[5. إذا قمت بجمع المهمة الأرجوانية بشكل مثالي، فلديك فرصة بنسبة 5% للحصول على جزء إضافي من خريطة الكنز المجزأة؛ إذا قمت بجمع المهمة البرتقالية بشكل مثالي، فلديك فرصة بنسبة 15% للحصول على جزء عشوائي إضافي من خريطة الكنز.]],
[668133]=[[6. التنقيب عن الكنوز الأسطورية مضمون. إذا لم يتم استخراج الكنوز الأسطورية لمدة 15 مرة متتالية، فسيتم الحصول على الكنوز الأسطورية بعد التنقيب السادس عشر.]],
[668134]=[[أرسل رسالة شكر للطرف الآخر]],
[668135]=[[{%s1}شكرًا لك على التبادل]],
[668136]=[[أريد استخدام {%s1} واستبدال {%s2}]],
[668137]=[[أنت حاليًا في مملكة أخرى ولا يمكنك أداء مهمة الحانة. يرجى العودة إلى المملكة الأصلية والمحاولة مرة أخرى.]],
[668138]=[[لم تبدأ هذه المهمة بعد، انتقل للتحقق من المهام الأخرى]],
[668139]=[[انتظر، رائحتها مثل الكنز!]],
[668140]=[[أين هو لماذا لم أشمه؟]],
[668141]=[[أما بالنسبة للمكافأة في Acorn Tavern هذه المرة، فمع سنوات خبرتي العديدة في البحث عن الكنوز، لن أخطئ بالتأكيد!]],
[668142]=[[عزيزي الرب، دعنا نذهب لجمع مكافآت الحانة الخاصة بنا!]],
[668143]=[[كنتُ مُحقًا، أليس كذلك؟ هناك بالفعل خريطة كنز!]],
[668144]=[[إذن فلنذهب للبحث عن الكنز!]],
[668145]=[[هذه القطع الصغيرة من خريطة الكنز تُشكّل خريطة كنز كاملة! يا لها من حظ!]],
[668146]=[[دعونا نحفر ونرى ما هي المكافآت التي لدينا!]],
[668151]=[[لقد ساعدك حليف مفيد في الحصول على المكافأة!]],
[668152]='شاكر',
[668153]=[[مثل النجاح]],
[668154]=[[شكرا لمساعدتك!]],
[668155]=[[أنت شخص لطيف جدًا!]],
[668156]=[[شكرا لمساعدتي في جمع المكافآت الخاصة بي!]],
[668157]=[[أنت حليفي الجيد!]],
[668158]=[[لقد ساعدتني حقا كثيرا!]],
[668159]='مثلك!',
[668160]='شكرًا!',
[668161]=[[قرار حكيم!]],
[668162]=[[ممتاز يا رب!]],
[668163]=[[حكمتك لا مثيل لها!]],
[668164]=[[لا توجد مهام لتحديثها حاليًا]],
[669001]='',
[669002]='',
[669003]='',
[669004]='',
[669005]='',
[669006]='',
[669007]='',
[669008]='',
[669009]='',
[669010]='',
[669011]='',
[669012]='',
[669013]='',
[669014]='',
[669015]='',
[669016]='',
[669017]='',
[669018]='',
[669019]='',
[669020]='',
[669021]='',
[669022]='',
[669023]='',
[669024]='',
[669025]='',
[669026]='',
[669027]='',
[669028]='',
[669029]='',
[669030]='',
[669031]='',
[669032]='',
[669033]='',
[669034]='',
[669035]='',
[669036]='',
[669037]='',
[669038]='',
[669039]='',
[669040]='',
[669041]='',
[669042]=[[<color=#319f38>موضوع المبارزة اليوم:</color> التحضير الشامل للحرب، وكسب النقاط بشكل أساسي من خلال تدريب الجنود وإكمال مهام الاستخبارات]],
[669043]='',
[669044]='',
[669045]='',
[669046]='',
[669047]='',
[669048]='',
[669049]='',
[669050]='',
[669051]='',
[669052]='',
[669053]='',
[669054]='',
[669055]='',
[670001]='',
[670002]='',
[670003]='',
[670004]='',
[670005]='',
[670006]='',
[670007]='',
[670008]='',
[670009]='',
[670010]='',
[670011]='',
[670012]='',
[670013]='',
[670014]='',
[670015]='',
[670016]='',
[670017]='',
[670018]='',
[670019]='',
[670020]='',
[670021]='',
[670022]='',
[670023]='',
[670024]='',
[670025]='',
[670026]='',
[670027]='',
[670028]='',
[670029]='',
[670030]='',
[670031]='',
[670032]='',
[670033]='عربي',
[670034]='',
[670035]='',
[670036]=[[الرجاء إدخال المحتوى]],
[670037]=[[لا يمكن عرض هذه الرسالة إلا بعد تنزيل أحدث حزمة ألعاب من متجر التطبيقات]],
[671001]='',
[671002]='',
[671003]='',
[671004]='',
[671005]='',
[671006]='',
[671007]='',
[671008]='',
[671009]='',
[671010]='',
[671011]='',
[671012]='',
[671013]='',
[671014]='',
[671015]='',
[671016]='',
[671017]='',
[671018]='',
[671019]='',
[671020]='',
[671021]='',
[671022]='',
[671023]='',
[671024]='',
[671025]='',
[671026]='',
[671027]='',
[671028]='',
[671029]='',
[671030]='',
[671031]='',
[671032]='',
[671033]='',
[671034]='',
[671035]='',
[671036]='',
[671037]='',
[671038]='',
[671039]='',
[671040]='',
[671041]='',
[671042]='',
[671043]='',
[671044]='',
[671045]='',
[671046]='',
[671047]='',
[671048]='',
[671049]='',
[671050]='',
[671051]='',
[671052]='',
[671053]='',
[671054]='',
[671055]='',
[671056]='',
[671057]='',
[671058]='',
[671059]='',
[671060]='',
[671061]='',
[671062]='',
[671063]='',
[671064]='',
[671065]='',
[671066]='',
[671067]='',
[671068]='',
[671069]='',
[671070]='',
[671071]='',
[671072]='',
[671073]='',
[671074]='',
[671075]='',
[671076]='',
[671077]='',
[671078]='',
[671079]='',
[671080]='',
[671081]='',
[671082]='',
[671083]='',
[671084]='',
[671085]='',
[671086]='',
[671087]='',
[671088]='',
[671089]='',
[671090]='',
[671091]='',
[671092]='',
[671093]='',
[671094]='',
[671095]='',
[671096]='',
[671097]='',
[671098]='',
[671099]='',
[671100]='',
[671101]='',
[671102]='',
[671103]='',
[671104]='',
[671105]='',
[671106]='',
[671107]='',
[671108]='',
[671109]='',
[671110]='',
[671111]='',
[671112]='',
[671113]='',
[671114]='',
[671115]='',
[671116]='',
[671117]='',
[671118]=[[بريد الملك]],
[671119]=[[عنوان البريد الملكي]],
[671120]=[[محتوى البريد الإلكتروني]],
[671121]='يرسل',
[671122]=[[وقت دوران الطابور]],
[671123]=[[الإعدادات التلقائية]],
[671124]=[[طلب رئاسي تمت الموافقة عليه تلقائيًا]],
[671125]=[[تمت الموافقة عليها تلقائيًا من قبل المسؤولين]],
[671126]=[[لا فاتح]],
[671127]=[[الفاتح يتولى منصبه]],
[671128]='',
[671129]='',
[672001]='',
[672002]='',
[672003]='',
[672004]='',
[672005]='',
[672006]='',
[672007]='',
[672008]='',
[672009]='',
[672010]='',
[672011]='',
[672012]='',
[672013]='',
[672014]='',
[672015]='',
[672016]='',
[672017]='',
[672018]='',
[672019]='',
[672020]='',
[672021]='',
[672022]='',
[672023]='',
[672024]='',
[672025]=[[أنت لا تستوفي متطلبات هذه الشارة]],
[672026]=[[لقد استوفيت متطلبات هذه الشارة]],
[672027]='',
[672028]='',
[672029]='',
[672030]='',
[672031]='',
[672032]='',
[672033]='',
[672034]='',
[672035]='',
[672036]='',
[672037]='',
[672038]='',
[672039]='',
[672040]='',
[672041]='',
[672042]='',
[672043]=[[الضغط لفترة طويلة للتبرع بشكل مستمر هو أكثر ملاءمة]],
[673001]='',
[673002]='',
[673003]='',
[673004]='',
[673005]='',
[673006]='',
[673007]='',
[673008]='',
[673009]='',
[673010]='',
[673011]='',
[673012]='',
[673013]='',
[673014]='',
[673015]='',
[673016]='',
[673017]=[[وقت إضافي!]],
[673018]=[[مكافأة إضافية]],
[673019]=[[احصل على جميع المكافآت]],
[674001]='',
[674101]='',
[674102]='',
[674202]='',
[674203]='',
[675001]='',
[675002]='',
[675003]=[[لم يتبق لديك عدد كافي من العناصر لإرسالها.]],
[675004]='',
[675005]='',
[675006]='',
[675007]='',
[675008]='',
[675009]='',
[675010]='',
[675011]='',
[675012]='',
[675013]='',
[675014]='',
[675015]='',
[675016]='',
[675017]='',
[675018]='',
[675019]='',
[675020]='',
[675021]='',
[675022]='',
[675023]='',
[675024]='',
[675025]=[[تم تعيين الكابتن الحالي من قبل عضو آخر، وفشل التعيين!]],
[675026]=[[الكابتن الحالي لم يعد في التحالف وفشل التعيين!]],
[675027]=[[فقط R4/R5 يمكنه تعيين قائد، وقد فشلت العملية!]],
[675028]='',
[675029]='',
[675030]='',
[675031]='',
[675032]='',
[675033]='',
[675034]='',
[675035]='',
[675036]='',
[675037]='',
[675038]='',
[675039]='',
[675040]='',
[675041]='',
[675042]='',
[675043]='',
[675044]='',
[675045]='',
[675046]='',
[675047]='',
[675048]='',
[675049]='',
[675050]='',
[675051]='',
[675052]='',
[675053]='',
[675054]='',
[675055]='',
[675056]='',
[675057]='',
[675058]='',
[675059]='',
[675060]='',
[675061]='',
[675062]='',
[675063]='',
[675064]='',
[675065]='',
[675066]='',
[675067]='',
[675068]='',
[675069]='',
[675070]='',
[675071]='',
[675072]='',
[675073]='',
[675074]='',
[675075]='',
[675076]='',
[675077]='',
[675078]='',
[675079]='',
[675080]='',
[675081]='',
[675082]='',
[675083]='',
[675084]='',
[675085]='',
[675086]='',
[675087]='',
[675088]='',
[675089]='',
[675090]='',
[675091]='',
[675092]='',
[675093]='',
[675094]='',
[675095]='',
[675096]='',
[675097]='',
[675098]='',
[675099]='',
[675100]='',
[675101]='',
[675102]='',
[675103]='',
[675104]='',
[675105]='',
[675106]='',
[675107]='',
[675108]=[[نجحت سفينة الفضاء {%s1} في مقاومة هجوم {%s2}!]],
[675109]=[[فشلت سفينة الفضاء {%s1} في مقاومة هجوم {%s2}!]],
[675110]='',
[675111]='',
[675112]='',
[675113]='',
[675114]='',
[675115]=[[سفينة فضاء عادية]],
[675116]=[[سفينة الفضاء الذهبية]],
[675117]=[[أنت حاليا الكابتن، لا حاجة لتبديل المراكز]],
[675118]=[[قواعد تشغيل سفينة الفضاء]],
[675119]=[[[شروط المشاركة]
1. عدد أعضاء التحالف أكبر من أو يساوي 20.
2. الأعضاء الذين فتحوا مبنى المنصة وكانوا في التحالف لأكثر من 24 ساعة.
3. يمكن لكل تحالف إرسال سفينة فضاء عادية واحدة وسفينتين فضائيتين ذهبيتين كل يوم. ملاحظة خاصة: فقط أفضل 20 تحالفًا في هذا الخادم يمكنهم استقبال سفن الفضاء العادية كل يوم.
[معلومات سفينة الفضاء]
1. سفينة الفضاء العادية: 0:00 بتوقيت الخادم كل يوم، يمكن للتحالفات المؤهلة ذلك
للحصول على سفينة فضاء عادية، يجب على R4 أو R5 تعيين قائد لها، وستدخل سفينة الفضاء في حالة الاستعداد.
2. سفينة الفضاء الذهبية: عندما لا تكون هناك سفينة فضاء قيد الإعداد على الطريق، يمكن للورد استدعاء سفينة فضاء ذهبية ويصبح القبطان تلقائيًا.
[مرحلة إعداد سفينة الفضاء]
1. تستمر مرحلة التحضير لمدة 4 ساعات. بعد النهاية، ستبحر المركبة الفضائية تلقائيًا إلى مدن مختلفة لبدء التجارة.
2. الكابتن: يمكنك تعيين مرافقة لسفينة الفضاء وتحديث الحمولة الموجودة على سفينة الفضاء. بعد التحديث 5 مرات، ستكون هناك مفاجآت إضافية!
3. الركاب: يمكنك "الانتظار" أمام المقصورة التي ترغب في اصطحابها وتغييرها في أي وقت. بعد انتهاء مرحلة الإعداد، لم يعد الانتظار ممكنا وسيقوم النظام بشكل عشوائي باختيار 5 ركاب من قائمة الانتظار في كل مقصورة للدخول إلى المقصورة.
[حالة الملاحة لسفينة الفضاء]
1. قد تتعرض سفينة الفضاء للهجوم من قبل قادة وأمراء آخرين أثناء إبحارها. بعد الهجوم الناجح، سيتم نهب 3 شبكات عشوائية من البضائع.
2. يمكن نهب كل سفينة حتى 3 مرات.
[مكافأة سفينة الفضاء]
1. الكابتن: احصل على مكافآت مقابل جميع البضائع المتبقية في سفينة الفضاء والمقصورة.
2. الركاب: احصل على مكافآت مقابل الحمولة المتبقية في المقصورة]],
[675120]=[[قم بإلغاء تحديد المربع لتحديد الأعضاء]],
[675121]=[[لقد غادرت المركبة الفضائية]],
[675122]=[[المركبة الفضائية على وشك المغادرة]],
[675123]=[[لقد تم إرسال خالص شكرك إلى %s1]],
[675124]=[[يمكن نهب كل سفينة فضائية بحد أقصى {%s1} مرة]],
[675125]=[[شبكة المكافآت الذهبية لكابينة السفينة الفضائية الذهبية {%s1}]],
[675126]=[[فتحات المكافآت المتبقية في مقصورة السفينة الفضائية الذهبية {%s1}]],
[676001]=[[صراع الممالك]],
[676002]=[[معلومات المباراة]],
[676003]=[[معركة هذا الاسبوع]],
[676004]=[[معركة المدينة الملكية]],
[676005]=[[الأسبوع {%s1}: {%s2}]],
[676006]=[[ربع النهائي]],
[676007]=[[نصف النهائي]],
[676008]='نهائيات',
[676009]='التجميع',
[676010]='سِجِلّ',
[676011]='جائزة',
[676012]='معاينة',
[676013]=[[1. سيشارك في هذا الحدث ما مجموعه {%s1} مملكة، وستستمر المعركة لمدة {%s2} أسبوعًا؛

2. سيحصل المشاركون على مكافآت سخية.]],
[676014]=[[سجلات الحرب]],
[676015]='تصنيف',
[676016]='المملكة',
[676017]=[[الأسبوع الأول]],
[676018]=[[الأسبوع الثاني]],
[676019]=[[الأسبوع الثالث]],
[676020]=[[انقر على مساحة فارغة لإغلاقها]],
[676021]=[[معاينة المكافآت]],
[676022]=[[مكافأة للمركز {%s1}]],
[676023]=[[جائزة رئاسية]],
[676024]=[[مكافآت المملكة]],
[676025]=[[أقوى مكافآت التحالف]],
[676026]=[[مكافأة المعركة (مرة واحدة على الأقل)]],
[676027]=[[اختر مملكة]],
[676028]='يفحص',
[676029]=[[التصنيف بدون ترتيب معين]],
[676030]=[[مساهمة المملكة MVP]],
[676031]='مثله',
[676032]=[[مرحلة النضال من أجل حق الغزو]],
[676033]=[[مقارنة النقاط]],
[676034]=[[الفائز في مباراة التحالف]],
[676035]=[[أفضل لاعب في مباراة الدوري]],
[676036]=[[فوز مملكة الزعيم العالمي]],
[676037]=[[أول ضرر شخصي لـ BOSS في العالم]],
[676038]=[[فوز عاصفة الصحراء]],
[676039]=[[أفضل لاعب في معركة عاصفة الصحراء]],
[676040]=[[سرقة عربة المملكة الأخرى]],
[676041]=[[سباق التسلح رقم 1]],
[676042]=[[أفضل لاعب]],
[676043]='معاينة',
[676044]=[[١. من الاثنين إلى الجمعة، تُحصَى عناصر المنافسة لكلا المملكتين في معركة المبارزة للحصول على النقاط؛
٢. تُجرى التسوية يوم الجمعة من كل أسبوع، وتصبح المملكة الحاصلة على أعلى مجموع نقاط هي الطرف المهاجم؛
٣. بعد فوز المملكة المهاجمة، يصبح ملك المملكة هو الفاتح.]],
[676045]=[[حالة المعركة]],
[676046]=[[لم تبدأ بعد]],
[676047]=[[حقوق الغزو]],
[676048]=[[مرحلة المنافسة: من الاثنين إلى الجمعة هي مرحلة منافسة النقاط، والمملكة التي تحصل على أعلى نقاط تحصل على حق الغزو.

الاستيلاء على المدينة الملكية: يمكن للطرف الغازي التوجه إلى مملكة العدو يوم السبت والاستيلاء على البرلمان.

دفاع المدينة الملكية: الطرف صاحب أقل النقاط هو المدافع، وعلى المدافع الدفاع عن ملكيته للبرلمان يوم السبت.]],
[676049]=[[اكسب نقاط المملكة عن طريق إكمال الأحداث التالية:]],
[676050]=[[نقاط المكافأة]],
[676051]='جزء',
[676052]=[[ترتيب النقاط]],
[676053]=[[تصنيف الدوري]],
[676054]=[[التصنيف الشخصي]],
[676055]='تصنيف',
[676056]='اسم',
[676057]=[[نقاط المساهمة]],
[676058]=[[نصيحة: يتم تحديث بيانات القائمة مع التأخير]],
[676059]=[[المملكة {%s1}]],
[676060]=[[لم تبدأ بعد]],
[676061]=[[العد التنازلي لبدء المعركة]],
[676062]=[[العد التنازلي لنهاية المعركة]],
[676063]=[[العد التنازلي لتسوية المكافآت]],
[676064]=[[اذهب للهجوم]],
[676065]=[[يمكنك نقل المدينة مرة أخرى بعد {%s1}]],
[676066]=[[مكافآت سلسلة انتصارات المملكة]],
[676067]='يستلم',
[676068]=[[النقاط الشخصية يجب أن تصل إلى {%s1}]],
[676069]=[[لا يمكن الحصول على النقاط إلا بعد {%s1}]],
[676070]=[[أكمل المهام التالية لكسب النقاط]],
[676071]='جائزة',
[676072]='???',
[676073]=[[القيمة {%s1}]],
[676074]=[[تعتمد مكافآت الترتيب على المملكتين.]],
[676075]=[[ترتيب النقاط]],
[676076]='تصنيف',
[676077]='جائزة',
[676078]='تصنيف',
[676079]=[[معاينة المكافآت]],
[676080]='رب',
[676081]='أساسي',
[676082]='مملكتي',
[676083]='تحالفي',
[676084]='الفاتح',
[676085]=[[قائد عسكري]],
[676086]=[[القائد السياسي]],
[676087]='التصنيفات',
[676088]=[[مكافأة الفاتح]],
[676089]=[[مواجهة الممالك - مسابقة الممالك]],
[676090]='يذهب',
[676091]='جائزة',
[676092]=[[افتح بعد {%s1}]],
[676093]=[[صراع الممالك - العرض الدعائي]],
[676094]='المعتدي',
[676095]='المدافع',
[676096]=[[مواجهة المملكة - معركة المدينة الملكية]],
[676097]=[[ساخن في التقدم]],
[676098]=[[القتال من أجل المدينة الملكية]],
[676099]=[[الحرس الشرقي]],
[676100]=[[الحرس الجنوبي]],
[676101]=[[الحرس الغربي]],
[676102]=[[الحرس الشمالي]],
[676103]='رب',
[676104]=[[مكافآت مملكة النصر]],
[676105]=[[مكافآت المملكة الفاشلة]],
[676106]=[[صراع الممالك - إعادة إصدار مكافأة التقدم]],
[676107]=[[لديك مكافآت تقدم لم تستلمها في فعالية مبارزة المملكة. أرسلناها إليك عبر البريد الإلكتروني. يُرجى التحقق!]],
[676108]=[[مواجهة المملكة - مكافأة الملك]],
[676109]=[[تهانينا لفوز مملكتك بالمركز {%s1} في فعالية مواجهة الممالك. يُرجى الاطلاع على المكافآت التالية الحصرية لملكك!]],
[676110]=[[مكافآت المشاركة في مواجهة المملكة]],
[676111]=[[تهانينا لفوز مملكتك بالمركز {%s1} في فعالية مواجهة الممالك. إليك مكافآت مشاركتك، يُرجى الاطلاع عليها!]],
[676112]=[[صراع الممالك - وداعا]],
[676113]=[[استراحت مملكتك في مواجهة الممالك الأخيرة. لديك مكافآت صندوق كنز لم تستلمها. يُرجى التحقق!]],
[676114]=[[مكافآت سلسلة انتصارات Kingdom Showdown]],
[676115]=[[لديك مكافآت سلسلة انتصارات غير مستلمة في لعبة Kingdom Duel. تم إرسالها إليك عبر البريد الإلكتروني. يُرجى التحقق!]],
[676116]=[[مواجهة المملكة - مكافآت التصنيف الشخصي]],
[676117]=[[تهانينا لفوزك بالمركز {%s1} في مرحلة منافسة المدينة الملكية - مواجهة المملكة. إليك مكافأتك، تفضل بزيارتها!]],
[676118]=[[مرحلة المعاينة]],
[676119]=[[ابدأ النشاط بعد {%s1}]],
[676120]=[[العد التنازلي للمعركة يبدأ]],
[676121]='انتهت',
[676122]=[[بدأت المعركة:]],
[676123]=[[معركة المدينة الملكية:]],
[676124]=[[وقت تسوية المكافأة:]],
[676125]=[[تم الإعجاب بنجاح]],
[606126]='يوضح',
[676127]=[[مقدمة المسابقة]],
[676128]=[[Kingdom Showdown هي منافسة 1 ضد 1 بين الممالك، حيث يتم تحديد الفائز من خلال الاستيلاء على مدينة المملكة الأخرى.]],
[676129]=[[يتم تجميع الممالك المتعددة في نفس المجموعة والتنافس لمدة 2-3 أسابيع حتى يتم تحديد المملكة البطلة!]],
[676130]=[[الفوز بالبطولة ليس بالأمر الهيّن، إذ يتطلّب من الرئيس حشد كل القوى الممكنة في المملكة!]],
[676131]=[[قواعد المنافسة]],
[676132]=[[خلال فترة توقف الموسم: سيتم اختيار الممالك في كل مجموعة عشوائيًا لتحديد جدول المباريات. ستتأهل المملكة الفائزة إلى الجولة التالية، وستُقابل الفائزين الآخرين حتى يتم تحديد البطل.]],
[676133]=[[خلال الموسم: التوفيق بين اللاعبين ضمن نفس مجموعة الموسم، بناءً على تصنيف المملكة في الموسم]],
[676134]=[[قواعد المطابقة: الفائزون يتنافسون مع الفائزين، والخاسرون يتنافسون مع الخاسرين]],
[676135]=[[الفوز: الفوز من خلال احتلال عاصمة مملكة محلية]],
[676136]=[[مرحلة المنافسة]],
[676137]=[[من الإثنين إلى الجمعة: مرحلة المنافسة على نقاط المملكة، قم بزيادة نقاط مملكتك من خلال الفوز بأنشطة مختلفة؛]],
[676138]=[[الغزاة والمدافعون: يتم تحديد وقت اللعبة في نهاية يوم الجمعة؛]],
[676139]=[[إن الحزب الذي لديه درجة مملكة أعلى هو الحزب الغازي؛ والحزب الذي لديه درجة مملكة أقل هو الحزب المدافع؛]],
[676140]=[[الدفاع عن الكابيتول: تبدأ اللعبة يوم السبت. يمكن للفريق الغازي نقل مدينته إلى مملكة الفريق المدافع، ومهاجمة مدينته الملكية، والقتال من أجل السيطرة النهائية.]],
[676141]=[[الدفاع عن الكونجرس]],
[676142]=[[على عكس معركة البرلمان داخل المملكة، عندما يقاتل أمراء ينتمون إلى نفس المملكة من أجل المباني المحايدة، فإن قواتك لن تهاجم قوات أمراء نفس المملكة، بل ستكون قوات صديقة لبعضها البعض (باستثناء مهاجمة قلعة الأمراء)]],
[676143]=[[بالنسبة لأمراء المملكة نفسها، من المهم جدًا العمل معًا!]],
[676144]=[[المركز الأول في الساحة]],
[676145]=[[اقتل جنديًا من المستوى الأول من مملكة العدو في المدينة الملكية باستخدام الحارس]],
[676146]=[[اقتل جنديًا من المستوى 2 من مملكة العدو في المدينة الملكية باستخدام الحارس]],
[676147]=[[اقتل جنديًا من المستوى 3 من مملكة العدو في المدينة الملكية باستخدام الحارس]],
[676148]=[[اقتل جنديًا من المستوى الرابع من مملكة العدو في المدينة الملكية باستخدام الحارس]],
[676149]=[[اقتل جنديًا من المستوى الخامس من مملكة العدو في المدينة الملكية باستخدام الحارس]],
[676150]=[[اقتل جنديًا من المستوى 6 من مملكة العدو في المدينة الملكية باستخدام الحارس]],
[676151]=[[اقتل جنديًا من المستوى 7 من مملكة العدو في المدينة الملكية باستخدام الحارس]],
[676152]=[[اقتل جنديًا من المستوى 8 من مملكة العدو في المدينة الملكية باستخدام الحارس]],
[676153]=[[اقتل جنديًا من المستوى 9 من مملكة العدو في المدينة الملكية باستخدام الحارس]],
[676154]=[[اقتل جنديًا من المستوى 10 من مملكة العدو في المدينة الملكية باستخدام الحارس]],
[676155]=[[في منطقة الأراضي الملوثة، اقتل جنديًا من المستوى 1 من مملكة العدو]],
[676156]=[[في منطقة الأراضي الملوثة، اقتل جنديًا من المستوى 2 من مملكة العدو]],
[676157]=[[في منطقة الأرض الملوثة، اقتل جنديًا من المستوى 3 من مملكة العدو]],
[676158]=[[في منطقة الأرض الملوثة، اقتل جنديًا من المستوى الرابع من مملكة العدو]],
[676159]=[[في منطقة الأرض الملوثة، اقتل جنديًا من المستوى 5 من مملكة العدو]],
[676160]=[[في منطقة الأرض الملوثة، اقتل جنديًا من المستوى 6 من مملكة العدو]],
[676161]=[[في منطقة الأراضي الملوثة، اقتل جنديًا من المستوى 7 من مملكة العدو]],
[676162]=[[في منطقة الأراضي الملوثة، اقتل جنديًا من المستوى 8 من مملكة العدو]],
[676163]=[[في منطقة الأرض الملوثة، اقتل جنديًا من المستوى 9 من مملكة العدو]],
[676164]=[[في منطقة الأراضي الملوثة، اقتل جنديًا من المستوى 10 من مملكة العدو]],
[676165]=[[اقتل جنديًا من المستوى الأول من مملكة معادية]],
[676166]=[[اقتل جنديًا من المستوى 2 من مملكة معادية]],
[676167]=[[اقتل جنديًا من المستوى 3 من مملكة معادية]],
[676168]=[[اقتل جنديًا من المستوى الرابع من مملكة معادية]],
[676169]=[[اقتل جنديًا من المستوى الخامس من مملكة معادية]],
[676170]=[[اقتل جنديًا من المستوى 6 من مملكة معادية]],
[676171]=[[اقتل جنديًا من المستوى 7 من مملكة معادية]],
[676172]=[[اقتل جنديًا من المستوى 8 من مملكة معادية]],
[676173]=[[اقتل جنديًا من المستوى 9 من مملكة معادية]],
[676174]=[[اقتل جنديًا من المستوى 10 من مملكة معادية]],
[676175]=[[حارب مع سيد مملكة العدو في الأرض الملوثة، واقتل جنديًا واحدًا من المستوى 1]],
[676176]=[[قتال مع سيد مملكة العدو في الأرض الملوثة، وقتل جندي واحد من المستوى 2]],
[676177]=[[في القتال مع سيد مملكة العدو في الأرض الملوثة، مات جندي واحد من المستوى 3]],
[676178]=[[في القتال مع سيد مملكة العدو في الأرض الملوثة، قُتل جندي واحد من المستوى الرابع]],
[676179]=[[حارب مع سيد مملكة العدو في الأرض الملوثة، وقُتل جندي واحد من المستوى الخامس]],
[676180]=[[في القتال مع سيد مملكة العدو في الأرض الملوثة، قُتل جندي واحد من المستوى 6]],
[676181]=[[في القتال مع سيد مملكة العدو في الأرض الملوثة، قُتل جندي واحد من المستوى 7]],
[676182]=[[في القتال مع سيد المملكة المعادية في الأرض الملوثة، قُتل جندي واحد من المستوى 8]],
[676183]=[[في القتال مع سيد مملكة العدو في الأرض الملوثة، مات جندي واحد من المستوى 9]],
[676184]=[[حارب مع سيد مملكة العدو في الأرض الملوثة، واقتل جنديًا واحدًا من المستوى 10]],
[676185]=[[تدمير قاعدة سيد مملكة العدو بنقطة دفاع واحدة]],
[676186]=[[هدم قاعدة سيد مملكة العدو]],
[676187]=[[عندما تعود القوات المتمركزة في الوصي أو المدينة الملكية، سيتم منح نقاط كل ثانية بناءً على طول الوقت الذي تم تواجدهم فيه.]],
[676188]=[[عندما تتمركز القوات عند حارس، في كل مرة يقتل الحارس جنديًا من المستوى 1]],
[676189]=[[عندما تتمركز القوات عند حارس، في كل مرة يقتل الحارس جنديًا من المستوى 2]],
[676190]=[[عندما تتمركز القوات عند الحارس، في كل مرة يقتل الحارس جنديًا من المستوى 3]],
[676191]=[[عندما تتمركز القوات عند الحارس، في كل مرة يقتل الحارس جنديًا من المستوى 4]],
[676192]=[[عندما تتمركز القوات عند الحارس، في كل مرة يقتل الحارس جنديًا من المستوى 5]],
[676193]=[[عندما تتمركز القوات عند الحارس، في كل مرة يقتل الحارس جنديًا من المستوى 6]],
[676194]=[[عندما تتمركز القوات عند الحارس، في كل مرة يقتل الحارس جنديًا من المستوى 7]],
[676195]=[[عندما تتمركز القوات عند الحارس، في كل مرة يقتل الحارس جنديًا من المستوى 8]],
[676196]=[[عندما تتمركز القوات عند الحارس، في كل مرة يقتل الحارس جنديًا من المستوى 9]],
[676197]=[[عندما تتمركز القوات عند الحارس، في كل مرة يقتل الحارس جنديًا من المستوى 10]],
[676198]=[[مكافأة التقدم في النقاط الشخصية 1]],
[676199]=[[مكافأة التقدم بالنقاط الشخصية 2]],
[676200]=[[مكافأة التقدم بالنقاط الشخصية 3]],
[676201]=[[مكافأة التقدم بالنقاط الشخصية 4]],
[676202]=[[مكافأة التقدم في النقاط الشخصية 5]],
[676203]=[[مكافأة التقدم بالنقاط الشخصية 6]],
[676204]=[[مكافأة التقدم بالنقاط الشخصية 7]],
[676205]=[[مكافأة التقدم بالنقاط الشخصية 8]],
[676206]=[[مكافأة التقدم بالنقاط الشخصية 9]],
[676207]=[[لا توجد معلومات عن المجموعة]],
[676208]='يوضح',
[676209]=[[تمثال ضخم يحرس المدينة الملكية. بمجرد الاستيلاء عليه، يُمكنه إلحاق ضرر كبير بها.]],
[676210]=[[مفتوح عند القتال من أجل المدينة الملكية]],
[676211]=[[بعد احتلال المدينة الملكية، سيهاجم المدافع المدينة الملكية كل ١٠ ثوانٍ، مهاجمًا جميع قوات العدو المتمركزة فيها. كل هجوم يُسبب خسارة ٢٪ من الحد الأقصى لعدد الجنود. لكل ٥ فرق مدافعة متمركزة في المدينة، سيتم تقليل فاصل هجوم المدافع بمقدار ثانية واحدة (حتى ٤ ثوانٍ).]],
[676212]=[[أصبح الآن كسب النقاط مفتوحًا]],
[676213]=[[{%s1} مشغول]],
[676214]=[[انتقل إلى الدفاع]],
[676215]=[[أقوى مكافآت التحالف]],
[676216]=[[منذ أن خسرت مملكتك في صراع الممالك، أصبح ملك مملكة {%s1} هو الفاتح لمملكتك.]],
[676217]=[[تهانينا لفوز مملكتك بالمركز {%s1} في فعالية مواجهة الممالك. إليك مكافآت أقوى تحالف، يُرجى الاطلاع عليها!]],
[676218]=[[{%s1} مشغول]],
[677001]=[[معسكر حصار [{%s1}] ]],
[677002]=[[أرسل قوات للمساعدة. بعد بدء الحصار، ستتوجه القوات تلقائيًا إلى هناك.]],
[677003]=[[معسكر حصار كبير]],
[677004]=[[عدد القوات: {%s1}]],
[677005]=[[بعد نجاح البناء، يمكنك إرسال القوات إلى الحامية، وبعد بدء الحصار، ستذهب القوات تلقائيًا لمهاجمة المدينة!]],
[677006]=[[التحالف على وشك البدء بالحصار]],
[677007]=[[التحالف بدأ الحصار]],
[700001]='',
[700002]='',
[700003]='',
[700004]='',
[700005]='',
[700006]='',
[700007]='',
[700008]='',
[700009]='',
[700010]='',
[700011]='',
[710001]='',
[710002]='',
[710003]='',
[710004]='',
[710005]='',
[710101]='',
[710102]='',
[710103]='',
[710104]=[[ما يُخيف أكثر من القوة هو قلب الإنسان الذي لا يُمكن التنبؤ بتصرفاته!
——لونغ شا

الثقة والقوة، هذا هو الانطباع الأول الذي يتركه لونغ شا في قلوب الجميع.
في الواقع، عندما كان لونغ شا طفلاً، لم يكن قوياً كما هو الآن، بل كان نحيلاً للغاية، مما جعله غريباً عن عائلة التنين الأزرق المجنح، المشهورة بـ"صلابتها وقسوتها". لذلك، كان لونغ شا الشاب يُسخر منه كثيراً.
لكن لونغ شا مختلف تماماً عن رفاقه من أبناء القبيلة. فهو بسيط القلب، متفائل وإيجابي تجاه كل شيء. حتى عند مواجهة سخرية رفاقه، كان لونغ شا يرد دائماً بابتسامة مشرقة، مما أكسبه صداقات كثيرة.
نقطة التحول الحقيقية في حياة لونغ شا يجب أن تكون يوم لقائه بقاتل التنانين، ويوم اندلاع "حرب قاتل التنانين".
قاتل التنانين - العدو الطبيعي لجميع التنانين! إنه أيضاً اسم لن يُمحى من ذاكرة لونغ شا! كان شتاء كوتشيوي قارس البرودة، حتى أن الحيوانات البرية العادية يصعب رؤيتها. وكما في السنوات السابقة، بدأ لونغشا، والد زعيم القبيلة، بالهجرة جنوبًا مع القبيلة. ونتيجةً لذلك، وقعوا في كمين في منتصف الطريق. وتسببت المعركة الشرسة في حريق غابة. هرب أفراد القبيلة مذعورين، لكن قاتل التنانين الذي تعرض لكمين طويل لم يُفوّت هذه الفرصة السانحة. ولحماية أفراد القبيلة، بقي والد لونغشا ليقاتل قاتل التنانين وحده، لكنه سقط في النهاية إلى الأبد.

واصل أفراد القبيلة الناجون الهجرة جنوبًا. عاد لونغشا الشاب إلى الغابة العميقة عندما لم يكن أفراد القبيلة مستعدين، لكن ليلًا مظلمًا أتى بعد سماعه الضجيج. منذ ذلك الحين، تغيرت حياة لونغشا تمامًا.]],
[710105]=[[لم يتردد لونغ شا في الحديث مع الآخرين عن أيام أسره على يد آن يي. بنبرته الهادئة، بدا وكأن المعاناة لا تستحق الذكر.

نشأ لونغ شا الصغير تحت رعاية آن يي، لكن رحلة نموه لم تكن بتلك الروعة. استخدم آن يي أساليب مختلفة لتدمير إرادة لونغ شا حتى أصبح لونغ شا دميةً تُطيع الأوامر تمامًا. لفترة طويلة، كان معنى وجود لونغ شا هو القتال تحت تلاعب آن يي، بغض النظر عن الصواب والخطأ. بالطبع، كان هذا الماضي مليئًا بالعنف والدماء.

حتى التقى آن يي بالشخص الذي يحمل دم التنين مثله.

خلال إحدى المهمات، أصيب لونغ شا بجروح خطيرة. في حالة غيبوبة، سارت نحوه امرأة غريبة ببطء. تذكر، لكن الألم الشديد والتعب المستمر جعلاه يفقد وعيه بسرعة.

عندما استيقظ، كان الجرح في جسده قد عولج. على الرغم من أن طريقة الضمادة بدت بدائية للغاية، إلا أن المرأة التي تحمل عظام الحيوانات أمامه لم تبدُ عدائية. "لقد استيقظتَ، رائع، هل تعلم كم وزنك؟!" قالت المرأة وهي ترى لونغ شا يستيقظ.
"..."
"أنت من مواليد التنين؟ وأنا أيضًا من مواليد التنين." بعد أن قالت ذلك، استدارت ولوّحت بذيل التنين خلفها للونغشا.
"بف~"
وكأنها اعتادت على ردة فعله، لم تغضب المرأة، بل ابتسمت ابتسامة عريضة عندما رأته يتحسن.
هذا الشعور، مألوف!
حاول لونغشا جاهدًا استيعاب هذا الشعور المألوف، كما لو كان قد شعر بنفس شعورها في صغره.
"أين قومك؟" رفعت عينيها الصافيتين وسألت بهدوء.
في قبيلة الأورك، لطالما اعتادوا العيش في جماعات. عندما رأت فتاة التنين، وهي أيضًا من الأورك، امتلأت بالفضول.
"قومي؟" غرق لونغشا تدريجيًا في تفكير عميق. بدا أن شيئًا ما قد حجب جزءًا من الذاكرة. أراد أن يلتقطه، لكنه لم يستطع. ازداد الصداع حدةً. بدا أن فتاة التنين قد اكتشفت خطبًا ما فيه. ضربته برمح عظمي حاسم، فأغمي على لونغشا.

بعد بضعة أشهر من التفاهم، لم يعد لونغ شا يكترث لهجوم لونغ نو المباغت. عامل كل منهما الآخر كأخ وأخت، وتعمقت مشاعرهما. على الرغم من أن لونغ شا كان غالبًا ما يبدو عابسًا، إلا أن لونغ نو كان يعلم أن قلبه دافئ رغم مظهره البارد، وشعر لونغ شا أيضًا أن قلبه البارد بدأ يشعر بالقلق.]],
[710106]=[[في البداية، لم يُخبر لونغشا لونغنف بأنه يعمل مع دارك نايت، ربما خوفًا من تجاهلها له، أو ربما خوفًا من أن يُؤذيها كشف هويته. في قلب لونغشا، ثمة دائمًا مكانٌ لا يتوافق مع لونغنف، يُدعى "الحرية".

قالت لونغنف إنها أرادت رؤية العالم الخارجي، فغادرت مسقط رأسها سرًا. بعد مغادرتها، أدركت أن العالم واسعٌ جدًا، فهناك قبائل تعيش على الماء والعشب، وممالك باردة قاسية، وأشجار عتيقة ناطقة في الغابة، وسلاسل من الزعرور المُسكر في الشرق... لم تسمع لونغشا بهذه الأشياء من قبل. في أيام دارك نايت، بالإضافة إلى مهمات الحياة والموت، هناك جدرانٌ عالية باردة في الليل. ما دفع لونغشا حقًا للتحرر من سطوة الليل المظلم لم يكن رغبته في الحرية، بل قالت فتاة التنين: "مسقط رأسي يُدعى أرض أنفاس التنين. تقع في شمال غرب القارة البعيدة. ويُطلق عليها الناس أيضًا اسم "أرض الغروب". عند غروب الشمس من كل عام، في يوم الاعتدال الخريفي، يجتمع أحفاد سلالة التنين على المنحدرات العالية المطلة على البحر لينشروا أجنحتهم، منتظرين رد أسلافهم... انتظروا الفرصة، سآخذكم لرؤيتها؟"

أدرك لونغشا أنه إن لم يغادر الليل المظلم، إن لم ينل الحرية، فلن تتاح له فرصة أخرى.

لم يكن لونغشا، الذي نشأ وحيدًا في الليل المظلم، يعرف معنى القلق، ناهيك عن معنى الاهتمام، ولكن مثل كل من كان على استعداد للمخاطرة بحياته، بدا كما لو أن هناك يدًا خفية تدفعه للأمام. عندما استعاد وعيه، كان واقفًا بالفعل وسط أنقاض المعركة، بندوبٍ تغطي جسده، ينحني يزأر بمزيجٍ من الألم والحماس - لقد تحرر.

عندما رافق لونغشا فتاة التنين إلى مسقط رأسه، كان هناك مدٌّ هائجٌ تحت الجرف، يُدحرج الغسق ويرشّ جناحيه الزرقاوين. كانت ندوب تلك المعركة لا تزال تؤلمه، لكن بالنظر إلى صيحات فتاة التنين المُتحمسة، بدا أن كل هذا الثمن كان يستحق العناء.

زأرت التنانين، ومحت أجنحة التنين على الفور ضوء الشمس المتبقي، كما لو أن الليل قد حلّ فجأة. دماء نفس السلالة المُسماة "دراغونبورن" لا تزال تحمل المجد، تنتظر يوم الإحياء،

نفس السلالة؟ بدا وكأن مشهدًا كهذا كان موجودًا منذ زمن بعيد - شتاء بارد، وثلوج متطايرة، وخطوات متجهة جنوبًا... لا، بل كانت هناك أيضًا غابات، وحرائق، ومعارك، ودماء...

أصبح زئير لونغشا مؤلمًا بعض الشيء، وبدا في غير محله. بدا المشهد أمامه وكأنه قد مُحي، وحلت محله ذكريات مؤلمة تدق عقله وقلبه، حتى اخترق زئير غاضب الظلام، وتلاشى كل شيء تدريجيًا وعاد إلى الواقع - "يا شعبي! إنه قاتل التنانين! إنه قاتل التنانين! إنه هنا مع فرسان المملكة! سيبقى نور عشيرة التنانين إلى الأبد! قاتلوا معي!"

قاتل التنانين! قاتل التنانين! قاتل التنانين! قاتل التنانين! قاتل التنانين! قاتل التنانين!

بدأت الذكريات التي حجبها الليل المظلم تتبدد، وأحاط برق أزرق داكن بجسد لونغشا. أدرك أن الآن هو وقت القتال بكل قوته!]],
[710107]='',
[710108]='',
[710109]='',
[710110]='',
[710111]='',
[710112]='',
[710113]='',
[710114]='',
[710115]='',
[710116]='',
[710117]='',
[710118]='',
[710119]='',
[710120]='',
[710121]='',
[710122]='',
[710123]='',
[710124]='',
[710125]='',
[710126]='',
[710127]='',
[710128]='',
[710129]='',
[710130]='',
[710201]='',
[710202]='',
[710203]='',
[710204]=[["هيا بنا! هيا بنا بسرعة! هيا بنا شمالاً! هيا بنا إلى أقصى الشمال!"

رفع ريكسار صولجانه ولوّح به في اتجاه الريح. كانت رياح كوتشيفي الباردة عاتية، تعوي وتمزق كل دفء. كانت الأرض التي اجتاحها الفراغ قد تحولت إلى خراب. في هذا الشتاء، غادرت قبيلة لينغنيو موطنها الذي عاشت فيه مئة عام، ووصلت إلى أقصى الشمال. كان ريكسار في السادسة عشرة من عمره في ذلك العام.

جعلت هوية الغريب الشك والعداء يلازمان كل خطوة من خطوات قبيلة لينغنيو. تحملت قبيلة لينغنيو أشد البرد، وأكثر الأراضي قحطاً، وأكثر العيون برودةً. وفي هذه الفترة المضطربة، أشرق ريكسار كنجم الشمال في سماء الليل المظلمة. لم يكتفِ بتتويج "مهرجان الجليد" الذي يُقام كل أربع سنوات في الشمال، بل رقص أيضًا رقصة النصر لقبيلة لينغنيو التي غزت الشمال قبل مئات السنين - "أغنية ملك الجليد" - على مرأى ومسمع من الجميع. في ذلك العام، دوى اسم ريكسار، البالغ من العمر تسعة عشر عامًا، في أرجاء حقل الجليد، وانبهر زعماء جميع القبائل بتصريحه الجريء: "سيعود مجد قبيلة لينغنيو إلى العالم!". مسترشدًا بالصولجان، ذهب وحيدًا إلى رفات أسلافه التي تُركت في حقل الجليد لمئة عام. باسم "لينغنيو"، ملك الشمال السابق، استطاع أن يقابل الروح البطولية ويعيد إلى القبيلة شاهد مجد الماضي، "الجليد النقي". كما أيقظ هذا العمل البطولي روح القتال والإيمان الكامنين في القبيلة. وتحت تأثير الجليد النقي، كانت بنية قبيلة لينغنيو تتغير بهدوء، وبدأت قوتها وروحها القتالية تستيقظ من جديد. بقيادة ريكسار، أصبح لا يُقهر. في ذلك العام، كان عمره 22 عامًا.

في عامه الثلاثين، رفرف علم قبيلة لينغنيو في كل ركن من أركان الشمال. وسمّت الأرض المغطاة بالثلوج الكثيفة ريكسار ملكًا. أشرق مجد قبيلة لينغنيو السابق من جديد على تربتها المتجمدة الأبدية. في الأسطورة، بعد مئة عام، لُقّب بأعظم بطل في تاريخ قبيلة لينغنيو.

في يوم التتويج، سقط العباءة اللازوردية التي تُمثل الجليد الشديد من تمثال إله الجليد وهبطت على كتفي ريكسار. أشادت جميع القبائل بهذا باعتباره وحيًا إلهيًا بميلاد الملك الحقيقي، لكن ريكسار وحده، الذي كان جالسًا على العرش شديد البرودة، عبس بشدة دون سبب...]],
[710205]=[[في السنة الثالثة من تتويجه ملكًا على الشمال، خلع ريكسار تاجه بهدوء واختفى في عاصفة ثلجية. ومعه، اختفى الجليد النقي الذي جلب مجدًا لا حدود له لقبيلة لينغنيو. لا أحد يعلم أين ذهب هذا البطل. يبدو أن العرش الفارغ الذي تركه خلفه هو ما يُخبرنا عن مجد الماضي.

في الواقع، كل هذا ليس بغريب على قبيلة لينغنيو. بصفتهم العائلة المالكة للشمال، كان "آخر ملوك" المينوي هو من خلع جليدهم النقي قبل أكثر من مئة عام ونقل القبيلة بأكملها جنوبًا إلى كوتشيفي. يُقال إن البرج القائم على قمة جبل برسيوس الثلجي، بوابة الشمال الأقصى، قد صُنع من البرونز بيد المينوي نفسه، وألزم أفراد القبيلة بقسمٍ لا يُنقض: لن تعود قبيلة لينغنيو إلى الشمال الأقصى أبدًا.

مع مرور الوقت، طواه النسيان. ومع تهديد الفراغ، نقضت قبيلة لينغنيو قسمها أخيرًا وعادت إلى الشمال. لكن التاريخ أشبه بالتناسخ، وتكرر الأمر نفسه. مع أن ريكسار لم يُجبر شعبه على مغادرة الشمال، إلا أن الشك أشبه بفيروس لا يُمحى. وكما هو الحال مع الشائعات حول المينويين، بدأت شائعة "رغبة ريكسار في احتكار قوة الجليد النقي" بالنمو تدريجيًا.

بدأت قبيلة لينغنيو، التي فقدت الجليد النقي وملكها، بالتراجع إلى ركن من أركان الشمال. ورغم أنهم ما زالوا يملكون ملجأً للبقاء مع ما تبقى من قوة ريكسار، إلا أن مجد الماضي وشرفه كانا كالثلج الناعم الذي يجرفه الريح.

أين ذهب ريكسار؟ هل كان حقًا يطمع في قوة الجليد النقي؟ هل هناك أي صلة بين المينويين وسلوك ريكسار؟ لطالما ظلت أسئلة لا تُحصى عالقة في قبيلة لينغنيو. وقد كُشفت إجابات كل هذه الأسئلة للعالم بعد عقود عندما كشفت الآلهة جرائم الفراغ للعالم.]],
[710206]=[[لا، لقد أخطأتُ. ليس مجدًا ولا قوة، بل لعنة! آمل ألا يكون الوقت قد فات... لا بد أنه فات!

بعد اكتشاف عالم القوى العظمى، لم يُجرِ أوميغا، سيد الفراغ، أي استعدادات. أُرسلت سبع "بذور" إلى أنحاء مختلفة من القارة عبر نفق الفراغ، مُقدّمةً على شكل "قوة"، قادرة على التأثير على عقول المخلوقات، وجعلها سريعة الانفعال ومولعة بالحرب، لتتحول تدريجيًا إلى خلائق فارغة. والجليد النقي، الذي يعتبره لينغنيو شيئًا مقدسًا، هو أحد بذور الفراغ السبع.

كان الملك مينوان السابق حكيمًا ولطيفًا. بعد أن أدرك كل شيء، فصله بالقوة عن القبيلة وختمه، مما أبعد لينغنيو عن الأرض الشمالية حيث كان الجليد النقي مختومًا. لولا ريكسار، لربما لم ير النور مرة أخرى.

عندما عرض شال إله الجليد هذا الماضي على ريكسار، تساءل في البداية عن سبب عدم كشف مينوان مباشرةً عن حقيقة الجليد النقي، بل اختياره إغلاقه وحده ثم الرحيل. لكن عندما طرح فكرة إغلاق الجليد النقي مجددًا على الشيوخ المحترمين، وعارضه معظمهم، أدرك ريكسار أخيرًا: ليس كل من يفتقد السلطة والسلطة، وخاصةً بعد تذوق حلاوته، فكيف يتخلى عنه كالحذاء البالي؟

اتضح أن الأسلاف أرادوا النسيان، وترك قبيلة لينغنيو تنسى هذا الماضي المجيد تمامًا. اذهبوا إلى كوتشيوي يا أبنائي، فهناك مراعي أوسع وهواء أدفأ. لا تطيلوا البقاء على الجليد في الشمال، ودعوا القوة المدفونة، انسوها للأبد، كما لو أنها لم تكن موجودة...

ولكن بالخطأ، سمح الفراغ لقبيلة التاورين بالعودة إلى الحقل الجليدي، وأطلق البطل ريكسار، الذي لم يُرَ منذ مئات السنين، سراح الشيطان. في البداية، كان ريكسار المغرور لا يزال لديه حظ، ولكن مع ازدياد قوة قبيلة لينغنيو، ازداد الفراغ الصامت نشاطًا، وبدأ شعب لينغنيو يقع في دوامة هوس الحرب والدمار والغزو. أدرك أن الوقت قد حان ليفعل شيئًا.

اتضح أن طريقة مينوان للهروب مفيدة، لكنها ليست علاجًا. قرر ريكسار المخاطرة. أراد أن يمحو الجليد النقي تمامًا وينهي اللعنة المتوارثة جيلًا بعد جيل. في سجلات الأسلاف، مع أن الجليد النقي يرمز إلى البرد القارس، إلا أنه قد لا يخلو من أعداء طبيعيين. قد تكون النار الشديدة قادرة على إذابته. وأشد نار في عالم القوى العظمى موجودة في عالم الآلهة فوق شجرة العالم.

في ذلك العام، خاطر ريكسار مخاطرة كبيرة وختم بذرة الفراغ، الجليد النقي، في جسده. وفي ذلك العام أيضًا، خلع التاج الذي يمثل القوة وانطلق في طريق مجهول إلى عالم الآلهة. في ذلك العام، كان ريكسار يبلغ من العمر 32 عامًا. في الواقع، في كتب تاريخ الأجيال اللاحقة، كان ريكسار يُدعى بطلًا قبل ذلك العام، وبعد ذلك العام، يُدعى بطلًا.]],
[710207]='',
[710208]='',
[710209]='',
[710210]='',
[710211]='',
[710212]='',
[710213]='',
[710214]='',
[710215]='',
[710216]='',
[710217]='',
[710218]='',
[710219]='',
[710220]='',
[710221]='',
[710222]='',
[710223]='',
[710224]='',
[710225]='',
[710226]='',
[710227]='',
[710228]='',
[710229]='',
[710230]='',
[710301]='',
[710302]='',
[710303]='',
[710304]='',
[710305]='',
[710306]='',
[710307]='',
[710308]='',
[710309]='',
[710310]='',
[710311]='',
[710312]='',
[710313]='',
[710314]='',
[710315]='',
[710316]='',
[710317]='',
[710318]='',
[710319]='',
[710320]='',
[710321]='',
[710322]='',
[710323]='',
[710324]='',
[710325]='',
[710326]='',
[710327]='',
[710328]='',
[710329]='',
[710330]='',
[710401]='',
[710402]='Sera',
[710403]='',
[710404]=[[يعيش الجان حياةً طويلة، والبقاء محاصرين في حديقة إيفرغرين يومًا بعد يوم أمرٌ مُملّ بعض الشيء، لذا ينتشر عددٌ كبيرٌ منهم في جميع أنحاء القارة. سيرا ليست استثناءً، فعلى عكس معظم الجان الذين يستقرون في العالم الخارجي، فهي تُحب السفر كثيرًا. في كل مرة تذهب فيها إلى مكان، تجمع ملصقاتٍ تُميّز المنطقة. الآن، حقيبتها المصنوعة من خشب الجان مُغطاة بمناظر طبيعية من جميع أنحاء العالم.

بالطبع، لن تنسى مهمتها الأصلية. في ذلك الوقت، ربّتت ملكة الجان على جبينها وقالت النعمة الموروثة من العصور القديمة بصوتٍ واضح: "ستحمي الغابة أطفالها المسافرين، وسيعمّ السلام أينما ذهبت".

من ذلك اليوم فصاعدًا، غادرت سيرا حديقة إيفرغرين وانطلقت في رحلةٍ للبحث عن الجان الظلاميين. لم تعد هذه المجموعة من "الأقارب" من عشرات الآلاف من السنين مزدهرةً كجنّ النور، ويصعب العثور على آثارهم حتى في فصول التاريخ. لكن وفقًا لنبوءة الشجرة العملاقة القديمة، يُلحق الفراغ دمارًا بالعالم، والليل المظلم يُراقب، ومن المُرجّح أن تعود الشياطين التي كانت مُختومةً سابقًا. إنهم بحاجة إلى قوة الجان الظلاميين الذين تعاقدوا مع الظلام لمُحاربة الشياطين.

"اذهب يا بني. ما دمتَ مُستمرًا، ستُلاقيهم يومًا ما... إذا غطى النور العالم، فقد حان وقت حلول الظلام." هذا أيضًا جزء من نبوءة الشجرة العملاقة القديمة.]],
[710405]=[[قوس سيرا الغدي قادر على اختراق أسباب وتأثيرات المستقبل، كرسالة مكتوبة في الزمن، مسجلةً نهاية الشخص المستهدف.

وفقًا لشهود عيان، أطلقت سيرا ذات مرة سهمًا نحو السماء عفويًا، وفي اليوم التالي هاجمها أحد سادة الليل المظلم. سقط السهم الذي أُطلق في اليوم السابق كسيل من الضوء في اللحظة الحاسمة، واخترق جسد السيد على الفور.

لم تُفاجأ سيرا إطلاقًا، ونتفت ذيل شعرها الشبيه بالغيوم وابتسمت قائلةً: "هكذا ينبغي أن يكون الأمر".

منذ ذلك الحين، طالما أطلقت سيرا سهمًا بطريقة غامضة، فلن يجرؤ أحد على استفزازها في اليوم التالي، وحتى في الأيام القليلة التالية، لن يجرؤ أحد على أن يكون عدوها - فمن يضمن أن "قوس الغد" لن يكون فعالًا إلا ليوم واحد؟

بالإضافة إلى ذلك، هناك شائعات بأن سيرا ليست في الواقع جنية بشرية، بل جنية فراشة على شكل حيوان، ولكن بفضل سنوات ميلادها الطويلة، يمكنها الحفاظ على شكل بشري كامل.

لكن ما هو مجهول هو أن كل من ينشر هذه الشائعة سيجد دائمًا سهمًا لامعًا عند استيقاظه في اليوم التالي، وورقة بريد قديمة مثبتة على رأس السرير. محتوى الرسالة مشابه جدًا - يعبر عن استياء شديد من الكلمات الأربع "سنوات طويلة"، مما يجعل الناس يشعرون بالرعب بعد قراءتها.

والأمر الأكثر إثارة للدهشة هو أن قوس وسهم سيرا لا يستطيعان عبور حاجز الزمن فحسب، بل حتى اتساع الفضاء لا يستحق الذكر - لأن أحدهم تحدث ذات مرة عن سنوات سيرا الطويلة في البرية الشمالية، ثم ركب المنطاد بسرعة عائدًا إلى مدينة الكيمياء الجنوبية. ونتيجة لذلك، لا يزال يرى السهم اللامع والحاد عندما استيقظ في اليوم التالي...]],
[710406]=[[حتى أن سيرا بدأت تشك في وجود ما يُسمى بجنّ الظلام، أم أنهم كانوا موجودين في الماضي فقط. بدت نبوءة الشجرة العملاقة القديمة مجرد كلام فارغ - فالنور لا يمكن أن يُغطي العالم تمامًا، ونصفه سيبقى دائمًا في الظلام. عندما جمعت ملصقات آخر مدينة وصلت إليها، ازدادت هذه الفكرة قوة.

امتلأ أخيرًا آخر مكان فارغ في حقيبة الجنّ الخشبية.

تموج الهواء قليلًا، كما لو أنه يُحرك برفق. شعرت سيرا فجأة وكأن أحدهم يسحب يدها. كان عليها أن تقاوم، لكن "ثقة" لا يمكن تفسيرها كانت تسيطر عليها. خطت خطوة نحو أن تُسحب، عابرةً تموجات الهواء. تموج المشهد المحيط بها على الفور. أينما ذهبت الأمواج، انقشع ضوء الشمس، وتغيرت المشاهد. عندما أفاقت سيرا، كانت تقف بالفعل بين الأشجار العملاقة. حجبت الأغصان والأوراق الضوء، وحلّ الظلام، وتبعته الظلال.

"أين هذا؟" شعرت سيرا بشيء من الغيبوبة. بدا لها أن هذا المكان أشبه بحديقة دائمة الخضرة لم تعد إليها منذ زمن، لكنه كان يفتقر إلى ضوء الشمس.

"إذا غطى النور العالم، فقد حان وقت الظلام."

ظهرت الشخصية المُحاطة بالظلال ببطء، وخلعت غطاء الرأس الذي يغطي وجهه، ونظرت إلى سيرا مبتسمة.

آذان مدببة، شعر أسود، جسد شفاف قليلاً، عيون داكنة - كانت سيرا متأكدة تمامًا أنها جنية الظلام التي كانت تبحث عنها!]],
[710407]='',
[710408]='',
[710409]='',
[710410]='',
[710411]='',
[710412]='',
[710413]='',
[710414]='',
[710415]='',
[710416]='',
[710417]='',
[710418]='',
[710419]='',
[710420]='',
[710421]='',
[710422]='',
[710423]='',
[710424]='',
[710425]='',
[710426]='',
[710427]='',
[710428]='',
[710429]='',
[710430]='',
[710501]='',
[710502]='',
[710503]='',
[710504]='',
[710505]='',
[710506]='',
[710507]='',
[710508]='',
[710509]='',
[710510]='',
[710511]='',
[710512]='',
[710513]='',
[710514]='',
[710515]='',
[710516]='',
[710517]='',
[710518]='',
[710519]='',
[710520]='',
[710521]='',
[710522]='',
[710523]=[[في المعركة ، قم بتحسين نفسك{%s1}السرعة وتقليل جميع الأضرار التي لحقت بوحدتنا بأعلى قوة هجوم{%s2}]],
[710524]=[[يزيد السرعة بمقدار 2 ويقلل جميع الأضرار التي تتلقاها بنسبة 1%]],
[710525]=[[يزيد السرعة بمقدار 2 ويقلل جميع الأضرار التي تتلقاها بنسبة 1%]],
[710526]=[[يزيد السرعة بمقدار 2 ويقلل جميع الأضرار التي تتلقاها بنسبة 1%]],
[710527]=[[يزيد السرعة بمقدار 2 ويقلل جميع الأضرار التي تتلقاها بنسبة 1%]],
[710528]=[[يزيد السرعة بمقدار 2 ويقلل جميع الأضرار التي تتلقاها بنسبة 1%]],
[710529]='',
[710530]='',
[710601]='',
[710602]='',
[710603]='',
[710604]='',
[710605]='',
[710606]='',
[710607]='',
[710608]='',
[710609]='',
[710610]='',
[710611]='',
[710612]='',
[710613]='',
[710614]='',
[710615]='',
[710616]='',
[710617]='',
[710618]='',
[710619]='',
[710620]='',
[710621]='',
[710622]='',
[710623]='',
[710624]='',
[710625]='',
[710626]='',
[710627]='',
[710628]='',
[710629]='',
[710630]='',
[710701]='',
[710702]='',
[710703]='',
[710704]='',
[710705]='',
[710706]='',
[710707]='',
[710708]='',
[710709]='',
[710710]='',
[710711]='',
[710712]='',
[710713]='',
[710714]='',
[710715]='',
[710716]='',
[710717]='',
[710718]='',
[710719]='',
[710720]='',
[710721]='',
[710722]='',
[710723]='',
[710724]='',
[710725]='',
[710726]='',
[710727]='',
[710728]='',
[710729]='',
[710730]='',
[710801]='',
[710802]='',
[710803]='',
[710804]='',
[710805]='',
[710806]='',
[710807]='',
[710808]='',
[710809]='',
[710810]='',
[710811]='',
[710812]='',
[710813]='',
[710814]='',
[710815]='',
[710816]='',
[710817]='',
[710818]='',
[710819]='',
[710820]='',
[710821]='',
[710822]='',
[710823]='',
[710824]='',
[710825]='',
[710826]='',
[710827]='',
[710828]='',
[710829]='',
[710830]='',
[710901]='',
[710902]='',
[710903]='',
[710904]='',
[710905]='',
[710906]='',
[710907]='',
[710908]='',
[710909]='',
[710910]='',
[710911]='',
[710912]='',
[710913]='',
[710914]='',
[710915]='',
[710916]='',
[710917]='',
[710918]='',
[710919]='',
[710920]='',
[710921]='',
[710922]='',
[710923]='',
[710924]='',
[710925]='',
[710926]='',
[710927]='',
[710928]='',
[710929]='',
[710930]='',
[711001]='',
[711002]='',
[711003]='',
[711004]='',
[711005]='',
[711006]='',
[711007]='',
[711008]='',
[711009]='',
[711010]='',
[711011]='',
[711012]='',
[711013]='',
[711014]='',
[711015]='',
[711016]='',
[711017]='',
[711018]='',
[711019]='',
[711020]='',
[711021]='',
[711022]='',
[711023]='',
[711024]='',
[711025]='',
[711026]='',
[711027]='',
[711028]='',
[711029]='',
[711030]='',
[711101]='',
[711102]='',
[711103]='',
[711104]='',
[711105]='',
[711106]='',
[711107]='',
[711108]='',
[711109]='',
[711110]='',
[711111]='',
[711112]='',
[711113]='',
[711114]='',
[711115]='',
[711116]='',
[711117]='',
[711118]='',
[711119]='',
[711120]='',
[711121]='',
[711122]='',
[711123]='',
[711124]='',
[711125]='',
[711126]='',
[711127]='',
[711128]='',
[711129]='',
[711130]='',
[711201]='',
[711202]='',
[711203]='',
[711204]='',
[711205]='',
[711206]='',
[711207]='',
[711208]='',
[711209]='',
[711210]='',
[711211]='',
[711212]='',
[711213]='',
[711214]='',
[711215]='',
[711216]='',
[711217]='',
[711218]='',
[711219]='',
[711220]='',
[711221]='',
[711222]='',
[711223]='',
[711224]='',
[711225]='',
[711226]='',
[711227]='',
[711228]='',
[711301]='',
[711302]='',
[711303]='',
[711304]='',
[711305]='',
[711306]='',
[711307]='',
[711308]='',
[711309]='',
[711310]='',
[711311]='',
[711312]='',
[711313]='',
[711314]='',
[711315]='',
[711316]='',
[711317]='',
[711318]='',
[711319]='',
[711320]='',
[711321]='',
[711322]='',
[711323]='',
[711324]='',
[711325]='',
[711326]='',
[711327]='',
[711328]='',
[711401]='',
[711402]='',
[711403]='',
[711404]='',
[711405]='',
[711406]='',
[711407]='',
[711408]='',
[711409]='',
[711410]='',
[711411]='',
[711412]='',
[711413]='',
[711414]='',
[711415]='',
[711416]='',
[711417]='',
[711418]='',
[711419]='',
[711420]='',
[711421]='',
[711422]='',
[711423]='',
[711424]='',
[711425]='',
[711426]='',
[711427]='',
[711428]='',
[711429]='',
[711430]='',
[711501]='',
[711502]='',
[711503]='',
[711504]='',
[711505]='',
[711506]='',
[711507]='',
[711508]='',
[711509]='',
[711510]='',
[711511]='',
[711512]='',
[711513]='',
[711514]='',
[711515]='',
[711516]='',
[711517]='',
[711518]='',
[711519]='',
[711520]='',
[711521]='',
[711522]='',
[711523]='',
[711524]='',
[711525]='',
[711526]='',
[711527]='',
[711528]='',
[711529]='',
[711530]='',
[711601]='',
[711602]='',
[711603]='',
[711604]='',
[711605]='',
[711606]='',
[711607]='',
[711608]='',
[711609]='',
[711610]='',
[711611]='',
[711612]='',
[711613]='',
[711614]='',
[711615]='',
[711616]='',
[711617]='',
[711618]='',
[711619]='',
[711620]='',
[711621]='',
[711622]='',
[711623]='',
[711624]='',
[711625]='',
[711626]='',
[711627]='',
[711628]='',
[711629]='',
[711630]='',
[711701]='',
[711702]='',
[711703]='',
[711704]='',
[711705]='',
[711706]='',
[711707]='',
[711708]='',
[711709]='',
[711710]='',
[711711]='',
[711712]='',
[711713]='',
[711714]='',
[711715]='',
[711716]='',
[711717]='',
[711718]='',
[711719]='',
[711720]='',
[711721]='',
[711722]='',
[711723]='',
[711724]='',
[711725]='',
[711726]='',
[711727]='',
[711728]='',
[711729]='',
[711730]='',
[711801]='',
[711802]='',
[711803]='',
[711804]='',
[711805]='',
[711806]='',
[711807]='',
[711808]='',
[711809]='',
[711810]='',
[711811]='',
[711812]='',
[711813]='',
[711814]='',
[711815]='',
[711816]='',
[711817]='',
[711818]='',
[711819]='',
[711820]='',
[711821]='',
[711822]='',
[711823]='',
[711824]='',
[711825]='',
[711826]='',
[711827]='',
[711828]='',
[711829]='',
[711830]='',
[711901]='',
[711902]='',
[711903]='',
[711904]='',
[711905]='',
[711906]='',
[711907]='',
[711908]='',
[711909]='',
[711910]='',
[711911]='',
[711912]='',
[711913]='',
[711914]='',
[711915]='',
[711916]='',
[711917]='',
[711918]='',
[711919]='',
[711920]='',
[711921]='',
[711922]='',
[711923]='',
[711924]='',
[711925]='',
[711926]='',
[711927]='',
[711928]='',
[711929]='',
[711930]='',
[712001]='',
[712002]='',
[712003]='',
[712004]='',
[712005]='',
[712006]='',
[712007]='',
[712008]='',
[712009]='',
[712010]='',
[712011]='',
[712012]='',
[712013]='',
[712014]='',
[712015]='',
[712016]='',
[712017]='',
[712018]='',
[712019]='',
[712020]='',
[712021]='',
[712022]='',
[712023]='',
[712024]='',
[712025]='',
[712026]='',
[712027]='',
[712028]='',
[712029]='',
[712030]='',
[712101]='',
[712102]='',
[712103]='',
[712104]='',
[712105]='',
[712106]='',
[712107]='',
[712108]='',
[712109]='',
[712110]='',
[712111]='',
[712112]='',
[712113]='',
[712114]='',
[712115]='',
[712116]='',
[712117]='',
[712118]='',
[712119]='',
[712120]='',
[712121]='',
[712122]='',
[712123]='',
[712124]='',
[712125]='',
[712126]='',
[712127]='',
[712128]='',
[712129]='',
[712130]='',
[712201]='',
[712202]='',
[712203]='',
[712204]='',
[712205]='',
[712206]='',
[712207]='',
[712208]='',
[712209]='',
[712210]='',
[712211]='',
[712212]='',
[712213]='',
[712214]='',
[712215]='',
[712216]='',
[712217]='',
[712218]='',
[712219]='',
[712220]='',
[712221]='',
[712222]='',
[712223]='',
[712224]='',
[712225]='',
[712226]='',
[712227]='',
[712228]='',
[712301]='',
[712302]='',
[712303]='',
[712304]=[[Trước khi trở thành thủ lĩnh máu sắt của đội quân quỷ, Sparta cũng có một quá khứ buồn.
Gia tộc Quỷ Lửa ​​nơi Sparta sinh sống ban đầu sống trên đảo Hita. Sau đó, một đám cháy rừng bùng phát trên đảo và bộ tộc di cư đến Đồng bằng Ermia. Quỷ Lửa ​​tự nhiên rất mạnh và rất giỏi chiến đấu. Họ nhanh chóng giành được một vị trí trên Đồng bằng Ermia, nhưng họ cũng trở thành kẻ thù của người dân bản địa ở đây.
Khi còn nhỏ, Sparta thường chơi ở cảng Amica. Vì ngoại hình độc đáo của Quỷ Lửa, những Quỷ Lửa ​​đơn độc rất dễ bị tấn công. Chính trong những cuộc tấn công này, Sparta đã phát triển một kỹ năng tốt và dần trở nên nổi tiếng.
Lúc đầu, Sparta kiếm sống bằng cách dỡ hàng ở bến tàu, nhưng số tiền kiếm được ở các ngõ bến tàu rất ít và không có nhiều thử thách. Sau một thời gian, Sparta khao khát một điều gì đó thử thách hơn. Tham vọng của Sparta đã sớm bị con rắn địa phương của Bang Thương mại Akami phát hiện. Dưới sự xúi giục của hắn, Sparta đã gia nhập đội của hắn và mắc phải một sai lầm không thể tha thứ. Gia tộc Hỏa Ma bị nhiều gia tộc lớn tàn sát! 
Cơn giận dữ bùng cháy hoàn toàn trong Sparta, anh muốn trả thù! Anh muốn trả thù thế giới đã lừa dối anh này!]],
[712305]=[[Bởi vì sách cổ của Hỏa Ma Tộc rất mơ hồ và khó hiểu, nên họ đã được gia tộc của mình cứu thoát khỏi sự hủy diệt. Khi Sparta chôn sâu sự trả thù trong lòng và đang choáng váng dọn dẹp quê hương đã biến thành đống đổ nát, anh vô tình phát hiện ra một bí mật: Hỏa Ma Tộc từng là kẻ thù cũ của Thánh Phượng Tộc thời xa xưa, chỉ vì Hỏa Ma Tộc cần Phượng Tộc để dấn thân vào "Con đường dập tắt". Nhưng khi Hỏa Ma Tộc dần rơi vào cảnh ô nhục, Thánh Phượng Tộc cũng lựa chọn ẩn mình khỏi thế gian, và lịch sử này đã dần bị chôn vùi trong lớp cát mỏng của thời gian.
Sparta, kẻ quyết tâm tiêu diệt một số gia tộc lớn, đã rời khỏi Cảng Akami và bắt đầu hành trình tìm kiếm Thánh Phượng Tộc. Khi cuối cùng anh vượt qua những ngọn núi và bước ra biển và tìm thấy vị trí của Thánh Phượng Tộc từ một vài manh mối nhỏ, anh tình cờ gặp phải Hư Không đang phát động một "Cuộc chiến lật đổ tổ" chống lại Thánh Phượng Tộc. Những chú phượng hoàng đã chết trong trận chiến. Cảnh tượng thảm họa trước mắt dường như khiến bi kịch của tộc Hỏa Ma Tộc tái hiện trước mắt Sparta. Không hiểu sao, anh lại cứu một cô gái từ tộc Phượng Hoàng Thánh Tộc.
Vì tộc Phượng Hoàng Thánh Tộc gần như đã bị hư không hủy diệt hoàn toàn, chỉ có Phượng Hoàng Hỏa Tộc còn sống mới có thể đánh thức "Dập Tắt Đạo", rõ ràng Sparta chỉ còn một lựa chọn duy nhất. Nhưng sau khi rời khỏi lãnh địa của tộc Phượng Hoàng Thánh Tộc, Hỏa Tộc luôn chăm sóc cho Phượng Hoàng Thiếu Nữ - "Nhất định phải có cách khác để trở nên mạnh mẽ hơn", Sparta tự an ủi mình không chỉ một lần.
Nhưng số phận có thể đã định sẵn để nỗi đau đồng hành cùng Sparta. Gần cảng Akami, Phượng Hoàng Thiếu Nữ vô tình để lộ thân phận. Những kẻ hầu người hạ của một số gia tộc lớn thèm muốn sức mạnh của tộc Phượng Hoàng Thánh Tộc và bắt cóc cô. Sparta cố gắng tuyệt vọng để cứu cô, nhưng bị áp đảo về số lượng và bị thương nặng. Vào thời khắc quan trọng, Phượng Hoàng Hỏa Tộc nán lại và trỗi dậy từ cơ thể cô gái, cuối cùng, giống như một đôi cánh tay dịu dàng, nhẹ nhàng ôm lấy cơ thể Sparta.
Phượng Hoàng Hỏa Tộc đã đốt cháy trái tim Sparta và cháy mãi mãi trong lồng ngực anh. Hối hận, đau đớn, phẫn nộ, phẫn nộ... một loạt cảm xúc trào ra, "Dập tắt con đường" trong nháy mắt kết thúc. Đôi mắt của cô gái dần khép lại, kèm theo tiếng gầm giận dữ của Sparta. Ngọn lửa dữ dội trong nháy mắt nuốt chửng móng vuốt của gia tộc, sau đó không chút do dự, chúng quét về phía vị trí của các gia tộc lớn ở cảng Akami...]],
[712306]=[[Sparta đã tự mình biến nhiều gia đình ở Cảng Akami thành cát bụi, nhưng cuối cùng đã bị đội thực thi pháp luật bắt giữ vì kiệt sức. Họ ban đầu muốn xử tử con quái vật đáng sợ này, nhưng đạn, thuốc độc và thậm chí cả vũ khí giả kim đều vô dụng. Vì vậy, họ phải nhốt anh ta ở nơi đáng sợ nhất của Akami - nhà tù trên biển. 
Người ta nói rằng sau khi phá hủy nhiều gia đình, Sparta đã biểu hiện sự im lặng khác thường. Ngay cả khi bị trói trong ngục tối tối tăm với những sợi xích nặng nề, anh ta dường như đã mất đi linh hồn và không có phản ứng gì. Chỉ có trong lưỡi kiếm ma thuật không thể tách rời khỏi Sparta, dường như có tiếng tim đập, và con ngươi ma thuật kỳ lạ lặng lẽ nhìn chằm chằm vào bóng tối, như thể đang chờ đợi điều gì đó. 
Câu trả lời đã được tiết lộ nhiều năm sau. Vào thời điểm này, nhiều người đã quên mất "con quỷ" bị giam cầm sâu trong nhà tù. Anh ta lại nhe nanh vào đêm đó. 
"Đêm hỗn loạn đột ngột" đến một cách lặng lẽ. Tất cả lính canh của nhà tù trên biển đều biến thành những sinh vật hư không, nhà tù bị phá hủy và tất cả những tên tội phạm nghiêm trọng đã trốn thoát. Một bộ phận lớn trong số họ đã đi theo Sparta và trở thành những thành viên đầu tiên của đội quân quỷ. Đây cũng là lá bài chủ và thủ đô để Sparta nhanh chóng trở thành tổng tư lệnh sau khi gia nhập Dark Night.
Không ai biết tại sao những người bảo vệ lại biến thành những sinh vật hư không, và không ai biết Sparta đã làm gì để những tên tội phạm nghiêm trọng này đi theo anh ta đến chết. Nhưng mọi thứ chắc chắn có liên quan đến con quỷ lửa đang bùng cháy với ngọn lửa của phượng hoàng.
"Từ nay trở đi, ta là kẻ thù của thế giới này."
Theo lời đồn, đây là câu nói cuối cùng mà Sparta nói khi rời khỏi Akami. Sau đó, nhà tù trên biển đã biến thành tro bụi trong ngọn lửa, và nó cũng thông báo rằng vị tổng tư lệnh sau này của Dark Night đã hoàn toàn đi về phía con đường đen tối.]],
[712307]='',
[712308]='',
[712309]='',
[712310]='',
[712311]='',
[712312]='',
[712313]='',
[712314]='',
[712315]='',
[712316]='',
[712317]='',
[712318]='',
[712319]='',
[712320]='',
[712321]='',
[712322]='',
[712323]='',
[712324]='',
[712325]='',
[712326]='',
[712327]='',
[712328]='',
[712329]='',
[712330]='',
[712401]='',
[712402]='',
[712403]='',
[712404]='',
[712405]='',
[712406]='',
[712407]='',
[712408]='',
[712409]='',
[712410]='',
[712411]='',
[712412]='',
[712413]='',
[712414]='',
[712415]='',
[712416]='',
[712417]='',
[712418]='',
[712419]='',
[712420]='',
[712421]='',
[712422]='',
[712423]='',
[712424]='',
[712425]='',
[712426]='',
[712427]='',
[712428]='',
[712429]='',
[712430]='',
[712501]='',
[712502]='',
[712503]='',
[712504]='',
[712505]='',
[712506]='',
[712507]='',
[712508]='',
[712509]='',
[712510]='',
[712511]='',
[712512]='',
[712513]='',
[712514]='',
[712515]='',
[712516]='',
[712517]='',
[712518]='',
[712519]='',
[712520]='',
[712521]='',
[712522]='',
[712523]='',
[712524]='',
[712525]='',
[712526]='',
[712527]='',
[712528]='',
[712529]='',
[712530]='',
[712601]='',
[712602]='',
[712603]='',
[712604]='',
[712605]='',
[712606]='',
[712607]='',
[712608]='',
[712609]='',
[712610]='',
[712611]='',
[712612]='',
[712613]='',
[712614]='',
[712615]='',
[712616]='',
[712617]='',
[712618]='',
[712619]='',
[712620]='',
[712621]='',
[712622]='',
[712623]='',
[712624]='',
[712625]='',
[712626]='',
[712627]='',
[712628]='',
[712629]='',
[712630]='',
[712701]='',
[712702]='',
[712703]='',
[712704]='',
[712705]='',
[712706]='',
[712707]='',
[712708]='',
[712709]='',
[712710]='',
[712711]='',
[712712]='',
[712713]='',
[712714]='',
[712715]='',
[712716]='',
[712717]='',
[712718]='',
[712719]='',
[712720]='',
[712721]='',
[712722]='',
[712723]='',
[712724]='',
[712725]='',
[712726]='',
[712727]='',
[712728]='',
[712729]='',
[712730]='',
[712801]='',
[712802]='',
[712803]='',
[712804]='',
[712805]='',
[712806]='',
[712807]='',
[712808]='',
[712809]='',
[712810]='',
[712811]='',
[712812]='',
[712813]='',
[712814]='',
[712815]='',
[712816]='',
[712817]='',
[712818]='',
[712819]='',
[712820]='',
[712821]='',
[712822]='',
[712823]='',
[712824]='',
[712825]='',
[712826]='',
[712827]='',
[712828]='',
[712829]='',
[712830]='',
[712901]='',
[712902]='',
[712903]='',
[712904]='',
[712905]='',
[712906]='',
[712907]='',
[712908]='',
[712909]='',
[712910]='',
[712911]='',
[712912]='',
[712913]='',
[712914]='',
[712915]='',
[712916]='',
[712917]='',
[712918]='',
[712919]='',
[712920]='',
[712921]='',
[712922]='',
[712923]='',
[712924]='',
[712925]='',
[712926]='',
[712927]='',
[712928]='',
[712929]='',
[712930]='',
[713001]='',
[713002]='',
[713003]='',
[713004]='',
[713005]='',
[713006]='',
[713007]='',
[713008]='',
[713009]='',
[713010]='',
[713011]='',
[713012]='',
[713013]='',
[713014]='',
[713015]='',
[713016]='',
[713017]='',
[713018]='',
[713019]='',
[713020]='',
[713021]='',
[713022]='',
[713023]='',
[713024]='',
[713025]='',
[713026]='',
[713027]='',
[713028]='',
[713029]='',
[713030]='',
[713101]='',
[713102]='',
[713103]='',
[713104]='',
[713105]='',
[713106]='',
[713107]='',
[713108]='',
[713109]='',
[713110]='',
[713111]='',
[713112]='',
[713113]='',
[713114]='',
[713115]='',
[713116]='',
[713117]='',
[713118]='',
[713119]='',
[713120]='',
[713121]='',
[713122]='',
[713123]='',
[713124]='',
[713125]='',
[713126]='',
[713127]='',
[713128]='',
[719001]='',
[719002]='',
[719003]='',
[719004]='',
[719005]='',
[719006]='',
[719007]='',
[719008]='',
[719101]='',
[720001]='',
[720002]='',
[720003]='',
[720005]='',
[720006]='',
[720007]='',
[720008]='',
[720009]='',
[720010]='',
[720011]='',
[720012]='',
[720013]='',
[720014]='',
[720025]=[[الماس الأحمر]],
[720026]='',
[720034]='',
[720035]='',
[720036]='',
[720037]='',
[720040]='',
[720041]='',
[720101]='',
[720102]='',
[720103]='',
[720104]='',
[720105]='',
[720106]='',
[720107]=[[الوحش الإلهي: بورياس]],
[720109]=[[النقل التجاري]],
[720311]='',
[720312]='',
[720313]='',
[720314]='',
[720315]='',
[720316]='',
[720317]='',
[720318]='',
[720319]='',
[720320]='',
[720321]='',
[720500]='',
[720501]='',
[720502]='',
[720521]='',
[720522]='',
[720523]='',
[720524]='',
[720541]='',
[720542]='',
[720601]='',
[720602]='',
[720603]='',
[720604]='',
[720605]='',
[720606]='',
[720607]='',
[720608]='',
[720650]='',
[720651]='',
[720652]='',
[720653]='',
[720654]='',
[720655]='',
[720671]='',
[720672]='',
[720673]='',
[720701]='',
[720702]='',
[720703]='',
[720704]='',
[720705]='',
[720706]='',
[720707]='',
[720708]='',
[720709]='',
[720710]='',
[720711]='',
[720712]='',
[720713]='',
[720714]='',
[720715]='',
[720716]='',
[720717]='',
[720718]='',
[720719]='',
[720720]='',
[720721]='',
[720751]='',
[720752]='',
[720753]='',
[720754]='',
[720761]='',
[720762]='',
[720763]='',
[720764]='',
[720765]='',
[720766]='',
[720767]='',
[720768]='',
[720769]='',
[720770]='',
[720771]='',
[720772]='',
[720801]='',
[720802]='',
[720803]='',
[720804]='',
[720805]='',
[720806]='',
[720811]='',
[720812]='',
[720813]='',
[720814]='',
[720815]='',
[720816]='',
[720821]='',
[720822]='',
[720823]='',
[720824]='',
[720825]='',
[720826]='',
[720831]='',
[720832]='',
[720833]='',
[720834]='',
[720835]='',
[720841]='',
[720842]='',
[720843]='',
[720844]='',
[720845]='',
[720850]='',
[720900]=[[النخبة زومبي نهب]],
[720901]=[[النخبة زومبي نهب]],
[720902]=[[النخبة زومبي نهب]],
[720903]=[[النخبة زومبي نهب]],
[720904]=[[النخبة زومبي نهب]],
[720905]=[[النخبة زومبي نهب]],
[720906]=[[النخبة زومبي نهب]],
[720907]=[[النخبة زومبي نهب]],
[720908]=[[النخبة زومبي نهب]],
[720909]=[[النخبة زومبي نهب]],
[720910]=[[النخبة زومبي نهب]],
[720911]=[[النخبة زومبي نهب]],
[720912]=[[النخبة زومبي نهب]],
[720913]=[[النخبة زومبي نهب]],
[720914]=[[النخبة زومبي نهب]],
[720915]=[[النخبة زومبي نهب]],
[720916]=[[النخبة زومبي نهب]],
[720917]=[[النخبة زومبي نهب]],
[720918]=[[النخبة زومبي نهب]],
[720919]=[[النخبة زومبي نهب]],
[720920]=[[النخبة زومبي نهب]],
[720950]=[[تجول أفرلورد نهب]],
[720951]=[[تجول أفرلورد نهب]],
[720952]=[[تجول أفرلورد نهب]],
[720953]=[[تجول أفرلورد نهب]],
[720954]=[[تجول أفرلورد نهب]],
[720955]=[[تجول أفرلورد نهب]],
[720956]=[[تجول أفرلورد نهب]],
[720957]=[[تجول أفرلورد نهب]],
[720958]=[[تجول أفرلورد نهب]],
[720959]=[[تجول أفرلورد نهب]],
[720960]=[[تجول أفرلورد نهب]],
[720961]=[[تجول أفرلورد نهب]],
[720962]=[[تجول أفرلورد نهب]],
[720963]=[[تجول أفرلورد نهب]],
[720964]=[[تجول أفرلورد نهب]],
[720965]=[[تجول أفرلورد نهب]],
[720966]=[[تجول أفرلورد نهب]],
[720967]=[[تجول أفرلورد نهب]],
[720968]=[[تجول أفرلورد نهب]],
[720969]=[[تجول أفرلورد نهب]],
[720970]=[[تجول أفرلورد نهب]],
[721001]='',
[721002]='',
[721003]='',
[721004]='',
[721005]='',
[721006]='',
[721051]='',
[721052]='',
[721053]='',
[721100]=[[جمع نهب الاعتداء]],
[721101]=[[جمع نهب الاعتداء]],
[722003]='',
[722004]='',
[722005]='',
[722100]=[[كتاب تجارب المهارة]],
[722101]='',
[722102]='',
[722103]='',
[722201]='',
[722202]='',
[722203]='',
[722204]=[[سوف تكون شظية]],
[722205]='',
[722206]='',
[722207]='',
[722208]='',
[722209]='',
[722210]='',
[722211]='',
[722212]='',
[722213]='',
[722214]='',
[722215]='',
[722216]='',
[722217]='',
[722218]='',
[722219]='',
[722220]='',
[722221]='',
[722222]='',
[722223]='',
[722224]='',
[722225]='',
[722226]='',
[722227]='',
[722228]='',
[722229]='',
[722230]='',
[722231]='',
[723001]=[[[أسطورة] سيف القدر]],
[723002]=[[[الأسطوري] درع القدر]],
[723003]=[[[الأسطوري] خوذة القدر]],
[723004]=[[[الأسطوري] جحافل القدر]],
[723005]='',
[723006]='',
[723007]='',
[723008]='',
[723009]='',
[723010]='',
[723011]='',
[723012]='',
[723013]='',
[723014]='',
[723015]='',
[723016]='',
[723201]='',
[723202]='',
[723203]='',
[723204]='',
[723205]='',
[723250]='',
[723252]='',
[723253]='',
[723254]='',
[723256]='',
[723301]='',
[723302]='',
[723303]='',
[724000]='',
[724001]='',
[724002]='',
[724003]='',
[724051]='',
[724052]='',
[724053]='',
[724054]='',
[724055]='',
[724056]='',
[724057]='',
[724058]='',
[724059]='',
[724060]='',
[724061]='',
[724062]='',
[724063]='',
[724064]='',
[724065]='',
[724066]='',
[724067]='',
[724068]='',
[724069]='',
[724070]='',
[724101]='',
[724102]='',
[724103]='',
[724104]='',
[724105]='',
[724106]='',
[724107]=''}