local tostring = tostring
local data_personalInfo = require "data_personalInfo"
local string = string
local json = require "dkjson"


local halloween_activity_slot_machine_data_helper = {}


function halloween_activity_slot_machine_data_helper.GetRoleKey(key)
    local roleID = tostring(data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleID))
    return string.format("%s_%s", roleID, key)
end

function halloween_activity_slot_machine_data_helper.GetJsonStr(data)
    return json.encode(data)
end

function halloween_activity_slot_machine_data_helper.DecodeJsonStr(jsonStr)
    return json.decode(jsonStr)
end



return halloween_activity_slot_machine_data_helper