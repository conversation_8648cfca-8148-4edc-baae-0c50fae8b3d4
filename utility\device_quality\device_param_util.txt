local Screen = CS.UnityEngine.Screen
local SystemInfo = CS.UnityEngine.SystemInfo
local AssetBundleManager = CS.War.Base.AssetBundleManager
local CachedGameObjectPool = CS.War.Base.CachedGameObjectPool
local RuntimePlatform = CS.UnityEngine.RuntimePlatform
local Application = CS.UnityEngine.Application
local QualitySettings = CS.UnityEngine.QualitySettings
local UtilityManager = CS.UtilityManager
local PostProcessingBehaviour = CS.UnityEngine.PostProcessing.PostProcessingBehaviour
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local Graphics = CS.UnityEngine.Graphics
local IsInEditor = CS.War.Script.Utility.IsInEditor

local tostring = tostring
local typeof = typeof
local math = math
local print = print
local __ENABLE_URP = __ENABLE_URP

local event = require "event"
local const = require "const"
local log = require "log"
local device_level_controller = require "device_level_controller"

local _M = {}

_M.eLowLevel = 1
_M.eMidLevel = 2
_M.eHighLevel = 3

local lowMem = false   --低内存限制

local levelCache = nil
local resolutionCoefficient = 1


_M.QUALITY_LOW = 1--低质量
_M.QUALITY_MID = 2--中等质量
_M.QUALITY_HIGH = 3--高质量
_M.QUALITY_ULTRA = 4--极致

_M.MESH_NORMAL = 4--高模
_M.MESH_LOW = 2--低模

_M.EFFECT_HIGH = 0--原质量
_M.EFFECT_MID = 0--原质量
_M.EFFECT_LOW = 1--减配LOD

_M.TEXTURE_NORMAL = 1--常规
_M.TEXTURE_HIGH = 2--高清

_M.LIGHT_MAX = 1--全开
_M.LIGHT_MIN = 2--最少

_M.ORIGIN_SCREEN_HEIGHT = Screen.height--原始屏幕高度
_M.screenHeightAdaptScale = 1--屏幕高度适配比例


local qualityInfo = {
    --后处理开关
    CameraEffect_On = true,
    --HDR开关
    --SystemHDR_On = true,
    --实时阴影开关
    RealtimeShadow_On = true,
    --简单阴影开关
    SimpleShadow_On = false,
    --模型网格等级
    Mesh_Level = 2,
    --特效等级
    Effect_Level = 0,
    --贴图等级
    Texture_Level = 1,
    --灯光控制
    Light_Level = 1,
    --帧率
    Frame_Rate = 60,
    --系统Qulity设置
    System_Quality_Level = 4,
    --原设备等级
    Origin_Device_Level = 4,
}


--判断设备等级
function _M.JudgeDeviceLevel()
    if IsInEditor() then
        levelCache = _M.eHighLevel
    end
    if levelCache then
        return levelCache
    end
    levelCache = device_level_controller.GetOriginDeviceLevel() or 2
    if levelCache == _M.eLowLevel then
        lowMem = true
    end
    --最低等级为1  level 1 低端机  2  中端机  3 高端机
    print("JudgeDeviceLevel:"..tostring(levelCache))
    return levelCache
end

--设置分辨率，低端机设置为720p 分辨率，中端机设置为1080p  高端机最低1080p 最高原始分辨率
function _M.SetResolution()
    local designWidth = 720
    local screenWidth = Screen.width
    local screenHeight = Screen.height
    if screenWidth < designWidth then
        designWidth = screenWidth
    end

    local deviceLevel = _M.JudgeDeviceLevel()
    if __ENABLE_URP then
        if (deviceLevel >= _M.eHighLevel ) then
            designWidth = 1080
        elseif deviceLevel == _M.eMidLevel then
            designWidth = 900
        end
        log.Log("set urp resolution "..designWidth)
    else
        if (deviceLevel >= _M.eHighLevel ) then
            designWidth = 920
        elseif deviceLevel == _M.eMidLevel then
            designWidth = 765
        end
        log.Log("set brp resolution "..designWidth)
    end

    --iphone机型不降分辨率
    if not __ENABLE_URP then
        if Application.platform == RuntimePlatform.IPhonePlayer then
            designWidth = screenWidth
            log.Log("set brp iphone resolution "..designWidth)
        end
    end

    if ((16 / 9) > (Screen.height / Screen.width)) then--iPad 
		if not __ENABLE_URP then 
        	designWidth = screenWidth * (1280 / screenHeight);
		else
			if screenWidth > 1800 then
				designWidth = 1440
			elseif screenWidth > 1350 then
				designWidth = screenWidth * 0.8
			else
				designWidth = 1080
			end
        end
        log.Log("set pad resolution "..designWidth)
    end


	if screenWidth < designWidth then
		designWidth = screenWidth
	end
    print("deviceLevel,screenWidth,designWidth",deviceLevel,screenWidth,designWidth)

    local targetHeight = math.round(designWidth * screenHeight / screenWidth)
    local targetWidth = designWidth

    log.Warning("targetHeight,targetWidth",targetHeight,targetWidth)
    Screen.SetResolution(targetWidth,targetHeight,true)
    _M.SetScreenHeightAdaptScale(targetHeight)
    
    -- log.Warning("手机 分辨率：",screenWidth,screenHeight, targetWidth,targetHeight, _M.GetScreenHeightAdaptScale())
    
end

function _M.GetScreenHeightAdaptScale()
    return screenHeightAdaptScale
end

function _M.SetScreenHeightAdaptScale(targetHeight)
    screenHeightAdaptScale = targetHeight/_M.ORIGIN_SCREEN_HEIGHT
    -- log.Warning("手机 SetScreenHeightAdaptScale：",targetHeight,targetHeight/_M.ORIGIN_SCREEN_HEIGHT,screenHeightAdaptScale)
end

function _M.ResetScreenHeightAdaptScale(adaptScale)
    screenHeightAdaptScale = adaptScale
    -- log.Warning("手机 ResetScreenHeightAdaptScale：",screenHeightAdaptScale)
end

function _M.SetWindowMgrParam(deviceLevel)
    if deviceLevel == nil then
        deviceLevel = _M.JudgeDeviceLevel()
    end
    if deviceLevel == _M.eLowLevel then
        local ui_window_mgr = require "ui_window_mgr"
        ui_window_mgr:SetUpdateInterval(0.1)

        local idle = require "idle"
        idle.MEM_LEVEL = device_level_controller.OpenViewCacheController()
    end
    if CachedGameObjectPool.ResetAllMaxCachedCount then
        CachedGameObjectPool.ResetAllMaxCachedCount(device_level_controller.GetCacheHeroCount())
    end
    
    if _M.IslowMem() then
        log.SetTraceBack(false)
    end
end

function _M.GetResolutionCoefficient()
    return resolutionCoefficient
end
function _M.IslowMem()
    return lowMem
end

-- 根据硬件情况，自动调整画质
function _M.AutoSetQuality(waitForLobby)
    print("SystemInfo.graphicsMemorySize",SystemInfo.graphicsMemorySize)

    if waitForLobby then
        --log.Warning("等待lobby >>>>> ",value)
        event.Register(event.UI_LOBBY_SHOW, _M.LoginEventHandler)
    else
        _M.SetQualityInfo()
    end
end

function _M.SetParamsLevel(deviceLevel)
    _M.SetResolution()
    _M.SetUpdateNum(deviceLevel)
    _M.AutoSetPostProcessing(deviceLevel)
    _M.SetWindowMgrParam(deviceLevel)
    _M.AutoSetFrameCount(deviceLevel)
    _M.ReportUserSet(deviceLevel)
end

function _M.ReportUserSet(level)
    local json = require "dkjson"
    local q1sdk = require "q1sdk"
    local json_str = json.encode({
        latest_device_level = level,
    })
    q1sdk.UserSet(json_str)
end

local MaxClearPoolCount = 10

function _M.SetClearPoolCount(level)
    if level == _M.eLowLevel then
        MaxClearPoolCount = 2
    elseif level == _M.eMidLevel then
        MaxClearPoolCount = 3
    else
        MaxClearPoolCount = 6
    end
end

function _M.GetMaxClearPoolCount()
    return MaxClearPoolCount
end

function _M.AutoSetFrameCount(deviceLevel)
    local const = require "const"
    local fps = PlayerPrefs.GetInt("User_Set_FPS", 0)
    if fps ~= 0 then
        const.TargetFrameRate = fps
        Application.targetFrameRate = const.TargetFrameRate
        return
    end
    if deviceLevel == _M.eHighLevel then
        fps = 60
    else
        fps = 45
    end

    const.TargetFrameRate = fps
    Application.targetFrameRate = const.TargetFrameRate
    PlayerPrefs.SetInt("User_Set_FPS", fps)
end

function _M.SetUpdateNum(deviceLevel)
    if not AssetBundleManager.UPDATE_NUM then 
        return 
    end
    if deviceLevel == nil then
        deviceLevel = _M.JudgeDeviceLevel()
    end

    if deviceLevel == _M.eLowLevel then
        print("降低UPDATE_NUM======================================")
        AssetBundleManager.UPDATE_NUM = 1
    end
end

function _M.AutoSetPostProcessing(deviceLevel)
    if deviceLevel == nil then
        deviceLevel = _M.JudgeDeviceLevel()
    end
    local utilityManager = UtilityManager.GetInstance()

    local result = false
    if deviceLevel == _M.eLowLevel then
        result = utilityManager:EnableComponentType(typeof(PostProcessingBehaviour), false)
    elseif utilityManager.DisableComponentType then
        result = utilityManager:DisableComponentType(typeof(PostProcessingBehaviour), true)
    end
    --log.Warning("PostProcess, 后处理开关：deviceLevel=",deviceLevel,"result=",result)
end

function _M.SetQualityInfo()
    if not levelCache then
        levelCache = _M.JudgeDeviceLevel()
    end
    qualityInfo.CameraEffect_On = levelCache > _M.eMidLevel and true or false
    qualityInfo.RealtimeShadow_On = levelCache == _M.eLowLevel and false or true
    qualityInfo.SimpleShadow_On = levelCache == _M.eLowLevel and true or false
    qualityInfo.Mesh_Level = device_level_controller.OpenMeshController()--_M.MESH_LOW
    qualityInfo.Effect_Level = levelCache == _M.eLowLevel and _M.EFFECT_LOW or 0--_M.EFFECT_LOW
    qualityInfo.Texture_Level = levelCache == _M.QUALITY_ULTRA and _M.TEXTURE_HIGH or _M.TEXTURE_NORMAL--_M.EFFECT_LOW_M.TEXTURE_NORMAL
    qualityInfo.Light_Level = levelCache > _M.eMidLevel and _M.LIGHT_MAX or _M.LIGHT_MIN
    qualityInfo.Frame_Rate = PlayerPrefs.GetInt("User_Set_FPS", 60)
    qualityInfo.System_Quality_Level = device_level_controller.OpenSystemQualityLevel()
    qualityInfo.Origin_Device_Level = levelCache
    _M.RefreshQualitySetting(levelCache)
end

local currentLevel = -1
function _M.RefreshQualitySetting(value)
    if currentLevel == value then
        return
    end
    currentLevel = value
    _M.SetParamsLevel(qualityInfo.Origin_Device_Level)
    Application.targetFrameRate = PlayerPrefs.GetInt("User_Set_FPS", 60)
    QualitySettings.SetQualityLevel(qualityInfo.System_Quality_Level)
    QualitySettings.skinWeights = qualityInfo.Mesh_Level
    log.Warning("qualityLevel：", value,"Graphics=",Graphics.activeTier,
            "\nCameraEffect_On = ",qualityInfo.CameraEffect_On,
            "\nSystemHDR_On =" ,qualityInfo.SystemHDR_On,
            "\nRealtimeShadow_On =" ,qualityInfo.RealtimeShadow_On,
            "\nSimpleShadow_On =", qualityInfo.SimpleShadow_On,
            "\nMesh_Level =", qualityInfo.Mesh_Level,
            "\nEffect_Level =", qualityInfo.Effect_Level,
            "\nTexture_Level =", qualityInfo.Texture_Level,
            "\ntargetFrameRate =", Application.targetFrameRate,
            "\nQualityLevel =", QualitySettings.GetQualityLevel(),
            "\nLight_Level =", qualityInfo.Light_Level)
end

function _M.LoginEventHandler(_)
    _M.SetQualityInfo()
    event.Unregister(event.UI_LOBBY_SHOW, _M.LoginEventHandler)
end

return _M