﻿---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by ha<PERSON><PERSON><PERSON>.
--- DateTime: 2025/9/12 15:41
--- 创建角色队列管理器
local table = table
local ipairs = ipairs
local require = require
local log = require "log"
local net = require "net"
local lang = require "lang"
local flow_text = require "flow_text"
local net_route = require "net_route"
local url_operation_mgr = require "url_operation_mgr"
local login_pb = require "login_pb"
local xManMsg_pb = require "xManMsg_pb"
local virtual_main_level_module = require "virtual_main_level_module"
local virtual_home_build_module = require "virtual_home_build_module"
local virtual_player_module = require "virtual_player_module"
local Application = CS.UnityEngine.Application
module("create_character_queue_mgr")

local isLocalNewBie = true
---@public 获取是否是本地新手
function GetLocalNewBie()
    local b_op_config = url_operation_mgr.GetConfig("enable_local_newbie") == true
    return isLocalNewBie and (b_op_config or Application.isEditor)
end

--region 网络消息处理
function MSG_ACTOR_CREATE_DATA_SYNC_REQ()
    local req = login_pb.TMSG_ACTOR_CREATE_DATA_SYNC_REQ()
    req.nLevel = 1
    req.nLevelChallenge = virtual_main_level_module.GetPassLevel()
    local levelChallengeData = virtual_main_level_module.GetLevelChallengeReqData()
    if levelChallengeData then
        for i, v in ipairs(levelChallengeData) do
            table.insert(req.arrLevelChallengeReq, v)
        end
    end
    local arrRepairMapid = virtual_home_build_module.GetSaveRepairMapIdList()
    if arrRepairMapid then
        for i, v in ipairs(arrRepairMapid) do
            table.insert(req.arrRepairMapid, v)
        end
    end
    local arrOpenGiftMapid = virtual_home_build_module.GetSaveOpenGiftMapIdList()
    if arrOpenGiftMapid then
        for i, v in ipairs(arrOpenGiftMapid) do
            table.insert(req.arrOpenGiftMapid, v)
        end
    end
   -- net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ACTOR_CREATE_DATA_SYNC_REQ, req)
end

function MSG_ACTOR_CREATE_DATA_SYNC_RSP(msg)
    if not msg then
        return
    end
    if msg.errorCode and msg.errorCode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errorCode))
        return false
    end
    if msg.arrBuilding then
        local GWHomeMgr = require "gw_home_mgr"
        GWHomeMgr.buildingData.UpdateData(msg.arrBuilding, isAll)    
    end    
end

---@public 创建队列通知
function S2CCreateQueueNTF(msg)
    if not msg or not msg.state then
        return
    end
    --判断是否是本地新手 就算服务器下发 也不进入 domain控制
    local isOpen = GetLocalNewBie()
    if not isOpen then
        return
    end
    if msg.state == 1 then
        virtual_player_module.DeleteNoviceEventValue()
        virtual_main_level_module.DeletePassLevel()
        virtual_home_build_module.DeleteLocalData()
        local virtual_server = require "virtual_server"
        virtual_server.Start()
    elseif msg.state == 2 then
        local virtual_server = require "virtual_server"
        virtual_server.Stop()
        MSG_ACTOR_CREATE_DATA_SYNC_REQ()
    else
        log.Error("S2CCreateQueueNTF 暂未有处理这个状态 state = ", msg.state)
    end
end

local MessageTable = {
    { xManMsg_pb.MSG_ACTOR_CREATE_DATA_SYNC_RSP, MSG_ACTOR_CREATE_DATA_SYNC_RSP, login_pb.TMSG_ACTOR_CREATE_DATA_SYNC_RSP },
    --创角排队
    { xManMsg_pb.MSG_LOGIN_ACTOR_CREATE_QUEUE_NTF, S2CCreateQueueNTF, login_pb.TMSG_LOGIN_ACTOR_CREATE_QUEUE_NTF, },
}
net_route.RegisterMsgHandlers(MessageTable)

--endregion