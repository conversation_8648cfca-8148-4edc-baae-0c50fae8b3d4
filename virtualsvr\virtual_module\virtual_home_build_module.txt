﻿---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by ha<PERSON><PERSON><PERSON>.
--- DateTime: 2025/9/5 10:17
--- desc : 家园建筑模块

local pairs = pairs
local table = table
local debug = debug
local ipairs = ipairs
local xpcall = xpcall
local string = string
local virtual_net = require "virtual_net"
local xManMsg_pb = require "xManMsg_pb"
local city_pb = require "city_pb"
local game_scheme = require "game_scheme"
local bit = require("bit")
local bor = bit.bor
local bnot = bit.bnot
local log = require "log"
local json = require "dkjson"
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
module("virtual_home_build_module")

local buildingList = {}
local saveBuildingList = {}
local saveRepairMapIdList = {}
local saveOpenGiftMapIdList = {}

local BuildingSavaKey = "BuildingSavaKey"

function OnVirtualServerStarted()
    --DeleteLocalData()
end

function InitRoleCityData()
    --读取本地数据
    ReadLocalData()
    local uSidIndex = 0
    --创建城建数据
    local count = game_scheme:BuildMaincityMap_nums()
    for i = 0, count - 1 do
        local buildMainCityCfg = game_scheme:BuildMaincityMap(i)
        if buildMainCityCfg and buildMainCityCfg.type == 1 and buildMainCityCfg.Hiddenbuildings == 0 then
            local build = {}
            uSidIndex = uSidIndex + 1
            local saveBuildInfo = GetSavaBuildingInfoByMapId(buildMainCityCfg.MapID)
            if saveBuildInfo then
                build = saveBuildInfo
            else
                build.nLevel = buildMainCityCfg.buildingID.data[1]
                build.nState = buildMainCityCfg.broken == 1 and city_pb.enBuildingState_Broken or city_pb.enBuildingState_Normal
                build.uFlag = 0
                if buildMainCityCfg.broken == 1 then
                    build.uFlag = bor(build.uFlag, city_pb.enCityFlag_CityWaitRepair)
                end
            end
            build.uSid = uSidIndex
            build.nBuildingID = buildMainCityCfg.buildingID.data[0]
            build.x = buildMainCityCfg.x
            build.y = buildMainCityCfg.y
            build.uMapUnitID = buildMainCityCfg.MapID
            buildingList[build.uSid] = build
        end
    end
end

function MSG_PROP_CREATEENTITY_NTF()
    InitRoleCityData()
    --prop_pb.PERSONPART_CITY
    local msg = city_pb.TMSG_CITY_GET_ALLINFO_RSP()
    --创建城建数据
    for i, info in pairs(buildingList) do
        local build = msg.building:add()
        build.uSid = info.uSid
        build.nBuildingID = info.nBuildingID
        build.nLevel = info.nLevel
        build.x = info.x
        build.y = info.y
        build.nState = info.nState
        build.uFlag = info.uFlag
        build.uMapUnitID = info.uMapUnitID
    end

    local areaMapCount = game_scheme:BuildAreaMap_nums()
    for i = 0, areaMapCount - 1 do
        local buildAreaCityCfg = game_scheme:BuildAreaMap(i)
        if buildAreaCityCfg and string.IsNullOrEmpty(buildAreaCityCfg.AreaUnlock) then
            local areaInfo = msg.area:add()
            areaInfo.nID = buildAreaCityCfg.AreaID
            areaInfo.uFlag = 0
            areaInfo.uFlag = bor(areaInfo.uFlag, city_pb.enCityFlag_AreaUnlock)
            areaInfo.uFlag = bor(areaInfo.uFlag, city_pb.enCityFlag_AreaCanBuilding)
        end
    end
    local data = msg:SerializeToString()
    return data
end

---@public 修复建筑请求
function MSG_CITY_REPAIR_A_BUILDING_REQ(msg)
    if not msg or not msg.uSid then
        log.Error("MSG_CITY_REPAIR_A_BUILDING_REQ error")
        return
    end
    local info = buildingList[msg.uSid]
    if not info then
        log.Error("MSG_CITY_REPAIR_A_BUILDING_REQ error", msg.uSid)
        return
    end
    info.uFlag = bor(info.uFlag, city_pb.enCityFlag_BuildingOpenGift)
    local nftMsg = city_pb.TMSG_CITY_BUILDING_UPDATE_NTF()
    local ntfBuildInfo = nftMsg.building:add()
    ntfBuildInfo.uSid = msg.uSid
    ntfBuildInfo.nBuildingID = info.nBuildingID
    ntfBuildInfo.nLevel = info.nLevel
    ntfBuildInfo.x = info.x
    ntfBuildInfo.y = info.y
    ntfBuildInfo.nState = city_pb.enBuildingState_Normal
    ntfBuildInfo.uFlag = info.uFlag
    ntfBuildInfo.uMapUnitID = info.uMapUnitID
    virtual_net.SendMessageToClient(xManMsg_pb.MSG_CITY_BUILDING_UPDATE_NTF, nftMsg)
    local addBuild = true
    for i, v in ipairs(saveRepairMapIdList) do
        if v == info.uMapUnitID then
            addBuild = false
        end
    end
    if addBuild then
        --更新本地数据
        table.insert(saveRepairMapIdList, info.uMapUnitID)
    end
    AddSaveBuildingList(info.uMapUnitID, info.nLevel, info.uFlag, city_pb.enBuildingState_Normal)
    SaveLocalData()
    local rspMsg = city_pb.TMSG_CITY_REPAIR_A_BUILDING_RSP()
    rspMsg.errCode = 0
    rspMsg.uSid = msg.uSid
    virtual_net.SendMessageToClient(xManMsg_pb.MSG_CITY_REPAIR_A_BUILDING_RSP, rspMsg)
end
---@public 建筑剪彩
function MSG_CITY_GET_BUILDING_OPENGIFT_REQ(msg)
    if not msg or not msg.uSid then
        log.Error("MSG_CITY_GET_BUILDING_OPENGIFT_REQ error")
        return
    end
    local info = buildingList[msg.uSid]
    if not info then
        log.Error("MSG_CITY_GET_BUILDING_OPENGIFT_REQ error", msg.uSid)
        return
    end
    info.uFlag = bnot(info.uFlag, bnot(city_pb.enCityFlag_BuildingOpenGift))
    info.nLevel = info.nLevel + 1
    local nftMsg = city_pb.TMSG_CITY_BUILDING_UPDATE_NTF()
    local ntfBuildInfo = nftMsg.building:add()
    ntfBuildInfo.uSid = msg.uSid
    ntfBuildInfo.nBuildingID = info.nBuildingID
    ntfBuildInfo.nLevel = info.nLevel
    ntfBuildInfo.x = info.x
    ntfBuildInfo.y = info.y
    ntfBuildInfo.nState = city_pb.enBuildingState_Normal
    ntfBuildInfo.uFlag = info.uFlag
    ntfBuildInfo.uMapUnitID = info.uMapUnitID
    virtual_net.SendMessageToClient(xManMsg_pb.MSG_CITY_BUILDING_UPDATE_NTF, nftMsg)
    local addBuild = true
    for i, v in ipairs(saveOpenGiftMapIdList) do
        if v == info.uMapUnitID then
            addBuild = false
        end
    end
    if addBuild then
        --更新本地数据
        table.insert(saveOpenGiftMapIdList, info.uMapUnitID)
    end
    AddSaveBuildingList(info.uMapUnitID, info.nLevel, info.uFlag, city_pb.enBuildingState_Normal)
    SaveLocalData()
    local rspMsg = city_pb.TMSG_CITY_GET_BUILDING_OPENGIFT_RSP()
    rspMsg.errCode = 0
    rspMsg.uSid = msg.uSid
    virtual_net.SendMessageToClient(xManMsg_pb.MSG_CITY_GET_BUILDING_OPENGIFT_RSP, rspMsg)
end

function AddSaveBuildingList(mapId, nLevel, uFlag, nState)
    if not saveBuildingList then
        saveBuildingList = {}
    end
    local addBuild = true
    for i, v in ipairs(saveBuildingList) do
        if v.uMapUnitID == mapId then
            addBuild = false
            v.nLevel = nLevel
            v.uFlag = uFlag
            v.nState = nState
        end
    end
    if addBuild then
        local buildInfo = {
            uMapUnitID = mapId,
            nLevel = nLevel,
            uFlag = uFlag,
            nState = nState
        }
        table.insert(saveBuildingList, buildInfo)
    end
end

function GetSavaBuildingInfoByMapId(mapId)
    for i, v in ipairs(saveBuildingList) do
        if v.uMapUnitID == mapId then
            return v
        end
    end
end

function SaveLocalData()
    -- 保存
    local save = {
        saveBuildingList = saveBuildingList,
        saveRepairMapIdList = saveRepairMapIdList,
        saveOpenGiftMapIdList = saveOpenGiftMapIdList
    }
    local s = json.encode(save)
    -- 调用 Unity 的 PlayerPrefs（如果你能直接访问 CS namespace）
    PlayerPrefs.SetString(BuildingSavaKey, s)
    PlayerPrefs.Save()
end

function ReadLocalData()
    local loaded = PlayerPrefs.GetString(BuildingSavaKey, "")
    if loaded ~= "" then
        local ok, resultOrErr = xpcall(function()
            local localLoadedData = json.decode(loaded)
            saveBuildingList = localLoadedData.saveBuildingList or {}
            saveRepairMapIdList = localLoadedData.saveRepairMapIdList or {}
            saveOpenGiftMapIdList = localLoadedData.saveOpenGiftMapIdList or {}
        end, function(err)
            -- 返回或打印堆栈信息，xpcall 会把这个返回值作为第二个返回值返回
            local trace = debug.traceback(err, 2)
            log.Error("xpcall decode failed: " .. tostring(trace))
        end)
    end
end

function DeleteLocalData()
    PlayerPrefs.DeleteKey(BuildingSavaKey)
end

function GetSaveRepairMapIdList()
    return saveRepairMapIdList
end

function GetSaveOpenGiftMapIdList()
    return saveOpenGiftMapIdList
end

virtual_net.RegisterMsgHandler(xManMsg_pb.MSG_CITY_REPAIR_A_BUILDING_REQ, MSG_CITY_REPAIR_A_BUILDING_REQ, city_pb.TMSG_CITY_REPAIR_A_BUILDING_REQ)
virtual_net.RegisterMsgHandler(xManMsg_pb.MSG_CITY_GET_BUILDING_OPENGIFT_REQ, MSG_CITY_GET_BUILDING_OPENGIFT_REQ, city_pb.TMSG_CITY_GET_BUILDING_OPENGIFT_REQ)