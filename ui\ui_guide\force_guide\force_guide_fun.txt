-- force_guide_fun.txt ‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐
-- author: 刘志远
-- date: 3/18/2019
-- ver: 1.0
-- desc: 新手引导-特殊处理函数
--‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐
local dump = dump
local pairs = pairs
local require = require
local print = print
local os = os
local type = type
local ipairs = ipairs
local typeof = typeof
local game_scheme = require "game_scheme"
local eventSys = require "event"
local util = require "util"
local CS = CS
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local GameObject = CS.UnityEngine.GameObject
local Canvas = CS.UnityEngine.Canvas
local ScrollRect = CS.UnityEngine.UI.ScrollRect
local log = require("log")
local tostring = tostring
local table = table
local xpcall = xpcall
local music_contorller = require "music_contorller"
module("force_guide_fun")

local sceneCheckTimer = nil
local monsterActionTimer = nil

function DisableMainSceneDrag()
    -- local new_scene_mgr = require "new_scene_mgr"
    -- new_scene_mgr.SetDragSwitchEnable(false)
end
function EnableMainSceneDrag()
    -- local new_scene_mgr = require "new_scene_mgr"
    -- new_scene_mgr.SetDragSwitchEnable(true)
end

function FindEquipment(datas)
    local id = datas.data[0]
    local ui_choice_good = require "ui_choice_good"
    local item = ui_choice_good.FindEquipment(id)
    return (not item), item
end

function TopItem(datas)
    local id = datas.data[0]
    local ui_create_array = require "ui_create_array"
    local result = ui_create_array.TopItem(id)
    ------ --print("TopItem>>>>>>>",result)
    return (not result), nil
end
function TopHero(datas)
    local result = false
    for i = 0, datas.count - 1 do
        local heroId = datas.data[i]
        local ui_hero_impl = require "ui_hero_impl"
        result = result or ui_hero_impl.TopHero(heroId)
        if result then
            break
        end
    end
    ------ --print("TopHero>>>>>>>",result)
    return (not result), nil
end

function TopGuideMail(datas)
    local mailID = datas.data[0]
    local ui_mail = require "ui_mail"
    return (not ui_mail.TopSomeMail(mailID)), nil
end

function TopFragment(datas)
    local tId = datas.data[0]
    local ui_package = require "ui_package_gw"
    return (not ui_package.TopGoods(tId)), nil
end

function ClickFold()
    local ui_menu_side = require "ui_menu_side"
    ui_menu_side.ClickFold()
end
function PackUpLeftMenu()
    local ui_lobby = require "ui_lobby"
    ui_lobby.PackUpLeftMenu()
end

function RefreshMail()
    --local net_mail_module=require"net_mail_module"
    --local mail_pb=require"mail_pb"
    --net_mail_module.GetPageMailByType_REQ(mail_pb.MailType_System)
end

function CheakMail(data)
    local mail_data = require "mail_data"
    local mDatas = mail_data.GetAllMail()
    for i = 1, #mDatas do
        if mDatas[i].mailTempletID == data.data[0] then
            return true
        end
    end
    ------ --print("CheakMail>>>>>>>>>>邮件不存在!!",data.data[0])
    return false
end
function CheckHasGoods(data)
    --[[
        --第一次忽略（因为可能下发物品无及时）
        local player_mgr=require"player_mgr"
        local roleID = player_mgr.GetPlayerRoleID()
        if  PlayerPrefs.GetInt("checkGo_"..roleID.."_"..data.data[0],0)==0 then
            PlayerPrefs.SetInt("checkGo_"..roleID.."_"..data.data[0],1)
            return true
        end
    ]]
    local player_mgr = require "player_mgr"
    local arrItemData = player_mgr.GetPacketGoods()
    local goodsId = data.data[0]
    local needNum = data.data[1]
    for k, v in ipairs(arrItemData) do
        -- --print("CheckHasGoods>>>>>>",v:GetGoodsID(),v:GetGoodsNum(),goodsId,needNum)
        if goodsId == v:GetGoodsID() and v:GetGoodsNum() >= needNum then
            -- --print("CheckHasGoods>>>>>>",v:GetGoodsID(),v:GetGoodsNum())
            return true
        end
    end
end

function CheckHeroNum(data)
    local need = data.data[0]
    local player_mgr = require "player_mgr"
    local herosData = player_mgr.GetPalPartData()

    if util.get_len(herosData) >= need then
        return true
    else
        return false
    end
end

function CheckHeroCanUpgrade()
    local hero_mgr = require "hero_mgr"
    return hero_mgr.HeroCanUpdate()
end

function CheckPassLevel(data)
    local lv = data.data[0]
    local laymain_data = require "laymain_data"
    --- --print("CheckPassLevel>>>>>>>>",laymain_data.GetPassLevel(),lv)
    return laymain_data.GetPassLevel() == lv

end

function CheckPassLevel_noEqual(data)
    local lv = data.data[0]
    local laymain_data = require "laymain_data"
    -- --print("CheckPassLevel_noEqual>>>>>>>>",laymain_data.GetPassLevel(),lv)
    return laymain_data.GetPassLevel() ~= lv

end

function CheckPassLevel_greater(data)
    local lv = data.data[0]
    local laymain_data = require "laymain_data"
    -- --print("CheckPassLevel_noEqual>>>>>>>>",laymain_data.GetPassLevel(),lv)
    return laymain_data.GetPassLevel() >= lv

end

function CheckPassLevel_lower(data)
    local lv = data.data[0]
    local laymain_data = require "laymain_data"
    -- --print("CheckPassLevel_noEqual>>>>>>>>",laymain_data.GetPassLevel(),lv)
    return laymain_data.GetPassLevel() <= lv

end

function CheckFreeLottery(data)
    local hero_lottery_data = require "hero_lottery_data"
    -- --print("CheckFreeLottery>>>>>>>>>>>",data.data[0],hero_lottery_data.HasFree(data.data[0]))
    return (not hero_lottery_data.HasFree(data.data[0]))
end

function ChangeFormationBefore()
    --开启可移动
    local ui_select_hero = require "ui_select_hero"
    ui_select_hero.TouchDisable(false)
    local p1 = "UIRoot/CanvasWithMesh/UIHeroSelect(Clone)/mainWindow/heros/myHerolayout/hero_2"
    local p2 = "UIRoot/CanvasWithMesh/UIHeroSelect(Clone)/mainWindow/heros/myHerolayout/hero_0"
    local pos1 = GameObject.Find(p1)
    local pos2 = GameObject.Find(p2)
    if pos1 and pos2 then
        --手指动画
        local ui_move_finger = require "ui_move_finger"
        ui_move_finger.ShowWithParam(pos1, pos2)

        --指定可移动框框
        ui_select_hero.SetCheckFram({ 2 }, { 0 })
    end
end
function ChangeFormationAfter()
    ------ --print("ChangeFormationAfter>>>>>>>>>>>>")
    local ui_select_hero = require "ui_select_hero"
    ui_select_hero.TouchDisable(true)
    local ui_move_finger = require "ui_move_finger"
    ui_move_finger.CloseWithParam()
end

function SetPreSlot(data)
    local ui_select_hero = require "ui_select_hero"
    ui_select_hero.preSlot = data.data[0]
end

function CloseDefeatForGuide()
    local ui_battle_defeat = require "ui_battle_defeat"
    ui_battle_defeat.CloseForGuide()
end

function CloseVictoryForGuide()
    local ui_battle_victory = require "ui_battle_victory"
    ui_battle_victory.CloseForGuide()
end

--禁止活动列表滑动
function DisDragActiList()
    local ui_activityBase = require "ui_activityBase"
    ui_activityBase.DisDragActiList()
end

function HeroSelectTouchDisable(data)
    local ui_select_hero = require "ui_select_hero"
    ui_select_hero.TouchDisable(data.data[0] == 1)
end

function Check1_2(data)
    HeroSelectTouchDisable(data)
    return (not CheckBattleArray1_2()) --返回true 是中断引导
end


--设置英雄界面是否屏蔽滑动
function SetHeroLeanTouchEable(data)
    ------ --print("SetHeroLeanTouchEable>>>>>>>>>>>>>",data.data[0]==1)
    -- local ui_hero_base = require "ui_hero_base"
    -- ui_hero_base.SetLeanTouchEable(data.data[0] == 1)
end

function DisLevelMapMove(data)
    local camera = GameObject.Find("shijieditu(Clone)/Camera")
    if camera then
        local LeanCameraMoveSmoothType = typeof(CS.Lean.Touch.LeanCameraMoveSmooth)
        if LeanCameraMoveSmoothType then
            local lean = camera:GetComponent(LeanCameraMoveSmoothType)
            if lean then
                lean.enabled = not (data.data[0] == 1)
            end
        end
    end
end

--根据升级按钮状态触发升级引导
function TriggerUpGuideByUpState()
    local ui_hero_property_impl = require "ui_hero_property_impl"
    local state = ui_hero_property_impl.GetUpBtnState()
    local force_guide_system = require "force_guide_system"
    local force_guide_event = require "force_guide_event"
    if state == ui_hero_property_impl.isUpgrade then
        --升级
        force_guide_system.TriEnterEvent(force_guide_event.tEventLvUp)
    elseif state == ui_hero_property_impl.isUpStep then
        --升阶
        force_guide_system.TriEnterEvent(force_guide_event.tEventStarLvUp)
    elseif state == ui_hero_property_impl.isMax then
        --满级
        force_guide_system.TriEnterEvent(force_guide_event.tEventMaxLv)
    elseif state == ui_hero_property_impl.isAwaken then
        --觉醒
        force_guide_system.TriEnterEvent(force_guide_event.tEventAwake)
    end
end

--英雄界面回到狩猎界面
function BackToHunt()
    local ui_window_mgr = require "ui_window_mgr"
    ui_window_mgr:UnloadModule("ui_hero_property_impl")
    ui_window_mgr:UnloadModule("ui_hero_base")

    local ui_menu_bot = require "ui_menu_bot"
    ui_menu_bot.OpenHunt()
end

--打开首充界面
local ifFristOpen = true
local isFirstEnterMonsterStage = true
function ShowFristRecharge()
    -- local ui_window_mgr = require "ui_window_mgr"
    -- if ui_window_mgr:IsModuleShown("ui_select_hero") or ui_window_mgr:IsModuleShown("ui_loading") then
    --     return
    -- end
    -- local timeStamp = os.server_time()
    -- local player_mgr = require "player_mgr"
    -- local key = player_mgr.GetPlayerRoleID().."FirstRechargeShow"
    -- PlayerPrefs.SetString(key, timeStamp)
    -- ui_window_mgr:ShowModule("ui_frist_rechage")
    -- ifFristOpen = false
    -- local force_guide_system = require "force_guide_system"
    -- local force_guide_event = require "force_guide_event"
    -- force_guide_system.TriComEvent(force_guide_event.cEventEndRecharge)
    --return true
end

function FristRechargeCanGetReward()
    -- local online_reward_data = require "online_reward_data"
    -- local data = online_reward_data.GetFirstRechargeData()
    -- local subsequentreward_pb = require "subsequentreward_pb"
    -- local type = (data and data.type ~= subsequentreward_pb.WELFARE_CSV and 1 or 3)
    -- local result = false
    -- if type == 3 then      
    --     local activity_mgr = require "activity_mgr"
    --     local activityData = activity_mgr.GetFristRechargeData()
    --     if activityData then
    --         result = ifFristOpen and (data == nil or data.nStage< 2)
    --     else
    --         result = false
    --     end
    -- else  
    --     local online_reward_data = require "online_reward_data"
    --     local data = online_reward_data.GetFirstRechargeData()
    --     result = data == nil or (data and data.times > 0)
    -- end
    -- if result then
    --     local timeStamp = os.server_time()
    --     local player_mgr = require "player_mgr"
    --     local key = player_mgr.GetPlayerRoleID().."FirstRechargeShow"
    --     local szPreShowTime = PlayerPrefs.GetString(key, "0")
    --     if timeStamp >= szPreShowTime + game_scheme:InitBattleProp_0(342).szParam.data[0] then
    --         result = true
    --     else
    --         result = false
    --     end
    -- end
    return false
end

--打开挂机领取界面
function ShowLayMainReward()
    local collect_source_scene = require "collect_source_scene"
    collect_source_scene.onTouchHandle()
end

function ChangeDotLayer(data)
    local go = GameObject.Find("UIRoot/CanvasWithMesh/UILobby(Clone)/top/Auto_TargetTask/red")
    if util.IsObjNull(go) then
        return
    end
    local canvas = go:GetComponent(typeof(Canvas))
    if util.IsObjNull(canvas) then
        return
    end

    if data.data[0] == 1 then
        canvas.sortingLayerName = "Top"
    else
        canvas.sortingLayerName = "Default"
    end
end

--确保底部按钮显示
function EnsureShowMenuBot()
    -- local log = require "log"
    -- log.Error("确保底部按钮显示 >>>>>> ")
    -- local ui_window_mgr = require "ui_window_mgr"
    -- if not ui_window_mgr:IsModuleShown("ui_menu_bot") then
    --     ui_window_mgr:CloseAll()
    --     ui_window_mgr:ShowModule("ui_menu_bot")
    -- else
    --     if ui_window_mgr:IsModuleShown("ui_frist_rechage") then
    --         ui_window_mgr:UnloadModule("ui_frist_rechage")
    --     end
    --     local force_guide_system = require "force_guide_system"
    --     local force_guide_event = require "force_guide_event"
    --     force_guide_system.TriEnterEvent(force_guide_event.tEventEnterMenuBot)
    -- end
end

--检查（阵容）
function CheckBattleArray1_2()

    --阵容
    local hook_hero_data = require "hook_hero_data"
    local hData = hook_hero_data.GetSaveHeroData()
    if hData == nil then
        return false
    end
    local pos1 = { 1, 1, 0, 0, 0, 0 }
    local pos2 = { 0, 0, 0, 0, 0, 0 }
    --取阵容
    for p, h in pairs(hData) do
        pos2[p + 1] = 1
    end
    ------ --print("CheckBattleArray1_2>>>>")
    --dump(pos1)
    --dump(pos2)
    --跟预设阵容校验
    for i = 1, #pos1 do
        if pos1[i] ~= pos2[i] then
            return false
        end
    end
    return true

end

function FindGuideHeroUI()
    local ui_hero_impl = require "ui_hero_impl"
    local pos = ui_hero_impl.FindGuideHeroPos()
    local tObj = GameObject.Find("UIRoot/CanvasWithMesh/UIHero(Clone)/MyHeroList/Viewport/Content")
    if util.IsObjNull(tObj) or tObj.transform.childCount <= pos then
        return false
    end
    local uiObj = tObj.transform:GetChild(pos)
    if uiObj then
        return false, { uiObj }
    else
        return true
    end
end

function IsDecorateOpen()
    --local hero_mgr=require"hero_mgr"
    local decoration_mgr = require "decoration_mgr"
    --local herosData=hero_mgr.GetHeroSortListBy_SortByPower()--按星级对英雄背包排序
    local freeCount = decoration_mgr.GetGuidRandFree()
    if freeCount and freeCount == 1 then
        return nil
    end
    local isOpen = false
    -- if herosData then
    --     for sid, hEntity in pairs(herosData) do
    --         local condition1,condition2,condition3 = decoration_mgr.IsDecorateOpen(hEntity.rawEntity)
    --         if hEntity.rawEntity and condition1 and condition2 and condition3 then--饰品入口开放
    --             isOpen = true
    --         end
    --     end
    -- end

    --优化计算次数
    local player_mgr = require "player_mgr"
    local arrHeroData = player_mgr.GetPalPartData()
    for key, hEntity in pairs(arrHeroData) do
        local condition1, condition2, condition3 = decoration_mgr.IsDecorateOpen(hEntity)
        if hEntity and condition1 and condition2 and condition3 then
            --饰品入口开放
            isOpen = true
            break
        end
    end

    local ui_window_mgr = require "ui_window_mgr"
    if isOpen then
        --限制次数
        local player_mgr = require "player_mgr"
        local record = PlayerPrefs.GetInt("UIDecorate_Guide" .. player_mgr.GetPlayerRoleID(), 0)
        --log.Warning("限制次数",record)
        if record == 0 then
            if ui_window_mgr:IsModuleShown("ui_merge_server_popup") then
                ui_window_mgr:UnloadModule("ui_merge_server_popup")
            end
        end
        return record == 0
    end
    --log.Warning("isOpen",isOpen)
    if isOpen then
        if ui_window_mgr:IsModuleShown("ui_merge_server_popup") then
            ui_window_mgr:UnloadModule("ui_merge_server_popup")
        end
    end
    return isOpen
end

function SetDecorateGuideCount()
    local player_mgr = require "player_mgr"
    PlayerPrefs.SetInt("UIDecorate_Guide" .. player_mgr.GetPlayerRoleID(), 1)
end

function FindGuideHeroUIOfDecorate()
    local ui_hero_impl = require "ui_hero_impl"
    local pos = ui_hero_impl.FindGuideHeroPosOfDecorate()
    local tObj = GameObject.Find("UIRoot/CanvasWithMesh/UIHero(Clone)/MyHeroList/Viewport/Content")
    local uiObj = tObj.transform:GetChild(pos)
    if uiObj then
        return false, { uiObj }
    else
        return true
    end
end

function FindGuideWantedLevel()
    -- local ui_illusion_tower = require "ui_illusion_tower"
    -- local uiObj = ui_illusion_tower.GetForceGuideObj()
    -- ----     --print("uiObj", uiObj)
    -- if uiObj then
    --     return false, { uiObj }
    -- else
    --     return true
    -- end
end

function FindGuideInstanceLevel()
    -- local ui_equipment_instance = require "ui_equipment_instance"
    -- local uiObj = ui_equipment_instance.GetForceGuideObj()
    -- if uiObj then
    --     return false, { uiObj }
    -- else
    --     return true
    -- end
end

function CheakStarNum(data)
    local need = data.data[0]
    local laymain_data = require "laymain_data"
    return laymain_data.GetStarNum() >= need
end

function SwitchHookSceneDrag(data)
    ----     --print("SwitchHookSceneDrag>>>",data)
    util.DelayCall(0.2, function(arg1, arg2, arg3)

        local param = data.data[0]
        local listObj = GameObject.Find("UIRoot/CanvasWithMesh/UIHookScene(Clone)/bgList")
        if listObj then
            local com = listObj:GetComponent(typeof(ScrollRect))
            if com then
                com.vertical = param ~= 1
            end
        end

        local go = GameObject.Find("UIRoot/CanvasWithMesh/UIMenuBot(Clone)/bottom/Auto_Fight")
        if util.IsObjNull(go) then
            return
        end
        local canvas = go:GetComponent(typeof(Canvas))
        if util.IsObjNull(canvas) then
            return
        end
        if data.data[1] and data.data[1] == 1 then
            canvas.sortingLayerName = "Top"
        else
            canvas.sortingLayerName = "Default"
        end
    end)
end

function CheckWantedGuide()
    -- local ui_illusion_tower = require "ui_illusion_tower"
    -- return ui_illusion_tower.GetChallengeTipsByType(5)
end

function CheckMazeGuide()
    local new_scene_mgr = require "new_scene_mgr"
    return new_scene_mgr.GetSceneItemDataByType(14).redDot
end

function CheckInstanceGuide(data)
    local skep_mgr = require "skep_mgr"
    local entity = skep_mgr.GetGoodsEntity(skep_mgr.const_id.equipInstanceChallege)
    if not entity then
        return false
    end
    return entity:GetGoodsNum() >= data.data[0]
end

function CheckArenaGuide(data)
    local skep_mgr = require "skep_mgr"
    local entity = skep_mgr.GetGoodsEntity(skep_mgr.const_id.arenaTicket)
    if not entity then
        return false
    end
    return entity:GetGoodsNum() >= data.data[0]
end

function CheckHomelandSlotGuide(data)
    local homeland_mgr = require "homeland_mgr"
    return homeland_mgr.GetEnergy() >= data.data[0]
end

function CloseAwake()
    local ui_window_mgr = require "ui_window_mgr"
    ui_window_mgr:UnloadModule("ui_hero_advanced")
end

function Check7dayPopup(data)
    local day7_challenge_data = require "day7_challenge_data"
    local leftTime, elapseTime = day7_challenge_data.GetSurplusTime()
    local targetDay = data.data[0]
    return elapseTime <= targetDay * 3600 * 24
end

function FindBiographyHeroItem()
    local ui_biography_tip_pop = require "ui_biography_tip_pop"
    local obj = ui_biography_tip_pop.GetGuideObj()
    if not obj then
        return true
    end
    local item = obj.gameObject
    if not item then
        return true
    end
    return false, { item }
end

function FindBiographyHero()
    local ui_biography_tip_pop = require "ui_biography_tip_pop"
    local obj, heroID = ui_biography_tip_pop.GetGuideObj()
    local ui_hero_impl = require "ui_hero_impl"
    local item = ui_hero_impl.GetItemObjByHeroID(heroID)
    if not item then
        return true
    end
    local obj = item.gameObject
    if not obj then
        return true
    end
    return false, { obj }
end
local waitMinigameSelectInitTimer
local SceneValidTimer
function OpenFuzzleGameFormHunt()
    if true then
        return false
    end
    --print("=========OpenFuzzleGameFormHunt=========")

    local minigame_select_manager = require "minigame_select_manager"
    local puzzlegame_mgr = require "puzzlegame_mgr"
    if minigame_select_manager.IsMinigameSelectMode() then
        if not puzzlegame_mgr.GetMinigameSelectDataUpdate() then
            if not waitMinigameSelectInitTimer then
                waitMinigameSelectInitTimer = util.IntervalCall(0.2, function()
                    if puzzlegame_mgr.GetMinigameSelectDataUpdate() then
                        OpenFuzzleGameFormHunt()
                        return true
                    end
                end)
            end
            return
        end
    end

    local ui_window_mgr = require "ui_window_mgr"

    local cid, levelid = 0
    local currentChapters = puzzlegame_mgr.GetCurrentChapters()
    for _, cfg in ipairs(currentChapters) do
        if cfg.AdvertisingID == puzzlegame_mgr.getMiniGameType() then
            cid = cfg.ChaptersID
            levelid = cfg.arrLevel.data[0]
            break
        end
    end
    --  --print("levelid:",levelid)
    local minilevelcfg = puzzlegame_mgr.GetTinyGameLevelInfo(levelid)
    local event = require "event"
    if minilevelcfg and cid > 0 and levelid > 0 then
        if puzzlegame_mgr.IsSceneValid() then
            ui_window_mgr:ShowModule("ui_puzzle_game_level")
            ui_window_mgr:ShowModule("ui_mini_game_loading", function()
                puzzlegame_mgr.OpenMiniGame(levelid)
            end)



            -- 历史打点
            if minilevelcfg.LevelType == puzzlegame_mgr.MiniGameType.Tower then
                event.Trigger(event.GAME_EVENT_REPORT, "yztower")
            elseif minilevelcfg.LevelType == puzzlegame_mgr.MiniGameType.Puzzle then
                local q1sdk = require "q1sdk"
                q1sdk.AdjustAndFirebaseReport("firstopen_puzzle")
            end
        else
            if SceneValidTimer then
                return
            end
            SceneValidTimer = util.IntervalCall(0.2, function()
                log.Warning("[guide] IsSceneValid", puzzlegame_mgr.IsSceneValid())
                if puzzlegame_mgr.IsSceneValid() then
                    ui_window_mgr:ShowModule("ui_puzzle_game_level")
                    ui_window_mgr:ShowModule("ui_mini_game_loading", function()
                        puzzlegame_mgr.OpenMiniGame(levelid)
                    end)
                    
                    -- 历史打点
                    if minilevelcfg.LevelType == puzzlegame_mgr.MiniGameType.Tower then
                        event.Trigger(event.GAME_EVENT_REPORT, "yztower")
                    elseif minilevelcfg.LevelType == puzzlegame_mgr.MiniGameType.Puzzle then
                        local q1sdk = require "q1sdk"
                        q1sdk.AdjustAndFirebaseReport("firstopen_puzzle")
                    end
                    SceneValidTimer = nil
                    return true
                end
            end)
        end
    else
        local param = "{\"miniGameType\":" .. puzzlegame_mgr.getMiniGameType() .. ",\"ChaptersID\":" .. cid .. ",\"levelid\":\"" .. (levelid or 0) .. "\"}"
        event.Trigger(event.GAME_EVENT_REPORT, "OpenFuzzleGameFail", param)
    end
end

function ShowMiniGame(data)
    local ui_window_mgr = require "ui_window_mgr"
    if not ui_window_mgr:IsModuleShown("ui_puzzle_game_level") and not ui_window_mgr:GetWindowObj("ui_puzzle_game_level") then
        local window = ui_window_mgr:ShowModule("ui_puzzle_game_level")
        if window and data and data.count > 0 then
            window.ChangePageByIndex(data.data[0])
        end
    end
end

function ShowHunt()
    local ui_window_mgr = require "ui_window_mgr"
    ui_window_mgr.ShowModule("ui_lobby")
end

function SetPuzzleGameFinger(data)
    local tObj = GameObject.Find("UIRoot/CanvasWithMesh/UILevel5(Clone)/level(Clone)/pullTab/pullTabNode_" .. data.data[0] .. "/finger")

    if util.IsObjNull(tObj) then
        print("没有找到UIRoot/CanvasWithMesh/UILevel5(Clone)/level(Clone)/pullTab/pullTabNode_" .. data.data[0] .. "/finger")
        return
    end

    if data.data[2] and data.data[2] > 0 then
        util.DelayOneCall("SetPuzzleGameFinger", function()
            if util.IsObjNull(tObj) then
                return
            end
            if data.data[1] == 0 then
                tObj.gameObject:SetActive(true)
            else
                tObj.gameObject:SetActive(false)
            end
        end, data.data[2])
    else
        if data.data[1] == 0 then
            tObj.gameObject:SetActive(true)
        else
            tObj.gameObject:SetActive(false)
        end
    end
    return false
end

function HideHeroSelect(data)
    -- dump(data)
    local ui_hero_reward_select = require "ui_hero_reward_select"
    ui_hero_reward_select.SetisShow(data.data[0] == 1)
end

function ReturnHookFormPuzzleGame()
    local ui_window_mgr = require "ui_window_mgr"
    if ui_window_mgr:IsModuleShown("ui_puzzle_game_level") then
        ui_window_mgr:UnloadModule("ui_puzzle_game_level")
    end
    local menu_bot_data = require "menu_bot_data"
    menu_bot_data.OpenHuntPage()
    local force_guide_system = require "force_guide_system"
    local force_guide_event = require "force_guide_event"
    force_guide_system.TriEnterEvent(force_guide_event.tEventEnterHunt)
    local event = require "event"
    event.Trigger(event.CHECK_HERO_REWARD)
end

function CheckMiniGame(data)
    --  --print("==================CheckMiniGame=============================")
    if true then
        return false
    end
    local puzzlegame_mgr = require "puzzlegame_mgr"
    local levelid = 0
    -- dump(data)
    if data.data[1] and data.data[1] > 0 then
        local currentChapters = puzzlegame_mgr.GetCurrentChapters()
        for _, cfg in ipairs(currentChapters) do
            if cfg.AdvertisingID == data.data[0] then
                cfg = cfg.arrLevel.data[data.data[1]]
                break
            end
        end
        local isFinish = puzzlegame_mgr.getIsFinishByLevelId(levelid)
        --  --print("puzzlegame_mgr.IsShowByMiniGameType(data.data[0]):",puzzlegame_mgr.IsShowByMiniGameType(data.data[0]),"levelid:",levelid,"isFinish:",isFinish)
        return puzzlegame_mgr.IsShowByMiniGameType(data.data[0]) and not isFinish
    end
    return puzzlegame_mgr.IsShowByMiniGameType(data.data[0])
end

function CheckIsOpenMiniGame()
    local puzzlegame_mgr = require "puzzlegame_mgr"
    return puzzlegame_mgr.IsUseMiniGameGuide()
end

function CheckOpenGameLobby()
    return false
end

function CheckCloseGameLobby()
    return true
end

function CheckFristRecharge()
    local ui_window_mgr = require "ui_window_mgr"
    if ui_window_mgr:IsModuleShown("ui_frist_rechage") then
        ui_window_mgr:UnloadModule("ui_frist_rechage")
    end
end

function CheckIsOpenMiniGameExceptPuzzleTower()
    local puzzlegame_mgr = require "puzzlegame_mgr"
    return puzzlegame_mgr.IsUseMiniGameGuide() and puzzlegame_mgr.getMiniGameType() ~= puzzlegame_mgr.MiniGameType.Puzzle and puzzlegame_mgr.getMiniGameType() ~= puzzlegame_mgr.MiniGameType.Tower
end

function TriggerTowerGuide()
    local player_mgr = require "player_mgr"
    -- PlayerPrefs.SetInt("UIMainTower_Guide"..player_mgr.GetPlayerRoleID(), 0)
    local step = PlayerPrefs.GetInt("UIMainTower_Guide" .. player_mgr.GetPlayerRoleID(), 0)
    if step == 0 then
        local p1 = "UIRoot/CanvasWithMesh/UIMainTower(Clone)/towerList/tower(Clone)/item/playerNode"
        local p2 = "UIRoot/CanvasWithMesh/UIMainTower(Clone)/towerList/tower1/item1/enemy/enemyNode4"
        local pos1 = GameObject.Find(p1)
        local pos2 = GameObject.Find(p2)
        if pos1 and pos2 then
            --手指动画
            local ui_move_finger = require "ui_move_finger"
            ui_move_finger.ShowWithParam(pos1, pos2)
        end
    elseif step == 1 then
        local p1 = "UIRoot/CanvasWithMesh/UIMainTower(Clone)/towerList/tower(Clone)/item/playerNode"
        local p2 = "UIRoot/CanvasWithMesh/UIMainTower(Clone)/towerList/tower1/item2/enemy/enemyNode4"
        local pos1 = GameObject.Find(p1)
        local pos2 = GameObject.Find(p2)
        if pos1 and pos2 then
            --手指动画
            local ui_move_finger = require "ui_move_finger"
            ui_move_finger.ShowWithParam(pos1, pos2)
        end
    end
end

function TriggerForceGuide()
    local ui_wish_hero = require "ui_wish_hero"
    ui_wish_hero.TriggerForceGuide()
end

function BlessTriggerForceGuide()
    local ui_bless_hero = require "ui_bless_hero"
    ui_bless_hero.TriggerForceGuide()
end

function SetAniMaskState()
    local ui_hero_summon = require "ui_hero_summon"
    ui_hero_summon.SetAniMaskState(false)
end

function CheckSelectedHeroNumLessThan(data)
    local ui_select_hero = require "ui_select_hero"
    local selected = ui_select_hero.GetSelectedHero()
    local util = require "util"
    return util.get_len(selected) < data.data[0]
end

function CheckHeroCallTotalCount(data)
    local hero_lottery_data = require "hero_lottery_data"
    local count = hero_lottery_data.GetCumulateiveLotteryCount()
    return count >= data.data[0]
end

function IsBlessHeroOpen()
    local cfg = game_scheme:InitBattleProp_0(945)
    local player_mgr = require "player_mgr"
    local createTime = player_mgr.GetRoleCreateTime()--创角时间
    local hadBennCreatedTime = os.difftime(os.server_time(), createTime)
    local isOpen = false
    if cfg then
        local openTime = cfg.szParam.data[0] * 24 * 60 * 60
        local callLimit1 = cfg.szParam.data[1]
        local callLimit2 = cfg.szParam.data[2]
        local hero_lottery_data = require "hero_lottery_data"
        local totlaCount = hero_lottery_data.GetCumulateiveLotteryCount()
        if hadBennCreatedTime <= openTime then
            -- 判断是否达到开放时间
            if callLimit1 <= totlaCount then
                isOpen = true
            end
        else
            if callLimit2 <= totlaCount then
                isOpen = true
            end
        end
    end
    --print("isOpen",isOpen)
    return isOpen
end

function OpenHeroSelect()
    util.DelayCallOnce(0.2, function()
        local hero_reward_mgr = require "hero_reward_mgr"
        hero_reward_mgr.ShowHeroSelect()
    end)
end

function IsModuleShown(moduleName)
    local ui_window_mgr = require "ui_window_mgr"
    moduleName = moduleName or ""
    return ui_window_mgr:IsModuleShown(moduleName)
end

function StartMiniGame(data)
    local puzzlegame_mgr = require "puzzlegame_mgr"
    puzzlegame_mgr.OpenMiniGame(puzzlegame_mgr.GetGameLevelIds()[data.data[0]])
end

local function CheckNoviceEventOpen()
    local gw_home_novice_chapter_data = require "gw_home_novice_chapter_data"
    if gw_home_novice_chapter_data.GetNoviceAndCheckPassLevel() then
        return false
    end
    return true
end

function SetStartTempleGodEquipListGuide()
    -- local force_guide_system = require "force_guide_system"
    -- local force_guide_event = require "force_guide_event"
    -- local star_temple_bag_data = require "star_temple_bag_data"
    -- if star_temple_bag_data.IsGodEquipWearable() then
    --     force_guide_system.TriEnterEvent(force_guide_event.tEventEnterStarTempleGodEquipList)
    -- end
end

function IsMinigameSelectMode()
    local minigame_select_manager = require "minigame_select_manager"
    return minigame_select_manager.IsMinigameSelectMode() and not minigame_select_manager.GetCurrentMinigameElement()
end

function ShowFirstSelectMinigame()
    local ui_window_mgr = require "ui_window_mgr"
    local ui_first_minigame_select = require "ui_first_minigame_select"
    ui_first_minigame_select.SetMode(ui_first_minigame_select.SelectMode.NewPlayer, function()
        ui_window_mgr:ShowModule("ui_mini_game_loading")
    end)
    ui_window_mgr:ShowModule("ui_first_minigame_select", nil, function()
        local force_guide_system = require "force_guide_system"
        local force_guide_event = require "force_guide_event"
        force_guide_system.TriEnterEvent(force_guide_event.tEventEnterHunt)
    end)
end

local Func_Table = {}
function Func_Table.CheckGuildIsOpen_38()
    return true
end
function Func_Table.CheckHomeLevelUp(data)
    if data.count <= 0 then
        return
    end
    local needLevel = data.data[0]
    local gw_home_building_data = require "gw_home_building_data"
    local level = gw_home_building_data.GetBuildingMainLevel()
    if level == needLevel then
        return true
    end
    return false
end

function Func_Table.CheckHomeLevelUp_2()
    local gw_home_building_data = require "gw_home_building_data"
    local level = gw_home_building_data.GetBuildingMainLevel()
    if level ~= 2 then
        return false
    end
    return Func_Table.CheckFinishEvent_8()
end

local function JumpCamera(mapId, time, isLock, callBack)
    local mapCfg = game_scheme:BuildMaincityMap_0(mapId)
    if not mapCfg then
        return
    end
    if not time then
        time = -999
    end
    local gw_home_camera_util = require "gw_home_camera_util"
    gw_home_camera_util.GWCameraDoMoveToGridPos(mapCfg.x, mapCfg.y, time, isLock, callBack)
end



--怪物冲进家里的表现
function Func_Table.MonsterEnterHomeShow(data)
    local ui_window_mgr = require "ui_window_mgr"
    ui_window_mgr:UnloadModule("ui_bs_operate")
    if data.count <= 0 then
        return
    end
    local gw_home_scene_util = require "gw_home_scene_util"
    ui_window_mgr:ShowModule("guide_common_mask")
    ui_window_mgr:UnloadModule("ui_chat_main_new")
    local xpcallFun = function()
        gw_home_scene_util.SetCameraOperate(false)
        gw_home_scene_util.SetHomeClickState(false)
        local sand_ui_event_define = require "sand_ui_event_define"
        --关闭主界面
        eventSys.Trigger(sand_ui_event_define.GW_CLOSE_SAND_MAIN_NO_TOP)
        local gw_home_novice_chapter_data = require "gw_home_novice_chapter_data"
        local monster = gw_home_novice_chapter_data.GetEventEntity(8)
        if not monster then
            return
        end
        --monster:Show()
        local eventCfg = game_scheme:BuildPreProcess_0(8)

        monster:SetGridPos(eventCfg.SpecialParam1.data[0], eventCfg.SpecialParam1.data[1])
        monster:ShowModel(function()
            monster:RunFunction("MoveNextEvent", 1000, function()
                monster:SetGridPos(eventCfg.SpecialParam1.data[0], eventCfg.SpecialParam1.data[1])
                monster:Show()
                local gw_story_mgr = require "gw_story_mgr"
                local talkCfgId = 40001
                gw_story_mgr.PlayStoryById(talkCfgId, function()
                    local force_guide_system = require "force_guide_system"
                    local force_guide_event = require "force_guide_event"
                    force_guide_system.TriComEvent(force_guide_event.cEventMonsterShow)
                    force_guide_system.TriEnterEvent(force_guide_event.tEventMonsterClick)
                end, function()
                    ui_window_mgr:UnloadModule("guide_common_mask")
                end)
            end)
        end)

        --monster:SetModelVisible(false)
        local mapId = data.data[0]
        local mapCfg = game_scheme:BuildMaincityMap_0(mapId)
        if not mapCfg then
            return
        end

        if isFirstEnterMonsterStage then
            isFirstEnterMonsterStage = false
            if monsterActionTimer then
                util.RemoveDelayCall(monsterActionTimer)
                monsterActionTimer = nil
            end
            --monster:RunFunction("MoveNextEvent",1000,function()
            --    monster:Show()
            --    local gw_story_mgr = require "gw_story_mgr"
            --    local talkCfgId = 40001
            --    gw_story_mgr.PlayStoryById(talkCfgId, function()
            --        local force_guide_system = require "force_guide_system"
            --        local force_guide_event = require "force_guide_event"
            --        force_guide_system.TriComEvent(force_guide_event.cEventMonsterShow)
            --        force_guide_system.TriEnterEvent(force_guide_event.tEventMonsterClick)
            --    end)
            --end)

            --monsterActionTimer = util.DelayCallOnce(1,function()
            --    monster:RunFunction("MoveNextEvent",1000,function()
            --        monster:Show()
            --        local gw_story_mgr = require "gw_story_mgr"
            --        local talkCfgId = 40001
            --        gw_story_mgr.PlayStoryById(talkCfgId, function()
            --            local force_guide_system = require "force_guide_system"
            --            local force_guide_event = require "force_guide_event"
            --            force_guide_system.TriComEvent(force_guide_event.cEventMonsterShow)
            --            force_guide_system.TriEnterEvent(force_guide_event.tEventMonsterClick)
            --        end)
            --    end)
            --    util.RemoveDelayCall(monsterActionTimer)
            --    monsterActionTimer = nil
            --end)

            --JumpCamera(mapId,500,nil,function()
            --    monster:SetModelVisible(true)
            --    monster:RunFunction("MoveNextEvent",1000,function()
            --        monster:Show()
            --        local gw_story_mgr = require "gw_story_mgr"
            --        local talkCfgId = 40001
            --        gw_story_mgr.PlayStoryById(talkCfgId, function()
            --            local force_guide_system = require "force_guide_system"
            --            local force_guide_event = require "force_guide_event"
            --            force_guide_system.TriComEvent(force_guide_event.cEventMonsterShow)
            --            force_guide_system.TriEnterEvent(force_guide_event.tEventMonsterClick)
            --        end)
            --    end)
            --end)
        else
            local GWG = require "gw_g"
            GWG.GWMgr.ShowCurScene()
            monster:Show()
            local gw_story_mgr = require "gw_story_mgr"
            local talkCfgId = 40001
            gw_story_mgr.PlayStoryById(talkCfgId, function()
                local force_guide_system = require "force_guide_system"
                local force_guide_event = require "force_guide_event"
                force_guide_system.TriComEvent(force_guide_event.cEventMonsterShow)
                force_guide_system.TriEnterEvent(force_guide_event.tEventMonsterClick)
            end, function()
                ui_window_mgr:UnloadModule("guide_common_mask")
            end)
        end
    end

    local f, res = xpcall(xpcallFun, function(err)
        log.Error("MonsterEnterHomeShow ", err)
    end)
    if not f then
        ui_window_mgr:UnloadModule("guide_common_mask")
        gw_home_scene_util.SetCameraOperate(true)
        gw_home_scene_util.SetHomeClickState(true)
    end
end

--检测通关Event8
function Func_Table.CheckFinishEvent_8()
    local gw_home_novice_chapter_data = require "gw_home_novice_chapter_data"
    local nextEventId = gw_home_novice_chapter_data:GetCurrentEventPoint()
    return nextEventId == 8
end

function Func_Table.CheckFinishEvent(data)
    if not CheckNoviceEventOpen() then
        return false
    end
    local gw_home_novice_chapter_data = require "gw_home_novice_chapter_data"
    local nextEventId = gw_home_novice_chapter_data:GetCurrentEventPoint()
    return nextEventId == data.data[0]
end

---@public 检测通关Event 和 修复repair 是否完成
function Func_Table.CheckFinishEventRepair(data)
    if not CheckNoviceEventOpen() then
        return false
    end
    local gw_home_novice_chapter_data = require "gw_home_novice_chapter_data"
    local nextEventId = gw_home_novice_chapter_data:GetCurrentEventPoint()
    local isPass = (nextEventId == data.data[0])
    for i = 1, data.count - 1 do
        local isCut = Func_Table.CheckFinishCut(data.data[i])
        if not isCut then
            return false
        end
    end
    return isPass
end

--高亮点击3d怪物
function Func_Table.GetMonsterObjById(data)
    local GWG = require "gw_g"
    local gw_home_novice_chapter_data = require "gw_home_novice_chapter_data"
    local monster = gw_home_novice_chapter_data.GetEventEntity(8)
    local ui_window_mgr = require "ui_window_mgr"
    ui_window_mgr:UnloadModule("ui_bs_operate")
    if not monster then
        return
    end
    local monsters = monster:GetModelComp()
    local objTable = {}
    if monster and not util.IsObjNull(monster.gameObject) then
        table.insert(objTable, monster.gameObject)
    end
    if monsters and not util.IsObjNull(monsters.gameObject) then
        table.insert(objTable, monsters.gameObject)
    end
    local fightHud = monster:GetFightHud()
    if fightHud and not util.IsObjNull(fightHud.gameObject) then
        table.insert(objTable, fightHud.gameObject)
    end
    local camera = GWG.GWMgr.comp:GetCurrentCameraComponent()
    if monster then
        return false, {
            camera = camera,
            objList = objTable,
            iTextureSize = nil,
            offRect = { x = -50, y = 0, width = 300, height = 300, position = monster.gameObject.transform.position },
            clickBack = function()
                local force_guide_system = require "force_guide_system"
                local force_guide_event = require "force_guide_event"
                force_guide_system.TriComEvent(force_guide_event.cEventMonsterClick)
                monster:StartEvent()
            end
        }
    end
    return false, {}
end



--解救人质相关--
--相机移动到大本
function Func_Table.MoveToHome(bFunParam)
    local mapId = bFunParam.data[0]
    local time = bFunParam.data[1]
    local GWG = require "gw_g"
    GWG.GWMgr.ShowCurScene()
    local gw_story_mgr = require "gw_story_mgr"
    local talkCfgId = 40002
    local ui_window_mgr = require "ui_window_mgr"
    ui_window_mgr:UnloadModule("ui_bs_operate")
    local gw_home_scene_util = require "gw_home_scene_util"
    local function playStoryCallBack()
        gw_home_scene_util.SetCameraOperate(false)
        gw_home_scene_util.SetHomeClickState(false)
        ui_window_mgr:ShowModule("guide_common_mask")
        local gw_home_novice_chapter_data = require "gw_home_novice_chapter_data"
        local monster = gw_home_novice_chapter_data.GetEventEntity(8)
        if monster then
            monster:Hide()
            monster:DestroyModel()
        end

        local mapCfg = game_scheme:BuildMaincityMap_0(mapId)
        if not mapCfg then
            return
        end
        JumpCamera(mapId, time, nil, function()
            local force_guide_system = require "force_guide_system"
            local force_guide_event = require "force_guide_event"
            force_guide_system.TriEnterEvent(force_guide_event.tEventBomberHomeClick)
        end)
    end
    local function xpcallFunc()
        local f, res = xpcall(playStoryCallBack, function(err)
            log.Error("MoveToHome ", err)
        end)
        if not f then
            ui_window_mgr:UnloadModule("guide_common_mask")
            gw_home_scene_util.SetCameraOperate(true)
            gw_home_scene_util.SetHomeClickState(true)
        end
    end
    gw_story_mgr.PlayStoryById(talkCfgId, xpcallFunc)
end
function Func_Table.GuideHomePop()
    local ui_window_mgr = require "ui_window_mgr"
    ui_window_mgr:UnloadModule("ui_bs_operate")
    local gw_home_scene_util = require "gw_home_scene_util"
    gw_home_scene_util.SetCameraOperate(true)
    gw_home_scene_util.SetHomeClickState(true)
    ui_window_mgr:UnloadModule("guide_common_mask")
    local GWG = require "gw_g"
    local GWConst = require "gw_const"
    --log.Error("引导气泡")
    local data = GWG.GWHomeMgr.buildingData.GetMinLevelBuildingDataByBuildingID(1000)
    local compData = GWG.GWHomeMgr.buildingData.GetBuildingCompBySid(data.uSid)
    local entity = compData:GetBubbleEntity(GWConst.EHomeBubbleEntityType.HostageRescue)
    if entity then
        entity:OnShow()
        entity.gameObject:SetActive(true)
        return nil, { entity.gameObject }
    end
    return nil, nil
end

function Func_Table.BomberManOpenTalk(data)
    local talkType = data.data[0]
    local talkId = data.data[1]
    local delay = data.data[2]
    util.DelayCallOnce(delay / 100, function()
        local gw_story_mgr = require "gw_story_mgr"
        local talkCfgId = 40003
        gw_story_mgr.PlayStoryById(talkCfgId, function()
            local force_guide_system = require "force_guide_system"
            local force_guide_event = require "force_guide_event"
            force_guide_system.TriEnterEvent(force_guide_event.tEventBomberBtnClick)
        end)
    end)
    return false, true
end

--弹窗
function Func_Table.BomberManPop(data)
    local ui_window_mgr = require "ui_window_mgr"
    local function xpcallFun()
        ui_window_mgr:ShowModule("guide_common_mask")
        local function onHideCallBack()
            ui_window_mgr:UnloadModule("ui_bs_operate")
            ui_window_mgr:ShowModule("guide_common_mask")
            util.DelayCallOnce(1, function()
                ui_window_mgr:UnloadModule("ui_bs_operate")
                local force_guide_system = require "force_guide_system"
                local force_guide_event = require "force_guide_event"
                force_guide_system.TriComEvent(force_guide_event.cEventBomberPop)
                local gw_story_mgr = require "gw_story_mgr"
                local talkCfgId = 40004
                gw_story_mgr.PlayStoryById(talkCfgId, function()
                    ui_window_mgr:ShowModule("guide_common_mask")
                    force_guide_system.TriEnterEvent(force_guide_event.tEventEnterNewHunt)
                end, function()
                    ui_window_mgr:UnloadModule("guide_common_mask")
                end)
            end)
        end
        util.DelayCallOnce(0.5, function()
            ui_window_mgr:ShowModule("ui_bomberman_tips", function()
                ui_window_mgr:UnloadModule("guide_common_mask")
            end, onHideCallBack, { notGoBtnShow = true, isFirst = true })
        end)
    end
    local f, res = xpcall(xpcallFun, function(err)
        log.Error("BomberManPop ", err)
    end)
    if not f then
        ui_window_mgr:UnloadModule("guide_common_mask")
    end
end

function Func_Table.JumpCameraToPosParams(param)
    if param.count <= 0 then
        return
    end
    local x = param.data[0]
    local y = param.data[1]
    local time = param.data[2]
    if not time then
        time = -999
    end
    local gw_home_camera_util = require "gw_home_camera_util"
    gw_home_camera_util.GWCameraDoMoveToGridPos(x, y, time)
end

function Func_Table.JumpCameraToParams(param)
    if param.count <= 0 then
        return
    end
    local mapId = param.data[0]
    local mapCfg = game_scheme:BuildMaincityMap_0(mapId)
    if not mapCfg then
        return
    end
    local time = param.data[1]
    if not time then
        time = -999
    end
    JumpCamera(mapId, time, nil, nil)
end
--region ------ 侦察机 ------
function Func_Table.BuildPlaneUI()
    local ui_window_mgr = require "ui_window_mgr"
    ui_window_mgr:UnloadModule("ui_bs_operate")
    ui_window_mgr:UnloadModule("ui_bomberman_tips")
    ui_window_mgr:UnloadModule("guide_common_mask")
end
--建筑队列界面
function Func_Table.BuildPlaneUIClick()
    
end

function Func_Table.GuidePlaneBuildingCutting(data)
    if not data then
        return
    end
    local buildType = data.data[0]
    local gw_home_building_data = require "gw_home_building_data"
    local compData = gw_home_building_data.GetBuildingCompByType(buildType)
    if not compData then
        return nil, nil
    end
    local cutComp = compData:GetCutComp()
    if cutComp and not util.IsObjNull(cutComp.gameObject) then
        return false, { cutComp.gameObject, cutComp }
    end
    return false, nil
end

local BuildPlaneShowAniFlow = nil 
function Func_Table.BuildPlaneShow()
    local gw_novice_comp_director_util = require "gw_novice_comp_director_util"
    local gw_novice_director_const = require "gw_novice_director_const"

    local GWG = require "gw_g"
    GWG.GWAdmin.HomeCameraUtil.GWCameraDoMoveToGridPos(57, 50, -999)
    GWG.GWAdmin.HomeSceneUtil.SetCameraDxf(1500, 0)
    local cameraEntity = GWG.GWMgr.comp:GetCameraComponent()
    local CameraTr = nil
    local UAV = nil
    if cameraEntity then
        CameraTr = cameraEntity.transform
    end
    local gw_home_building_data = require "gw_home_building_data"
    local buildList = gw_home_building_data.GetBuildingDataListByBuildingID(30000)
    if buildList then
        for i, v in pairs(buildList) do
            local buildEntity = gw_home_building_data.GetBuildingCompBySid(v.uSid)
            if buildEntity then
                UAV = buildEntity:GetModel()
                UAV_D = buildEntity:GetModelAnimator()
                buildEntity:ShowNotSimpleModel()
                music_contorller.PlayFxAudio(319004)
                break
            end
        end
    end
    if util.IsObjNull(UAV) or util.IsObjNull(CameraTr) or util.IsObjNull(UAV_D) then
        log.Error("侦察机飞行失败")
        return
    end
    local mapParam = {
        ["Camera"] = CameraTr, --获取摄像机
        ["UAV"] = UAV, --获取飞机模型
        ["UAV_D"] = UAV_D, --获取飞机模型动画
    }

    local sand_ui_event_define = require "sand_ui_event_define"
    eventSys.Trigger(sand_ui_event_define.GW_CLOSE_SAND_MAIN_NO_TOP)
    local id, director = gw_novice_comp_director_util.InitDirectorComponent(gw_novice_director_const.NoviceDirectorType.Reconnaissance, mapParam)
    local ui_window_mgr = require "ui_window_mgr"
    ui_window_mgr:CloseAll()
    ui_window_mgr:ShowModule("guide_common_mask")
    local function TimeEndCallback()
        if buildList then
            for i, v in pairs(buildList) do
                local buildEntity = gw_home_building_data.GetBuildingCompBySid(v.uSid)
                if buildEntity then
                    UAV = buildEntity:GetModel()
                    buildEntity:HideNotSimpleModel()
                    break
                end
            end
        end
        gw_novice_comp_director_util.RemoveDirectorComponent(director)
        local gw_story_mgr = require "gw_story_mgr"
        local talkCfgId = 40005
        local defaultZoom = 1200
        GWG.GWAdmin.HomeSceneUtil.SetCameraDxf(defaultZoom, -999)
        GWG.GWAdmin.HomeCameraUtil.GWCameraDoMoveToGridPos(56, 52, -999)
        gw_story_mgr.PlayStoryById(talkCfgId, function()
            ui_window_mgr:ShowModule("guide_common_mask")
            eventSys.Trigger(sand_ui_event_define.GW_OPEN_SAND_MAIN)
            local moveCameraCallBack = function()
                local gw_home_chapter_data = require "gw_home_chapter_data"
                gw_home_chapter_data.NoviceUnLockEventPerform()
                local force_guide_system = require "force_guide_system"
                local force_guide_event = require "force_guide_event"
                force_guide_system.TriComEvent(force_guide_event.cEventBuildPlaneShow)
                ui_window_mgr:ShowModule("guide_common_mask")
            end
            defaultZoom = GWG.GWAdmin.HomeCommonUtil.GetDefaultCameraZoom()
            GWG.GWAdmin.HomeSceneUtil.SetCameraDxf(defaultZoom, 750)
            GWG.GWAdmin.HomeCameraUtil.GWCameraDoMoveToGridPos(65, 41, 750, false, moveCameraCallBack)
        end, function()
            ui_window_mgr:UnloadModule("guide_common_mask")
        end)
    end 
    
    local function xpcallFun()
        local flow = BuildPlaneShowAniFlow and BuildPlaneShowAniFlow.flow
        if flow then return end
        
        if BuildPlaneShowAniFlow then
            BuildPlaneShowAniFlow:Dispose()
            BuildPlaneShowAniFlow = nil
        end
        local ani_flow = require "ani_flow"
        BuildPlaneShowAniFlow = ani_flow()
        local flow = {
            {
                105, -- eventID
                1.5, -- 开始时间点
                0, -- 持续时间
            },
            {
                113, -- eventID
                3, -- 开始时间点
                0, -- 持续时间
            },
            {
                120, -- eventID
                5, -- 开始时间点
                0, -- 持续时间
            },
            {
                125, -- eventID
                5.5, -- 开始时间点
                0, -- 持续时间
            },
            {
                130, -- eventID
                6, -- 开始时间点
                0, -- 持续时间
            },
            {
                nil,
                10.5,
                0,
            },
            endCallback = function()
                TimeEndCallback()
                if BuildPlaneShowAniFlow then
                    BuildPlaneShowAniFlow:Dispose()
                    BuildPlaneShowAniFlow = nil
                end
            end,
        }
        local refreshEventData = function(eventId)
            local gw_home_chapter_data = require "gw_home_chapter_data"
            local entity = gw_home_chapter_data.GetEventEntity(eventId)
            if not entity then
                return
            end
            entity:DisposeBubble()
            entity:RefreshData()
        end
        local register_func = function(event, state)
            local eventId = state[1][1]
            if event ~= "finish_ani" then
                return
            end
            if not eventId then
                return
            end
            refreshEventData(eventId)
        end
        BuildPlaneShowAniFlow:InitFlow(flow, register_func)
        BuildPlaneShowAniFlow:Play()
    end
    local f, res = xpcall(xpcallFun, function(err)
        log.Error("BuildPlaneShow ", err)
    end)
    if not f then
        ui_window_mgr:UnloadModule("guide_common_mask")
    end
end

function Func_Table.CheckGuidePlane()
    local gw_home_building_data = require "gw_home_building_data"
    local level = gw_home_building_data.GetBuildingMainLevel()
    if level ~= 2 then
        return false
    end
    local gw_home_cfg_util = require "gw_home_cfg_util"
    local gw_const = require "gw_const"
    local isCanBuild = gw_home_cfg_util.GetOneCanBuildByTypeId(gw_const.enBuildingType.enBuildingType_Reconnaissance)
    return isCanBuild
end

function Func_Table.CheckGuidePlane2(data)
    if not data then
        return
    end
    local buildId = data.data[0]
    local gw_home_building_data = require "gw_home_building_data"
    local buildList = gw_home_building_data.GetBuildingDataListByBuildingID(buildId)
    local index = util.get_len(buildList)
    if index > 0 and index <= 1 then
        for i, serData in pairs(buildList) do
            if serData and serData.nBuildingID == 30000 and serData.nLevel == 0 then
                return true
            end
        end
    end
    return false
end

--endregion

function Func_Table.CutJumpCameraAndMonsterObj(param)
    if param.count <= 0 then
        return
    end
    local mapId = param.data[0]
    local mapCfg = game_scheme:BuildMaincityMap_0(mapId)
    if not mapCfg then
        return
    end
    local time = param.data[1]
    if not time then
        time = -999
    end
    
    JumpCamera(mapId, time, nil, nil)
    
    local id = param.data[2]
    if id then
        local gw_home_novice_chapter_data = require "gw_home_novice_chapter_data"
        local entity = gw_home_novice_chapter_data.GetEventEntity(id)
        if entity then
            return false, { entity.gameObject, entity }
        end
    end
    return false, nil
end

--高亮点击3d怪物
function Func_Table.GetMonsterObjById2(data)
    local id = data.data[0]
    if id then
        local gw_home_novice_chapter_data = require "gw_home_novice_chapter_data"
        local entity = gw_home_novice_chapter_data.GetEventEntity(id)
        if entity then
            return false, { entity.gameObject, entity }
        end
    end
    return false, nil
end

function Func_Table.NoviceEvent2CameraMove()
    --镜头移动 --显示障碍物
    --镜头移动 --显示障碍物
    local gw_home_camera_util = require "gw_home_camera_util"
    local gw_home_novice_util = require "gw_home_novice_util"
    local ui_window_mgr = require "ui_window_mgr"
    local gw_ed = require "gw_ed"
    local GWG = require "gw_g"
    --GWG.GWMgr.comp:SetCameraDxf(dxf, time, callback)
    ui_window_mgr:ShowModule("guide_common_mask")
    local function xpcallFunction()
        GWG.GWAdmin.HomeSceneUtil.SetHomeClickState(false)
        GWG.GWAdmin.HomeSceneUtil.SetCameraDxf(1600, 500, function()
            gw_ed.mgr:Trigger(gw_ed.GW_HOME_EVENT_SHOW_FENCE)
            if monsterActionTimer then
                util.RemoveDelayCall(monsterActionTimer)
                monsterActionTimer = nil
            end
            monsterActionTimer = util.DelayCallOnce(1, function()
                gw_home_novice_util.RecoverCameraZoom(300)
                gw_home_camera_util.GWCameraDoMoveToGridPos(51, 30.5, 300, nil, function()
                    gw_ed.mgr:Trigger(gw_ed.GW_HOME_EVENT_HIDE_EFFECT)
                    local force_guide_system = require "force_guide_system"
                    local force_guide_event = require "force_guide_event"
                    local curEvent = GWG.GWHomeMgr.noviceChapterData.GetCurrentEventPoint()
                    force_guide_system.TriComEvent(force_guide_event.cEventNoviceEventBattleOver .. tostring(curEvent - 1))
                    force_guide_system.TriEnterEvent(force_guide_event.tEventEnterNewHunt)
                    GWG.GWAdmin.HomeSceneUtil.SetHomeClickState(true)
                    ui_window_mgr:UnloadModule("guide_common_mask")
                end)
                util.RemoveDelayCall(monsterActionTimer)
                monsterActionTimer = nil
            end)
        end)
        util.DelayCallOnce(1.5, function()
            ui_window_mgr:UnloadModule("guide_common_mask")
            GWG.GWAdmin.HomeSceneUtil.SetHomeClickState(true)
        end)
    end
    local f, res = xpcall(xpcallFunction, function(err)
        log.Error("NoviceEvent2CameraMove ", err)
    end)
    if not f then
        ui_window_mgr:UnloadModule("guide_common_mask")
        GWG.GWAdmin.HomeSceneUtil.SetHomeClickState(true)
    end
end
---@public 新手事件 对白
function Func_Table.NoviceEventDialogue(data)
    local id = data.data[0]
    local function callBack()
        local force_guide_system = require "force_guide_system"
        local force_guide_event = require "force_guide_event"
        local GWG = require "gw_g"
        local curEvent = GWG.GWHomeMgr.noviceChapterData.GetCurrentEventPoint()
        force_guide_system.TriComEvent(force_guide_event.cEventNoviceEventBattleOver .. tostring(curEvent - 1))
        force_guide_system.TriEnterEvent(force_guide_event.tEventEnterNewHunt)
    end
    if id then
        if id == 40006 then
            local mapId = 80
            JumpCamera(mapId, 500, nil, function()
                local gw_story_mgr = require "gw_story_mgr"
                local talkCfgId = id
                gw_story_mgr.PlayStoryById(talkCfgId, callBack)
            end)
            return
        end
        util.DelayCallOnce(1, function()
            local gw_story_mgr = require "gw_story_mgr"
            local talkCfgId = id
            gw_story_mgr.PlayStoryById(talkCfgId, callBack)
        end)
    else
        callBack()
    end
end
---@public 显示通用修复气泡
function Func_Table.GuideCommonBubbleRepairPop(data)
    if data.count <= 0 then
        log.Error("CheckFinishBuildRepair 策划表配置错误")
        return false
    end
    local GWConst = require "gw_const"
    local gw_home_common_util = require "gw_home_common_util"
    local gw_home_building_data = require "gw_home_building_data"
    local cameraCityId = data.data[1]
    JumpCamera(cameraCityId, nil, nil, nil)
    local buildData = gw_home_common_util.GetBuildDataByMapCityId(data.data[0])
    local compData = gw_home_building_data.GetBuildingCompBySid(buildData.uSid)
    if not compData then
        return nil, nil
    end
    local GWG = require "gw_g"
    local show, scale = GWG.GWAdmin.GWHomeCfgUtil.GetNoviceIsRepair(buildData.nBuildingID)
    local entity = compData:GetBubbleEntity(GWConst.EHomeBubbleEntityType.REPAIR)
    if not entity then
        return nil, nil
    end
    local gw_home_novice_util = require "gw_home_novice_util"
    gw_home_novice_util.LogWarning("scale = ", scale)
    entity:OnShow(scale)
    return nil, { entity.gameObject, entity }
end

function Func_Table.NoviceEventBattleOver7(data)
    if data.count <= 0 then
        log.Error("CheckFinishBuildRepair 策划表配置错误")
        return false
    end
    local mapId = data.data[0]
    local function DeadCallBack()
        JumpCamera(mapId, 500, nil, function()
            local force_guide_system = require "force_guide_system"
            local force_guide_event = require "force_guide_event"
            force_guide_system.TriComEvent(force_guide_event.cEventNoviceEventBattleOver .. tostring(7))
            force_guide_system.TriEnterEvent(force_guide_event.tEventEnterNewHunt)
        end)
    end
    --播放事件7 死亡溶解动画
    local gw_home_novice_chapter_data = require "gw_home_novice_chapter_data"
    gw_home_novice_chapter_data.DeadEventModel(7)
    DeadCallBack()
    return
end

local function GuideCommonBuildCut(id, callBack)
    local gw_home_common_util = require "gw_home_common_util"
    local gw_home_building_data = require "gw_home_building_data"
    local buildData = gw_home_common_util.GetBuildDataByMapCityId(id)
    local compData = gw_home_building_data.GetBuildingCompBySid(buildData.uSid)
    if not compData then
        return nil, nil
    end
    local cutComp = compData:GetCutComp()
    if cutComp and not util.IsObjNull(cutComp.gameObject) then
        return false, { cutComp.gameObject, cutComp }
    end
    return false, nil
end

---@public 显示通用建筑剪彩
function Func_Table.GuideCommonBuildingCutting(data)
    if data.count <= 0 then
        log.Error("CheckFinishBuildRepair 策划表配置错误")
        return false
    end
    local cityId = data.data[0]
    return GuideCommonBuildCut(cityId)
end

---@public 修复农场、金矿建筑
function Func_Table.NoviceEventRepairFarm(data)
    --镜头移动到mapId 位置
    if data.count <= 0 then
        return
    end
    local mapId = data.data[0]
    local mapCfg = game_scheme:BuildMaincityMap_0(mapId)
    if not mapCfg then
        return
    end
    local gw_home_camera_util = require "gw_home_camera_util"
    local gw_home_common_util = require "gw_home_common_util"
    local gw_home_novice_util = require "gw_home_novice_util"
    local gw_home_grid_data = require "gw_home_grid_data"
    local GWG =  require "gw_g"
    local GWHomeNode = require("gw_home_node")
    gw_home_novice_util.RecoverCameraZoom()
    local repairIds = { 10, 11, 12, 13 }
    --创建士兵模型
    local function CreateSoliderModelMove(x,y,z)
        local res = "art/characters/shibing/prefabs/shibing_simple.prefab";
        local modelId = GWG.GWHomeMgr.modelMgr.CreatedModel("gw_home_comp_model_worker", res, GWHomeNode.otherNode(), nil, nil, nil, { x = 51, y = 0, z = 40 });
        local model = GWG.GWHomeMgr.modelMgr.GetModelById(modelId);
        model:SetThisUpdateLocalTween({ x = x, y = y, z = z},2, function()
            GWG.GWHomeMgr.modelMgr.DisposeModel(modelId)
        end)
    end
    
    for i, id in pairs(repairIds) do
        local buildData = gw_home_common_util.GetBuildDataByMapCityId(id)
        if buildData then
            local x,y,z = gw_home_grid_data.GetPosByGridXY(buildData.x,buildData.y)
            CreateSoliderModelMove(x,y,z)
            util.DelayCallOnce(0.5, function()
                CreateSoliderModelMove(x,y,z)
            end)
        end
    end
    gw_home_camera_util.GWCameraDoMoveToGridPos(48, 41, 2100, nil, function()
        local index = 0
        for i, id in pairs(repairIds) do
            local isRepair = gw_home_common_util.GetBuildDataIsRepairByMapCityId(id)
            if isRepair then
                index = index + 1
                util.DelayCallOnce(index * 0.1, function()
                    --发送修复事件 --开始修复
                    local net_city_module = require "net_city_module"
                    local buildData = gw_home_common_util.GetBuildDataByMapCityId(id)
                    if buildData then
                        net_city_module.MSG_CITY_REPAIR_A_BUILDING_REQ(buildData.uSid)
                    end
                end)
                local force_guide_system = require "force_guide_system"
                local force_guide_event = require "force_guide_event"
                force_guide_system.TriComEvent(force_guide_event.cEventNoviceEventRepairFarm)
            end
        end
    end)
end

function Func_Table.NoviceEventRepairIron(data)
    --镜头移动到mapId 位置
    if data.count <= 0 then
        return
    end
    local mapId = data.data[0]
    local mapCfg = game_scheme:BuildMaincityMap_0(mapId)
    if not mapCfg then
        return
    end
    local gw_home_camera_util = require "gw_home_camera_util"
    local gw_home_common_util = require "gw_home_common_util"
    local gw_home_novice_util = require "gw_home_novice_util"
    local gw_home_grid_data = require "gw_home_grid_data"
    local GWG =  require "gw_g"
    local GWHomeNode = require("gw_home_node")
    gw_home_novice_util.RecoverCameraZoom()
    local repairIds = { 6, 7, 8, 9 }
    --创建士兵模型
    local function CreateSoliderModelMove(x,y,z)
        local res = "art/characters/shibing/prefabs/shibing_simple.prefab";
        local modelId = GWG.GWHomeMgr.modelMgr.CreatedModel("gw_home_comp_model_worker", res, GWHomeNode.otherNode(), nil, nil, nil, { x = 51, y = 0, z = 44 });
        local model = GWG.GWHomeMgr.modelMgr.GetModelById(modelId);
        model:SetThisUpdateLocalTween({ x = x, y = y, z = z},2, function()
            GWG.GWHomeMgr.modelMgr.DisposeModel(modelId)
        end)
    end
    for i, id in pairs(repairIds) do
        local buildData = gw_home_common_util.GetBuildDataByMapCityId(id)
        if buildData then
            local x,y,z = gw_home_grid_data.GetPosByGridXY(buildData.x,buildData.y)
            CreateSoliderModelMove(x,y,z)
            util.DelayCallOnce(0.5, function()
                CreateSoliderModelMove(x,y,z)
            end)
        end
    end
    util.DelayCallOnce(0, function()
        gw_home_novice_util.RecoverCameraZoom(400, nil)
        gw_home_camera_util.GWCameraDoMoveToGridPos(45, 46, 2100, false, function()
            local index = 0
            for i, id in pairs(repairIds) do
                local isRepair = gw_home_common_util.GetBuildDataIsRepairByMapCityId(id)
                if isRepair then
                    index = index + 1
                    util.DelayCallOnce(index * 0.1, function()
                        --发送修复事件 --开始修复
                        local net_city_module = require "net_city_module"
                        local buildData = gw_home_common_util.GetBuildDataByMapCityId(id)
                        if buildData then
                            net_city_module.MSG_CITY_REPAIR_A_BUILDING_REQ(buildData.uSid)
                        end
                    end)
                    local force_guide_system = require "force_guide_system"
                    local force_guide_event = require "force_guide_event"
                    force_guide_system.TriComEvent(force_guide_event.cEventNoviceEventRepairIron)
                end
            end
        end)
    end)
end
local farmCityMapTable = { 10, 11, 12, 13 }
function Func_Table.CheckRepairFarm()
    if not CheckNoviceEventOpen() then
        return false
    end
    local gw_home_common_util = require "gw_home_common_util"
    local isCheck = true
    for i, cityId in pairs(farmCityMapTable) do
        isCheck = gw_home_common_util.GetBuildDataIsRepairByMapCityId(cityId)
        if not isCheck then
            break
        end
    end
    return isCheck
end
local ironCityMapTable = { 6, 7, 8, 9 }
function Func_Table.CheckRepairIron()
    if not CheckNoviceEventOpen() then
        return false
    end
    local gw_home_common_util = require "gw_home_common_util"
    local isCheck = true
    for i, cityId in pairs(ironCityMapTable) do
        isCheck = gw_home_common_util.GetBuildDataIsRepairByMapCityId(cityId)
        if not isCheck then
            break
        end
    end
    return isCheck
end
--检测是否需要对这个cityId 进行剪彩逻辑判断
function CheckCityId(cityId)
    for i, id in ipairs(ironCityMapTable) do
        if id == cityId then
            return true
        end
    end
    for i, id in ipairs(farmCityMapTable) do
        if id == cityId then
            return true
        end
    end
    return false
end
---获取城市对应的表
local function GetCityMapTable(cityId)
    local cityMapTable = {}
    for i, id in ipairs(ironCityMapTable) do
        if id == cityId then
            cityMapTable = ironCityMapTable
            break
        end
    end
    for i, id in ipairs(farmCityMapTable) do
        if id == cityId then
            cityMapTable = farmCityMapTable
            break
        end
    end
    return cityMapTable
end
--检测所有农田or 冶炉是否是否有存在剪彩状态
function CheckRepairAllIsCutState(cityId)
    if not CheckNoviceEventOpen() then
        return false
    end
    local cityMapTable = GetCityMapTable(cityId)
    local gw_home_common_util = require "gw_home_common_util"
    local isCheck = false
    for i, id in pairs(cityMapTable) do
        isCheck = gw_home_common_util.GetBuildDataIsCutByMapCityId(id)
        if isCheck then
            break
        end
    end
    return isCheck
end
--检测所有农田or 冶炉是否全部修复
function CheckRepairAll(cityId)
    if not CheckNoviceEventOpen() then
        return false
    end
    local cityMapTable = GetCityMapTable(cityId)
    local gw_home_common_util = require "gw_home_common_util"
    local isCheck = false
    for i, id in pairs(cityMapTable) do
        isCheck = gw_home_common_util.GetBuildDataIsRepairSuccessByMapCityId(id)
        if not isCheck then
            break
        end
    end
    return isCheck
end

---@public 通用战斗结束
function Func_Table.NoviceEventBattleOver(data)
    local id = data.data[0]
    local force_guide_system = require "force_guide_system"
    local force_guide_event = require "force_guide_event"
    force_guide_system.TriComEvent(force_guide_event.cEventNoviceEventBattleOver .. tostring(id))
    force_guide_system.TriEnterEvent(force_guide_event.tEventEnterNewHunt)
end

---@public 检测通用建筑是否已经修复
function Func_Table.CheckFinishBuildRepair(data)
    if not CheckNoviceEventOpen() then
        return false
    end
    if data.count <= 0 then
        log.Error("CheckFinishBuildRepair 策划表配置错误")
        return false
    end
    local gw_home_common_util = require "gw_home_common_util"
    return gw_home_common_util.GetBuildDataIsRepairByMapCityId(data.data[0])
end

---@public 检测通用建筑是否正存在修复的剪彩状态
function Func_Table.CheckFinishBuildCutting(data)

    if not CheckNoviceEventOpen() then
        return false
    end

    if data.count <= 0 then
        log.Error("CheckFinishBuildRepair 策划表配置错误")
        return false
    end
    local GWConst = require "gw_const"
    local gw_home_common_util = require "gw_home_common_util"
    local id = data.data[0]
    local buildData = gw_home_common_util.GetBuildDataByMapCityId(id)
    if not buildData then
        return false
    end
    local enCityFlag = gw_home_common_util.GetCityFlagState(buildData.uFlag, GWConst.enCityFlag.enCityFlag_BuildingOpenGift)
    if buildData.nLevel <= 0 and buildData.nState ~= GWConst.enBuildingState.enBuildingState_Repairing and enCityFlag then
        return true
    end
    return false
end

function Func_Table.CheckFinishCut(mapId)
    if not CheckNoviceEventOpen() then
        return false
    end
    local GWConst = require "gw_const"
    local gw_home_common_util = require "gw_home_common_util"
    local buildData = gw_home_common_util.GetBuildDataByMapCityId(mapId)
    if not buildData then
        return false
    end
    return buildData.nLevel >= 1
end


function Func_Table.CheckFinishBuildCuttingAndEvent4(data)
    if data then
        local cutting = Func_Table.CheckFinishBuildCutting(data)
        local parentData = {}
        parentData.data = {}
        parentData.data[0] = 5
        local event4 = Func_Table.CheckFinishEvent(parentData)
        return cutting and event4
    end
    return false
end

function Func_Table.CheckFinishBuildCuttingAndEvent5(data)
    if data then
        local cutting = Func_Table.CheckFinishBuildCutting(data)
        local parentData = {}
        parentData.data = {}
        parentData.data[0] = 6
        local event5 = Func_Table.CheckFinishEvent(parentData)
        return cutting and event5
    end
    return false
end

---@public 关卡3开始播放对话
function Func_Table.NoviceEventDialogue3(data)
    local id = data.data[0]
    local cameraCallBack = function()
        local force_guide_system = require "force_guide_system"
        local force_guide_event = require "force_guide_event"
        force_guide_system.TriComEvent(force_guide_event.cEventNoviceDialogue3)
        force_guide_system.TriEnterEvent(force_guide_event.tEventEnterNewHunt)
    end
    local showCardCallBack = function()
        local cameraCityId = data.data[1]
        local time = data.data[2]
        JumpCamera(cameraCityId, time, true, cameraCallBack)
        
        --获取新手玩家 -出气泡
        local gw_home_novice_chapter_data = require "gw_home_novice_chapter_data"
        gw_home_novice_chapter_data.NoviceTrigger6(true);
    end
    local function callBack()
        local ui_show_hero_card = require "ui_show_hero_card"
        ui_show_hero_card.ShowWithData(ui_show_hero_card.ParseArrHeroId(111), cameraCallBack, nil, nil, true)
    end
    --if id then
    --    util.DelayCallOnce(0, function()
    --        local gw_story_mgr = require "gw_story_mgr"
    --        local talkCfgId = id
    --        gw_story_mgr.PlayStoryById(talkCfgId, callBack)
    --    end)
    --else
    --    callBack()
    --end
    callBack()
end
---@public 关卡4开始播放对话
function Func_Table.NoviceEventDialogue4(data)
    local id = data.data[0]
    local function callBack()
        local force_guide_system = require "force_guide_system"
        local force_guide_event = require "force_guide_event"
        force_guide_system.TriComEvent(force_guide_event.cEventNoviceDialogue4)
        force_guide_system.TriEnterEvent(force_guide_event.tEventNoviceEventRepairFarm)
    end
    --if id then
    --    util.DelayCallOnce(0, function()
    --        local gw_story_mgr = require "gw_story_mgr"
    --        local talkCfgId = id
    --        gw_story_mgr.PlayStoryById(talkCfgId, callBack)
    --    end)
    --else
    --    callBack()
    --end
    --[[if id then
        util.DelayCallOnce(0, function()
            local gw_story_mgr = require "gw_story_mgr"
            local talkCfgId = id
            gw_story_mgr.PlayStoryById(talkCfgId, nil)
        end)
    end]]
    callBack()
end
local NoviceEventDialogue7AniFlow = nil
---@public 关卡7开始播放对话
function Func_Table.NoviceEventDialogue7(data)
    local id = data.data[0]
    local GWG = require "gw_g"
    local gw_home_camera_util = require "gw_home_camera_util"
    local gw_home_novice_chapter_data = require "gw_home_novice_chapter_data"
    local monster = gw_home_novice_chapter_data.GetEventEntity(7)
    if monster then
        monster:NoviceLimitClick(true)
    end
    local cameraMoveCallBack = function()
        local gw_home_novice_util = require "gw_home_novice_util"
        gw_home_novice_chapter_data.NoviceTrigger6();
        monster = gw_home_novice_chapter_data.GetEventEntity(7)

        local flow = NoviceEventDialogue7AniFlow and NoviceEventDialogue7AniFlow.flow
        if flow then return end
        
        if NoviceEventDialogue7AniFlow then
            NoviceEventDialogue7AniFlow:Dispose()
            NoviceEventDialogue7AniFlow = nil
        end
        local ani_flow = require "ani_flow"
        NoviceEventDialogue7AniFlow = ani_flow()
        
        local functionData = {
            ResetCamera = function ()
                gw_home_novice_util.RecoverCameraZoom(500)
                gw_home_camera_util.GWCameraDoMoveToGridPos(51, 51, 500)
            end,
            ShowModelAbilityTrigger = function()
                --播放怪物动作
                if monster then
                    monster:ShowModelAbilityTrigger()
                end
            end,
            NoviceLimitClick = function()
                --播放怪物动作    
                if monster then
                    monster:NoviceLimitClick(false)
                end
            end
        }
        
        local flow = {
            {
                "ResetCamera", -- eventID
                1, -- 开始时间点
                0, -- 持续时间
            },
            {
                "ShowModelAbilityTrigger", -- eventID
                2, -- 开始时间点
                0, -- 持续时间
            },
            {
                "NoviceLimitClick", -- eventID
                2.1, -- 开始时间点
                0, -- 持续时间
            },
            {
                nil, -- eventID
                2.2, -- 开始时间点
                0, -- 持续时间
            },
            endCallback = function()
                local force_guide_system = require "force_guide_system"
                local force_guide_event = require "force_guide_event"
                force_guide_system.TriComEvent(force_guide_event.cEventNoviceDialogue7)
                force_guide_system.TriEnterEvent(force_guide_event.tEventEnterEvent7)
                if NoviceEventDialogue7AniFlow then
                    NoviceEventDialogue7AniFlow:Dispose()
                    NoviceEventDialogue7AniFlow = nil
                end
            end,
        }
        local register_func = function(event, state)
            local eType = state[1][1]
            if event ~= "finish_ani" then
                return
            end
            if not eType then
                return
            end
            if functionData and functionData[eType] and type(functionData[eType]) == 'function' then
                functionData[eType]()
            end
        end
        NoviceEventDialogue7AniFlow:InitFlow(flow, register_func)
        NoviceEventDialogue7AniFlow:Play()
    end
    local showCardCallBack = function()
        local initBattlePropCft = game_scheme:InitBattleProp_0(81958195)
        if initBattlePropCft then
            default = initBattlePropCft.szParam[1][0]
        end
        local default = 1000
        GWG.GWAdmin.HomeSceneUtil.SetCameraDxf(default, 500)
        gw_home_camera_util.GWCameraDoMoveToGridPos(51, 48, 500)
        util.DelayCallOnce(0.5, cameraMoveCallBack)
    end
    local function callBack()
        local ui_show_hero_card = require "ui_show_hero_card"
        ui_show_hero_card.ShowWithData(ui_show_hero_card.ParseArrHeroId(108), showCardCallBack, nil, nil, true)
    end
    --if id then
    --    util.DelayCallOnce(0, function()
    --        local gw_story_mgr = require "gw_story_mgr"
    --        local talkCfgId = id
    --        gw_story_mgr.PlayStoryById(talkCfgId, callBack)
    --    end)
    --else
    --    callBack()
    --end
    callBack()
end
function Func_Table.MainUpgradeShow()
    --主城升级 \开始表演
    local GWG = require "gw_g"
    local GWConst = require "gw_const"
    local gw_home_camera_util = require "gw_home_camera_util"

    --显示资源田气泡
    local function showResourceBubble()
        local GWCompName = require "gw_comp_name"
        local listComps = GWG.GWAdmin.GetBSComponentsByType(GWCompName.gw_home_comp_building_resource)
        if listComps then
            for i, v in pairs(listComps) do
                if v then
                    v:RefreshBubbleData()
                end
            end
        end
    end

    --播放完毕剧情 开始任务若引导
    local function storyCallBack()
        --引导结束
        local force_guide_system = require "force_guide_system"
        local force_guide_event = require "force_guide_event"
        force_guide_system.TriComEvent(force_guide_event.cEventNoviceMainUpgrade)
        local ui_window_mgr = require "ui_window_mgr"
        ui_window_mgr:CloseAll({ ui_main_slg = 1, ui_main_slg_top = 1, ui_main_slg_bottom = 1 })
        force_guide_system.TriEnterEvent(force_guide_event.tEventEnterNewHunt)
    end

    local function funcOpenSuccessCallBack()
        local gw_story_mgr = require "gw_story_mgr"
        gw_story_mgr.PlayStoryById(40014, storyCallBack)
    end

    local function cameraZoomMinCallBack()
        showResourceBubble()

    end
    local function cameraZoomMaxCallBack()
        local initBattlePropCft = game_scheme:InitBattleProp_0(8195)
        local default = 1400
        if initBattlePropCft then
            default = initBattlePropCft.szParam[1][0]
        end

        local ui_window_mgr = require "ui_window_mgr"
        ui_window_mgr:ShowModule("ui_function_unlock_tips", nil, funcOpenSuccessCallBack, {})
        GWG.GWAdmin.HomeSceneUtil.SetCameraDxf(default, 500, cameraZoomMinCallBack)
    end

    local function cameraCallBack()
        --播放特效
        local effectId = GWG.GWHomeMgr.effectMgr.CreateEffects(GWConst.HomeEffectType.NoviceMainFreeEffect)
        local effectId2 = GWG.GWHomeMgr.effectMgr.CreateEffects(GWConst.HomeEffectType.NoviceMainFreeEffect2)
        util.DelayCallOnce(1, function()
            GWG.GWHomeMgr.effectMgr.RemoveEffect(effectId)
            cameraZoomMaxCallBack()
        end)
        util.DelayCallOnce(1.5, function()
            GWG.GWHomeMgr.effectMgr.RemoveEffect(effectId2)
        end)
    end

    gw_home_camera_util.GWCameraDoMoveToGridPos(51, 46, 750, nil, nil)
    GWG.GWAdmin.HomeSceneUtil.SetCameraDxf(1800, 750, cameraCallBack)
end

--region 新手引导 ------------ 挂机
function Func_Table.GuideOnHookStart(data)
    local onePlayStoryId = data.data[0]
    local mapId = data.data[1]
    local cameraMoveTime = data.data[2]
    local twoPlayStoryId = data.data[3]

    local twoPlayStoryCallBack = function()
        local force_guide_system = require "force_guide_system"
        local force_guide_event = require "force_guide_event"
        force_guide_system.TriComEvent(force_guide_event.cEventNormalEvent103)
        force_guide_system.TriEnterEvent(force_guide_event.tEventOnHookBubble103)
    end

    local cameraCallBack = function()
        local gw_story_mgr = require "gw_story_mgr"
        gw_story_mgr.PlayStoryById(twoPlayStoryId, twoPlayStoryCallBack)
    end

    local onePlayStoryCallBack = function()
        local gw_home_camera_util = require "gw_home_camera_util"
        local mapCfg = game_scheme:BuildMaincityMap_0(mapId)
        if not mapCfg then
            return
        end
        gw_home_camera_util.GWCameraDoMoveToGridPos(mapCfg.x, mapCfg.y, cameraMoveTime, nil, cameraCallBack)
    end
    local gw_story_mgr = require "gw_story_mgr"
    gw_story_mgr.PlayStoryById(onePlayStoryId, onePlayStoryCallBack)

    local gw_home_building_data = require "gw_home_building_data"
    local compData = gw_home_building_data.GetBuildingCompByType(39)
    if not compData then
        return nil, nil
    end
    if compData.CreateRewardBubble then
        --刷新下气泡状态
        compData:CreateRewardBubble()
    end
    local gw_hangup_mgr = require "gw_hangup_mgr"
    gw_hangup_mgr.C2S_GetHangUpInfo_First()
    gw_hangup_mgr.RefeshCompByHangUpStauts(gw_hangup_mgr.HangUpStautsEnum.Little)
end

function Func_Table.GuideOnHookBubblePop(data)
    if data.count <= 0 then
        log.Error("CheckFinishBuildRepair 策划表配置错误")
        return false
    end
    local GWConst = require "gw_const"
    local gw_home_building_data = require "gw_home_building_data"
    local compData = gw_home_building_data.GetBuildingCompByType(data.data[0])
    if not compData then
        return nil, nil
    end
    if compData.CreateRewardBubble then
        --刷新下气泡状态
        compData:CreateRewardBubble()
    end
    local entity = compData:GetBubbleEntity(GWConst.EHomeBubbleEntityType.HangUpReward)
    if not entity then
        --防止引导卡住出不来
        log.Error("资源产出气泡未出现 结束当前引导")
        return nil, nil
    end
    local gw_home_novice_util = require "gw_home_novice_util"
    gw_home_novice_util.LogWarning("scale = ", scale)
    return nil, { entity.gameObject, entity }
end


function Func_Table.GuideOnHookAttackBubblePop(data)
    if data.count <= 0 then
        log.Error("CheckFinishBuildRepair 策划表配置错误")
        return false
    end
    local GWConst = require "gw_const"
    local gw_home_building_data = require "gw_home_building_data"
    local compData = gw_home_building_data.GetBuildingCompByType(data.data[0])
    if not compData then
        return nil, nil
    end
    if compData.CreateAttackBubble then
        --刷新下气泡状态
        compData:CreateAttackBubble()
    end
    local entity = compData:GetBubbleEntity(GWConst.EHomeBubbleEntityType.HangUpAttack)
    if not entity then
        --防止引导卡住出不来
        log.Error("攻击气泡未出现 结束当前引导")
        return nil, nil
    end
    local gw_home_novice_util = require "gw_home_novice_util"
    gw_home_novice_util.LogWarning("scale = ", scale)
    return nil, { entity.gameObject, entity }
end

function Func_Table.CheckNormalFinishEvent(data)
    local gw_home_novice_util = require "gw_home_novice_util"
    local open = gw_home_novice_util.GetIsNovice()
    if not open then
        return false
    end
    local gw_home_chapter_data = require "gw_home_chapter_data"
    local passEvent = gw_home_chapter_data.CheckPassEvent(data.data[0])
    return passEvent
end

--高亮点击3d怪物
function Func_Table.GetNormalMonsterObjById(data)
    local id = data.data[0]
    if id then
        local gw_home_chapter_data = require "gw_home_chapter_data"
        local entity = gw_home_chapter_data.GetEventEntity(id)
        if entity then
            return false, { entity.gameObject, entity }
        end
    end
    return false, nil
end

--endregion

--region ------ 首充拍脸 ------

function Func_Table.FirstChargePop(data)
    if not data then
        return
    end
    local id = data.data[0]
    local heroId = data.data[1]
    local function showCardCallBack()
        local gw_home_common_util = require "gw_home_common_util"
        gw_home_common_util.FirstRechargeBuildClick()
        local force_guide_system = require "force_guide_system"
        local force_guide_event = require "force_guide_event"
        force_guide_system.TriComEvent(force_guide_event.cEventNormalEvent106)
    end
    local function callBack()
        local ui_show_hero_card = require "ui_show_hero_card"
        ui_show_hero_card.ShowWithData(ui_show_hero_card.ParseArrHeroId(heroId), showCardCallBack, nil, nil, true)
    end
    if id then
        local gw_story_mgr = require "gw_story_mgr"
        gw_story_mgr.PlayStoryById(id, callBack)
    else
        callBack()
    end
end

--通关107之后弹出剧情
function Func_Table.FirstChargePop1(data)
    if not data then
        return
    end
    local id = data.data[0]
    local function callBack()
        local force_guide_system = require "force_guide_system"
        local force_guide_event = require "force_guide_event"
        force_guide_system.TriComEvent(force_guide_event.cEventNormalEvent107)
    end
    if id then
        local gw_story_mgr = require "gw_story_mgr"
        gw_story_mgr.PlayStoryById(id, callBack)
    else
        callBack()
    end
end

--endregion

--region ------ 区域扩建2 ------

function Func_Table.GuideFirstArea(data)
    if not data then
        return
    end
    local id = data.data[0]
    local areaId = data.data[1]
    local function callBack()
        local force_guide_system = require "force_guide_system"
        local force_guide_event = require "force_guide_event"
        force_guide_system.TriComEvent(force_guide_event.cEventNormalEvent109)
        --区域2 实例化成功之后再出现引导 - 双重判断
        force_guide_system.TriEnterEvent(force_guide_event.tEventNoviceArea2)
    end
    local function storyCallBack()
        local gw_home_chapter_data = require "gw_home_chapter_data"
        gw_home_chapter_data.MoveToAreaPos(areaId,1000,callBack)
    end
    if id then
        local gw_story_mgr = require "gw_story_mgr"
        gw_story_mgr.PlayStoryById(id, storyCallBack)
    else
        storyCallBack()
    end
end

function Func_Table.GuideCommonNoviceArea(data)
    local id = data.data[0]
    if id then
        local gw_home_chapter_data = require "gw_home_chapter_data"
        local entity = gw_home_chapter_data.GetAreaModel(id)
        if entity then
            return false, { entity.gameObject, entity }
        end
    end
    return false, nil
end

--endregion

--region ------ 大本加速 ------

function Func_Table.GetMainBuilding()
    local gw_home_building_data = require "gw_home_building_data"
    local gw_const = require "gw_const"
    local compData = gw_home_building_data.GetBuildingCompByType(gw_const.enBuildingType.enBuildingType_Main)
    if not compData then
        return nil, nil
    end
    if not util.IsObjNull(compData.gameObject) then
        return false, { compData.gameObject, compData }
    end
    return false, nil
end

function Func_Table.CheckMainSpeedUp(data)
    --    return false
    if not data then
        return false
    end
    local buildNeedLevel = data.data[0]
    local itemNeedNum = data.data[1]
    local gw_home_building_data = require "gw_home_building_data"
    local gw_const = require "gw_const"
    local buildId = gw_home_building_data.GetBuildingIdByBuildingType(gw_const.enBuildingType.enBuildingType_Main)
    local maxLevel = gw_home_building_data. GetBuildingDataMaxLevel(buildId)
    if maxLevel ~= 3 and maxLevel ~= buildNeedLevel and maxLevel ~= 5 then
        return false
    end
    local player_mgr = require "player_mgr"
    local dataLen = data.count -1
    local playerOwnNum = 0
    for i = 2, dataLen do
        local itemId = data.data[i]
        local itemNum = player_mgr.GetPlayerOwnNum(itemId)
        playerOwnNum = playerOwnNum + itemNum
    end
    if playerOwnNum < itemNeedNum then
        return false
    end
    return true
end

--endregion

--region ------ 引导联盟中心 ------

function Func_Table.CheckGuideAlliance(data)
    local id = data.data[0]
    local gw_home_chapter_data = require "gw_home_chapter_data"
    if not id or not gw_home_chapter_data.CheckPassEvent(id) then
        return false
    end
    local gw_home_cfg_util = require "gw_home_cfg_util"
    local gw_const = require "gw_const"
    local isCanBuild = gw_home_cfg_util.GetOneCanBuildByTypeId(gw_const.enBuildingType.enBuildingType_Alliance)
    return isCanBuild
end

function Func_Table.CheckGuideAllianceCut(data)
    if not data then
        return
    end
    local buildId = data.data[0]
    local gw_home_building_data = require "gw_home_building_data"
    local buildList = gw_home_building_data.GetBuildingDataListByBuildingID(buildId)
    local index = util.get_len(buildList)
    if index > 0 and index <= 1 then
        for i, serData in pairs(buildList) do
            if serData and serData.nBuildingID == buildId and serData.nLevel == 0 then
                return true
            end
        end
    end
    return false
end

---@public 联盟剧情
function Func_Table.AllianceStory(data)
    local storyId = nil
    if data then
        storyId = data.data[0]
    end
    local ui_window_mgr = require "ui_window_mgr"
    ui_window_mgr:CloseAll()
    local function storyCallBack()
        local force_guide_event = require "force_guide_event"
        local force_guide_system = require "force_guide_system"
        force_guide_system.TriComEvent(force_guide_event.cEventAllianceStory)
        force_guide_system.TriEnterEvent(force_guide_event.tEventBuildAllianceUI)
    end
    if storyId then
        local gw_story_mgr = require "gw_story_mgr"
        gw_story_mgr.PlayStoryById(storyId, storyCallBack)
    else
        storyCallBack()
    end
end
---@public 联盟建造列表
function Func_Table.BuildAllianceUI()
    local ui_window_mgr = require "ui_window_mgr"
    ui_window_mgr:UnloadModule("ui_bomberman_tips")
end
---@public 联盟建造剪彩镜头移动
function Func_Table.AllianceCamera()
    local gw_home_building_data = require "gw_home_building_data"
    local GWConst = require "gw_const"
    local compData = gw_home_building_data.GetBuildingCompByType(GWConst.enBuildingType.enBuildingType_Alliance)
    if not compData or not compData.serData then
        return nil, nil
    end
    local function callback()
        local force_guide_event = require "force_guide_event"
        local force_guide_system = require "force_guide_system"
        force_guide_system.TriComEvent(force_guide_event.cEventAllianceCut)
        force_guide_system.TriEnterEvent(force_guide_event.tEventAllianceCutClick)
    end
    local gw_home_camera_util = require "gw_home_camera_util"
    gw_home_camera_util.GWCameraDoMoveToGridPos(compData.serData.x, compData.serData.y, 500, true, callback)
end
----@public 联盟建造剪彩
function Func_Table.GuideAllianceBuildingCutting(data)
    if not data then
        return
    end
    local buildType = data.data[0]
    local gw_home_building_data = require "gw_home_building_data"
    local compData = gw_home_building_data.GetBuildingCompByType(buildType)

    local GWG = require "gw_g"
    local objTable = {}
    if compData then
        local cutComp = compData:GetCutComp()
        if cutComp and not util.IsObjNull(cutComp.gameObject) then
            table.insert(objTable, cutComp.gameObject)
        end
    end
    local camera = GWG.GWMgr.comp:GetCurrentCameraComponent()
    if compData then
        local cutComp = compData:GetCutComp()
        return false, {
            camera = camera,
            objList = objTable,
            iTextureSize = nil,
            offRect = { x = 0, y = 0, width = 200, height = 200, position = cutComp.gameObject.transform.position },
            clickBack = function()
                local force_guide_system = require "force_guide_system"
                local force_guide_event = require "force_guide_event"
                force_guide_system.TriComEvent(force_guide_event.cEventAllianceCutClick)
                cutComp:OnClickGrid()
            end
        }
    end
    return false, {}
end

--endregion

--region ------ 任务引导 ------

function Func_Table.CheckGuideTask(data)
    if data.count <= 0 then
        return false
    end
    local needLevel = data.data[0]
    local gw_home_building_data = require "gw_home_building_data"
    local level = gw_home_building_data.GetBuildingMainLevel()
    if level > needLevel then
        return false
    end
    local player_prefs = require "player_prefs"
    --player_prefs.SetCacheData("CheckGuideTask", false)
    if player_prefs.GetCacheData("CheckGuideTask", false) then
        return false
    end
    return true
end

function Func_Table.StartMainTaskGuide()
    local ui_window_mgr = require "ui_window_mgr"
    if not ui_window_mgr:IsModuleShown("ui_main_slg") then
        return
    end
    local view = ui_window_mgr:GetWindowObj("ui_main_slg")
    if not view then
        return
    end
    if view.StartTaskGuideFunc then
        view:StartTaskGuideFunc()
    end
end

function Func_Table.OverMainTaskGuide()
    local ui_window_mgr = require "ui_window_mgr"
    if not ui_window_mgr:IsModuleShown("ui_main_slg") then
        return
    end
    local view = ui_window_mgr:GetWindowObj("ui_main_slg")
    if not view then
        return
    end
    if view.OverTaskGuideFunc then
        view:OverTaskGuideFunc()
    end
    local player_prefs = require "player_prefs"
    player_prefs.SetCacheData("CheckGuideTask", true)
end

--endregion

--region ------ 引导地块2 ------

function Func_Table.GetMonsterObjByNormalEvent2(data)
    local ui_window_mgr = require "ui_window_mgr"
    ui_window_mgr:CloseAll({ui_main_slg = 1,ui_main_slg_top = 1,ui_main_slg_bottom = 1})
    local id = data.data[0]
    if not id then
        return false, {}
    end
    local gw_home_chapter_data = require "gw_home_chapter_data"
    local entity = gw_home_chapter_data.GetEventEntity(id)
    local GWG = require "gw_g"
    if not entity then
        return false, {}
    end
    local monsters = entity:GetModelComp()
    local objTable = {}
    if entity and not util.IsObjNull(entity.gameObject) then
        table.insert(objTable, entity.gameObject)
    end
    if monsters and not util.IsObjNull(monsters.gameObject) then
        table.insert(objTable, monsters.gameObject)
    end
    local fightHud = entity:GetFightHud()
    if fightHud and not util.IsObjNull(fightHud.gameObject) then
        table.insert(objTable, fightHud.gameObject)
    end
    local camera = GWG.GWMgr.comp:GetCurrentCameraComponent()
    if entity then
        return false, {
            camera = camera,
            objList = objTable,
            iTextureSize = nil,
            offRect = { x = 0, y = 0, width = 300, height = 300, position = entity.gameObject.transform.position },
            clickBack = function()
                local force_guide_system = require "force_guide_system"
                local force_guide_event = require "force_guide_event"
                local player_prefs = require "player_prefs"
                player_prefs.SetCacheData("CheckGuideNormalEvent2", true)
                force_guide_system.TriComEvent(force_guide_event.cEventNormalEvent2Click)                
                entity:OnClickGrid()
            end
        }
    end
    return false, {}
end

function Func_Table.CheckGuideNormalEvent2(data)
    if data.count <= 0 then
        return false
    end
    local needLevel = data.data[0]
    local gw_home_building_data = require "gw_home_building_data"
    local level = gw_home_building_data.GetBuildingMainLevel()
    if level > needLevel then
        return false
    end
    local player_prefs = require "player_prefs"
    --player_prefs.SetCacheData("CheckGuideNormalEvent2", false)
    if player_prefs.GetCacheData("CheckGuideNormalEvent2", false) then
        return false
    end
    return true
end

--endregion

--region ------ 引导 加入联盟 ------

function Func_Table.JoinAllianceUI()
    --弹出界面
    local ui_window_mgr = require "ui_window_mgr"
    local alliance_data = require "alliance_data"
    local joinTime = alliance_data.GetAllianceFirstJoinTime()
    ui_window_mgr:ShowModule("ui_alliance_first_join", nil, nil, { joinTime = joinTime })
    local force_guide_system = require "force_guide_system"
    local force_guide_event = require "force_guide_event"
    force_guide_system.TriComEvent(force_guide_event.cEventAllianceJoin)
end

function Func_Table.CheckGuideJoinAlliance()
    local AllianceMgr = require "alliance_mgr"
    if AllianceMgr.GetIsJoinAlliance() then
        return false
    end
    return true
end


--endregion

--region ------ 沙漠风暴 ------

function Func_Table.CheckGuideDesert()
    local player_prefs = require "player_prefs"
    --player_prefs.SetCacheData("CheckGuideDesert", false)
    if player_prefs.GetCacheData("CheckGuideDesert", false) then
        return false
    end
    return true
end

function Func_Table.DesertNotifyUI()
    local player_prefs = require "player_prefs"
    player_prefs.SetCacheData("CheckGuideDesert", true)
    local reward_mgr = require "reward_mgr"
    local hideCallBack = function()
        local force_guide_system = require "force_guide_system"
        local force_guide_event = require "force_guide_event"
        force_guide_system.TriComEvent(force_guide_event.cEventDesertNotify)
        force_guide_system.TriEnterEvent(force_guide_event.tEventDesertIntegral)
    end
    local ui_window_mgr = require "ui_window_mgr"
    ui_window_mgr:ShowModule("ui_desert_storm_guide_tips_panel", nil, hideCallBack)
end

--endregion

--region ------ 解救人质最后引导 ------

function Func_Table.OpenBomberManTip()
    local force_guide_system = require "force_guide_system"
    local force_guide_event = require "force_guide_event"
    force_guide_system.TriComEvent(force_guide_event.cEventLastBomberman)
    local ui_window_mgr = require "ui_window_mgr"
    ui_window_mgr:ShowModule("ui_bomberman_tips")
end

function Func_Table.CheckGuideLastBomberMan()
    --判断130事件 和 red>0
    local gw_home_chapter_data = require "gw_home_chapter_data"
    local unforced_guide_const = require "unforced_guide_const"
    local passEvent = gw_home_chapter_data.CheckPassEvent(unforced_guide_const.LastBombermanEventId)
    if not passEvent then
        return false
    end
    local gw_bomberman_mgr = require "gw_bomberman_mgr"
    local red = gw_bomberman_mgr.GetBomberManRed()
    return red >= 0 
end

--endregion

--region ------ 橡果酒馆 隐秘宝藏引导 ------

function Func_Table.ShowStoryDialog(data)
    local storyId = nil
    if data then
        storyId = data.data[0]
    end
    -- local ui_window_mgr = require "ui_window_mgr"
    -- ui_window_mgr:CloseAll()
    local function storyCallBack()
        local force_guide_event = require "force_guide_event"
        local force_guide_system = require "force_guide_system"
        force_guide_system.TriComEvent(force_guide_event.cEventStoryDialogClick)
        local endEvent = storyId and force_guide_event.tEventStoryDialogClick..storyId or force_guide_event.tEventStoryDialogClick
        force_guide_system.TriEnterEvent(endEvent)
    end
    if storyId then
        local gw_story_mgr = require "gw_story_mgr"
        gw_story_mgr.PlayStoryById(storyId, storyCallBack)
    else
        storyCallBack()
    end
end
-- 隐秘宝藏是否解锁且有开启藏宝图次数
function Func_Table.CheckOpenTavernTreasure()
    local tavern_treasure_data = require "tavern_treasure_data"
    local tavern_treasure_mgr = require "tavern_treasure_mgr"
    return tavern_treasure_data.GeteGuideFlag() >= 2 and tavern_treasure_data.GetActOpen() and tavern_treasure_mgr.GetChipData().canDigCount > 0
end
-- 隐秘宝藏是否解锁且有任务奖励
function Func_Table.CheckTavernOpenAndCanReward()
    local tavern_treasure_data = require "tavern_treasure_data"
    local ui_tavern_mgr = require "ui_tavern_mgr"
    return tavern_treasure_data.GeteGuideFlag() < 2 and tavern_treasure_data.GetActOpen() and #ui_tavern_mgr.GetAllCanReviceTaskTab() > 0
end

-- 首充副本无尽模式是否开启
function Func_Table.CheckEndlessIsOpen()
    local hero_first_charge_data = require "hero_first_charge_data"
    local isLock = hero_first_charge_data.GetEndlessIsLock()--是否开启
    local isFirstTime = util.GetFirstEnter("ui_hero_first_charge_main_Endless")
    return isLock and isFirstTime
end
--endregion

local func_table = {
    ["DisableMainSceneDrag"] = DisableMainSceneDrag,
    ["CheakStarNum"] = CheakStarNum,
    ["EnableMainSceneDrag"] = EnableMainSceneDrag,
    ["TopGuideMail"] = TopGuideMail,
    ["TopFragment"] = TopFragment,
    ["ClickFold"] = ClickFold,
    ["RefreshMail"] = RefreshMail,
    ["CheakMail"] = CheakMail,
    ["CheckHasGoods"] = CheckHasGoods,
    ["DisDragActiList"] = DisDragActiList,
    ["CheckHeroNum"] = CheckHeroNum,
    ["HeroSelectTouchDisable"] = HeroSelectTouchDisable,
    ["TopItem"] = TopItem,
    ["ChangeFormationBefore"] = ChangeFormationBefore,
    ["ChangeFormationAfter"] = ChangeFormationAfter,
    ["CheckPassLevel"] = CheckPassLevel,
    ["CheckPassLevel_noEqual"] = CheckPassLevel_noEqual,
    ["CheckFreeLottery"] = CheckFreeLottery,
    ["TopHero"] = TopHero,
    ["CloseDefeatForGuide"] = CloseDefeatForGuide,
    ["CloseVictoryForGuide"] = CloseVictoryForGuide,
    ["TriggerUpGuideByUpState"] = TriggerUpGuideByUpState,
    ["SetHeroLeanTouchEable"] = SetHeroLeanTouchEable,
    ["BackToHunt"] = BackToHunt,
    ["ShowFristRecharge"] = ShowFristRecharge,
    ["FristRechargeCanGetReward"] = FristRechargeCanGetReward,
    ["ShowLayMainReward"] = ShowLayMainReward,
    ["FindEquipment"] = FindEquipment,
    ["ChangeDotLayer"] = ChangeDotLayer,
    ["EnsureShowMenuBot"] = EnsureShowMenuBot,
    ["Check1_2"] = Check1_2,
    ["PackUpLeftMenu"] = PackUpLeftMenu,
    ["FindGuideHeroUI"] = FindGuideHeroUI,
    ["SwitchHookSceneDrag"] = SwitchHookSceneDrag,
    ["DisLevelMapMove"] = DisLevelMapMove,
    ["SetPreSlot"] = SetPreSlot,
    ["CheckHeroCanUpgrade"] = CheckHeroCanUpgrade,
    ["FindGuideWantedLevel"] = FindGuideWantedLevel,
    ["FindGuideInstanceLevel"] = FindGuideInstanceLevel,
    ["CheckWantedGuide"] = CheckWantedGuide,
    ["CheckMazeGuide"] = CheckMazeGuide,
    ["CheckArenaGuide"] = CheckArenaGuide,
    ["CheckInstanceGuide"] = CheckInstanceGuide,
    ["CheckHomelandSlotGuide"] = CheckHomelandSlotGuide,
    ["CloseAwake"] = CloseAwake,
    ["Check7dayPopup"] = Check7dayPopup,
    ["FindBiographyHeroItem"] = FindBiographyHeroItem,
    ["FindBiographyHero"] = FindBiographyHero,
    ["OpenFuzzleGameFormHunt"] = OpenFuzzleGameFormHunt,
    ["SetPuzzleGameFinger"] = SetPuzzleGameFinger,
    ["HideHeroSelect"] = HideHeroSelect,
    ["ReturnHookFormPuzzleGame"] = ReturnHookFormPuzzleGame,
    ["TriggerForceGuide"] = TriggerForceGuide,
    ["SetAniMaskState"] = SetAniMaskState,
    ["CheckSelectedHeroNumLessThan"] = CheckSelectedHeroNumLessThan,
    ["CheckMiniGame"] = CheckMiniGame,
    ["TriggerTowerGuide"] = TriggerTowerGuide,
    ["CheckHeroCallTotalCount"] = CheckHeroCallTotalCount,
    ["IsBlessHeroOpen"] = IsBlessHeroOpen,
    ["BlessTriggerForceGuide"] = BlessTriggerForceGuide,
    ["CheckIsOpenMiniGame"] = CheckIsOpenMiniGame,
    ["CheckIsOpenMiniGameExceptPuzzleTower"] = CheckIsOpenMiniGameExceptPuzzleTower,
    ["CheckOpenGameLobby"] = CheckOpenGameLobby,
    ["CheckCloseGameLobby"] = CheckCloseGameLobby,
    ["CheckFristRecharge"] = CheckFristRecharge,
    ["FindGuideHeroUIOfDecorate"] = FindGuideHeroUIOfDecorate,
    ["IsDecorateOpen"] = IsDecorateOpen,
    ["SetDecorateGuideCount"] = SetDecorateGuideCount,
    ["ShowMiniGame"] = ShowMiniGame,
    ["OpenHeroSelect"] = OpenHeroSelect,
    ["IsModuleShown"] = IsModuleShown,
    ["ShowHunt"] = ShowHunt,
    ["StartMiniGame"] = StartMiniGame,
    ["SetStartTempleGodEquipListGuide"] = SetStartTempleGodEquipListGuide,
    ["IsMinigameSelectMode"] = IsMinigameSelectMode,
    ["ShowFirstSelectMinigame"] = ShowFirstSelectMinigame,

    ["ShowStoryDialog"] = ShowStoryDialog,

    --XMAN-- (不走这里了，走Func_Table)
    -- ["CheckGuildIsOpen_38"] = CheckGuildIsOpen_38,
    -- ["MonsterEnterHomeShow"] = MonsterEnterHomeShow,
    -- ["GetMonsterObjById"] = GetMonsterObjById,
    -- ["MoveToBuild"] = MoveToBuild,
    -- ["GuideHomePop"] = GuideHomePop,
    -- ["BomberManOpenTalk"] = BomberManOpenTalk,
    -- ["BomberManPop"] = BomberManPop,
}

function GetFun(name)
    if Func_Table[name] then
        return Func_Table[name]
    end
    return func_table[name]
end
function Clear()
    ifFristOpen = true
    isFirstEnterMonsterStage = true
    if monsterActionTimer then
        util.RemoveDelayCall(monsterActionTimer)
        monsterActionTimer = nil
    end
end
eventSys.Register(eventSys.SCENE_DESTROY, Clear)
eventSys.Register(eventSys.ACCOUNT_CHANGE_WORLD_RSP, Clear)
--eventSys.Register(eventSys.NET_DISCONNECT_EVENT,Clear)