local cysoldierssortie_comp_level = bc_Class("cysoldierssortie_comp_level") --类名用小游戏名加后缀保证全局唯一
cysoldierssortie_comp_level.dataSrc = nil --gameobject 上 gameluabehaviour组件数据
local minigame_mgr= require "minigame_mgr"
local ui_window_mgr = require("ui_window_mgr")
local bc_load_mgr = require "cysoldierssortie_load_mgr"
local NavMesh = CS.UnityEngine.AI.NavMesh
local MainLoop = CS.War.Script.MainLoop
local UnityEngine = CS.UnityEngine
local minigame_buff_mgr= require "minigame_buff_mgr"
local cysoldierssortie_DisposeTimer = cysoldierssortie_DisposeTimer
local log = require "log"
local game_scheme = require "game_scheme"
local cysoldierssortie_GetMgr = cysoldierssortie_GetMgr
local cysoldierssortie_MgrName = cysoldierssortie_MgrName
local cysoldierssortie_event_name = cysoldierssortie_event_name
local cysoldierssortie_LevelMode = cysoldierssortie_LevelMode
local bc_IsNotNull = bc_IsNotNull
local cysoldierssortie_FxName = cysoldierssortie_FxName
local cysoldierssortie_PlaySfx = cysoldierssortie_PlaySfx
local cysoldierssortie_DelayCallOnce = cysoldierssortie_DelayCallOnce
local bc_Time = bc_Time
local cysoldierssortie_OpenMusicController = cysoldierssortie_OpenMusicController
local cysoldierssortie_StopDelayCall = cysoldierssortie_StopDelayCall
local cysoldierssortie_scene_data = cysoldierssortie_scene_data
local isAsync = isAsync
local cysoldierssortie_GetLuaComp = cysoldierssortie_GetLuaComp
local string = string
local tonumber = tonumber
local cysoldierssortie_CampaignFR_TrialHeroID = cysoldierssortie_CampaignFR_TrialHeroID
local ipairs = ipairs
local pairs = pairs
local math = math
local ApiHelper = CS.XLuaUtil.LuaApiHelper
local SetTransformLocalPositionAndLocalRotation = ApiHelper.SetTransformLocalPositionAndLocalRotation
local GetTransformPositionXYZ = ApiHelper.GetTransformPositionXYZ
local cysoldierssortie_urp_ecs = cysoldierssortie_urp_ecs
local entity_manager = require("entity_manager")
local EntityHybridUtility = CS.Unity.Entities.EntityHybridUtility
local util = require "util"
local IsEntityHybridUtility = util.IsCSharpClass(EntityHybridUtility)
local cysoldierssortie_hero_anim_set = cysoldierssortie_hero_anim_set
local cysoldierssortie_character_state = cysoldierssortie_character_state
local cysoldierssortie_EventName = cysoldierssortie_EventName
local event = require "event"
local event_activity_define = require "event_activity_define"
local isEditor = CS.UnityEngine.Application.isEditor

--在编辑器下只加载DefineList,不管其他部分
if ExecuteInEditorScript then
    return cysoldierssortie_comp_level
end

local NeeGame = CS.CasualGame.lib_ChuagnYi.NeeG.NeeGame



-- cysoldierssortie_comp_level
-- comp_level为实际的关卡，挂载在level预制体上，其gameObject上面会有遗留cs代码NeeLevel.cs
-- NeeLevel方便在LuaBuildScene中点击设置唯一来快速与切换关卡
-- comp_level作为关卡逻辑中心，一般会去初始化保存一些关卡元素table，通过cysoldierssortie_GetLuaCompsInChildren方法
function cysoldierssortie_comp_level.__init(self, luaMono, referCol, luaData, ...)

    if luaMono then
        self.luaMono = luaMono
    end
    if referCol then
        referCol:Bind(self)
    end
    if luaData then
        cysoldierssortie_InitLuaData(self, luaData)
    end
    self.cysoldierssortie_firstRecharge_Endless = cysoldierssortie_firstRecharge_Endless
    self.cysoldierssortie_firstRecharge_Normal = cysoldierssortie_firstRecharge_Normal
    ---@type cysoldierssortie_mgr_cardBuff
    self.cardBuffMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.CardBuffMgr)
end
-- lua脚本正式开始

--生命周期函数
function cysoldierssortie_comp_level:OnEnable(data)
    if self.enabledOnce then
        return
    end
    self.enabledOnce = true;

    self.dataSrc = cysoldierssortie_CshapToLuaValue(data)
    self.gameObject = self.dataSrc.selfCshap.gameObject
    self.transform = self.dataSrc.selfCshap.transform

    self.init=false

    self.eventMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.event)
    self.eventMgr:RegisterEvt(self, cysoldierssortie_EventName.CSUpdate)
end

function cysoldierssortie_comp_level:OnDisable()
    if self.EL_CheckPower_Fun then
        event.Unregister(event_activity_define.MSG_XYX_ENDLESS_SET_HERO_POWER_RSP, self.EL_CheckPower_Fun)
        self.EL_CheckPower_Fun = nil
    end
    self.eventMgr:UnRegisterEvt(self, cysoldierssortie_EventName.CSUpdate)
    local levelMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
    if levelMgr then
        levelMgr:SetCurLevelFailFlag()
    end
    
    self:DisposeAutoVictoryCoroutine()
    local totalMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.total)
    totalMgr:SwitchMainLoop(false,true)
    if self._sceneItems then 
        self._sceneItems = nil
    end
    
    bc_Time.SetTimeScale(1)
end

function cysoldierssortie_comp_level:Start()
end

function cysoldierssortie_comp_level:Dispose()
    self:DisposeNavMeshData()
    cysoldierssortie_DisposeTimer()
end

function cysoldierssortie_comp_level:GetPlayerInitPos()
    return self._playerInitPos
end

function cysoldierssortie_comp_level:ResetLevel()
    self.MiniLevelCfg = self.levelMgr.MiniLevelConfig
    self.EventSpeed = self.MiniLevelCfg.EventSpeed * 0.01

    self.keyIdIndex = 0
    self.UpdateTimerIndex = 0
    self.allEnemyTable = {}
    self.enemyCount = 0
    self.rewardLevelCount = 0    --奖励模式总数量
    self.rewardLevelGetCount = 0 --奖励获得数量
    self.deadEnemyCount = 0      --死亡怪物数量
    self.deadBossCount = 0       --死亡Boss数量
    self.allPropsDic = {}
    self.allObstacleDic = {}
    self.heroUpPropDic = {}
    self.isEnterAutoTowerDefStage = nil
    self.autoTowerDefEnemyCount = 0 --(跑酷+塔防)自动塔防阶段数量统计
    self.autoTowerDefDeadEnemyCount = 0
    
    minigame_mgr.SetLevelScr(self)

    local delayComputeLevelTarget = self:CurLevelDelayCompute()
    minigame_mgr.OpenUiMiniGameInfo()

    local actorInstanceMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.ActorInstanceMgr)
    actorInstanceMgr:ResetEarlyWarning()

    self:LoadEnemy()
    self:LoadProps()
    if self.MiniLevelCfg.VictoryConditions == 1 then
        if not delayComputeLevelTarget then
            self.winSize = self.MiniLevelCfg.VictoryConditionsParameter.data[0]
        else
            self.winSize = self.MiniLevelCfg.VictoryConditionsParameter2.data[0]
        end
    elseif self.MiniLevelCfg.VictoryConditions == 2 and self.MiniLevelCfg.VictoryConditionsParameter.data then
        if not delayComputeLevelTarget then
            self.winSize = #self.MiniLevelCfg.VictoryConditionsParameter.data
        else
            self.winSize = #self.MiniLevelCfg.VictoryConditionsParameter2.data
        end
    elseif self.MiniLevelCfg.VictoryConditions == 4 then
        local param2DataCount = self.MiniLevelCfg.VictoryConditionsParameter2.count
        local parameter2Data = self.MiniLevelCfg.VictoryConditionsParameter2.data
        if parameter2Data and param2DataCount > 0 then
            self.winSize = parameter2Data[0]
        else
            self.winSize = 0
        end
    end

    if self.MiniLevelCfg.RewardLevel and self.MiniLevelCfg.RewardLevel > 0 then
        self.levelMgr:LoadRewardLevel(self.MiniLevelCfg.RewardLevel)
    end

    self:SetStartGame(true)
end

function cysoldierssortie_comp_level:CollectAllDrops()
    if self.dropMgr then
        self.dropMgr:PickUpAllDrop()
    end
end

---@return cysoldierssortie_dropData[] 获取已获得的掉落物品(已经通关的波次的凋落物)
---@return cysoldierssortie_dropData[] 获取已拾取的掉落物品(当前波次拾取的，但是未结算)
function cysoldierssortie_comp_level:GetCollectDropDatas()
    if self.dropMgr then
        return self.dropMgr:GetCollectDropDatas()
    end
    return nil, nil
end

function cysoldierssortie_comp_level:InitLevel(levelMgr)
    self.init=true
    self.levelMgr = levelMgr
    ---@type cysoldierssortie_mgr_drop
    self.dropMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.DropMgr) 
    self.RewardLevelMode=false
    self.isStartGame = false
    self.uiMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.ui) 
    self.uiMgr:InitUi(self.levelMgr.totalMgr.levelIndex)

    self.MiniLevelCfg = self.levelMgr.MiniLevelConfig
    self.EventSpeed=self.MiniLevelCfg.EventSpeed*0.01
    if not self.rfPlayer then
        return
    end
    
    local scene_data = cysoldierssortie_scene_data[self.MiniLevelCfg.MapID]
    if scene_data then
        self._playerInitPos = scene_data.playerInitPos
    end

    self.UseSoldierDic={}
    local UseSoldierIndex=1
    --使用配置士兵
    if self.MiniLevelCfg.UseSoldier and self.MiniLevelCfg.UseSoldier.data  then
        for k=0,#self.MiniLevelCfg.UseSoldier.data do
            if self.MiniLevelCfg.UseSoldier.data[k] then
                self.UseSoldierDic[UseSoldierIndex]=self.MiniLevelCfg.UseSoldier.data[k]
                UseSoldierIndex=UseSoldierIndex+1
            end
        end
    end

    if UseSoldierIndex>2 then
        self._useSpecialSoldier = true
    end

    --没配置使用默认士兵
    if UseSoldierIndex<6 then
        for k=UseSoldierIndex,5 do
            self.UseSoldierDic[k]=cysoldierssortie_config_character_level[k]
        end
    end
    
    self.playerLua = cysoldierssortie_GetLuaComp(self.rfPlayer)
    self.playerLua:Init(self);
    local typeof=typeof
    local CanvasType      = typeof(CS.UnityEngine.Canvas)
    local WorldCanvas = self.gameObject.transform:Find("Props/WorldCanvas"):GetComponent(CanvasType)
    WorldCanvas.sortingOrder=1
    self.winSize = 1
    self.ObstacleHPFloatMin=0
    self.ObstacleHPFloatMax=0
    if self.MiniLevelCfg.ObstacleHPFloat and self.MiniLevelCfg.ObstacleHPFloat.data then
        self.ObstacleHPFloatMin=self.MiniLevelCfg.ObstacleHPFloat.data[0] or 0
        self.ObstacleHPFloatMax=self.MiniLevelCfg.ObstacleHPFloat.data[1] or 0
    end
    
    self.isAllEnemyInitMove = false
    self.keyIdIndex = 0
    self.UpdateTimerIndex=0
    self.allEnemyTable={}
    self.enemyCount = 0
    self.rewardLevelCount=0--奖励模式总数量
    self.rewardLevelGetCount=0--奖励获得数量
    self.deadEnemyCount=0--死亡怪物数量
    self.deadBossCount=0--死亡Boss数量
    self._ai_nav = false
    self.allPropsDic={}
    self.allObstacleDic={}
    self.heroUpPropDic={}
    self.isEnterAutoTowerDefStage = nil
    self.autoTowerDefEnemyCount = 0 --(跑酷+塔防)自动塔防阶段数量统计
    self.autoTowerDefDeadEnemyCount = 0
    
    self:ResInitOnLoad()
    minigame_mgr.SetLevelScr(self)
    
    local delayComputeLevelTarget = self:CurLevelDelayCompute()
    minigame_mgr.OpenUiMiniGameInfo()
    
    self:LoadScene(self.MiniLevelCfg.MapID)--self.MiniLevelCfg.MapID
    self:LoadEnemy()
    self:LoadProps()
    if self.MiniLevelCfg.VictoryConditions==1 then
        if not delayComputeLevelTarget  then
            self.winSize = self.MiniLevelCfg.VictoryConditionsParameter.data[0]
        else
            self.winSize = self.MiniLevelCfg.VictoryConditionsParameter2.data[0]
        end
    elseif self.MiniLevelCfg.VictoryConditions==2 and self.MiniLevelCfg.VictoryConditionsParameter.data  then
        if not delayComputeLevelTarget  then
            self.winSize = #self.MiniLevelCfg.VictoryConditionsParameter.data
        else
            self.winSize = #self.MiniLevelCfg.VictoryConditionsParameter2.data
        end
    elseif self.MiniLevelCfg.VictoryConditions == 4 then
        local param2DataCount = self.MiniLevelCfg.VictoryConditionsParameter2.count
        local parameter2Data = self.MiniLevelCfg.VictoryConditionsParameter2.data
        if parameter2Data and param2DataCount > 0 then
            self.winSize = parameter2Data[0]
        else
            self.winSize = 0
        end
    end

    if self.MiniLevelCfg.RewardLevel and self.MiniLevelCfg.RewardLevel>0 then
        self.levelMgr:LoadRewardLevel(self.MiniLevelCfg.RewardLevel)
    end
    
    self:EndlessRushReady()
end

-- 关卡进度加速，当敌人小于N时开启无限索敌
function cysoldierssortie_comp_level:IsStartLevelAcceleration(leftEnemyCount)
    if self._isStartedLevelAcceleration then
        return false
    end
    if not self.MiniLevelCfg then
        return false
    end
    if not self.MiniLevelCfg.LevelAcceleration or self.MiniLevelCfg.LevelAcceleration <=0 then
        return false        
    end

    if leftEnemyCount<= self.MiniLevelCfg.LevelAcceleration then
        self._isStartedLevelAcceleration = true
        return true
    end
    
    return false
end

function cysoldierssortie_comp_level:GetCurMapSprintModule()
    if self.MiniLevelCfg and self.MiniLevelCfg.MapID then
        return cysoldierssortie_scene_data[self.MiniLevelCfg.MapID].sprintModule
    end
    return nil
end

function cysoldierssortie_comp_level:GetCurMapEnterAutoDefenceDis()
    --优先使用表格配置数据
    if self.MiniLevelCfg and self.MiniLevelCfg.VictoryConditionsParameter and self.MiniLevelCfg.VictoryConditionsParameter.count > 0 then
        return self.MiniLevelCfg.VictoryConditionsParameter.data[0]
    end
    
    if self.MiniLevelCfg and self.MiniLevelCfg.MapID then
        return cysoldierssortie_scene_data[self.MiniLevelCfg.MapID].enterAutoTowerDefenceDis
    end
    return nil
end

function cysoldierssortie_comp_level:GetCurMapFogMaskData()
    if self.MiniLevelCfg and self.MiniLevelCfg.MapID then
        return cysoldierssortie_scene_data[self.MiniLevelCfg.MapID].fogMask
    end
    return nil
end

function cysoldierssortie_comp_level:IsUseSpecialSoldier()
    return self._useSpecialSoldier
end

---@return boolean 是否开启神兽系统
function cysoldierssortie_comp_level:IsOpenDrone()
    return not (self.cysoldierssortie_firstRecharge_Endless or self.cysoldierssortie_firstRecharge_Normal)
end

function cysoldierssortie_comp_level:IsEnemyAutoMoveForward()
    if not self.MiniLevelCfg then
        return true
    end
    if not self.MiniLevelCfg.AutoAdvance then
        return true
    end
    
    local enableForwardMove = true
    if self.MiniLevelCfg.AutoAdvance == 0  then
        enableForwardMove = true
    else
        enableForwardMove = false
    end
    
    return enableForwardMove
end

function cysoldierssortie_comp_level:LoadEnemy()
    local enemyGroup = self.transform:Find("EnemyGroup")
    if bc_IsNotNull(enemyGroup) then
        local childCount =  enemyGroup.childCount
        for i = 0, (childCount - 1) do
            local spawnGo = enemyGroup:GetChild(i).gameObject
            local spawnEntity = cysoldierssortie_GetLuaComp(spawnGo)
            if bc_IsNotNull(spawnEntity) then
                spawnEntity:Init()
                if self.dropMgr then
                    self.dropMgr:AddEnemyGroup(spawnEntity)
                end
            end
        end
    end
end

function cysoldierssortie_comp_level:LoadProps()
    local propGroup = self.transform:Find("Props")
    if bc_IsNotNull(propGroup) then
        local childCount =  propGroup.childCount
        for i = 0, (childCount - 1) do
            local spawnGo = propGroup:GetChild(i).gameObject
            local spawnEntity = cysoldierssortie_GetLuaComp(spawnGo)
            if bc_IsNotNull(spawnEntity) then
                spawnEntity:Init()
            end
        end
    end
end



function cysoldierssortie_comp_level:DebugEnemyLevelInfo()
    local enemyGroup = self.transform:Find("EnemyGroup")
    local debugLevelInfo = "\n"
    local infoTables = {}
    if bc_IsNotNull(enemyGroup) then
        local childCount =  enemyGroup.childCount
        for i = 0, (childCount - 1) do
            local spawnGo = enemyGroup:GetChild(i).gameObject
            local spawnEntity = cysoldierssortie_GetLuaComp(spawnGo)
            if bc_IsNotNull(spawnEntity) then
                local debugInfo = spawnEntity:DebugSpawnPointInfo()
                infoTables[#infoTables+1] = debugInfo
                --debugInfo = "第"..(i+1).."波"..debugInfo.."\n"
                --debugLevelInfo = debugLevelInfo..debugInfo
            end
        end
        table.sort(infoTables,function(a,b)
            return a.time < b.time
        end)
        for i=1,#infoTables do
            local tmp_info = "第"..(i).."波"..infoTables[i].info.."\n"
            debugLevelInfo =  debugLevelInfo..tmp_info
        end
    end
    return debugLevelInfo
end

function cysoldierssortie_comp_level:DebugPropLevelInfo()
    local propGroup = self.transform:Find("Props")
    local debugLevelInfo = "\n"
    local infoTables = {}
    if bc_IsNotNull(propGroup) then
        local childCount =  propGroup.childCount
        for i = 0, (childCount - 1) do
            local propGo = propGroup:GetChild(i).gameObject
            local propEntity = cysoldierssortie_GetLuaComp(propGo)
            if bc_IsNotNull(propEntity) then
                local res,infoTable = xpcall(function()
                   return propEntity:DebugPropInfo()
                end,debug.traceback)
                if infoTable then
                    infoTables[#infoTables+1] = infoTable
                end
            end
        end
        
        table.sort(infoTables,function(a,b)
            return a.time < b.time
        end)
    end
    for i=1,#infoTables do
        debugLevelInfo = debugLevelInfo..infoTables[i].info
    end
    return debugLevelInfo
end

function cysoldierssortie_comp_level:AddSceneItem(item)
    if self.MiniLevelCfg.LevelMode ==  cysoldierssortie_LevelMode.HeroRunnerMode or
            self.MiniLevelCfg.LevelMode == cysoldierssortie_LevelMode.HeroTowerDefenceMode then
        self._sceneItems = self._sceneItems or {}
        self._sceneItems[#self._sceneItems+1] = item
        item.gameObject:SetActive(false)
    end
end

function cysoldierssortie_comp_level:ShowSceneItems()
    if self._sceneItems then
        for i=1, #self._sceneItems do
            self._sceneItems[i].gameObject:SetActive(true)
        end
    end    
end

--正试开始游戏
function cysoldierssortie_comp_level:SetStartGame(nextFlag)
    if not self.isStartGame and not self.isGameOver then
        self.isStartGame = true
        -- 活动模式开始时，要移除不提供试用的英雄
        if not nextFlag and (self.cysoldierssortie_firstRecharge_Endless or self.cysoldierssortie_firstRecharge_Normal) then
            self:RemoveTrailHero()
        end

        if not nextFlag then
            cysoldierssortie_OpenMusicController(self.MiniLevelCfg.Bgm)
            minigame_mgr.SetStartGame()
        else
            minigame_mgr.ReSetStartGame()
        end
        local delayComputeLevelTarget = self:CurLevelDelayCompute()
        if not delayComputeLevelTarget then
            minigame_mgr.ShowUiMiniGameInfo()
        end
        local actorInstanceMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.ActorInstanceMgr)
        actorInstanceMgr:AddEarlyWarningComp()

        self:AutoVictory()
        self:StopMainLoop()

        local eventMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.event)
        eventMgr:TriggerEvt(cysoldierssortie_event_name.START_GAME)
    end
end

function cysoldierssortie_comp_level:StopMainLoop()
    local totalMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.total)
    totalMgr:SwitchMainLoop(self.isStartGame,true)
end

function cysoldierssortie_comp_level:InputMgrOnBeginDrag(eventData)
    -- 活动模式，不使用滑动开始游戏
    if self.cysoldierssortie_firstRecharge_Endless or self.cysoldierssortie_firstRecharge_Normal then
        return
    end
    self:SetStartGame()
    minigame_mgr.InputMgrOnBeginDrag(eventData)
end

function cysoldierssortie_comp_level:AutoVictory()
    if not self.MiniLevelCfg or not self.MiniLevelCfg.limitTime or self.MiniLevelCfg.limitTime <=0 then
        return
    end
    local limitTime = self.MiniLevelCfg.limitTime
    self._auto_victory_coroutine =  cysoldierssortie_StartCoroutine(self,function()
        coroutine.yield(UnityEngine.WaitForSeconds(limitTime))
        self:GameOver(true)
    end)
end

function cysoldierssortie_comp_level:DisposeAutoVictoryCoroutine()
    if self._auto_victory_coroutine then
        cysoldierssortie_StopCoroutine(self,self._auto_victory_coroutine)
        self._auto_victory_coroutine = nil
    end
end

function cysoldierssortie_comp_level:DisposeNavMeshData()
    if self._ai_nav then
        self._ai_nav = false
    end
    if  self._nav_mesh_instance then
        NavMesh.RemoveAllNavMeshData()
        self._nav_mesh_instance = nil
    end
end

-- 是否是跑酷模式
function cysoldierssortie_comp_level:IsRunnerMode()
    if not self.MiniLevelCfg then
        return false
    end
    local levelMode =  self.MiniLevelCfg.LevelMode
    if levelMode == cysoldierssortie_LevelMode.HeroRunnerMode or  levelMode == cysoldierssortie_LevelMode.SoldierRunnerMode then
        return true
    end
    
    return false
end

-- 是否是可以上阵英雄的模式
function cysoldierssortie_comp_level:IsHeroMode()
    if not self.MiniLevelCfg then
        return false
    end
    local levelMode =  self.MiniLevelCfg.LevelMode
    if levelMode == cysoldierssortie_LevelMode.HeroRunnerMode or levelMode == cysoldierssortie_LevelMode.HeroTowerDefenceMode then
        return true
    end
    
    return false
end

function cysoldierssortie_comp_level:GetLevelForwardSpeed()
    local forwardSpeed =  (self.MiniLevelCfg.ForwardSpeed or 0) / 100
    return forwardSpeed
end

function cysoldierssortie_comp_level:GetTargetDis()
    return self.MiniLevelCfg.VictoryConditionsParameter.data[0]
end

--关卡目标延迟计算
function cysoldierssortie_comp_level:CurLevelDelayCompute()
    local runnerMode = self:IsRunnerMode()
    if not runnerMode then
        return false
    end

    local victoryByDistance =   self:LevelVictoryDistance()
    if victoryByDistance then
        return false
    end
    
    return true
end

function cysoldierssortie_comp_level:IsEnterAutoTowerDefence()
    if not self:IsAutoTowerDefAfterRunner() then
        return false
    end

    if self.isEnterAutoTowerDefStage then
        return true
    end
    
    local playerZ = self:GetPlayerZ()
    local enterTowerDefenceDis =  self:GetCurMapEnterAutoDefenceDis()
    if not enterTowerDefenceDis then
        enterTowerDefenceDis = self.levelMgr:GetEnterTowerDefenceDis()
    end
    if playerZ >= enterTowerDefenceDis then
        self.isEnterAutoTowerDefStage = true
        self._enterFreeMoveState = true
        return true
    end
    
    return false
end

function cysoldierssortie_comp_level:GetPlayerZ()
    local x,y,z = self.playerLua:GetPositionXYZ()
    return z
end

function cysoldierssortie_comp_level:GetDontAllowControl()
    return self._dont_allow_control
end

function cysoldierssortie_comp_level:GetMaxViewEnemyCount()
    if self._performanceMaxEnemyViewNum then
        return self._performanceMaxEnemyViewNum
    end
    local performanceMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PerformanceMgr)
    local maxEnemyViewNum =  performanceMgr:GetEnemyViewNum()
    maxEnemyViewNum = math.min(self._maxEnemyNum or 60,maxEnemyViewNum)
    self._performanceMaxEnemyViewNum = maxEnemyViewNum
    return self._performanceMaxEnemyViewNum
end

function cysoldierssortie_comp_level:LoadScene(id)
    local scene_data = cysoldierssortie_scene_data[id]
    if not scene_data then
        return
    end
    local scene_path = scene_data.path
    local pool_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
    if not pool_mgr then
        return
    end
    
    local navMeshDataPath =  scene_data.navMeshDataPath
    if navMeshDataPath then
        self._ai_nav = true
    else
        self._ai_nav = false
    end

    self._freedomMoveCameraData = scene_data.freedomMoveCameraData
    
    self._dont_allow_control = scene_data.DontAllowControl
    
    self._cull_offset = scene_data.cull_offset or 25
    
    self._maxEnemyNum = scene_data.maxViewEnemyNum
    
    local cam_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.cam)
    if(id == 5) then
        cam_mgr:EnableDepthBuffer()
    end
    
    local loadCallBack = function()
        local isHeroModel = self:IsHeroMode()
        if isHeroModel then
            local ui_mini_select_hero = ui_window_mgr:ShowModule("ui_mini_select_hero")
            if self.cysoldierssortie_firstRecharge_Endless or self.cysoldierssortie_firstRecharge_Normal then
                ui_mini_select_hero:SetInputParam(cysoldierssortie_firstRecharge_SlotSaveTag, true, true, self)
            end
            self.playerLua._selectHeroUI:SetActive(true)
            if self.uiMgr._miniUIRoot then
                self.uiMgr._miniUIRoot:SetActive(false)
            end
        end
      
        if navMeshDataPath then
            bc_load_mgr.LoadRes(navMeshDataPath,function(asset)
                self._nav_mesh_instance =   NavMesh.AddNavMeshData(asset)
            end)
        end
        --告诉进度条当前关卡加载完毕了
        event.Trigger(event.MINIGAME_SCENE_LOAD_SUCCESS)
    end
    
    local isRunnerMode = self:IsRunnerMode()
    local level_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
    if isRunnerMode and scene_data.mapModule then
        --调用mgr_level 中 sceneDynamicLoader 方法，传入mapModule
        local data = 
        { 
            mapModule = scene_data.mapModule,   
            targetDis = self.MiniLevelCfg.VictoryConditionsParameter.data[0] or 50,
            parent = level_mgr.transform
        }
        self.levelMgr:DynamicLoader(data)
        loadCallBack()
    else
        pool_mgr:CreateEntityAsync(scene_path,level_mgr.transform,loadCallBack,false)
    end
    
    if cam_mgr then
        cam_mgr:SetCameraByData(scene_data.cameraData)
        if scene_data.cameraData.height and  scene_data.cameraData.distance then
            local cam_follow_pos = self.playerLua.rfCameraFollow.position
            self.playerLua.rfCameraFollow.position = {x= cam_follow_pos.x, y = scene_data.cameraData.height, z = scene_data.cameraData.distance}
        end
    end
    if scene_data.mapLeftWidth and scene_data.mapRightWidth then
        self.playerLua:SetMapWidthRightLeft(scene_data.mapLeftWidth,scene_data.mapRightWidth)
    else
        self.playerLua:SetMapWidth(scene_data.mapWidth)
    end
    if scene_data.camMoveLeftPosXLimit and scene_data.camMoveRightPosXLimit then
        self.playerLua:SetCamMovePosLimit(scene_data.camMoveLeftPosXLimit,scene_data.camMoveRightPosXLimit)
    else
        self.playerLua:SetCamMovePosLimit()
    end
    
    self.playerLua:SetCameraFollowData(scene_data.cameraData)
    self._cam_move = scene_data.cameraData.follow
    
    --render data
    local scene_mgr =  cysoldierssortie_GetMgr(cysoldierssortie_MgrName.scene)
    if scene_data.renderIndex then
        scene_mgr:InitSceneGlobalValue(scene_data.renderIndex) 
    end
end

function cysoldierssortie_comp_level:IsCamCanMove()
    return self._cam_move
end

function cysoldierssortie_comp_level:SmoothToFreedomView(followRoot)
    if not self._freedomMoveCameraData then
        return
    end
    local camMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.cam)
    camMgr:SmoothToFreedomView(followRoot,self._freedomMoveCameraData.distance,self._freedomMoveCameraData.height,self._freedomMoveCameraData.fov, self._freedomMoveCameraData.rotate_x,1.2)
end

--资源预加载
function cysoldierssortie_comp_level:ResInitOnLoad()
    local upgrade_effect_path = "art/effects/effects/effect_mini_upgrade/prefabs/effect_mini_upgrade.prefab"
    local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
    poolMgr:CreateEntityAsync(upgrade_effect_path,poolMgr.transform,function(go)
        go:SetActive(false)
        if isAsync then
            NeeGame.ReturnObjectAsync(go)
        else
            NeeGame.ReturnObject(go)
        end
    end)
end

function cysoldierssortie_comp_level:GetKeyIdIndex()
    self.keyIdIndex = self.keyIdIndex +1
    return self.keyIdIndex
end

function cysoldierssortie_comp_level:DelEnemy(enemyLua,dontCheckBuff)
    local fxName=cysoldierssortie_FxName.enemyDie
    local  cfg=game_scheme:MiniUnit_0(enemyLua._unitID)
    if cfg and cfg.DeadSound and cfg.DeadSound~="" then
        fxName=cfg.DeadSound
       -- log.Error(fxName)
    end
    cysoldierssortie_PlaySfx(fxName,1)
    if not dontCheckBuff then
        minigame_buff_mgr.CheckCondition(enemyLua,minigame_buff_mgr.ConditionType.Die)
    end
    for i, v in ipairs(self.allEnemyTable) do
        if v.keyIdIndex==enemyLua.keyIdIndex then
            table.remove(self.allEnemyTable,i)
            enemyLua.isDelKey=true
            return
        end
    end

end

function cysoldierssortie_comp_level:OnStartCoroutine(func, ...)
    cysoldierssortie_StartCoroutine(self,func,...)
end
function cysoldierssortie_comp_level:PlaySfx(fxName,cd)
    cysoldierssortie_PlaySfx(fxName,1)
end

function cysoldierssortie_comp_level:LoadPoolRes(resPath, cb)
    local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
    if not bc_IsNotNull(poolMgr.transform) then
        return
    end
    poolMgr:CreateEntityAsync(resPath,poolMgr.transform,function(go)
        if not bc_IsNotNull(go) then
            return
        end
        if cb then
            cb(go)
        end
    end)
end
function cysoldierssortie_comp_level:PlaySkillEffectRes(effectParam)
    local effect_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.EffectMgr)
    effectParam.sid=effect_mgr:CreateEffect(effectParam)
end
function cysoldierssortie_comp_level:ReleaseEffect(effect_sid)
    local effect_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.EffectMgr)
   effect_mgr:ReleaseEffect(effect_sid)
end

function cysoldierssortie_comp_level:GetCysoldierssortie_GetMgr()
    local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)

    return poolMgr
end

function cysoldierssortie_comp_level:GetUseSoldier(level)
    return self.UseSoldierDic[level] or 101
end

function cysoldierssortie_comp_level:AddObstacle(ObstacleItem)
    self.allObstacleDic[ObstacleItem]=true
end
function cysoldierssortie_comp_level:GetAllObstacle()
    return self.allObstacleDic
end
function cysoldierssortie_comp_level:DelObstacle(ObstacleItem)
    self.allObstacleDic[ObstacleItem]=nil;
end

function cysoldierssortie_comp_level:AddProps(PropItem)
        self.allPropsDic[PropItem]=true
end
function cysoldierssortie_comp_level:GetProps()
        return self.allPropsDic
end

function cysoldierssortie_comp_level:GetHeroUpProp(heroId)
   return self.heroUpPropDic[heroId]
end
function cysoldierssortie_comp_level:SetHeroUpProp(heroId)
    if not self.heroUpPropDic[heroId] then
        self.heroUpPropDic[heroId]=1
    else
        self.heroUpPropDic[heroId]=self.heroUpPropDic[heroId]+1
    end
end
function cysoldierssortie_comp_level:AddEnemy(enemyLua)
    enemyLua.keyIdIndex=self:GetKeyIdIndex()
    self.allEnemyTable[#self.allEnemyTable+1]=enemyLua
end
function cysoldierssortie_comp_level:GetAllEnemy()
    return  self.allEnemyTable
end

function cysoldierssortie_comp_level:LevelVictoryDistance()
    return  self.MiniLevelCfg.VictoryConditions == 4
end

function cysoldierssortie_comp_level:IsAutoTowerDefAfterRunner()
    local runnerMode = self:IsRunnerMode()
    if not runnerMode then
        return false
    end
    return self.MiniLevelCfg.VictoryConditions == 4 and self.winSize and self.winSize > 0
end

function cysoldierssortie_comp_level:AddEnemyCount(num, isAutoTowerDefSpawn)
    self.enemyCount = self.enemyCount + num
    if self:IsAutoTowerDefAfterRunner() and isAutoTowerDefSpawn then
        self.autoTowerDefEnemyCount = self.autoTowerDefEnemyCount + num
    end
    
    if self.MiniLevelCfg.VictoryConditions ~= 4 or self:IsEnterAutoTowerDefence() then
        minigame_mgr.UpdataUiMiniGameInfo()
    end
end
function cysoldierssortie_comp_level:AddRewardLevelCount(num)
    self.rewardLevelCount = self.rewardLevelCount + num
    --if self.MiniLevelCfg.VictoryConditions ~= 4 then
    --    minigame_mgr.UpdataUiMiniGameInfo()
   -- end
end

function cysoldierssortie_comp_level:UpdateDisVictoryState()
    if self.MiniLevelCfg.VictoryConditions == 4 then
        if not self.isEnterAutoTowerDefStage then
            minigame_mgr.UpdataUiMiniGameInfo() 
        end
        local playerZ = self:GetPlayerZ()
        local targetDis = self:GetTargetDis()
        if playerZ >= targetDis then
            if self:IsAutoTowerDefAfterRunner() then
                self.isEnterAutoTowerDefStage = true
            else
                self.playerLua:PlayerWin()
                self:GameOver(true)
            end
        end
    end
end

local RewardLeveDelayResultTime = 2.86 --奖励关卡延迟弹出界面时间
--胜利检测
function cysoldierssortie_comp_level:RaduceEnemyCount(num,enemyItem,dontCheckBuff)
    if not enemyItem.keyIdIndex then
        return
    end
   -- log.Error("RaduceEnemyCount="..enemyItem.keyIdIndex)
   -- log.Error("1111RaduceEnemyCount="..self.deadEnemyCount)
    self:DelEnemy(enemyItem,dontCheckBuff)
   local enemyId =enemyItem._unitID
   -- self.enemyCount = self.enemyCount - num
    
    local curEnemyCount = self:GetCurEnemyCount()
    minigame_buff_mgr.CheckFailureCondition(nil,minigame_buff_mgr.FailureConditions.EnemySizeMin,curEnemyCount)
    minigame_buff_mgr.CheckCondition(nil,minigame_buff_mgr.ConditionType.EnemySizeMax,curEnemyCount)
    
    --延迟关卡目标, 改为完成两个目标（距离+怪物数量）
    --local curLevelDelayCompute = self:CurLevelDelayCompute()
    --local isEnterAutoTowerDefence = self:IsEnterAutoTowerDefence()
    --if curLevelDelayCompute and not isEnterAutoTowerDefence then
    --    return
    --end

    self.deadEnemyCount = self.deadEnemyCount + num
    local isEnterAutoTowerDefence = self:IsEnterAutoTowerDefence()
    if isEnterAutoTowerDefence then
        self.autoTowerDefDeadEnemyCount = self.autoTowerDefDeadEnemyCount + num
    end

    if self.dropMgr then
        self.dropMgr:CheckDropByCharactor(enemyItem, self.enemyCount - self.deadEnemyCount)
    end
    
    --关卡进度加速
    local isStartedAcceleration = self:IsStartLevelAcceleration(curEnemyCount)
   
    if isStartedAcceleration then
        self.playerLua:AllSoldierUnLimitDisLookAtTarget()
    end
    
    if self.RewardLevelMode then
        local tempUnit = game_scheme:MiniUnit_0(enemyId)
        local coin= tempUnit.coin or 1
        if coin==0 then
            coin=1
        end
        
        self.rewardLevelGetCount=self.rewardLevelGetCount+coin
        if not self._delayUpdateRewardTimer then
            self._delayUpdateRewardTimer = cysoldierssortie_DelayCallOnce(2.0,function()
                minigame_mgr.UpdataUiMiniGameInfo()
                self._delayUpdateRewardTimer = nil
            end)
        end
        if self.rewardLevelCount <= self.rewardLevelGetCount then
                local levelMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
                if levelMgr then
                    levelMgr:GameOverWinFeedBack()
                end
                self.playerLua:PlayerWin()
                cysoldierssortie_DelayCallOnce(RewardLeveDelayResultTime,function()
                    self:GameOver(true)
                end)
        end
        return
    end

    if self.MiniLevelCfg.VictoryConditions ~= 4 or isEnterAutoTowerDefence then
        minigame_mgr.UpdataUiMiniGameInfo()
    end
    if self.MiniLevelCfg.VictoryConditions==1 then
        if self.deadEnemyCount>=self.winSize then
            self.playerLua:PlayerWin()
            self:GameOver(true)
        end
    elseif self.MiniLevelCfg.VictoryConditions==2 and self.MiniLevelCfg.VictoryConditionsParameter.data  then
        for k=0,#self.MiniLevelCfg.VictoryConditionsParameter.data do
            if self.MiniLevelCfg.VictoryConditionsParameter.data[k]==enemyId then
                self.deadBossCount=self.deadBossCount+1
                if self.deadBossCount>=self.winSize then
                    self.playerLua:PlayerWin()
                    self:GameOver(true)
                end
                return
            end
        end
    elseif self.MiniLevelCfg.VictoryConditions == 4 then
        if isEnterAutoTowerDefence and self.autoTowerDefEnemyCount <= self.autoTowerDefDeadEnemyCount then
            self.playerLua:PlayerWin()
            self:GameOver(true)
        end
        return
    else
        if self.enemyCount <= self.deadEnemyCount then
            self.playerLua:PlayerWin()
            self:GameOver(true)
        end

    end
end

function cysoldierssortie_comp_level:GetMainCam()
    local camMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.cam)
    if camMgr then
        return camMgr.rfMainCam
    end
    return nil
end




function cysoldierssortie_comp_level:GetCurEnemyCount()
    return self.enemyCount - self.deadEnemyCount
end

function cysoldierssortie_comp_level:AddSpawnEnemy(id, pos,spawnTransform,hp,att)
    --self:CreateCharacter(id,pos)
    self._actor_instance_mgr = self._actor_instance_mgr or cysoldierssortie_GetMgr(cysoldierssortie_MgrName.ActorInstanceMgr)
    self._actor_instance_mgr:CreateCharacter(id,pos,spawnTransform,true,nil,hp,att)
    self:AddEnemyCount(1)
end

function cysoldierssortie_comp_level:Update()


   -- cysoldierssortie_log("UpdateUpdateUpdate")
end

function cysoldierssortie_comp_level:PlayEnterRewardLevelFX()
    cysoldierssortie_PlaySfx("art/sound/ui/300211.mp3",1,0.1,true)
end

function cysoldierssortie_comp_level:UpdateTimer()
     --self:EnemySeparation()
    minigame_mgr.UpdateTimer()
end
--怪物分离
function cysoldierssortie_comp_level:EnemySeparation()

    self.UpdateTimerIndex=self.UpdateTimerIndex+1
    if self.UpdateTimerIndex%60~=0 then
       return
    end

    local size=#self.allEnemyTable
    if size <2 then
        return
    end
    local newEnemyTable={}
    for i, v in ipairs(self.allEnemyTable) do
        if not v._character_entity._cull then
            table.insert(newEnemyTable,v)
        end
    end
     size=#newEnemyTable
    if size <2 then
        return
    end
   --log.Error("aaaaaa="..size)
    for i=size,1,-1 do
        local enemyItem=newEnemyTable[i]
        self:EnemySeparationOne(enemyItem,newEnemyTable)
    end


end
function cysoldierssortie_comp_level:EnemySeparationOne(enemy,enemyList)
    local separationDistance=1.5
    for i, v in ipairs(enemyList) do
        if enemy.keyIdIndex~=v.keyIdIndex then
                local curDis = CS.UnityEngine.Vector3.Distance(enemy.transform.position,  v.transform.position)
                if curDis<separationDistance then
                    local dir = (enemy.transform.position - v.transform.position).normalized
                    enemy.transform.position = enemy.transform.position + dir* enemy._moveSpeed * bc_Time.deltaTime;
                end
        end
    end
end
function cysoldierssortie_comp_level:InitRewardLevel()
    self.RewardLevelMode=true
    local totalMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.total)
    totalMgr:AddMainLoopCounter()
    self.levelMgr:InitLevelJsonData(self.levelMgr.RewardLevelData,function()
        totalMgr:ReduceMainLoopCounter()
        minigame_mgr.StartRewardLevelMode()
    end)
end
function cysoldierssortie_comp_level:StartRewardLevel()
    log.Warning("开始奖励关卡模式")
    self:LoadProps()
    self:LoadEnemy()
end

function cysoldierssortie_comp_level:GameOver(isWin)
    -- 无尽模式下，有主动退出的结算。这时候不能再结算了
    if self.isGameOver or self.EL_OverFlag then
        return
    end
    self.isGameOver = true
    self.isStartGame = false
    -- 发送服务器波次/关卡通关消息
    if self.cysoldierssortie_firstRecharge_Endless or self.cysoldierssortie_firstRecharge_Normal then
        self.dropMgr:LevelEnd(isWin)
    end
    local overFlag = true
    -- 无尽模式下，波次结束后，要判断下一波是否能玩：是否解锁/战力达到要求
    if isWin and self.cysoldierssortie_firstRecharge_Endless then
        local totalMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.total)
        -- 记录通关的关卡
        self.EL_CompletedLevel = totalMgr.levelIndex
        if totalMgr.levelIndex < #totalMgr.levelIDS then
            overFlag = false
            self:EndlessCheckNextOpen(function(powerFlag, unlockFlag)
                if powerFlag and unlockFlag then
                    self.isGameOver = false
                    self.levelMgr:EndlessNextLevel()
                else
                    self.EL_BreakType = not unlockFlag and 2 or 1
                    self:GameOverImmediately(isWin)
                end
            end)
        end
    end
    if overFlag then
        self:GameOverImmediately(isWin)
    end
end

--游戏结束
---@param isWin --是否胜利
function cysoldierssortie_comp_level:GameOverImmediately(isWin)
    -- 通关结算，要拾取所有掉落
    if self.cysoldierssortie_firstRecharge_Endless or self.cysoldierssortie_firstRecharge_Normal then
        self.dropMgr:PickUpAllDrop()
        self.EL_OverFlag = true
    end

    if isWin and self.levelMgr and self.levelMgr.RewardLevelData and not self.RewardLevelMode then
        log.Warning("进入奖励关卡模式")
        self:InitRewardLevel()
        return
    end
    
    self:DisposeAutoVictoryCoroutine()
    
    local totalMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.total)
    if totalMgr then
        totalMgr:SwitchMainLoop(false,true)
    end
    
    if isWin then
        cysoldierssortie_PlaySfx(cysoldierssortie_FxName.gameWin)
    else
        cysoldierssortie_PlaySfx(cysoldierssortie_FxName.gameFail)
    end
    --等待结算界面进度条增长后显示胜利or失败界面
    
    local runnerMode = self:IsRunnerMode()
    local sprintDelayShowUI = 0.5 --冲刺后延迟多久正式结算
    local waitSettlement = 0.5 --等待结算时长
    if self.cysoldierssortie_firstRecharge_Normal or self.cysoldierssortie_firstRecharge_Endless then
        waitSettlement = 0.85
    end
    cysoldierssortie_DelayCallOnce(waitSettlement,function()
        if isWin then
            if runnerMode then
               self.playerLua:Sprint()
                cysoldierssortie_DelayCallOnce(sprintDelayShowUI,function()
                    self:GameWin()
                end)
            else
                self:GameWin()
            end
        else
            self:GameFail()
        end
    end)
end

function cysoldierssortie_comp_level:GameFail()
   -- cysoldierssortie_MsgCenter:GetInstance():Broadcast(cysoldierssortie_MsgCode.STAGE_FAIL)
    minigame_mgr.BroadcastMsg(cysoldierssortie_MsgCode.STAGE_FAIL)
end

function cysoldierssortie_comp_level:GameWin()
    minigame_mgr.BroadcastMsg(cysoldierssortie_MsgCode.STAGE_SUCCESS)
   -- cysoldierssortie_MsgCenter:GetInstance():Broadcast(cysoldierssortie_MsgCode.STAGE_SUCCESS)
end

--- 获取英雄位置的屏幕坐标
---@param slotIndex --英雄位置索引
function cysoldierssortie_comp_level:GetSlotScreenPos(slotIndex, off)
    local slotMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.SlotMgr)
    local camMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.cam)
    if not slotMgr or not camMgr or not slotMgr._slots then
        return nil
    end
    local slot = slotMgr._slots[slotIndex]
    if not slot then
        return nil
    end
    local pos = slot.transform.position
    pos.x = pos.x + off.x
    pos.y = pos.y + off.y
    pos.z = pos.z + off.z
    return camMgr.rfMainCam:WorldToScreenPoint(pos)
end

--- 获取试用英雄配置
function cysoldierssortie_comp_level:GetCampaignFR_TrialUnitConfig()
    if self.campaignFRTrailUnitConfig ~= nil then
        return self.campaignFRTrailUnitConfig
    end
    if self.cysoldierssortie_firstRecharge_Endless or self.cysoldierssortie_firstRecharge_Normal then
        local initialUnit = self.MiniLevelCfg.InitialUnit
        --- 记录是否提供试用英雄
        local campaignFRUseTrail = not string.IsNullOrEmpty(initialUnit)
        if campaignFRUseTrail then
            self.campaignFRTrailUnitConfig = game_scheme:MiniUnit_0(tonumber(initialUnit))
        end
        return self.campaignFRTrailUnitConfig
    end
    return nil
end

---@return boolean 判断是否拥有英雄
function cysoldierssortie_comp_level:CheckIsOwnedByHeroID(heroID)
    local haveFlag = false
    local gw_hero_data = require "gw_hero_data"
    local ownHeroList = gw_hero_data.GetOwnedHeroIDList()
    for i,v in ipairs(ownHeroList) do
        if v == heroID then
            haveFlag = true
            break
        end
    end
    return haveFlag
end

---@return integer 当前关卡ID
---@return integer[] 已上阵得英雄ID列表只有已拥有得
---@return number 战力总和
---@return number 关卡限制战力
function cysoldierssortie_comp_level:GetSlotsHeroAndPower(lvIndex)
    lvIndex = lvIndex or self.levelMgr.totalMgr.levelIndex
    local lvId = self.levelMgr.totalMgr.levelIDS[lvIndex]
    local heroIDs = {}
    local allPower = 0
    local limitPower = 0
    local slotMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.SlotMgr)
    if slotMgr and slotMgr._slots then
        local gw_power_mgr = require "gw_power_mgr"
        for k, v in pairs(slotMgr._slots) do
            if v._hero and self:CheckIsOwnedByHeroID(v._hero.heroID) then
                heroIDs[#heroIDs + 1] = v._hero.heroID
                allPower = allPower + gw_power_mgr.GetHeroPowerByCfgId(v._hero.heroID)
            end
        end
        limitPower = string.IsNullOrEmpty(self.MiniLevelCfg.LevelCheckPower) and 0 or
            tonumber(self.MiniLevelCfg.LevelCheckPower)
    end
    return lvId, heroIDs, allPower, limitPower
end

function cysoldierssortie_comp_level:TriggerOpenPanelInGame()
    if self.lastBcTimeScale == nil then
        self.lastBcTimeScale = bc_Time.SetTimeScale(0)
    end
    local totalMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.total)
    if totalMgr then
        totalMgr:AddMainLoopCounter()
    end
end

function cysoldierssortie_comp_level:TriggerClosePanelInGame()
    if self.lastBcTimeScale then
        bc_Time.SetTimeScale(self.lastBcTimeScale)
        self.lastBcTimeScale = nil
    end
    local totalMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.total)
    if totalMgr then
        totalMgr:ReduceMainLoopCounter()
    end
    -- 有界面关闭后，要刷新ui_window_mgr内的 GRaycaster.BlockAll
    self.RefreshWindowMgrGRaycaster = true
end

function cysoldierssortie_comp_level:TriggerEndlessOver()
    self.dropMgr:PickUpAllDrop()
    self.EL_OverFlag = true
    self.isGameOver = true
    self.isStartGame = false
    local totalMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.total)
    if totalMgr then
        totalMgr:SwitchMainLoop(self.isStartGame, true)
    end
end

---@return integer 当前最大波次
---@return integer 通过波次= 当前最大波次-进入的波次
function cysoldierssortie_comp_level:GetEndlessWaveData()
    local firstLvIndex = self.levelMgr.EL_FirstLevelIndex
    local tmpLastLv = self.EL_CompletedLevel or self.levelMgr.totalMgr.levelIndex
    local tmpCompLv = self.EL_CompletedLevel or 0
    return tmpLastLv, math.max(0, tmpCompLv - firstLvIndex + 1)
end

-- 无尽模式，冲锋位置准备
function cysoldierssortie_comp_level:EndlessRushReady()
    if not self.cysoldierssortie_firstRecharge_Endless then
        return
    end
    -- 玩家小队起点偏移
    local offZ = -50
    self.playerLua:InitEndlessPos(offZ)
    local cam_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.cam)
    local camParent = cam_mgr.mainCamParent
    SetTransformLocalPositionAndLocalRotation(camParent, cam_mgr.readyPos.x, cam_mgr.readyPos.y, cam_mgr.readyPos.z + offZ, cam_mgr.readyRot.x, cam_mgr.readyRot.y, cam_mgr.readyRot.z)
end

function cysoldierssortie_comp_level:EndlessRushStart(callBack)
    self:RemoveTrailHero()
    self.EL_RushFlag = true
    self.playerLua:EndlessRushStart(50, 16.6, function()
        self.EL_RushFlag = false
        self:ClearAllEndlessRushEnemy()
        callBack()
    end)
end

--- 移除试用的英雄
function cysoldierssortie_comp_level:RemoveTrailHero()
    -- 活动模式开始时，要移除不提供试用的英雄
    local hasTrailHero = self:GetCampaignFR_TrialUnitConfig()
    if hasTrailHero == nil and not self:CheckIsOwnedByHeroID(cysoldierssortie_CampaignFR_TrialHeroID) then
        local slotMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.SlotMgr)
        slotMgr:RemoveHero({ heroID = cysoldierssortie_CampaignFR_TrialHeroID }, nil)
    end
end

--- 无尽模式冲锋阶段生成的敌人
function cysoldierssortie_comp_level:AddEndlessRushEnemy(character)
    if self.EL_RushEnemyPair == nil then
        self.EL_RushEnemyPair = {}
    end
    self.EL_RushEnemyPair[character.transform.gameObject] = {
        Character = character,
        ShowFlag = false
    }
end

--- 无尽模式冲锋阶段，敌人死亡
function cysoldierssortie_comp_level:EndlessRushEnemyDie(go)
    local entity = self.EL_RushEnemyPair[go]
    if entity then
        local fxName = cysoldierssortie_FxName.enemyDie
        local cfg = game_scheme:MiniUnit_0(entity.Character._unitID)
        if cfg and not string.IsNullOrEmpty(cfg.DeadSound) then
            fxName = cfg.DeadSound
        end
        cysoldierssortie_PlaySfx(fxName, 1)
        entity.Character:EndlessRushDie(self.EL_TmpPlayerPos)
        self.EL_RushEnemyPair[go] = nil
    end
end

function cysoldierssortie_comp_level:ClearAllEndlessRushEnemy()
    if self.EL_RushEnemyPair then
        for k, v in pairs(self.EL_RushEnemyPair) do
            if v.Character._characterState ~= cysoldierssortie_character_state.Die then
                v.Character:EndlessRushDie(nil)
            end
        end
        self.EL_RushEnemyPair = nil
    end
end

function cysoldierssortie_comp_level:ResetPlayerStatus(resetFlag)
    if resetFlag then
        self.playerLua:ResetStatus(false)
    end
    self.EL_ResetPlayerStatusFlag = resetFlag
end

function cysoldierssortie_comp_level:CSUpdate()
    if self.cysoldierssortie_firstRecharge_Endless then
        if self.EL_ResetPlayerStatusFlag then
            self.playerLua:ResetStatus(true)
        end
        if self.EL_RushFlag and self.EL_RushEnemyPair then
            local ecsFlag = entity_manager.URP22 and cysoldierssortie_urp_ecs
            self.EL_TmpPlayerPos = self.playerLua.transform.position
            for _, v in pairs(self.EL_RushEnemyPair) do
                local charEntity = v.Character._character_entity
                if not v.ShowFlag then
                    if ecsFlag then
                        if charEntity._entity then
                            EntityHybridUtility.SetEnable(charEntity._entity, true)
                            v.ShowFlag = true
                        end
                    else
                        if bc_IsNotNull(charEntity.modelGo) then
                            charEntity.modelGo:SetActive(true)
                            v.ShowFlag = true
                        end
                    end
                    -- 首次显示成功后，要调用一次播放动画。并且冲击玩家队伍
                    if v.ShowFlag then
                        v.Character:PlayAnim(cysoldierssortie_hero_anim_set.Run)
                        v.Character:CreateNavAgent()
                        charEntity:InitNavAgentData()
                    end
                else
                    if charEntity:IsActiveNavAgent() then
                        charEntity:SetDestinationXYZ(self.EL_TmpPlayerPos.x, self.EL_TmpPlayerPos.y,
                            self.EL_TmpPlayerPos.z)
                    end
                end
            end
        end
    end
    -- 刷新ui_window_mgr内的 GRaycaster.BlockAll
    if self.RefreshWindowMgrGRaycaster then
        self.RefreshWindowMgrGRaycaster = false
        ui_window_mgr:CheckState()
    end
end

---@return integer 获取无尽模式主动跳出的类型 1=战力不足，2=关卡未解锁
function cysoldierssortie_comp_level:GetEndlessBreakType()
    return self.EL_BreakType
end

---无尽模式，检测下一关是否可用
---@param callBack fun(powerFlag:boolean,unlockFlag:boolean) 是否可以进入下一关
function cysoldierssortie_comp_level:EndlessCheckNextOpen(callBack)
    self.EL_CheckPowerFlag = nil
    local powerFlag = nil
    local unlockFlag = nil
    local taskCount = 2
    local completed = function()
        taskCount = taskCount - 1
        if taskCount < 1 then
            callBack(powerFlag, unlockFlag)
        end
    end
    if self.EL_CheckPower_Fun == nil then
        self.EL_CheckPower_Fun = function(eventName, msg)
            if not self.EL_CheckPowerFlag then
                return
            end
            self.EL_CheckPowerFlag = false
            powerFlag = msg.errorcode == 0
            --校验通过
            completed()
        end
        event.Register(event_activity_define.MSG_XYX_ENDLESS_SET_HERO_POWER_RSP, self.EL_CheckPower_Fun)
    end
    local nextLvIndex = self.levelMgr.totalMgr.levelIndex + 1
    -- 战力校验
    local curLvID, heroIds, allPower, limitPower = self:GetSlotsHeroAndPower(nextLvIndex)
    -- 先本地判断
    if allPower < limitPower then
        powerFlag = false
        completed()
    else
        self.EL_CheckPowerFlag = true
        local skipFlag = false
        -- 跳过服务器校验，给策划配关用
        if isEditor then
            local tmpValue = CS.UnityEngine.PlayerPrefs.GetInt("SoldierSSortieCampaign_Skip_CheckPower", 0)
            if tmpValue == 1 then
                skipFlag = true
                self.EL_CheckPower_Fun(event_activity_define.MSG_XYX_ENDLESS_SET_HERO_POWER_RSP, {errorcode = 0})
            end
        end
        if not skipFlag then
            local hero_first_charge_mgr = require "hero_first_charge_mgr"
            -- 服务器校验战斗力
            hero_first_charge_mgr.HeroPowerCheckReq(heroIds, curLvID)
        end
    end
    local skipUnlockFlag = false
    -- 要判断是否下一关是否解锁
    if isEditor then
        local tmpValue = CS.UnityEngine.PlayerPrefs.GetInt("SoldierSSortieCampaign_Skip_CheckUnlock", 0)
        skipUnlockFlag = tmpValue == 1
        if skipUnlockFlag then
            unlockFlag = true
        end
    end
    if not skipUnlockFlag then
        local nextLvID = self.levelMgr.totalMgr.levelIDS[nextLvIndex]
        local hero_first_charge_data = require "hero_first_charge_data"
        unlockFlag = hero_first_charge_data.JudgeLevelIsUnlock(nextLvID)
    end
    completed()
end

return cysoldierssortie_comp_level