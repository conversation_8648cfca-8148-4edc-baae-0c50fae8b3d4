﻿local PlayerPrefs = CS.UnityEngine.PlayerPrefs

local halloween_activity_slot_machine_data_helper = require "halloween_activity_slot_machine_data_helper"

local key = "halloween_activity_slot_machine_history_data"
local halloween_activity_slot_machine_history_data = {
    inited = false,
    data = {

    }
}

local single_history_data = {
    time_stamp = 0,
    result_id = 0,
    count = 1,
    pattern_group_randomed = {},
}

function halloween_activity_slot_machine_history_data:Init()
    if self.inited then
        return
    end
    local real_key = halloween_activity_slot_machine_data_helper.GetRoleKey(key)
    local data_str = PlayerPrefs.GetString(real_key, "[]")
    self.data = halloween_activity_slot_machine_data_helper.DecodeJsonStr(data_str)

    self.inited = true
end

function halloween_activity_slot_machine_history_data:SetTestData()
    local halloween_activity_slot_machine_history_test_data = require "halloween_activity_slot_machine_history_test_data"
    self.data = halloween_activity_slot_machine_history_test_data
end

function halloween_activity_slot_machine_history_data:Save()
    local real_key = halloween_activity_slot_machine_data_helper.GetR<PERSON><PERSON>ey(key)
    PlayerPrefs.SetString(real_key, halloween_activity_slot_machine_data_helper.GetJsonStr(self.data))
    PlayerPrefs.Save()
end

function halloween_activity_slot_machine_history_data:GetDataSplitByServerDate(serverTimeZone)
    -- 1. 按时间戳倒序排序
    table.sort(self.data, function(a, b)
        return a.time_stamp > b.time_stamp
    end)

    local result = {}
    local date_map = {}

    for _, item in ipairs(self.data) do
        -- 2. 计算带时区的时间戳
        local adj_time = item.time_stamp + serverTimeZone * 3600
        local t = os.date("!*t", adj_time) -- 用 UTC 表达，再加时区补偿

        -- 3. 拼出日期 key
        local date_key = string.format("%04d-%02d-%02d", t.year, t.month, t.day)

        -- 4. 归类到对应日期
        if not date_map[date_key] then
            date_map[date_key] = { date = date_key, items = {} }
            table.insert(result, date_map[date_key])
        end
        table.insert(date_map[date_key].items, item)
    end

    -- result 已按 list 遍历顺序保持（新日期先出现）
    return result
end

function halloween_activity_slot_machine_history_data:AddHistory(time_stamp, reward_id, count, pattern_group_randomed)
    table.insert(self.data, {
        time_stamp = time_stamp,
        reward_id = reward_id,
        count = count,
        pattern_group_randomed = pattern_group_randomed,
    })
    self:Save()
end

function halloween_activity_slot_machine_history_data:GetLastData()
    if #self.data > 0 then
        return self.data[#self.data]
    end
    return nil
end

function halloween_activity_slot_machine_history_data:GetLastPattern()
    local data = self:GetLastData()
    return data and data.pattern_group_randomed or nil
end


return halloween_activity_slot_machine_history_data