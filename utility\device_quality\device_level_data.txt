local SystemInfo = CS.UnityEngine.SystemInfo
local Application = CS.UnityEngine.Application
local Graphics = CS.UnityEngine.Graphics
local Utility = CS.War.Script.Utility
local RuntimePlatform = CS.UnityEngine.RuntimePlatform
local GraphicsDeviceType = CS.UnityEngine.Rendering.GraphicsDeviceType

local tostring = tostring
local string = string
local tonumber = tonumber
local math = math
local ipairs = ipairs
local print = print
local require = require

local log = require "log"

local json = require "dkjson"

local _M = {}

local deviceData = nil
local callbacks = {}
local jsonValue
local reportLevel = nil
local GraphicsDeviceCfg

function _M.GetDeviceLevelData(cb)
    if _M.CheckValueData() then
        if cb then
            cb()
        end
        return deviceData
    end
    local game_scheme = require "game_scheme"
    local count = game_scheme:DeviceLevel_nums()
    for i = 0, count-1 do
        local config = game_scheme:DeviceLevel(i)
        if config and config.ID then
            if _M.CheckValueData() == false then
                deviceData = {}
            end
            deviceData[tostring(config.ID)] = config
        end
    end
    if cb then
        cb()
    end
end

function _M.GetReportLevel()
    return reportLevel
end

function _M.CheckValueData()
    return deviceData ~= nil
end

function _M.GetMemorySizeID()
    if Application.platform == RuntimePlatform.IPhonePlayer then
        return _M.GetMemIOS()
    end
    if SystemInfo.systemMemorySize < 1500 then
        return 1001
    elseif SystemInfo.systemMemorySize < 2500 then
        return 1002
    elseif SystemInfo.systemMemorySize < 3500 then
        return 1003
    elseif SystemInfo.systemMemorySize < 4000 then
        return 1004
    elseif SystemInfo.systemMemorySize < 4500 then
        return 1005
    elseif SystemInfo.systemMemorySize < 5000 then
        return 1006
    else
        return 1007
    end
end

function _M.GetGPUMemorySizeID()
    if SystemInfo.graphicsMemorySize < 1024 then
        return 2001
    else
        return 2002
    end
end

function _M.GetFrequencyID()
    --if SystemInfo.processorFrequency <= 1500 then
    --    return 3001
    --elseif SystemInfo.processorFrequency <= 2200 then
    --    return 3002
    --else
    --    return 3003
    --end
    --print("SystemInfo.processorFrequency", SystemInfo.processorFrequency)
    return 3003
end

function _M.GetOpenGLCfgID()
    local opengles = Graphics.minOpenGLESVersion
    local openGLVer = SystemInfo.graphicsDeviceVersion
    local deviceType = SystemInfo.graphicsDeviceType
    local num = string.find(openGLVer, "3.2", 1, true)
    --log.Warning("graphics device info:", opengles, openGLVer, deviceType, num)
    
    if deviceType == GraphicsDeviceType.Null or deviceType == GraphicsDeviceType.OpenGLES2 then
        return 4001
    elseif deviceType == GraphicsDeviceType.OpenGLES3 and not num then
        --OpenGL ES 3.2算4003
        return 4002
    else
        return 4003
    end
end


function _M.GetGPUID()
    local deviceLevel = _M.GetDeviceLevelByConfig()
    if deviceLevel ~= nil then
        --log.Warning("GPUdevicelevel", SystemInfo.graphicsDeviceName, deviceLevel)
        return deviceLevel + 6000
    else
        deviceLevel = -1
    end
    if Application.platform == RuntimePlatform.IPhonePlayer then
        deviceLevel = _M.GetGPUIOS()
    else
        deviceLevel = _M.GetGPUAndroidID()
    end
    if deviceLevel == -1 then
        return 6002
    else
        return deviceLevel
    end
end

function _M.GetDeviceLevelByConfig()
    if GraphicsDeviceCfg == nil then
        GraphicsDeviceCfg = {}
        local game_scheme = require "game_scheme"
        local count = game_scheme:GraphicsDevice_nums()
        for i = 0, count-1 do
            local config = game_scheme:GraphicsDevice(i)
            local dict = GraphicsDeviceCfg[config.type]
            if dict == nil then
                dict = {}
                GraphicsDeviceCfg[config.type] = dict
            end
            dict[config.graphicsDevice] = config.deviceLevel
        end
    end
    local deviceLevel = nil
    local deviceModel = SystemInfo.deviceModel
    local name = SystemInfo.graphicsDeviceName
    if GraphicsDeviceCfg[1] then
        deviceLevel = GraphicsDeviceCfg[1][deviceModel]
        if deviceLevel then
            --print("识别到配置机型1，读取到的设备等级为："..deviceLevel)
        end
    end
    if deviceLevel == nil and GraphicsDeviceCfg[2] then
        deviceLevel = GraphicsDeviceCfg[2][name]
        if deviceLevel then
            --print("识别到配置机型2，读取到的设备等级为："..deviceLevel)
        end
    end
    return deviceLevel
end

--IOS使用内存分级，目前看GPU足够
function _M.GetGPUIOS()
    local gpuName = SystemInfo.graphicsDeviceName
    print("gpuName [" .. gpuName .. "]")
    local result = -1
    gpuName = string.lower(gpuName)
    local tmp = string.gsub(gpuName,"apple","")
    tmp = string.gsub(tmp,"gpu","")
    local index = string.find(tmp,"a")
    if index ~= nil then
        local num = tonumber(string.sub(tmp,index+1)) or 0
        if num > 0 then
            result =  math.min(num - 9, 4) --n+1 3档最高
            if result > 4 then
                return 11002
            else
                return 11001
            end
        end
    end
    local isM1 = string.find(tmp,"m")
    if isM1 ~= nil then
        return 11002
    end
    return result
end

function _M.GetMemIOS()
    local systemMemorySize = SystemInfo.systemMemorySize
    local result = 10001
    if systemMemorySize < 2500 then
        result = 10001
    elseif systemMemorySize < 3000 then
        result = 10002
    elseif systemMemorySize < 3500 then
        result = 10003
    elseif systemMemorySize < 4000 then
        result = 10004
    elseif systemMemorySize < 4500 then
        result = 10005
    else
        result = 10006
    end
    return result
end

function _M.GetGPUAndroidID()
    local result = 0
    local gpuName = string.lower(SystemInfo.graphicsDeviceName)
    local splitList = {" ","\t" ,"\r","\n","+","-",":"}
    local tokens = nil
    local util = require "util"
    for i,v in ipairs(splitList) do
        tokens = util.SplitString(gpuName,v)
        if tokens and #tokens > 1 then
            break
        end
    end
    --print("gpuName",json.encode(tokens))
    -- for i,v in ipairs(tokens) do
    -- end
    if string.find(tokens[1],"vivante") then
        -- deviceGPUGroup = "vivante"
        result = 0
    elseif tokens[1] == "adreno" then
        result = _M.CheckGPUAdreno(tokens)
    elseif tokens[1] == "powervr" or tokens[1] == "imagination" or tokens[1] == "sgx" then
        result = _M.CheckGPUPowerVR(tokens)
    elseif tokens[1] == "arm" or tokens[1] == "mali" or  tokens[2] == "mali" then
        result = _M.CheckGPUMali(tokens)
    elseif tokens[1] == "tegra" or tokens[1] == "nvidia" then
        result = _M.CheckGPUTegra(tokens)
    end
    if result == 0 then
        reportLevel = true
        result = 6002
    end
    log.Warning("GPUdevicelevel csv no GPU", SystemInfo.graphicsDeviceName, result - 6000)
    return result
end

function _M.CheckGPUAdreno(tokens)
    -- deviceGPUGroup = "Adreno"
    local result = 0
    local num = 0
    for i,v in ipairs(tokens) do
        if result > 6001 then
            break
        end
        num = tonumber(v)
        if num  then
            if num < 400 then
                -- ISGPUOUT = true
                result = 6003
            elseif num < 500 then
                if num < 420 then
                    -- ISGPUOUT = true
                    result = 6001
                else
                    result = 6002
                end
            elseif num < 600 then
                if num <= 512 then
                    result = 6001
                elseif num < 530 then
                    result = 6001
                elseif num <= 540 then
                    result = 6002
                else
                    result = 6003
                end
            elseif num < 700 then
                if num <= 612 then
                    result = 6001
                elseif num <= 615 then
                    result = 6001
                elseif num < 630 then
                    result = 6002
                else
                    result = 6003
                end
            else
                if num > 999 then
                    return 6003
                end
                local tmp = tostring(num)
                tmp = string.sub(tmp,2)
                num = tonumber(tmp) or 0
                if num < 12 then
                    result = 6001
                elseif num < 15 then
                    result = 6001
                elseif num < 30 then
                    result = 6002
                else
                    result = 6003
                end
            end
        end
    end
    return result
end

function _M.CheckGPUTegra(tokens)
    -- deviceGPUGroup = "Tegra"
    local result = 0
    local num = 0
    for i,v in ipairs(tokens) do
        num = tonumber(v)
        if num then
            result = 6001
        else
            local  text = v
            if text == "k1" then
                result = 6001
                break
            elseif text == "x1" then
                result = 6002
                break
            end
        end
    end
    return result
end

function _M.CheckGPUPowerVR(tokens)
    --过于老旧的GPU，暂时无需判断
    -- deviceGPUGroup = "PowerVR(Android)"
    --local result = -1
    --local rogue = false
    --for i,v in ipairs(tokens) do
    --    if result >= 0 then
    --        break
    --    end
    --    local text = v
    --    if text == "sgx" then
    --        return 6010
    --    end
    --    if text == "rogue" then
    --        rogue = true
    --    end
    --end
    return 6001
end

function _M.CheckGPUMali(tokens)
    local result = 0
    local num = 0 --GPU数字型号
    local core = 0--GPU核数
    local text = tokens[2]
    local isT = false
    if text then
        local fristStr = string.sub(text,1,1)
        isT = fristStr == "t"
        if  fristStr == "t" or fristStr == "g" then
            text = string.sub(text,2)
            num = tonumber(text) or 0
        end
    end
    text = tokens[3]
    if text then
        local title  = string.find(text,"mp")
        if title == nil then
            title = string.find(text,"mc")
        end
        if title  then
            text = string.sub(text,title+2)
            core = tonumber(text) or 0
        end
    end
    if num > 0 then
        if isT then--T系列GPU  例如T880 T760 这类的机器基本都是低端机  代表机型Helio X27  ，Kirin 955，Exynos 8890
            result = 6001
        else --P系列GPU
            if num < 60 then
                if num < 57 or (num == 57 and core <= 3) then --低于3核G57的作为低端机，代表机型为天玑720和天玑800U
                    return 6001
                else  --大于3核 G57均为中端机
                    return 6002
                end
            else
                if num < 71 or (num == 71 and core <= 8) then --低于8核G71的作为低端机，代表机型为麒麟960
                    result = 6001
                elseif num < 77 or (num == 77 and core <= 8) then --低于8核G77的作为中端机  代表机型8核G77的麒麟985与7核G77的天玑1000L
                    if num == 76 and core == 16 then --特殊机型 16核的G76 作为高端机 这里只有麒麟990的特殊机型 
                        result = 6003
                    else
                        result = 6002
                    end
                else --大于8核G77的均为高端机
                    result = 6003
                end
            end
        end
    end
    return result
end

return _M
